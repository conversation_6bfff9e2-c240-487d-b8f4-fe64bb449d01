#!/bin/bash

# Find all XML files in the demo directory
for file in $(find /mnt/extra-addons/ai_amigo_attires_demo/demo -name "product_*.xml"); do
  echo "Processing $file..."
  
  # Update attribute references to include the module name
  sed -i 's/ref="product_attribute_/ref="ai_amigo_attires_demo.product_attribute_/g' "$file"
  sed -i 's/ref="product_attribute_value_/ref="ai_amigo_attires_demo.product_attribute_value_/g' "$file"
  
  echo "Done with $file"
done

echo "All files processed!"
