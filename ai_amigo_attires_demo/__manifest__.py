# -*- coding: utf-8 -*-
{
    'name': 'Amigo Attires Demo Data',
    'version': '1.0',
    'category': 'Website/eCommerce',
    'summary': 'Demo data for Amigo Attires eCommerce',
    'description': """
        This module provides demo data for Amigo Attires eCommerce website.
        It includes products for all categories with proper attributes and variants.
    """,
    'author': 'Amigo Attires',
    'website': 'https://www.amigoattires.com',
    'depends': [
        'website_sale',
        'product',
        'ai_amigo_attires',
    ],
    'data': [
        # Attributes
        'data/product_attribute_data.xml',
    ],
    'demo': [
        # Categories
        'demo/product_category_men.xml',
        'demo/product_category_women.xml',

        # Men's products
        'demo/product_men_kurta_pyjama.xml',
        'demo/product_men_nehru_jacket.xml',
        'demo/product_men_kurta_set_nehru_jacket.xml',

        # Women's products
        'demo/product_women_indian_outfit_kurta_dupatta.xml',
        'demo/product_women_indian_outfit_anarkali.xml',
        'demo/product_women_indian_outfit_embroided_kurta.xml',
        'demo/product_women_indian_outfit_ethenic_wear.xml',
        'demo/product_women_chaniya_choli_festive.xml',
        'demo/product_women_chaniya_choli_wedding.xml',
        'demo/product_women_gown.xml',
    ],
    'images': [],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
