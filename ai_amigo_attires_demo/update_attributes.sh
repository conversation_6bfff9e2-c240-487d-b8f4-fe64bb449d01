#!/bin/bash

# Find all XML files in the demo directory
for file in $(find /mnt/extra-addons/ai_amigo_attires_demo/demo -name "product_*.xml" | grep -v "product_attributes.xml" | grep -v "product_men_kurta_pyjama.xml"); do
  echo "Processing $file..."
  
  # Replace attribute references
  sed -i 's/attribute_id" ref="product.product_attribute_2"/attribute_id" ref="product_attribute_color"/g' "$file"
  sed -i 's/attribute_id" ref="product.product_attribute_1"/attribute_id" ref="product_attribute_size"/g' "$file"
  sed -i 's/attribute_id" ref="product.product_attribute_3"/attribute_id" ref="product_attribute_fabric"/g' "$file"
  
  # Replace color attribute values
  sed -i 's/ref('"'"'product.product_attribute_value_3'"'"')/ref('"'"'product_attribute_value_color_red'"'"')/g' "$file"
  sed -i 's/ref('"'"'product.product_attribute_value_4'"'"')/ref('"'"'product_attribute_value_color_black'"'"')/g' "$file"
  sed -i 's/ref('"'"'__export__.product_attribute_value_29_6edb5f0e'"'"')/ref('"'"'product_attribute_value_color_blue'"'"')/g' "$file"
  sed -i 's/ref('"'"'__export__.product_attribute_value_30_8315417c'"'"')/ref('"'"'product_attribute_value_color_green'"'"')/g' "$file"
  sed -i 's/ref('"'"'__export__.product_attribute_value_144_f98309aa'"'"')/ref('"'"'product_attribute_value_color_green'"'"')/g' "$file"
  sed -i 's/ref('"'"'__export__.product_attribute_value_145_bc2cacb2'"'"')/ref('"'"'product_attribute_value_color_pink'"'"')/g' "$file"
  
  # Replace size attribute values
  sed -i 's/ref('"'"'product.size_attribute_s'"'"')/ref('"'"'product_attribute_value_size_s'"'"')/g' "$file"
  sed -i 's/ref('"'"'product.size_attribute_m'"'"')/ref('"'"'product_attribute_value_size_m'"'"')/g' "$file"
  sed -i 's/ref('"'"'product.size_attribute_l'"'"')/ref('"'"'product_attribute_value_size_l'"'"')/g' "$file"
  sed -i 's/ref('"'"'__export__.product_attribute_value_23_23b7372d'"'"')/ref('"'"'product_attribute_value_size_xl'"'"')/g' "$file"
  sed -i 's/ref('"'"'__export__.product_attribute_value_24_6fddad93'"'"')/ref('"'"'product_attribute_value_size_xxl'"'"')/g' "$file"
  sed -i 's/ref('"'"'__export__.product_attribute_value_25_511880eb'"'"')/ref('"'"'product_attribute_value_size_xxxl'"'"')/g' "$file"
  
  # Replace fabric attribute values
  sed -i 's/ref('"'"'__export__.product_attribute_value_27_738dbd09'"'"')/ref('"'"'product_attribute_value_fabric_silk'"'"')/g' "$file"
  sed -i 's/ref('"'"'__export__.product_attribute_value_28_94a1f0bd'"'"')/ref('"'"'product_attribute_value_fabric_cotton'"'"')/g' "$file"
  sed -i 's/ref('"'"'product.fabric_attribute_custom'"'"')/ref('"'"'product_attribute_value_fabric_blend'"'"')/g' "$file"
  
  echo "Done with $file"
done

echo "All files processed!"
