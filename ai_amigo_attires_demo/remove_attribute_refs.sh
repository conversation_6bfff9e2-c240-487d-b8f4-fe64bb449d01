#!/bin/bash

# Find all XML files in the demo directory
for file in $(find /mnt/extra-addons/ai_amigo_attires_demo/demo -name "product_*.xml" | grep -v "product_category"); do
  echo "Processing $file..."
  
  # Remove all attribute lines
  sed -i '/<record id=".*_color_attribute_line"/,/<\/record>/d' "$file"
  sed -i '/<record id=".*_size_attribute_line"/,/<\/record>/d' "$file"
  sed -i '/<record id=".*_fabric_attribute_line"/,/<\/record>/d' "$file"
  
  echo "Done with $file"
done

echo "All files processed!"
