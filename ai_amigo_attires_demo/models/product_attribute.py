# -*- coding: utf-8 -*-
from odoo import api, fields, models

class ProductAttribute(models.Model):
    _inherit = 'product.attribute'
    
    @api.model
    def _create_demo_attributes(self):
        """Create demo attributes and values"""
        # Create Size attribute
        size_attribute = self.env['product.attribute'].create({
            'name': 'Size',
            'sequence': 1,
            'create_variant': 'always',
            'display_type': 'select',
        })
        
        # Create Size attribute values
        size_values = ['S', 'M', 'L', 'XL', 'XXL', 'XXXL']
        for i, value in enumerate(size_values, 1):
            self.env['product.attribute.value'].create({
                'name': value,
                'attribute_id': size_attribute.id,
                'sequence': i,
            })
        
        # Create Color attribute
        color_attribute = self.env['product.attribute'].create({
            'name': 'Color',
            'sequence': 2,
            'create_variant': 'always',
            'display_type': 'color',
        })
        
        # Create Color attribute values
        color_values = [
            ('Red', '#FF0000'),
            ('Black', '#000000'),
            ('Blue', '#0000FF'),
            ('Green', '#008000'),
            ('Pink', '#FFC0CB'),
        ]
        for i, (name, html_color) in enumerate(color_values, 1):
            self.env['product.attribute.value'].create({
                'name': name,
                'attribute_id': color_attribute.id,
                'sequence': i,
                'html_color': html_color,
            })
        
        # Create Fabric attribute
        fabric_attribute = self.env['product.attribute'].create({
            'name': 'Fabric',
            'sequence': 3,
            'create_variant': 'always',
            'display_type': 'select',
        })
        
        # Create Fabric attribute values
        fabric_values = ['Cotton', 'Silk', 'Cotton-Silk Blend']
        for i, value in enumerate(fabric_values, 1):
            self.env['product.attribute.value'].create({
                'name': value,
                'attribute_id': fabric_attribute.id,
                'sequence': i,
            })
        
        return True
