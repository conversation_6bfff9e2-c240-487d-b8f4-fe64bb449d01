<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Product Attributes -->
        <record id="product_attribute_size" model="product.attribute">
            <field name="name">Size</field>
            <field name="sequence">1</field>
            <field name="create_variant">always</field>
            <field name="display_type">select</field>
        </record>
        
        <record id="product_attribute_color" model="product.attribute">
            <field name="name">Color</field>
            <field name="sequence">2</field>
            <field name="create_variant">always</field>
            <field name="display_type">color</field>
        </record>
        
        <record id="product_attribute_fabric" model="product.attribute">
            <field name="name">Fabric</field>
            <field name="sequence">3</field>
            <field name="create_variant">always</field>
            <field name="display_type">select</field>
        </record>
        
        <!-- Size Attribute Values -->
        <record id="product_attribute_value_size_s" model="product.attribute.value">
            <field name="name">S</field>
            <field name="attribute_id" ref="product_attribute_size"/>
            <field name="sequence">1</field>
        </record>
        
        <record id="product_attribute_value_size_m" model="product.attribute.value">
            <field name="name">M</field>
            <field name="attribute_id" ref="product_attribute_size"/>
            <field name="sequence">2</field>
        </record>
        
        <record id="product_attribute_value_size_l" model="product.attribute.value">
            <field name="name">L</field>
            <field name="attribute_id" ref="product_attribute_size"/>
            <field name="sequence">3</field>
        </record>
        
        <record id="product_attribute_value_size_xl" model="product.attribute.value">
            <field name="name">XL</field>
            <field name="attribute_id" ref="product_attribute_size"/>
            <field name="sequence">4</field>
        </record>
        
        <record id="product_attribute_value_size_xxl" model="product.attribute.value">
            <field name="name">XXL</field>
            <field name="attribute_id" ref="product_attribute_size"/>
            <field name="sequence">5</field>
        </record>
        
        <record id="product_attribute_value_size_xxxl" model="product.attribute.value">
            <field name="name">XXXL</field>
            <field name="attribute_id" ref="product_attribute_size"/>
            <field name="sequence">6</field>
        </record>
        
        <!-- Color Attribute Values -->
        <record id="product_attribute_value_color_red" model="product.attribute.value">
            <field name="name">Red</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">1</field>
            <field name="html_color">#FF0000</field>
        </record>
        
        <record id="product_attribute_value_color_black" model="product.attribute.value">
            <field name="name">Black</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">2</field>
            <field name="html_color">#000000</field>
        </record>
        
        <record id="product_attribute_value_color_blue" model="product.attribute.value">
            <field name="name">Blue</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">3</field>
            <field name="html_color">#0000FF</field>
        </record>
        
        <record id="product_attribute_value_color_green" model="product.attribute.value">
            <field name="name">Green</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">4</field>
            <field name="html_color">#008000</field>
        </record>
        
        <record id="product_attribute_value_color_pink" model="product.attribute.value">
            <field name="name">Pink</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">5</field>
            <field name="html_color">#FFC0CB</field>
        </record>
        
        <!-- Fabric Attribute Values -->
        <record id="product_attribute_value_fabric_cotton" model="product.attribute.value">
            <field name="name">Cotton</field>
            <field name="attribute_id" ref="product_attribute_fabric"/>
            <field name="sequence">1</field>
        </record>
        
        <record id="product_attribute_value_fabric_silk" model="product.attribute.value">
            <field name="name">Silk</field>
            <field name="attribute_id" ref="product_attribute_fabric"/>
            <field name="sequence">2</field>
        </record>
        
        <record id="product_attribute_value_fabric_blend" model="product.attribute.value">
            <field name="name">Cotton-Silk Blend</field>
            <field name="attribute_id" ref="product_attribute_fabric"/>
            <field name="sequence">3</field>
        </record>
    </data>
</odoo>
