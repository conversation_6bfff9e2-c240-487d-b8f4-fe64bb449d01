from odoo import models, fields, api, _
import logging

_logger = logging.getLogger(__name__)


class ResCurrencyRateProvider(models.Model):
    """Dummy model to replace the missing OCA model during uninstall"""
    _name = "res.currency.rate.provider"
    _description = "Dummy Currency Rate Provider (for cleanup)"
    
    name = fields.Char(string="Name", default="Dummy Provider")
    service = fields.Selection([('dummy', 'Dummy')], default='dummy')
    company_id = fields.Many2one('res.company', string="Company", default=lambda self: self.env.company)
    active = fields.Boolean(default=True)
    currency_ids = fields.Many2many('res.currency', string="Currencies")
    available_currency_ids = fields.Many2many('res.currency', string="Available Currencies")
    interval_type = fields.Selection([('days', 'Days')], default='days')
    interval_number = fields.Integer(default=1)
    update_schedule = fields.Char(default="Dummy")
    last_successful_run = fields.Date()
    next_run = fields.Date(default=fields.Date.today)
    daily = fields.Boolean(default=False)
    currency_name = fields.Char(related="company_id.currency_id.name")
    
    def _scheduled_update(self):
        """Dummy method to prevent errors"""
        _logger.info("Dummy scheduled update called - doing nothing")
        return True
    
    def _compute_available_currency_ids(self):
        """Dummy compute method"""
        for record in self:
            record.available_currency_ids = self.env['res.currency'].search([])
    
    def _compute_name(self):
        """Dummy compute method"""
        for record in self:
            record.name = "Dummy Provider"
    
    def _compute_update_schedule(self):
        """Dummy compute method"""
        for record in self:
            record.update_schedule = "Dummy Schedule"
    
    def _compute_daily(self):
        """Dummy compute method"""
        for record in self:
            record.daily = False


class ResCurrencyRateUpdateWizard(models.TransientModel):
    """Dummy wizard model to replace the missing OCA wizard"""
    _name = "res.currency.rate.update.wizard"
    _description = "Dummy Currency Rate Update Wizard (for cleanup)"
    
    provider_ids = fields.Many2many('res.currency.rate.provider', string="Providers")
    date_from = fields.Date(string="Date From", default=fields.Date.today)
    date_to = fields.Date(string="Date To", default=fields.Date.today)
    
    def action_update(self):
        """Dummy action method"""
        _logger.info("Dummy wizard action called - doing nothing")
        return {'type': 'ir.actions.act_window_close'}


class CleanupHelper(models.TransientModel):
    """Helper model with cleanup actions"""
    _name = "cleanup.helper"
    _description = "OCA Cleanup Helper"
    
    message = fields.Html(
        string="Instructions",
        default="""
        <h3>OCA Currency Rate Update Cleanup Helper</h3>
        <p>This module provides dummy models to help safely uninstall the OCA currency_rate_update module.</p>
        
        <h4>Steps to clean up:</h4>
        <ol>
            <li><strong>This module is now installed</strong> - providing dummy models</li>
            <li><strong>Go to Apps</strong> and find "Currency Rate Update" (OCA module)</li>
            <li><strong>Uninstall the OCA module</strong> - it should work now without errors</li>
            <li><strong>Come back and uninstall this helper module</strong></li>
            <li><strong>Use the ai_currencyrateupdate module</strong> for your currency needs</li>
        </ol>
        
        <h4>Why this works:</h4>
        <p>The OCA module left references to models that no longer exist. This helper module 
        creates dummy versions of those models so Odoo can safely remove all references 
        during uninstallation.</p>
        
        <p><strong>Note:</strong> This is a temporary helper module. Uninstall it after 
        cleaning up the OCA module.</p>
        """,
        readonly=True
    )
    
    def action_force_cleanup(self):
        """Force cleanup action using Odoo ORM"""
        try:
            # Mark OCA module as uninstalled
            oca_module = self.env['ir.module.module'].search([('name', '=', 'currency_rate_update')])
            if oca_module:
                oca_module.write({'state': 'uninstalled'})
                
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Cleanup Completed"),
                    'message': _("OCA module has been marked as uninstalled. You can now uninstall this helper module."),
                    'type': 'success',
                }
            }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Cleanup Error"),
                    'message': _("Error during cleanup: %s") % str(e),
                    'type': 'danger',
                }
            }
