2025-07-18 10:59:34,853 - INFO - ============================================================
2025-07-18 10:59:34,853 - INFO - J&J BEARING WEBSITE MODULE INSTALLATION
2025-07-18 10:59:34,853 - INFO - ============================================================
2025-07-18 10:59:34,854 - INFO - Connecting to Odoo at http://jnj18.arihantai.com:8069
2025-07-18 10:59:34,970 - INFO - Odoo version: {'server_version': '18.0-20241123', 'server_version_info': [18, 0, 0, 'final', 0, ''], 'server_serie': '18.0', 'protocol_version': 1}
2025-07-18 10:59:35,629 - INFO - Successfully connected as user ID: 8
2025-07-18 10:59:35,630 - INFO - Updating module list...
2025-07-18 10:59:36,365 - ERROR - Error executing ir.module.module.update_list: <Fault 3: 'Access Denied'>
2025-07-18 10:59:36,370 - ERROR - Failed to update module list: <Fault 3: 'Access Denied'>
2025-07-18 10:59:36,372 - ERROR - Failed to update module list. Continuing anyway...
2025-07-18 10:59:36,372 - INFO - Checking if module jnj_bearing_website exists...
2025-07-18 10:59:36,496 - INFO - ✅ Module jnj_bearing_website found in system
2025-07-18 10:59:36,601 - ERROR - Error executing ir.module.module.read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3782, in read\n    self._origin.fetch(fields)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4057, in fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names, ignore_when_in_cache=True)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'version\' on model \'ir.module.module\'\n'>
2025-07-18 10:59:36,601 - ERROR - Error getting module info: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3782, in read\n    self._origin.fetch(fields)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4057, in fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names, ignore_when_in_cache=True)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'version\' on model \'ir.module.module\'\n'>
2025-07-18 10:59:36,601 - ERROR - Could not retrieve module information.
