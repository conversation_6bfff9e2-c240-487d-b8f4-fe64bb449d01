from odoo import models, fields, api
from odoo.exceptions import UserError


class ResPartner(models.Model):
    _inherit = 'res.partner'

    x_client_document_ids = fields.One2many('x_client_documents', 'x_client', string='Client Documents')


class ClientDocuments(models.Model):
    _name = 'x_client_documents'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Client Documents'

    x_task_ref = fields.Many2one('project.task', string='Task Reference', required=True, store=True)
    x_client = fields.Many2one('res.partner', string='Client', readonly=True, store=True , ondelete='set null', related='x_task_ref.partner_id')
    x_document = fields.Binary(string='Document', required=True, store=True)
    x_document_folder = fields.Many2one('project.project', string='Document Folder', readonly=True, store=True, related='x_task_ref.project_id')
    name = fields.Char(string='Name', required=True, store=True)

