<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Partner Form View Extension for Client Documents -->
    <record id="view_partner_form_client_documents" model="ir.ui.view">
        <field name="name">res.partner.form.client.documents</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page string="Documents" name="documents_info">
                    <field name="x_client_document_ids" nolabel="1">
                        <list>
                            <field name="name"/>
                            <field name="x_task_ref"/>
                            <field name="x_document_folder"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <record id="view_x_client_documents_form" model="ir.ui.view">
        <field name="name">x_client_documents.form</field>
        <field name="model">x_client_documents</field>
        <field name="arch" type="xml">
            <form string="Client Documents">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Document Name..."/>
                        </h1>
                    </div>
                    <group>
                        <group name="document_info" string="Document Information">
                            <field name="x_task_ref"/>
                            <field name="x_client"/>
                            <field name="x_document_folder"/>
                            <field name="x_document" filename="name"/>
                        </group>
                    </group>
                </sheet>
            </form>

        </field>
    </record>

    <record id="view_x_client_documents_list" model="ir.ui.view">
        <field name="name">x_client_documents.list</field>
        <field name="model">x_client_documents</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="x_client"/>
                <field name="x_task_ref"/>
                <field name="x_document_folder"/>
            </list>
        </field>
    </record>

    <record id="view_x_client_documents_search" model="ir.ui.view">
        <field name="name">x_client_documents.search</field>
        <field name="model">x_client_documents</field>
        <field name="arch" type="xml">
            <search>
                <field name="name" string="Document Name"/>
                <field name="x_client" string="Client"/>
                <field name="x_task_ref" string="Task Reference"/>
                <field name="x_document_folder" string="Document Folder"/>
                <group expand="0" string="Group By">
                    <filter string="Client" name="group_client" context="{'group_by': 'x_client'}"/>
                    <filter string="Document Folder" name="group_folder" context="{'group_by': 'x_document_folder'}"/>
                    <filter string="Task Reference" name="group_task" context="{'group_by': 'x_task_ref'}"/>
                </group>
            </search>
        </field>
    </record>

    <menuitem id="menu_x_client_documents" name="Documents" parent="imca_groups.menu_ca_management_root" action="action_x_client_documents" sequence="40"/>
</odoo>
