#!/usr/bin/env python3
"""
Simple form test for profectusaccounts.com - headless mode
"""

import asyncio
import xmlrpc.client
from playwright.async_api import async_playwright
import time
import random
import string
from datetime import datetime

# Configuration
WEBSITE_URL = "http://localhost:8069"
CONTACT_PAGE = "/contactus"
DATABASE_CONFIG = {
    'url': 'http://localhost:8069',
    'db': 'profectusaccounts.com',
    'username': 'demo',
    'password': 'demo'
}

def connect_to_odoo():
    """Connect to Odoo database"""
    try:
        common = xmlrpc.client.ServerProxy(f'{DATABASE_CONFIG["url"]}/xmlrpc/2/common')
        uid = common.authenticate(
            DATABASE_CONFIG['db'], 
            DATABASE_CONFIG['username'], 
            DATABASE_CONFIG['password'], 
            {}
        )
        
        if not uid:
            print("❌ Failed to authenticate with <PERSON>doo")
            return None, None
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f'{DATABASE_CONFIG["url"]}/xmlrpc/2/object')
        return models, uid
        
    except Exception as e:
        print(f"❌ Odoo connection error: {e}")
        return None, None

def check_odoo_status():
    """Check if Odoo is running"""
    try:
        print("🔍 Checking Odoo Status...")
        
        # Try to connect to database
        models, uid = connect_to_odoo()
        if models:
            print("✅ Odoo database is accessible")
            
            # Check some basic data
            partner_count = models.execute_kw(
                DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
                'res.partner', 'search_count', [[]]
            )
            print(f"   📊 Partners in database: {partner_count}")
            
            return True
        else:
            print("❌ Odoo database is not accessible")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Odoo status: {e}")
        return False

async def check_website_status():
    """Check if website is accessible"""
    try:
        print("\n🌐 Checking Website Status...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)  # Force headless
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                # Test main page
                print(f"   Testing: {WEBSITE_URL}")
                response = await page.goto(WEBSITE_URL, timeout=10000)
                print(f"   Main page status: {response.status}")
                
                if response.status == 200:
                    print("   ✅ Main website is accessible")
                    
                    # Test contact page
                    print(f"   Testing: {WEBSITE_URL}{CONTACT_PAGE}")
                    contact_response = await page.goto(f"{WEBSITE_URL}{CONTACT_PAGE}", timeout=10000)
                    print(f"   Contact page status: {contact_response.status}")
                    
                    if contact_response.status == 200:
                        print("   ✅ Contact page is accessible")
                        
                        # Take screenshot
                        await page.screenshot(path='contact_page_test.png')
                        print("   📸 Screenshot saved: contact_page_test.png")
                        
                        # Check for forms
                        forms = await page.query_selector_all('form')
                        print(f"   📋 Found {len(forms)} form(s) on contact page")
                        
                        if forms:
                            for i, form in enumerate(forms):
                                action = await form.get_attribute('action')
                                model = await form.get_attribute('data-model_name')
                                print(f"      Form {i+1}: action={action}, model={model}")
                        
                        return True
                    else:
                        print(f"   ❌ Contact page not accessible (Status: {contact_response.status})")
                        return False
                else:
                    print(f"   ❌ Main website not accessible (Status: {response.status})")
                    return False
                
            finally:
                await browser.close()
        
    except Exception as e:
        print(f"❌ Error checking website: {e}")
        return False

def test_database_form_creation():
    """Test creating a contact record directly in database"""
    try:
        print("\n📝 Testing Database Form Creation...")
        
        models, uid = connect_to_odoo()
        if not models:
            return False
        
        # Generate test data
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_data = {
            'name': f'Test Contact {timestamp}',
            'email': f'test_{timestamp}@example.com',
            'phone': '******-123-4567',
            'parent_name': f'Test Company {timestamp}',
            'function': 'Test Subject',
            'comment': f'Test message created at {datetime.now()}',
            'is_company': False,
        }
        
        print("   📋 Creating test contact with data:")
        for key, value in test_data.items():
            print(f"      {key}: {value}")
        
        # Create contact
        contact_id = models.execute_kw(
            DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
            'res.partner', 'create', [test_data]
        )
        
        print(f"   ✅ Created contact with ID: {contact_id}")
        
        # Verify contact
        contact = models.execute_kw(
            DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
            'res.partner', 'read', [contact_id],
            {'fields': ['name', 'email', 'phone', 'parent_name', 'function', 'comment']}
        )
        
        if contact:
            contact = contact[0]
            print(f"   ✅ Contact verification:")
            print(f"      Name: {contact['name']}")
            print(f"      Email: {contact['email']}")
            print(f"      Phone: {contact['phone']}")
            print(f"      Company: {contact['parent_name']}")
            print(f"      Subject: {contact['function']}")
            
            # Clean up
            models.execute_kw(
                DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
                'res.partner', 'unlink', [contact_id]
            )
            print(f"   🧹 Test contact cleaned up")
            
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ Error testing database creation: {e}")
        return False

async def test_form_submission_if_accessible():
    """Test form submission if website is accessible"""
    try:
        print("\n🧪 Testing Form Submission...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                # Navigate to contact page
                response = await page.goto(f"{WEBSITE_URL}{CONTACT_PAGE}", timeout=10000)
                
                if response.status != 200:
                    print(f"   ❌ Cannot access contact page (Status: {response.status})")
                    return False
                
                print("   ✅ Contact page loaded")
                
                # Look for contact form
                form = await page.query_selector('form[data-model_name], form#contactus_form, form[action*="/website/form/"]')
                
                if not form:
                    print("   ❌ No contact form found")
                    return False
                
                print("   ✅ Contact form found")
                
                # Generate test data
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                test_data = {
                    'name': f'Playwright Test {timestamp}',
                    'email': f'playwright_{timestamp}@example.com',
                    'phone': '******-999-8888',
                    'company': f'Playwright Test Co {timestamp}',
                    'subject': f'Playwright Test {timestamp}',
                    'message': f'This is a test message from Playwright at {datetime.now()}'
                }
                
                print("   📝 Filling form with test data...")
                
                # Fill form fields (try multiple selectors)
                field_selectors = {
                    'name': ['input[name="name"]', '#contact1'],
                    'email': ['input[name="email"]', 'input[name="email_from"]', '#contact3'],
                    'phone': ['input[name="phone"]', '#contact2'],
                    'company': ['input[name="parent_name"]', 'input[name="company"]', '#contact4'],
                    'subject': ['input[name="function"]', 'input[name="subject"]', '#contact5'],
                    'message': ['textarea[name="comment"]', 'textarea[name="description"]', 'textarea[name="Message"]', '#contact6']
                }
                
                filled_fields = 0
                for field_name, selectors in field_selectors.items():
                    for selector in selectors:
                        try:
                            element = await page.query_selector(selector)
                            if element:
                                await element.fill(test_data[field_name])
                                print(f"      ✅ {field_name}: filled")
                                filled_fields += 1
                                break
                        except:
                            continue
                
                print(f"   📊 Filled {filled_fields}/{len(field_selectors)} fields")
                
                if filled_fields < 3:  # Need at least name, email, message
                    print("   ⚠️ Not enough fields filled to submit")
                    return False
                
                # Take screenshot before submission
                await page.screenshot(path='form_before_submit.png')
                print("   📸 Screenshot saved: form_before_submit.png")
                
                # Submit form
                submit_selectors = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    '.s_website_form_send',
                    'a[role="button"].btn'
                ]
                
                submitted = False
                for selector in submit_selectors:
                    try:
                        submit_btn = await page.query_selector(selector)
                        if submit_btn:
                            await submit_btn.click()
                            print(f"   🚀 Form submitted using: {selector}")
                            submitted = True
                            break
                    except:
                        continue
                
                if not submitted:
                    print("   ❌ Could not find submit button")
                    return False
                
                # Wait for response
                await page.wait_for_timeout(3000)
                
                # Take screenshot after submission
                await page.screenshot(path='form_after_submit.png')
                print("   📸 Screenshot saved: form_after_submit.png")
                
                # Check current URL
                current_url = page.url
                print(f"   🔗 Current URL after submit: {current_url}")
                
                # Check if redirected to success page
                if 'thank-you' in current_url or 'success' in current_url:
                    print("   ✅ Redirected to success page")
                    return True
                else:
                    print("   ⚠️ No clear success indication")
                    return False
                
            finally:
                await browser.close()
        
    except Exception as e:
        print(f"❌ Error testing form submission: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Simple Form Test for profectusaccounts.com")
    print("=" * 60)
    
    # Step 1: Check Odoo status
    odoo_ok = check_odoo_status()
    
    # Step 2: Check website status
    website_ok = await check_website_status()
    
    # Step 3: Test database form creation
    db_test_ok = test_database_form_creation()
    
    # Step 4: Test form submission if website is accessible
    form_test_ok = False
    if website_ok:
        form_test_ok = await test_form_submission_if_accessible()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    print(f"✅ Odoo Database: {'WORKING' if odoo_ok else 'FAILED'}")
    print(f"✅ Website Access: {'WORKING' if website_ok else 'FAILED'}")
    print(f"✅ Database Forms: {'WORKING' if db_test_ok else 'FAILED'}")
    print(f"✅ Form Submission: {'WORKING' if form_test_ok else 'FAILED'}")
    
    if odoo_ok and website_ok and db_test_ok:
        if form_test_ok:
            print("\n🎉 ALL TESTS PASSED!")
            print("   • Odoo is running and accessible")
            print("   • Website forms are working")
            print("   • Database integration is working")
            print("   • Form submissions are successful")
        else:
            print("\n⚠️ MOSTLY WORKING!")
            print("   • Odoo and database are working")
            print("   • Website is accessible")
            print("   • Form submission needs attention")
    else:
        print("\n❌ ISSUES FOUND:")
        if not odoo_ok:
            print("   • Odoo database connection failed")
        if not website_ok:
            print("   • Website is not accessible")
        if not db_test_ok:
            print("   • Database form creation failed")
    
    print("\n📁 Files created:")
    print("   • contact_page_test.png - Contact page screenshot")
    print("   • form_before_submit.png - Form before submission")
    print("   • form_after_submit.png - Form after submission")
    
    print("\n🎯 Next steps:")
    if not odoo_ok:
        print("   1. Start Odoo service: sudo systemctl start odoo")
        print("   2. Check Odoo logs: sudo journalctl -u odoo -f")
    if not website_ok:
        print("   1. Verify Odoo is running on port 8069")
        print("   2. Check firewall settings")
    if odoo_ok and website_ok and not form_test_ok:
        print("   1. Check form field mappings")
        print("   2. Run SQL update scripts")
        print("   3. Verify form model configuration")

if __name__ == "__main__":
    asyncio.run(main())
