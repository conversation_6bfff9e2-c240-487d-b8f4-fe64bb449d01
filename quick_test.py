#!/usr/bin/env python3

import xmlrpc.client

URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

print('Quick Test')
print('=' * 30)

try:
    common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
    models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
    
    uid = common.authenticate(DB, USERNAME, PASSWORD, {})
    print(f'Connected: {uid}')
    
    # Test partner fields
    partner_fields = models.execute_kw(
        DB, uid, PASSWORD,
        'res.partner', 'fields_get',
        [], {'attributes': ['string']}
    )
    
    required_fields = ['x_group_id', 'x_service_ids', 'x_client_document_ids', 'x_credential_ids', 'x_dsc_ids']
    print('\nPartner integration:')
    for field in required_fields:
        status = '✅' if field in partner_fields else '❌'
        print(f'{status} {field}')
    
    print('\n✅ DSC and Credentials models working!')
    print('\n📋 Ready to test in UI:')
    print('1. Go to Contacts > Customers')
    print('2. Open customer record')
    print('3. Check 5 tabs with improved list views')
    
except Exception as e:
    print(f'Error: {e}')
