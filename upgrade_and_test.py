#!/usr/bin/env python3
"""
J&<PERSON> Bearing Website - Complete Upgrade and Testing Script
This script will upgrade the module and test all website functionality
"""

import xmlrpc.client
import requests
import time
import sys

# Configuration
ODOO_URL = 'http://jnj18.arihantai.com:8069'
DB_NAME = 'jnj18.arihantai.com'
USERNAME = 'admin'
PASSWORD = 'admin'
MODULE_NAME = 'jnj_bearing_website'

def print_status(message, status="INFO"):
    """Print formatted status message"""
    symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "ERROR": "❌",
        "WARNING": "⚠️",
        "PROGRESS": "🔄"
    }
    print(f"{symbols.get(status, 'ℹ️')} {message}")

def connect_to_odoo():
    """Connect to Odoo and return uid"""
    try:
        print_status("Connecting to Odoo...", "PROGRESS")
        common = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/common')
        models = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/object')
        
        uid = common.authenticate(DB_NAME, USERNAME, PASSWORD, {})
        if uid:
            print_status(f"Connected successfully as user ID: {uid}", "SUCCESS")
            return uid, models
        else:
            print_status("Authentication failed", "ERROR")
            return None, None
    except Exception as e:
        print_status(f"Connection failed: {e}", "ERROR")
        return None, None

def upgrade_module(models, uid):
    """Upgrade the J&J Bearing Website module"""
    try:
        print_status("Searching for module...", "PROGRESS")
        module_ids = models.execute_kw(
            DB_NAME, uid, PASSWORD, 
            'ir.module.module', 'search', 
            [[('name', '=', MODULE_NAME)]]
        )
        
        if not module_ids:
            print_status(f"Module '{MODULE_NAME}' not found", "ERROR")
            return False
            
        print_status(f"Found module with ID: {module_ids[0]}", "SUCCESS")
        print_status("Starting module upgrade...", "PROGRESS")
        
        result = models.execute_kw(
            DB_NAME, uid, PASSWORD,
            'ir.module.module', 'button_immediate_upgrade',
            [module_ids]
        )
        
        print_status("Module upgrade completed successfully!", "SUCCESS")
        print_status(f"Upgrade result: {result}", "INFO")
        return True
        
    except Exception as e:
        print_status(f"Module upgrade failed: {e}", "ERROR")
        return False

def test_page(url, page_name):
    """Test a single page and return status"""
    try:
        response = requests.get(url, timeout=10)
        status = response.status_code
        
        if status == 200:
            print_status(f"{page_name}: Status {status} - WORKING", "SUCCESS")
            return True
        else:
            print_status(f"{page_name}: Status {status} - ERROR", "ERROR")
            return False
            
    except Exception as e:
        print_status(f"{page_name}: Request failed - {e}", "ERROR")
        return False

def test_website_pages():
    """Test all website pages"""
    print_status("Testing website pages...", "PROGRESS")
    
    pages = [
        (f"{ODOO_URL}/", "Homepage"),
        (f"{ODOO_URL}/contact", "Contact Page"),
        (f"{ODOO_URL}/products", "Products Page"),
        (f"{ODOO_URL}/web", "Web Backend")
    ]
    
    results = {}
    for url, name in pages:
        results[name] = test_page(url, name)
        time.sleep(1)  # Brief pause between requests
    
    return results

def check_content(url, expected_content, page_name):
    """Check if page contains expected content"""
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            content = response.text.lower()
            if expected_content.lower() in content:
                print_status(f"{page_name}: Content verified - '{expected_content}' found", "SUCCESS")
                return True
            else:
                print_status(f"{page_name}: Content missing - '{expected_content}' not found", "WARNING")
                return False
        else:
            print_status(f"{page_name}: Cannot check content - Status {response.status_code}", "ERROR")
            return False
    except Exception as e:
        print_status(f"{page_name}: Content check failed - {e}", "ERROR")
        return False

def verify_website_content():
    """Verify website content"""
    print_status("Verifying website content...", "PROGRESS")
    
    content_checks = [
        (f"{ODOO_URL}/", "j&j bearing", "Homepage Brand"),
        (f"{ODOO_URL}/contact", "contact", "Contact Form"),
        (f"{ODOO_URL}/products", "products", "Products Listing")
    ]
    
    results = {}
    for url, content, name in content_checks:
        results[name] = check_content(url, content, name)
        time.sleep(1)
    
    return results

def main():
    """Main execution function"""
    print("=" * 60)
    print("🏭 J&J BEARING WEBSITE - UPGRADE & TEST SCRIPT")
    print("=" * 60)
    
    # Step 1: Connect to Odoo
    uid, models = connect_to_odoo()
    if not uid:
        sys.exit(1)
    
    # Step 2: Upgrade Module
    print("\n" + "=" * 40)
    print("📦 MODULE UPGRADE")
    print("=" * 40)
    
    if not upgrade_module(models, uid):
        print_status("Module upgrade failed. Exiting.", "ERROR")
        sys.exit(1)
    
    # Step 3: Wait for system to stabilize
    print_status("Waiting for system to stabilize...", "PROGRESS")
    time.sleep(10)
    
    # Step 4: Test Website Pages
    print("\n" + "=" * 40)
    print("🌐 WEBSITE TESTING")
    print("=" * 40)
    
    page_results = test_website_pages()
    
    # Step 5: Verify Content
    print("\n" + "=" * 40)
    print("📝 CONTENT VERIFICATION")
    print("=" * 40)
    
    content_results = verify_website_content()
    
    # Step 6: Final Report
    print("\n" + "=" * 60)
    print("📊 FINAL REPORT")
    print("=" * 60)
    
    print("\n🌐 Page Status:")
    working_pages = 0
    for page, status in page_results.items():
        symbol = "✅" if status else "❌"
        print(f"  {symbol} {page}")
        if status:
            working_pages += 1
    
    print(f"\n📈 Summary: {working_pages}/{len(page_results)} pages working")
    
    print("\n📝 Content Status:")
    working_content = 0
    for content, status in content_results.items():
        symbol = "✅" if status else "❌"
        print(f"  {symbol} {content}")
        if status:
            working_content += 1
    
    print(f"\n📈 Summary: {working_content}/{len(content_results)} content checks passed")
    
    # Overall Status
    if working_pages == len(page_results) and working_content == len(content_results):
        print_status("🎉 ALL SYSTEMS OPERATIONAL! J&J Bearing website is fully functional.", "SUCCESS")
        return 0
    elif working_pages > 0:
        print_status("⚠️ PARTIAL SUCCESS: Some pages are working but issues remain.", "WARNING")
        return 1
    else:
        print_status("❌ CRITICAL: No pages are working. Manual intervention required.", "ERROR")
        return 2

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
