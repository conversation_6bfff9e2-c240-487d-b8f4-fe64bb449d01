from odoo import models, fields, api
from odoo.exceptions import UserError


class ResPartner(models.Model):
    _inherit = 'res.partner'

    x_dsc_ids = fields.One2many('x_dsc', 'x_manager', string='DSC Management')


class DSCManagement(models.Model):
    _name = 'x_dsc'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'DSC Management'
    _order = 'x_expiry_date desc, name'

    # Basic Information
    name = fields.Char(string='DSC Name', required=True, tracking=True)
    x_partner_id = fields.Many2one('res.partner', string='Client', required=True, tracking=True)
    x_manager = fields.Many2one('res.partner', string='Manager', store=True, ondelete='set null')

    # Related fields for quick access
    x_client = fields.Char(string='Client Name', readonly=True, store=True, related='x_partner_id.name')
    x_email = fields.Char(string='Email', store=True, related='x_partner_id.email', readonly=True)
    x_mobile = fields.Char(string='Mobile', store=True, related='x_partner_id.mobile', readonly=True)

    # Certificate Details
    x_certificate_number = fields.Char(string='Certificate Number', tracking=True)
    x_certificate_type = fields.Selection([
        ('Class 1', 'Class 1 Individual'),
        ('Class 2', 'Class 2 Individual'),
        ('Class 3', 'Class 3 Individual'),
        ('Class 1 Org', 'Class 1 Organization'),
        ('Class 2 Org', 'Class 2 Organization'),
        ('Class 3 Org', 'Class 3 Organization'),
    ], string='Certificate Type', tracking=True)
    x_issuing_authority = fields.Selection([
        ('eMudhra', 'eMudhra'),
        ('Sify', 'Sify'),
        ('NCode', 'NCode Solutions'),
        ('SafeScrypt', 'SafeScrypt'),
        ('TCS', 'TCS'),
        ('IDRBT', 'IDRBT'),
        ('Other', 'Other')
    ], string='Issuing Authority', tracking=True)

    # Dates
    x_issued_date = fields.Date(string='Issued Date', tracking=True)
    x_expiry_date = fields.Date(string='Expiry Date', required=True, tracking=True)

    # Status and Location
    x_status = fields.Selection([
        ('Active', 'Active'),
        ('Expired', 'Expired'),
        ('Revoked', 'Revoked'),
        ('Suspended', 'Suspended')
    ], string='Status', default='Active', tracking=True)
    x_location = fields.Selection([
        ('Client', 'Client'),
        ('Office', 'Office')
    ], string='Location', default='Client', tracking=True)

    # Additional Information
    x_notes = fields.Text(string='Notes')

    # Computed fields
    days_to_expiry = fields.Integer(string='Days to Expiry', compute='_compute_days_to_expiry', store=True)
    is_expiring_soon = fields.Boolean(string='Expiring Soon', compute='_compute_expiring_soon', store=True)

    @api.depends('x_expiry_date')
    def _compute_days_to_expiry(self):
        today = fields.Date.today()
        for record in self:
            if record.x_expiry_date:
                delta = record.x_expiry_date - today
                record.days_to_expiry = delta.days
            else:
                record.days_to_expiry = 0

    @api.depends('days_to_expiry')
    def _compute_expiring_soon(self):
        for record in self:
            record.is_expiring_soon = record.days_to_expiry <= 30 and record.days_to_expiry >= 0

    @api.model
    def create(self, vals):
        # Auto-set partner_id if manager is provided but partner_id is not
        if 'x_manager' in vals and 'x_partner_id' not in vals:
            vals['x_partner_id'] = vals['x_manager']
        return super().create(vals)
