<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Partner Form View Extension for DSC Management -->
    <record id="view_partner_form_dsc" model="ir.ui.view">
        <field name="name">res.partner.form.dsc</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page string="DSC Management" name="dsc_info">
                    <field name="x_dsc_ids" nolabel="1">
                        <list editable="bottom">
                            <field name="name"/>
                            <field name="x_expiry_date"/>
                            <field name="x_location"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <record id="view_x_dsc_form" model="ir.ui.view">
        <field name="name">x_dsc.form</field>
        <field name="model">x_dsc</field>
        <field name="arch" type="xml">
            <form string="DSC management">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="DSC Name..."/>
                        </h1>
                    </div>
                    <group>
                        <group string="Client Information">
                            <field name="x_manager" string="Signer Name" domain="[['parent_id','!=',False]]"/>
                            <field name="x_client"/>
                            <field name="x_mobile"/>
                            <field name="x_email"/>
                        </group>
                        <group string="DSC Details">
                            <field name="x_location"/>
                            <field name="x_expiry_date"/>
                            <field name="x_notes"/>
                        </group>
                    </group>
                </sheet>
            </form>


        </field>
    </record>

    <record id="view_x_dsc_list" model="ir.ui.view">
        <field name="name">x_dsc.list</field>
        <field name="model">x_dsc</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="x_client"/>
                <field name="x_mobile"/>
                <field name="x_email"/>
                <field name="x_manager" string="Signer Name"/>
                <field name="x_location"/>
                <field name="x_expiry_date"/>
            </list>
        </field>
    </record>

    <record id="view_x_dsc_search" model="ir.ui.view">
        <field name="name">x_dsc.search</field>
        <field name="model">x_dsc</field>
        <field name="arch" type="xml">
            <search>
                <field name="name" string="DSC Name"/>
                <field name="x_client" string="Client Name"/>
                <field name="x_manager" string="Signer Name"/>
                <field name="x_mobile" string="Mobile"/>
                <field name="x_email" string="Email"/>
                <filter string="Client Location" name="client_location" domain="[('x_location', '=', 'Client')]"/>
                <filter string="Office Location" name="office_location" domain="[('x_location', '=', 'Office')]"/>
                <filter string="Expiring Soon" name="expiring_soon" domain="[('x_expiry_date', '&lt;=', (context_today() + datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Location" name="group_location" context="{'group_by': 'x_location'}"/>
                    <filter string="Expiry Date" name="group_expiry" context="{'group_by': 'x_expiry_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <menuitem id="menu_x_dsc" name="DSC Management" parent="imca_groups.menu_ca_management_root" action="action_x_dsc" sequence="60"/>
</odoo>
