<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Partner Form View Extension for DSC Management -->
    <record id="view_partner_form_dsc" model="ir.ui.view">
        <field name="name">res.partner.form.dsc</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page string="DSC Management" name="dsc_info">
                    <field name="x_dsc_ids" nolabel="1">
                        <list editable="bottom">
                            <field name="name"/>
                            <field name="x_certificate_number"/>
                            <field name="x_certificate_type"/>
                            <field name="x_issuing_authority"/>
                            <field name="x_issued_date"/>
                            <field name="x_expiry_date"/>
                            <field name="x_status"/>
                            <field name="x_location"/>
                            <field name="is_expiring_soon" invisible="1"/>
                            <field name="days_to_expiry" invisible="1"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <record id="view_x_dsc_form" model="ir.ui.view">
        <field name="name">x_dsc.form</field>
        <field name="model">x_dsc</field>
        <field name="arch" type="xml">
            <form string="DSC Management">
                <header>
                    <field name="x_status" widget="statusbar" statusbar_visible="Active,Expired,Revoked,Suspended"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="DSC Name..."/>
                        </h1>
                    </div>
                    <group>
                        <group string="Client Information">
                            <field name="x_partner_id"/>
                            <field name="x_manager" string="Signer Name" domain="[('parent_id','!=',False)]"/>
                            <field name="x_client"/>
                            <field name="x_mobile"/>
                            <field name="x_email"/>
                        </group>
                        <group string="Certificate Details">
                            <field name="x_certificate_number"/>
                            <field name="x_certificate_type"/>
                            <field name="x_issuing_authority"/>
                            <field name="x_location"/>
                        </group>
                    </group>
                    <group>
                        <group string="Validity Information">
                            <field name="x_issued_date"/>
                            <field name="x_expiry_date"/>
                            <field name="days_to_expiry" readonly="1"/>
                            <field name="is_expiring_soon" readonly="1"/>
                        </group>
                        <group string="Additional Information">
                            <field name="x_notes"/>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_x_dsc_list" model="ir.ui.view">
        <field name="name">x_dsc.list</field>
        <field name="model">x_dsc</field>
        <field name="arch" type="xml">
            <list decoration-danger="is_expiring_soon" decoration-muted="x_status == 'Expired'">
                <field name="name"/>
                <field name="x_partner_id" string="Client"/>
                <field name="x_certificate_number"/>
                <field name="x_certificate_type"/>
                <field name="x_issuing_authority"/>
                <field name="x_issued_date"/>
                <field name="x_expiry_date"/>
                <field name="days_to_expiry"/>
                <field name="x_status"/>
                <field name="x_location"/>
                <field name="is_expiring_soon" invisible="1"/>
            </list>
        </field>
    </record>

    <record id="view_x_dsc_search" model="ir.ui.view">
        <field name="name">x_dsc.search</field>
        <field name="model">x_dsc</field>
        <field name="arch" type="xml">
            <search>
                <field name="name" string="DSC Name"/>
                <field name="x_partner_id" string="Client"/>
                <field name="x_certificate_number" string="Certificate Number"/>
                <field name="x_manager" string="Signer Name"/>
                <field name="x_issuing_authority" string="Issuing Authority"/>

                <!-- Filters -->
                <filter string="Active" name="filter_active" domain="[('x_status', '=', 'Active')]"/>
                <filter string="Expired" name="filter_expired" domain="[('x_status', '=', 'Expired')]"/>
                <filter string="Expiring Soon" name="expiring_soon" domain="[('is_expiring_soon', '=', True)]"/>
                <separator/>
                <filter string="Client Location" name="client_location" domain="[('x_location', '=', 'Client')]"/>
                <filter string="Office Location" name="office_location" domain="[('x_location', '=', 'Office')]"/>
                <separator/>
                <filter string="Class 3 Individual" name="class3_individual" domain="[('x_certificate_type', '=', 'Class 3')]"/>
                <filter string="eMudhra" name="emudhra" domain="[('x_issuing_authority', '=', 'eMudhra')]"/>

                <group expand="0" string="Group By">
                    <filter string="Client" name="group_client" context="{'group_by': 'x_partner_id'}"/>
                    <filter string="Status" name="group_status" context="{'group_by': 'x_status'}"/>
                    <filter string="Location" name="group_location" context="{'group_by': 'x_location'}"/>
                    <filter string="Certificate Type" name="group_cert_type" context="{'group_by': 'x_certificate_type'}"/>
                    <filter string="Issuing Authority" name="group_authority" context="{'group_by': 'x_issuing_authority'}"/>
                    <filter string="Expiry Date" name="group_expiry" context="{'group_by': 'x_expiry_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <menuitem id="menu_x_dsc" name="DSC Management" parent="imca_groups.menu_ca_management_root" action="action_x_dsc" sequence="60"/>
</odoo>
