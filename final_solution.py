#!/usr/bin/env python3
"""
Final Solution for IMCA Modules Upgrade
1. Create temporary manifest files without view dependencies
2. Upgrade modules to load model extensions
3. Restore original manifest files
4. Upgrade again to load view extensions
"""

import xmlrpc.client
import time
import sys
import os
import shutil
import logging
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

IMCA_MODULES = [
    'imca_groups',
    'imca_services', 
    'imca_client_documents',
    'imca_crednetials_manager',
    'imca_dsc_management'
]

class FinalSolution:
    def __init__(self):
        self.common = None
        self.models = None
        self.uid = None
        self.manifest_backups = {}
        
    def connect(self):
        """Connect to Odoo"""
        try:
            logger.info(f"Connecting to Odoo at {URL}")
            self.common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
            self.models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
            
            self.uid = self.common.authenticate(DB, USERNAME, PASSWORD, {})
            if not self.uid:
                raise Exception("Authentication failed")
                
            logger.info(f"Successfully connected as user ID: {self.uid}")
            return True
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    def backup_manifest_files(self):
        """Backup manifest files"""
        logger.info("Backing up manifest files...")
        
        for module in IMCA_MODULES:
            manifest_path = f"{module}/__manifest__.py"
            if os.path.exists(manifest_path):
                with open(manifest_path, 'r') as f:
                    content = f.read()
                
                self.manifest_backups[manifest_path] = content
                logger.info(f"Backed up {manifest_path}")
        
        return len(self.manifest_backups) > 0
    
    def create_temp_manifest_files(self):
        """Create temporary manifest files without view dependencies"""
        logger.info("Creating temporary manifest files without view dependencies...")
        
        for module in IMCA_MODULES:
            manifest_path = f"{module}/__manifest__.py"
            if os.path.exists(manifest_path):
                with open(manifest_path, 'r') as f:
                    content = f.read()
                
                # Parse the manifest content
                manifest_dict = eval(content)
                
                # Remove view files from data list
                if 'data' in manifest_dict:
                    new_data = []
                    for item in manifest_dict['data']:
                        if not (item.endswith('_views.xml') or 'views/' in item):
                            new_data.append(item)
                    
                    manifest_dict['data'] = new_data
                
                # Write the modified manifest
                with open(manifest_path, 'w') as f:
                    f.write(repr(manifest_dict))
                
                logger.info(f"Created temporary manifest for {module} without view dependencies")
    
    def restore_manifest_files(self):
        """Restore original manifest files"""
        logger.info("Restoring original manifest files...")
        
        for manifest_path, content in self.manifest_backups.items():
            with open(manifest_path, 'w') as f:
                f.write(content)
            
            logger.info(f"Restored {manifest_path}")
    
    def upgrade_module(self, module_name):
        """Upgrade a specific module"""
        try:
            logger.info(f"Upgrading module: {module_name}")
            
            # Search for the module
            module_ids = self.models.execute_kw(
                DB, self.uid, PASSWORD,
                'ir.module.module', 'search',
                [[('name', '=', module_name)]]
            )
            
            if not module_ids:
                logger.error(f"Module {module_name} not found")
                return False
            
            module_id = module_ids[0]
            
            # Get current state
            module_data = self.models.execute_kw(
                DB, self.uid, PASSWORD,
                'ir.module.module', 'read',
                [module_id], {'fields': ['state']}
            )[0]
            
            current_state = module_data['state']
            logger.info(f"Module {module_name} current state: {current_state}")
            
            if current_state == 'installed':
                # Upgrade the module
                try:
                    result = self.models.execute_kw(
                        DB, self.uid, PASSWORD,
                        'ir.module.module', 'button_immediate_upgrade',
                        [[module_id]]
                    )
                    logger.info(f"✅ Upgrade initiated for {module_name}")
                    return True
                except Exception as e:
                    logger.error(f"❌ Error during upgrade of {module_name}: {e}")
                    return False
            else:
                logger.warning(f"Module {module_name} not in installed state: {current_state}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error upgrading {module_name}: {e}")
            return False
    
    def test_partner_fields(self):
        """Test if partner fields exist"""
        logger.info("Testing partner field access...")
        
        fields_to_test = {
            'x_group_id': 'imca_groups',
            'x_service_ids': 'imca_services',
            'x_client_document_ids': 'imca_client_documents', 
            'x_credential_ids': 'imca_crednetials_manager',
            'x_dsc_ids': 'imca_dsc_management'
        }
        
        results = {}
        
        try:
            # Get partner fields
            partner_fields = self.models.execute_kw(
                DB, self.uid, PASSWORD,
                'res.partner', 'fields_get',
                [], {'attributes': ['string', 'relation']}
            )
            
            for field_name, module in fields_to_test.items():
                if field_name in partner_fields:
                    field_info = partner_fields[field_name]
                    logger.info(f"✅ Field {field_name} exists: {field_info.get('string', 'No description')}")
                    results[field_name] = True
                else:
                    logger.warning(f"❌ Field {field_name} does not exist in res.partner")
                    results[field_name] = False
                    
        except Exception as e:
            logger.error(f"❌ Error testing partner fields: {e}")
            
        return results
    
    def check_partner_views(self):
        """Check if partner views are properly loaded"""
        logger.info("Checking partner views...")
        
        view_ids_to_check = [
            'view_partner_form_services',
            'view_partner_form_client_documents',
            'view_partner_form_credentials',
            'view_partner_form_dsc'
        ]
        
        results = {}
        
        try:
            for view_id in view_ids_to_check:
                try:
                    # Search for the view
                    views = self.models.execute_kw(
                        DB, self.uid, PASSWORD,
                        'ir.ui.view', 'search_read',
                        [[('name', '=', view_id)]],
                        {'fields': ['name', 'model', 'active']}
                    )
                    
                    if views:
                        view = views[0]
                        logger.info(f"✅ View {view_id} found: {view}")
                        results[view_id] = True
                    else:
                        logger.warning(f"❌ View {view_id} not found")
                        results[view_id] = False
                        
                except Exception as e:
                    logger.error(f"❌ Error checking view {view_id}: {e}")
                    results[view_id] = False
                    
        except Exception as e:
            logger.error(f"❌ Error checking partner views: {e}")
            
        return results

def main():
    """Main execution function"""
    logger.info("=" * 60)
    logger.info("FINAL SOLUTION FOR IMCA MODULES UPGRADE")
    logger.info("=" * 60)
    
    solution = FinalSolution()
    
    # Step 1: Connect to Odoo
    if not solution.connect():
        logger.error("Failed to connect to Odoo. Exiting.")
        sys.exit(1)
    
    # Step 2: Backup manifest files
    if not solution.backup_manifest_files():
        logger.error("Failed to backup manifest files. Exiting.")
        sys.exit(1)
    
    try:
        # Step 3: Create temporary manifest files without view dependencies
        solution.create_temp_manifest_files()
        
        # Step 4: First upgrade - Create model extensions
        logger.info("\n" + "=" * 40)
        logger.info("STEP 1: FIRST UPGRADE - CREATE MODEL EXTENSIONS")
        logger.info("=" * 40)
        
        first_upgrade_results = {}
        for module in IMCA_MODULES:
            first_upgrade_results[module] = solution.upgrade_module(module)
            time.sleep(3)  # Wait between upgrades
        
        # Wait for upgrades to complete
        logger.info("\n⏳ Waiting 15 seconds for model upgrades to complete...")
        time.sleep(15)
        
        # Step 5: Test partner fields
        logger.info("\n" + "=" * 40)
        logger.info("STEP 2: TESTING PARTNER FIELDS")
        logger.info("=" * 40)
        
        field_results = solution.test_partner_fields()
        
        # Step 6: Restore original manifest files
        logger.info("\n" + "=" * 40)
        logger.info("STEP 3: RESTORING ORIGINAL MANIFEST FILES")
        logger.info("=" * 40)
        
        solution.restore_manifest_files()
        
        # Step 7: Second upgrade - Load view extensions
        logger.info("\n" + "=" * 40)
        logger.info("STEP 4: SECOND UPGRADE - LOAD VIEW EXTENSIONS")
        logger.info("=" * 40)
        
        second_upgrade_results = {}
        for module in IMCA_MODULES:
            second_upgrade_results[module] = solution.upgrade_module(module)
            time.sleep(3)  # Wait between upgrades
        
        # Wait for upgrades to complete
        logger.info("\n⏳ Waiting 15 seconds for view upgrades to complete...")
        time.sleep(15)
        
        # Step 8: Check partner views
        logger.info("\n" + "=" * 40)
        logger.info("STEP 5: CHECKING PARTNER VIEWS")
        logger.info("=" * 40)
        
        view_results = solution.check_partner_views()
        
        # Final summary
        logger.info("\n" + "=" * 60)
        logger.info("FINAL SUMMARY")
        logger.info("=" * 60)
        
        logger.info("First Upgrade (Models):")
        for module, success in first_upgrade_results.items():
            status = "✅ SUCCESS" if success else "❌ FAILED"
            logger.info(f"  {module}: {status}")
        
        logger.info("\nPartner Fields Test:")
        for field, exists in field_results.items():
            status = "✅ EXISTS" if exists else "❌ MISSING"
            logger.info(f"  {field}: {status}")
        
        logger.info("\nSecond Upgrade (Views):")
        for module, success in second_upgrade_results.items():
            status = "✅ SUCCESS" if success else "❌ FAILED"
            logger.info(f"  {module}: {status}")
        
        logger.info("\nPartner Views Test:")
        for view, exists in view_results.items():
            status = "✅ EXISTS" if exists else "❌ MISSING"
            logger.info(f"  {view}: {status}")
        
        # Calculate success rates
        successful_fields = sum(field_results.values())
        successful_views = sum(view_results.values())
        successful_final_upgrades = sum(second_upgrade_results.values())
        
        logger.info(f"\nOverall Results:")
        logger.info(f"  Partner fields: {successful_fields}/{len(field_results)}")
        logger.info(f"  Partner views: {successful_views}/{len(view_results)}")
        logger.info(f"  Final upgrades: {successful_final_upgrades}/{len(IMCA_MODULES)}")
        
        if successful_fields == len(field_results) and successful_views == len(view_results):
            logger.info("\n🎉 SUCCESS! All modules upgraded and partner extensions are now visible!")
            logger.info("\n📋 Next Steps:")
            logger.info("1. Open a partner record in Odoo")
            logger.info("2. Check for new tabs: Groups, Services, Documents, Credentials, DSC Management")
            logger.info("3. Verify the CA Management menu structure")
        else:
            logger.warning(f"\n⚠️  Partial success: {successful_fields}/{len(field_results)} fields, {successful_views}/{len(view_results)} views")
        
    finally:
        # Always restore manifest files
        if hasattr(solution, 'manifest_backups') and solution.manifest_backups:
            logger.info("\nEnsuring manifest files are restored...")
            solution.restore_manifest_files()
    
    logger.info("\nUpgrade process completed!")

if __name__ == "__main__":
    main()
