<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="product_pdf_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="product">
                <t t-call="web.external_layout">
                    <style>
                        .product-table {
                            width: 100%;
                            border-collapse: collapse;
                            }
        
                            .product-table tr {
                            border-bottom: 1px solid #DEE2E6;
                            border-top: 1px solid #FFFFFF;
                            border-left: 1px solid #FFFFFF;
                            border-right: 1px solid #FFFFFF;
                            transition: background-color 0.2s ease;
                            }
        
        
                            .product-table td {
                            padding: 0.5rem;
                            }
        
                            .product-table td:first-child {
                            font-weight: 500;
                            color: #555;
                            }
        
                            .product-table td:last-child {
                            color: #11111;
                            display: flex;
                            align-items: center;
                            gap: 0.5rem;
                            }
                            
                    </style>
                    <div class="page">
                        <h1 class="heading mb-1 text-center">
                            <t t-esc="product.name"/>
                        </h1>
                        <hr style="max-width:40%; margin: auto;" class="mb-5"/>

                        <div class="row mt-1">
                            <div class="col-6">
                                <img t-if="product.public_categ_ids[0].x_product_image" t-att-src="'/web/image/product.public.category/%s/x_product_image' % product.public_categ_ids[0].id" style="max-width: 300px"/>
                            </div>
                            <div class="col-6">
                                <p t-field="product.public_categ_ids[0].x_product_description"/>
                            </div>
                        </div>

                        <h3 class="mt-5 text-center">Technical Specifications</h3>
                        <hr style="max-width:40%; margin: auto;" class="mb-4"/>
                        <div class="container">
                            <div class="row">
                                <!-- Left column for images -->
                                <div class="col-6">
                                    <div class="product-images-container">
                                        <t t-foreach="product.public_categ_ids[0].x_diagrams" t-as="diagram">
                                            <div class="mb-4 text-center">
                                                <img t-att-src="'/web/image/%s' % diagram.id" t-att-alt="diagram.name" class="img img-fluid center-block" style="max-width: 200px ;max-height: 100px"/>
                                                <div class="mt-2 text-center">
                                                    <span t-field="diagram.name"/>
                                                </div>
                                            </div>
                                        </t>
                                    </div>
                                </div>

                                <!-- Right column for technical details -->
                                <div class="col-6">
                                    <h4 class="heading mb-1">Product Technical Details</h4>
                                    <hr/>
                                    <table class="mt-2 product-table">
                                        <tbody>
                                            <tr t-if="product.x_adapter_sleeve">
                                                <td>Adapter Sleeve</td>
                                                <td>
                                                    <t t-esc="product.x_adapter_sleeve"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_appro_bearing">
                                                <td>Appropriate Bearing</td>
                                                <td>
                                                    <t t-esc="product.x_appro_bearing"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_bearing_adapter_sleeve_designation">
                                                <td>Bearing And Adapter Sleeve Designation</td>
                                                <td>
                                                    <t t-esc="product.x_bearing_adapter_sleeve_designation"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_bearing_seating_Ca">
                                                <td>Bearing Seating Ca</td>
                                                <td>
                                                    <t t-esc="product.x_bearing_seating_Ca"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_bearing_seating_Da">
                                                <td>Bearing Seating Da</td>
                                                <td>
                                                    <t t-esc="product.x_bearing_seating_Da"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_bearing_seating_db">
                                                <td>Bearing Seating db</td>
                                                <td>
                                                    <t t-esc="product.x_bearing_seating_db"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_felt_strip">
                                                <td>Felt Strip</td>
                                                <td>
                                                    <t t-esc="product.x_felt_strip"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_desi">
                                                <td>Housing Designation</td>
                                                <td>
                                                    <t t-esc="product.x_housing_desi"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_A">
                                                <td>Housing Dimension A</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_A"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_A1">
                                                <td>Housing Dimension A1</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_A1"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_A2">
                                                <td>Housing Dimension A2</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_A2"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_A3">
                                                <td>Housing Dimension A3</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_A3"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_B">
                                                <td>Housing Dimension B</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_B"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_Ca">
                                                <td>Housing Dimension Ca</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_Ca"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_D">
                                                <td>Housing Dimension D</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_D"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_Da">
                                                <td>Housing Dimension Da</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_Da"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_E">
                                                <td>Housing Dimension E</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_E"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_G">
                                                <td>Housing Dimension G</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_G"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_H">
                                                <td>Housing Dimension H</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_H"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_H1">
                                                <td>Housing Dimension H1</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_H1"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_H2">
                                                <td>Housing Dimension H2</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_H2"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_I">
                                                <td>Housing Dimension I</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_I"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_J">
                                                <td>Housing Dimension J</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_J"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_J1">
                                                <td>Housing Dimension J1</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_J1"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_L">
                                                <td>Housing Dimension L</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_L"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_N">
                                                <td>Housing Dimension N</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_N"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_N1">
                                                <td>Housing Dimension N1</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_N1"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_S">
                                                <td>Housing Dimension S</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_S"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_dimensions_V">
                                                <td>Housing Dimension V</td>
                                                <td>
                                                    <t t-esc="product.x_housing_dimensions_V"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_housing_without_seals">
                                                <td>Housing Without Seals</td>
                                                <td>
                                                    <t t-esc="product.x_housing_without_seals"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_rings_no_desg">
                                                <td>Rings No. &amp; Designations</td>
                                                <td>
                                                    <t t-esc="product.x_rings_no_desg"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_rubber_orings">
                                                <td>Rubber O-rings</td>
                                                <td>
                                                    <t t-esc="product.x_rubber_orings"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_shaft_dia_in">
                                                <td>Shaft Diameter (in)</td>
                                                <td>
                                                    <t t-esc="product.x_shaft_dia_in"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_shift_dia_D">
                                                <td>Shaft Diameter D</td>
                                                <td>
                                                    <t t-esc="product.x_shift_dia_D"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_shift_dia_d1">
                                                <td>Shaft Diameter d1</td>
                                                <td>
                                                    <t t-esc="product.x_shift_dia_d1"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_shift_dia_da">
                                                <td>Shaft Diameter da</td>
                                                <td>
                                                    <t t-esc="product.x_shift_dia_da"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_shift_dia_db">
                                                <td>Shaft Diameter db</td>
                                                <td>
                                                    <t t-esc="product.x_shift_dia_db"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_shift_dia_mm">
                                                <td>Shaft Diameter (mm)</td>
                                                <td>
                                                    <t t-esc="product.x_shift_dia_mm"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_width_across_seal_A2">
                                                <td>A2 Width Across</td>
                                                <td>
                                                    <t t-esc="product.x_width_across_seal_A2"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_width_across_seal_A3">
                                                <td>A3 Width Across</td>
                                                <td>
                                                    <t t-esc="product.x_width_across_seal_A3"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_width_across_seal_Da">
                                                <td>Width Across Seal Da</td>
                                                <td>
                                                    <t t-esc="product.x_width_across_seal_Da"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_without_seal_housing">
                                                <td>Housing Designation Without Seals</td>
                                                <td>
                                                    <t t-esc="product.x_without_seal_housing"/>
                                                </td>
                                            </tr>
                                            <tr t-if="product.x_without_seal_housing_des">
                                                <td>Designation Without Seals</td>
                                                <td>
                                                    <t t-esc="product.x_without_seal_housing_des"/>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </t>
            </t>
        </t>
    </template>
    <record id="action_report_product_pdf" model="ir.actions.report">
        <field name="name">Product Technical Specifications</field>
        <field name="model">product.template</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">ai_bearing_website.product_pdf_template</field>
        <field name="report_file">ai_bearing_website.product_pdf_template</field>
        <field name="binding_model_id" ref="product.model_product_template" />
        <field name="binding_type">report</field>
    </record>
</odoo>