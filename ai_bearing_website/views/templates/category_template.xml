<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="category_template" name="Bearing Category">
         <t t-call="website.layout">
            <div id="wrap" class="oe_structure" t-att-data-category-id="category.id">
                <!-- Category Header -->
                                <script src="/ai_bearing_website/static/js/bearing_filter.js"/>

            <style>
                #productsTable tbody tr {
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                #productsTable tbody tr:hover {
                    background-color: #f5f5f5;
                    transform: translateY(-2px);
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                .dropdown .btn-secondary::after {
                    position: absolute;
                    right: 0.5rem; 
                    top: 50%;
                    transform: translateY(-50%);
                }
            </style>
        

                <!-- Category Description -->
                <section class="s_text_image o_cc o_cc2 pt72 pb72">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-lg-7 pt16 pb16">
                                <h2 class="mb-4">
                                    <t t-esc="category.display_name"/>
                                </h2>
                                <div t-field="category.x_category_page_description" class="lead"/>
                            </div>
                            <div class="col-lg-5 pt16 pb16">
                                <img t-if="category.x_product_image" t-att-src="'/web/image/product.public.category/%s/x_product_image' % category.id" class="img img-fluid mx-auto rounded shadow" style="width: 100% !important;"/>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Search Filters -->
                <section class="filter-section pt32 pb32 " style="background-color:#EEEEEE">
                    <div class="container">
                        <h4 class="mb-4">FILTER</h4>
                        <div class="row" id="searchFilters">
                            <t t-foreach="searchable_headers" t-as="header">
                                <div class="col-auto">
                                    <div class="dropdown">
                                        <button class="btn btn-secondary dropdown-toggle w-100" type="button" t-att-id="'dropdownMenu_' + header['name']" t-att-data-original-text="header['label']" data-bs-toggle="dropdown" aria-expanded="false">
                                            <t t-esc="header['label']"/>
                                        </button>
                                        <div class="dropdown-menu p-3" t-att-id="'dropdown_content_' + header['name']">
                                            <!-- Text Search -->
                                            <t t-if="header['search_type'] == 'text'">
                                                <div class="form-group">
                                                    <input type="text" class="form-control search-input" id="searchInput123" t-att-data-field="header['name']" t-att-placeholder="'Search ' + header['label']"/>
                                                </div>
                                            </t>

                                            <!-- Number Range -->
                            <t t-if="header['search_type'] == 'number'">
                                <div class="form-group number-range-filter" t-att-data-field="header['name']">
                                    <label class="form-label" t-esc="header['label'] + ' Range'"/>
                                    <div class="range-inputs">
                                        <div class="d-flex align-items-center gap-2 mb-3">
                                            <div class="input-group">
                                                <input type="number" class="form-control min-value" t-att-data-field="header['name']" placeholder="Min" id="minValue"/>
                                                <div class="input-group-append">
                                                    <span class="input-group-text">mm</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center gap-2 mb-3">
                                            <div class="input-group">
                                                <input type="number" class="form-control max-value" t-att-data-field="header['name']" placeholder="Max" id="maxValue"/>
                                                <div class="input-group-append">
                                                    <span class="input-group-text">mm</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between gap-2">
                                            <button class="btn btn-secondary btn-sm reset-filter" type="reset" id="resetfilter">
                                                <i class="fa fa-undo me-1"/>Reset
                                            </button>
                                            <button class="btn btn-primary btn-sm apply-filter" type="button" id="applyfilter">
                                                <i class="fa fa-check me-1"/>Apply
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </t>

                                            <!-- Checkbox Filter -->
                            <t t-if="header['search_type'] == 'checkbox'">
                                <!-- <script>
                                    function toggleCheckbox(checkboxId, dropdownContentId) {
                                        toggleCheckbox_inside(checkboxId, dropdownContentId);
                                    }
                                    </script> -->

                                <div class="checkbox-group mx-5" t-att-data-field="header['name']" t-att-id="'checkboxGroup_' + header['name']">
                                    <t t-foreach="header['unique_values']" t-as="value">
                                        <div class="form-check">
                                            
               <label class="form-check-label" t-att-for="header['name'] + '_' + value">
               <input type="checkbox" class="form-check-input filter-checkbox" t-att-id="header['name'] + '_' + value" t-att-data-field="header['name']" t-att-value="value" t-att-onclick="'toggleCheckbox(\'' + header['name'] + '_' + value + '\', \'dropdown_content_' + header['name'] + '\');'"/>
            <t t-esc="' ' + value"/>
        </label>
                                        </div>
                                    </t>
                                </div>
                            </t>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </div>
                </section>

                <!-- Products Table -->
                <section class="s_products pt32 pb32" style="background-color:#EEEEEE">
                    <div class="container">
                        <div class="table-responsive position-relative">
                            <!-- Loading Overlay -->
                            <div id="loadingOverlay" class="loading-overlay" style="display: none;">
                                <div class="loading-spinner"/>
                            </div>

                            <table class="table" id="productsTable">
                                <thead>
                                    <!-- Parent Headers Row -->
                                    <tr>
                                        <t t-foreach="parent_headers" t-as="parent">
                                            <th t-att-colspan="parent['colspan']" class="text-center">
                                                <span t-esc="parent['name']"/>
                                            </th>
                                        </t>
                                    </tr>
                                    <!-- Child Headers Row -->
                                    <tr>
                                        <t t-foreach="headers" t-as="header">
                                            <th class="text-center" t-att-data-field="header['name']">
                                                <span t-esc="header['label']"/>
                                                <i t-if="header['help_text']" class="fa fa-question-circle" t-att-title="header['help_text']"/>
                                            </th>
                                        </t>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    <!-- Product rows will be populated via JS -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <nav aria-label="Product navigation">
                                    <ul class="pagination justify-content-center" id="productPagination">
                                        <!-- Pagination will be handled via JS -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Initialize products data -->
                <script type="text/javascript"> window.INITIAL_DATA = { products: <t t-out="json.dumps(products)"/>, headers: <t t-out="json.dumps(headers)"/>,
                    parent_headers: <t t-out="json.dumps(parent_headers)"/>, searchable_headers: <t t-out="json.dumps(searchable_headers)"/> }; </script>
                <!-- <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script> -->

            </div>
        </t>
    </template>
    <template id="description_template" name="Bearing Description">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet"/>
                <style>
                    .findbutton{
                    margin-bottom: .75rem;
                    padding: 1rem 1.5rem 1.3125rem;
                    height: 3.5rem;
                    width: 16.75rem;
                    line-height: 1.2825rem;
                    font-size: 1.125rem;}


                    .heading {
                    font-size: 1.5rem;
                    color: #333;
                    padding-bottom: .5rem !important;
                    border-bottom: 3px solid black;
                    }

                    .product-table {
                    width: 100%;
                    border-collapse: collapse;
                    }

                    .product-table tr {
                    border-bottom: 1px solid #555 ;
                    transition: background-color 0.2s ease;
                    }


                    .product-table td {
                    padding: 0.5rem;
                    }

                    .product-table td:first-child {
                    font-weight: 500;
                    color: #333333;
                    }

                    .product-table td:last-child {
                    color: #11111;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    }

                    .external-link-icon {
                    width: 16px;
                    height: 16px;
                    opacity: 0.7;
                    color: black;
                    }

                    @media (max-width: 600px) {
                    .Associatedtable {
                    margin: 1rem auto;
                    }

                    .product-table td {
                    padding: 0.75rem;
                    }

                    .heading {
                    font-size: 1.25rem;
                    }
                    }

                    @media (max-width: 400px) {
                    .product-table {
                    font-size: 0.9rem;
                    }

                    .product-table td {
                    padding: 0.5rem;
                    }
                    }
                </style>
                <section class="s_text_image o_cc o_cc4 pt72 o_colored_level pb48" data-snippet="s_image_text" data-name="Image - Text" style="background-image: none;">
                    <div class="container">
                        <div class="row o_grid_mode" data-row-count="17">

                            <div class="o_colored_level o_grid_item g-col-lg-6 g-height-17 col-lg-6" style="grid-area: 1 / 7 / 12 / 13; z-index: 2;">
                                <h1>
                                    <span style="font-size: 2.75rem;">
                                        <strong><t t-esc="product.name"/></strong>
                                    </span>
                                </h1>
                
                                <p t-field="category.x_product_description" class="lead justify-content-between" style=""/>
                                <p>

                                </p>
                                <p>
                                    <a t-att-href="'/product/pdf/%s' % product.id" class="btn btn-secondary btn-lg" data-bs-original-title="" title=""><i class="fas fa-download" style="margin-left: 0;margin-right: .75rem;"/>Download <t t-esc="product.name"/> Brochure</a>
                                    <br/>
                                </p>
                                <p>
                                    <a t-att-href="'/category/%s' % category.id" style="text-decoration: none;" data-bs-original-title="" title="">
                                        <font class="text-o-color-2">
                                            <i class="fas fa-arrow-right" style="margin-left: 0;margin-right: .75rem;"/>
                                            <strong>View all <t t-esc="category.name"/> Products</strong>
                                        </font>
                                    </a>

                                </p>
                                <p>
                                    <!-- <a t-att-href="'/bearing/pdf/%s' % product.id" style="text-decoration: none;">
                                        <font class="text-o-color-2">
                                            <i class="fas fa-download" style="margin-left: 0;margin-right: .75rem;"/>
                                            <strong>Download PDF</strong>
                                        </font>
                                    </a> -->

                                </p>
                            </div>
                            <div class="o_colored_level o_grid_item g-col-lg-6 o_grid_item_image g-height-10 col-lg-6" style="z-index: 1; grid-area: 2 / 1 / 12 / 7;">
                                <img t-if="category.x_product_image" t-att-src="'/web/image/product.public.category/%s/x_product_image' % category.id" alt="" class="img-fluid img o_we_custom_image" data-mimetype="image/webp" data-original-id="1717" data-mimetype-before-conversion="image/png" data-resize-width="600" loading="lazy"/>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="s_tabs pt48 pb48 o_colored_level" data-vcss="001" data-snippet="s_tabs" data-name="Tabs" style="background-image: none;">
                    <div class="container">
                        <div class="s_tabs_main">
                            <div class="s_tabs_nav mb-3">
                                <ul class="nav nav-pills justify-content-center" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link o_default_snippet_text active" id="nav_tabs_link_1737437826655_52" data-bs-toggle="tab" href="#nav_tabs_content_1737437826655_52" role="tab" aria-controls="nav_tabs_content_1737437826655_52" aria-selected="false">
                                            Technical Specification
                                        </a>
                                    </li>
                                    
                                </ul>
                            </div>
                            <!-- <div class="s_tabs_content tab-content">
                                <div class="tab-pane fade active show" id="nav_tabs_content_1737437826655_52"
                                    role="tabpanel" aria-labelledby="nav_tabs_link_1737437826655_52">
                                    <div class="oe_structure oe_empty"
                                        data-editor-message-default="true"
                                        data-editor-message="DRAG BUILDING BLOCKS HERE">
                                        <section class="s_product_list"
                                            data-snippet="s_product_list" data-name="Items"
                                            style="background-image: none;">
                                            <div class="container-fluid">
                                                <div class="row">
                                                    <div
                                                        class="col-md-4 col-6 o_colored_level col-lg-3">
                                                        <a href="" data-bs-original-title=""
                                                            title="">
                                                            <img
                                                                src="/web/image/1719-86c5c21e/0901d196803c87a3_svg_preview.svg"
                                                                alt=""
                                                                class="img img-fluid o_we_custom_image"
                                                                data-mimetype="image/svg+xml"
                                                                data-original-id="1719"
                                                                data-original-src="/web/image/1719-86c5c21e/0901d196803c87a3_svg_preview.svg"
                                                                data-mimetype-before-conversion="image/svg+xml"
                                                                loading="lazy" />
                                                        </a>
                                                        <div class="s_product_list_item_link"
                                                            style="bottom: 15% !important;">
                                                            2 xTSN 3036 S
                                                        </div>
                                                    </div>
                                                    <div
                                                        class="col-md-4 col-6 o_colored_level col-lg-3">
                                                        <a href="" data-bs-original-title=""
                                                            title="">
                                                            <img
                                                                src="/web/image/1720-c52045b2/0901d19680470aad_svg_preview.svg"
                                                                alt=""
                                                                class="img img-fluid o_we_custom_image"
                                                                data-mimetype="image/svg+xml"
                                                                data-original-id="1720"
                                                                data-original-src="/web/image/1720-c52045b2/0901d19680470aad_svg_preview.svg"
                                                                data-mimetype-before-conversion="image/svg+xml"
                                                                loading="lazy" />
                                                        </a>
                                                        <div class="s_product_list_item_link"
                                                            style="bottom: 15% !important;">
                                                            2 xTK 36 F
                                                        </div>
                                                    </div>


                                                    <div
                                                        class="col-md-4 col-6 o_colored_level col-lg-3">
                                                        <a href="" data-bs-original-title=""
                                                            title="">
                                                            <img
                                                                src="/web/image/1721-094b11f4/0901d196803dc54f_svg_preview.svg"
                                                                alt=""
                                                                class="img img-fluid o_we_custom_image"
                                                                data-mimetype="image/svg+xml"
                                                                data-original-id="1721"
                                                                data-original-src="/web/image/1721-094b11f4/0901d196803dc54f_svg_preview.svg"
                                                                data-mimetype-before-conversion="image/svg+xml"
                                                                loading="lazy" />
                                                        </a>
                                                        <div class="s_product_list_item_link"
                                                            style="bottom: 15% !important;">
                                                            ETS 3036
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </section>
                                        <section>
                                            <div class="container">
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <h1 class="heading">Product Technical Details</h1>
                                                        <table class="product-table">
                                                            
                                                        </table>

                                                    </div>
                                                </div>
                                            </div>
                                        </section>

                                        
                                    </div>
                                </div>
                            </div> -->

                            <div class="s_tabs_content tab-content">
                                <div class="tab-pane fade active show" id="nav_tabs_content_1737437826655_52" role="tabpanel" aria-labelledby="nav_tabs_link_1737437826655_52">
                                    <div class="oe_structure oe_empty" data-editor-message-default="true" data-editor-message="DRAG BUILDING BLOCKS HERE">
                                        <section class="py-5">
                                            <div class="container">
                                                <div class="row">
                                                    <!-- Left column for images -->
                                                    <div class="col-lg-4">
                                                        <div class="product-images-container">
                                                            <t t-foreach="category.x_diagrams" t-as="diagram">
                                                                <div class="mb-4 text-center">
                                                                        <img t-att-src="'/web/image/%s' % diagram.id" t-att-alt="diagram.name" class="img img-fluid center-block"/>
                                                                        <div class="text-center mt-2">
                                                                            <span t-field="diagram.name"/>
                                                                        </div>
                                                                </div>
                                                            </t>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- Right column for technical details -->
                                                    <div class="col-lg-8">
                                                        <h1 class="heading mb-4">Product Technical Details</h1>
                                                        <table class="product-table">
                                                            <tbody>
                            <tr t-if="product.x_adapter_sleeve">
                                <td>Adapter Sleeve</td>
                                <td><t t-esc="product.x_adapter_sleeve"/></td>
                            </tr>
                            <tr t-if="product.x_appro_bearing">
                                <td>Appropriate Bearing</td>
                                <td><t t-esc="product.x_appro_bearing"/></td>
                            </tr>
                            <tr t-if="product.x_bearing_adapter_sleeve_designation">
                                <td>Bearing And Adapter Sleeve Designation</td>
                                <td><t t-esc="product.x_bearing_adapter_sleeve_designation"/></td>
                            </tr>
                            <tr t-if="product.x_bearing_seating_Ca">
                                <td>Bearing Seating Ca</td>
                                <td><t t-esc="product.x_bearing_seating_Ca"/></td>
                            </tr>
                            <tr t-if="product.x_bearing_seating_Da">
                                <td>Bearing Seating Da</td>
                                <td><t t-esc="product.x_bearing_seating_Da"/></td>
                            </tr>
                            <tr t-if="product.x_bearing_seating_db">
                                <td>Bearing Seating db</td>
                                <td><t t-esc="product.x_bearing_seating_db"/></td>
                            </tr>
                            <tr t-if="product.x_felt_strip">
                                <td>Felt Strip</td>
                                <td><t t-esc="product.x_felt_strip"/></td>
                            </tr>
                            <tr t-if="product.x_housing_desi">
                                <td>Housing Designation</td>
                                <td><t t-esc="product.x_housing_desi"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_A">
                                <td>Housing Dimension A</td>
                                <td><t t-esc="product.x_housing_dimensions_A"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_A1">
                                <td>Housing Dimension A1</td>
                                <td><t t-esc="product.x_housing_dimensions_A1"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_A2">
                                <td>Housing Dimension A2</td>
                                <td><t t-esc="product.x_housing_dimensions_A2"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_A3">
                                <td>Housing Dimension A3</td>
                                <td><t t-esc="product.x_housing_dimensions_A3"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_B">
                                <td>Housing Dimension B</td>
                                <td><t t-esc="product.x_housing_dimensions_B"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_Ca">
                                <td>Housing Dimension Ca</td>
                                <td><t t-esc="product.x_housing_dimensions_Ca"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_D">
                                <td>Housing Dimension D</td>
                                <td><t t-esc="product.x_housing_dimensions_D"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_Da">
                                <td>Housing Dimension Da</td>
                                <td><t t-esc="product.x_housing_dimensions_Da"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_E">
                                <td>Housing Dimension E</td>
                                <td><t t-esc="product.x_housing_dimensions_E"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_G">
                                <td>Housing Dimension G</td>
                                <td><t t-esc="product.x_housing_dimensions_G"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_H">
                                <td>Housing Dimension H</td>
                                <td><t t-esc="product.x_housing_dimensions_H"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_H1">
                                <td>Housing Dimension H1</td>
                                <td><t t-esc="product.x_housing_dimensions_H1"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_H2">
                                <td>Housing Dimension H2</td>
                                <td><t t-esc="product.x_housing_dimensions_H2"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_I">
                                <td>Housing Dimension I</td>
                                <td><t t-esc="product.x_housing_dimensions_I"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_J">
                                <td>Housing Dimension J</td>
                                <td><t t-esc="product.x_housing_dimensions_J"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_J1">
                                <td>Housing Dimension J1</td>
                                <td><t t-esc="product.x_housing_dimensions_J1"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_L">
                                <td>Housing Dimension L</td>
                                <td><t t-esc="product.x_housing_dimensions_L"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_N">
                                <td>Housing Dimension N</td>
                                <td><t t-esc="product.x_housing_dimensions_N"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_N1">
                                <td>Housing Dimension N1</td>
                                <td><t t-esc="product.x_housing_dimensions_N1"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_S">
                                <td>Housing Dimension S</td>
                                <td><t t-esc="product.x_housing_dimensions_S"/></td>
                            </tr>
                            <tr t-if="product.x_housing_dimensions_V">
                                <td>Housing Dimension V</td>
                                <td><t t-esc="product.x_housing_dimensions_V"/></td>
                            </tr>
                            <tr t-if="product.x_housing_without_seals">
                                <td>Housing Without Seals</td>
                                <td><t t-esc="product.x_housing_without_seals"/></td>
                            </tr>
                            <tr t-if="product.x_rings_no_desg">
                                <td>Rings No. &amp; Designations</td>
                                <td><t t-esc="product.x_rings_no_desg"/></td>
                            </tr>
                            <tr t-if="product.x_rubber_orings">
                                <td>Rubber O-rings</td>
                                <td><t t-esc="product.x_rubber_orings"/></td>
                            </tr>
                            <tr t-if="product.x_shaft_dia_in">
                                <td>Shaft Diameter (in)</td>
                                <td><t t-esc="product.x_shaft_dia_in"/></td>
                            </tr>
                            <tr t-if="product.x_shift_dia_D">
                                <td>Shaft Diameter D</td>
                                <td><t t-esc="product.x_shift_dia_D"/></td>
                            </tr>
                            <tr t-if="product.x_shift_dia_d1">
                                <td>Shaft Diameter d1</td>
                                <td><t t-esc="product.x_shift_dia_d1"/></td>
                            </tr>
                            <tr t-if="product.x_shift_dia_da">
                                <td>Shaft Diameter da</td>
                                <td><t t-esc="product.x_shift_dia_da"/></td>
                            </tr>
                            <tr t-if="product.x_shift_dia_db">
                                <td>Shaft Diameter db</td>
                                <td><t t-esc="product.x_shift_dia_db"/></td>
                            </tr>
                            <tr t-if="product.x_shift_dia_mm">
                                <td>Shaft Diameter (mm)</td>
                                <td><t t-esc="product.x_shift_dia_mm"/></td>
                            </tr>
                            <tr t-if="product.x_width_across_seal_A2">
                                <td>A2 Width Across</td>
                                <td><t t-esc="product.x_width_across_seal_A2"/></td>
                            </tr>
                            <tr t-if="product.x_width_across_seal_A3">
                                <td>A3 Width Across</td>
                                <td><t t-esc="product.x_width_across_seal_A3"/></td>
                            </tr>
                            <tr t-if="product.x_width_across_seal_Da">
                                <td>Width Across Seal Da</td>
                                <td><t t-esc="product.x_width_across_seal_Da"/></td>
                            </tr>
                            <tr t-if="product.x_without_seal_housing">
                                <td>Housing Designation Without Seals</td>
                                <td><t t-esc="product.x_without_seal_housing"/></td>
                            </tr>
                            <tr t-if="product.x_without_seal_housing_des">
                                <td>Designation Without Seals</td>
                                <td><t t-esc="product.x_without_seal_housing_des"/></td>
                            </tr>
                        </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </section>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </t>
    </template>
</odoo>