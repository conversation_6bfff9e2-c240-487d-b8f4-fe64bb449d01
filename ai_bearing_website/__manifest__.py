# -*- coding: utf-8 -*-
{
    'name': 'AI Bearing Website',
    'version': '********.0',
    'category': 'Website',
    'summary': 'Dynamic Bearing Housing Products Website',
    'description': """
        This module provides a dynamic website interface for bearing housing products.
        Features:
        - Dynamic category-based product display
        - Advanced filtering options
        - Two-level table headers
        - Customizable search fields
        - Pagination support
    """,
    'author': 'Arihant AI',
    'website': 'https://www.arihantai.com',
    'depends': [
        'base',
        'website',
        'product',
        'website_sale',
        # 'ai_jnjbearing_website',  # Added dependency on the main module
    ],
    'data': [
        'views/templates/category_template.xml',
        'views/templates/product_pdf_template.xml',
        'views/templates/product_views.xml',
        'views/templates/pages_3.xml',
        'views/templates/webmenu.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            '/ai_bearing_website/static/css/bearing_style.css',
            '/ai_bearing_website/static/js/bearing_filter.js',
        ],
    },
    'application': True,
    'installable': True,
    'auto_install': False,
    'license': 'LGPL-3',
}
