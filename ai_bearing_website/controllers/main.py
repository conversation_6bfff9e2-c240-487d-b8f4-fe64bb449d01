# -*- coding: utf-8 -*-
from odoo import http
from odoo.http import request
from odoo.addons.website.controllers.main import Website
from odoo.addons.portal.controllers.portal import CustomerPortal
from odoo.exceptions import ValidationError

import json
import logging

_logger = logging.getLogger(__name__)

class BearingWebsite(http.Controller):

    def _get_unique_values(self, products, field_name):
        """Helper method to get unique values for a field across products"""
        values = set()
        for product in products:
            if hasattr(product, field_name):
                value = getattr(product, field_name)
                if value:
                    if isinstance(value, (list, tuple)):
                        values.update(value)
                    else:
                        values.add(value)
        return sorted(list(values))
    @http.route(['/category/<model("product.public.category"):category>'], type='http', auth="public", website=True)
    def category_page(self, category, **kwargs):
        try:
            # Get all headers for this category
            headers = request.env['x_category_headers'].sudo().search([
                ('x_category_id', '=', category.id),
                ('x_visible_in_table', '=', True)
            ], order='x_sequence')

            # Get parent headers
            parent_headers = request.env['x_category_parent_headers'].sudo().search([
                ('x_category_id', '=', category.id)
            ], order='x_sequence')

            # Get searchable headers
            searchable_headers = request.env['x_category_headers'].sudo().search([
                ('x_category_id', '=', category.id),
                ('x_is_searchable', '=', True)
            ])

            # Get products for this category
            products = request.env['product.template'].sudo().search([
                ('public_categ_ids', 'in', category.id),
                # ('website_published', '=', True),
                ('is_published', '=', True)
            ])

            # Prepare header data
            header_data = []
            for header in headers:
                header_data.append({
                    'name': header.x_name,
                    'label': header.x_label,
                    'help_text': header.x_help_text if hasattr(header, 'x_help_text') else '',
                    'parent_header': header.x_category_parent_header_id.id if header.x_category_parent_header_id else None,
                    'search_type': header.x_search_type
                })

            # Prepare parent header data
            parent_header_data = []
            for parent in parent_headers:
                visible_child_headers = parent.x_category_headers.filtered(
                    lambda h: h.x_visible_in_table and h.x_category_id.id == int(category.id)
                )
                child_count = len(visible_child_headers)
                parent_header_data.append({
                    'name': parent.x_name,
                    'colspan': child_count if visible_child_headers else 1
                })

            # Prepare searchable header data
            searchable_data = []
            for header in searchable_headers:
                search_type = 'text'
                unique_values = []
                
                if header.x_search_type == 'number_filter':
                    search_type = 'number'
                elif header.x_search_type == 'checkbox_filter':
                    search_type = 'checkbox'
                    unique_values = self._get_unique_values(products, header.x_name)

                searchable_data.append({
                    'name': header.x_name,
                    'label': header.x_label,
                    'search_type': search_type,
                    'unique_values': unique_values
                })

            # Prepare product data
            product_data = []
            for product in products:
                product_dict = {}
                for header in headers:
                    field_name = header.x_name
                    try:
                        if hasattr(product, field_name):
                            value = getattr(product, field_name)
                            if isinstance(value, (float, int)):
                                product_dict[field_name] = value
                            else:
                                product_dict[field_name] = str(value) if value else ''
                        else:
                            product_dict[field_name] = ''
                    except Exception as e:
                        _logger.error(f"Error getting product field {field_name}: {str(e)}")
                        product_dict[field_name] = ''
                # Add product ID at the end of the dictionary
                product_dict['product_website_url'] = product.website_url
                product_data.append(product_dict)
            values = {
                'category': category,
                'headers': header_data,
                'parent_headers': parent_header_data,
                'searchable_headers': searchable_data,
                'products': product_data
            }
            _logger.info("values.get('searchable_headers')")
            _logger.info(values.get('searchable_headers'))

            # _logger.info(f"Category Page Values: {values.get('products')}")
            return request.render('ai_bearing_website.category_template', values)
        
        except Exception as e:
            _logger.error(f"Error rendering category page: {str(e)}")
            raise e
        
    @http.route(['/bearing/filter'], type='json', auth="public", website=True)
    def filter_products(self, category_id, filters, **post):
        try:
            domain = [
                ('public_categ_ids', 'in', int(category_id)),
                ('website_published', '=', True),
                ('is_published', '=', True)
            ]
            
            # Process filters
            for field, value in filters.items():
                header = request.env['x_category_headers'].sudo().search([
                    ('x_name', '=', field),
                    ('x_category_id', '=', int(category_id))
                ], limit=1)
                
                if header:
                    if header.x_search_type == 'number_filter':
                        if value.get('min'):
                            domain.append((field, '>=', float(value['min'])))
                        if value.get('max'):
                            domain.append((field, '<=', float(value['max'])))
                    elif header.x_search_type == 'checkbox_filter':
                        if value:
                            domain.append((field, 'in', value))
                    else:  # text search
                        if value:
                            domain.append((field, 'ilike', value))

            # Get filtered products
            products = request.env['product.template'].sudo().search(domain)
            
            # Prepare product data
            product_data = []
            headers = request.env['x_category_headers'].sudo().search([
                ('x_category_id', '=', int(category_id)),
                ('x_visible_in_table', '=', True)
            ], order='x_sequence')

            for product in products:
                product_dict = {}
                for header in headers:
                    field_name = header.x_name
                    try:
                        if hasattr(product, field_name):
                            value = getattr(product, field_name)
                            if isinstance(value, (float, int)):
                                product_dict[field_name] = value
                            else:
                                product_dict[field_name] = str(value) if value else ''
                        else:
                            product_dict[field_name] = ''
                    except Exception as e:
                        product_dict[field_name] = ''
                product_data.append(product_dict)

            return {
                'success': True,
                'products': product_data
            }
        
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    @http.route(['/product/<website_url>'], type='http', auth="public", website=True)
    def bearing_description(self, website_url):
        try:
            # Fetch the product details
            
            website_url = '/shop/'+website_url
            _logger.info(f"Product URL: {website_url}")
            product = request.env['product.template'].sudo().search([
                ('x_website_url', '=', website_url)
            ], limit=1)
            
            _logger.info(f"Product: {product}")
            
            if not product:
                return {'error': 'Product not found'}
            
            if product.public_categ_ids:
                category = product.public_categ_ids[0]
                _logger.info(f"Category: {category}")
            return request.render('ai_bearing_website.description_template', {'product': product,'category': category})
        except Exception as e:
            raise ValidationError("Some issue in fetching product details : " + str(e))

        
    @http.route(['/product/pdf/<model("product.template"):product>'], type='http', auth="public", website=True)
    def print_product_pdf(self, product, **kwargs):
        report_name = 'ai_bearing_website.product_pdf_template'
        pdf_content = request.env['ir.actions.report']._render_qweb_pdf(report_name, [product.id])[0]
        
        pdfhttpheaders = [
            ('Content-Type', 'application/pdf'),
            ('Content-Length', len(pdf_content)),
            ('Content-Disposition', f'attachment; filename="{product.name}.pdf"')
        ]
        return request.make_response(pdf_content, headers=pdfhttpheaders)
    
class WebsitePageController(http.Controller):

    @http.route(['/'], type='http', auth="public", website=True)
    def homepage(self, **kwargs):
        return request.render('ai_bearing_website.homepage_template')

    @http.route(['/about-us'], type='http', auth="public", website=True)
    def about_us(self, **kwargs):
        return request.render('ai_bearing_website.about_us_template')

    @http.route('/our-team', type='http', auth="public", website=True)
    def our_team(self, **kwargs):
        # You can fetch any data you need from the database here
        # For example, if you have a model named 'team.member', you can fetch all members:
        # team_members = request.env['team.member'].search([])

        # Render the template and pass any data you need to the template
        return request.render('ai_bearing_website.our_team_template', {
            # 'team_members': team_members,  # Pass the data to the template if needed
        })

    @http.route('/jnj-products', type='http', auth="public", website=True)
    def jnj_products(self, **kwargs):
        # Fetch product categories with parent_id = 17
        categories = request.env['product.public.category'].sudo().search([('parent_id', '=', 'J&J')])

        # Render the template and pass the categories to it
        return request.render('ai_bearing_website.jnj_products_template', {
            'categories': categories,
        })

    # @http.route('/jnj-subproducts', type='http', auth="public", website=True)
    # def jnj_subproducts(self, **kwargs):
    #     # Fetch the category ID from the query parameters
    #     category_id = int(kwargs.get('s', 0))

    #     # Fetch the subcategories of the selected category
    #     subcategories = request.env['product.public.category'].sudo().search([('parent_id', '=', category_id)])

    #     # Render the subproducts template and pass the subcategories to it
    #     return request.render('ai_bearing_website.jnj_subproducts_template', {
    #         'categories': subcategories,
    #     })

    @http.route('/jnj-category/<int:category_id>', type='http', auth="public", website=True)
    def jnj_category(self, category_id, **kwargs):
        # Fetch the products in the selected category
        category = request.env['product.public.category'].sudo().browse(category_id)
        products = request.env['product.template'].sudo().search([('public_categ_ids', 'in', [category_id])])

        # Fetch category headers and searchable headers
        category_headers = request.env['x_category_headers'].sudo().search([
            ('x_category_id', '=', category_id),
            ('x_visible_in_table', '=', True)
        ], order='x_sequence ASC')

        category_searchable_headers = request.env['x_category_headers'].sudo().search([
            ('x_category_id', '=', category_id),
            ('x_visible_in_table', '=', True),
            ('x_is_searchable', '=', True)
        ], order='x_sequence ASC')

        # Render the category template and pass the category, products, and headers to it
        return request.render('ai_bearing_website.jnj_category_template', {
            'category': category,
            'products': products,
            'category_headers': category_headers,
            'category_searchable_headers': category_searchable_headers,
        })

    @http.route('/j-j-sand-plant', type='http', auth="public", website=True)
    def jnj_sand_plant(self, **kwargs):
        # Render the template
        return request.render('ai_bearing_website.jnj_sand_plant_template', {})

    @http.route('/our-services', type='http', auth="public", website=True)
    def our_services(self, **kwargs):
        # Render the template
        return request.render('ai_bearing_website.services_template', {})

    @http.route('/privacy', type='http', auth="public", website=True)
    def privacy_policy(self, **kwargs):
        # Render the template
        return request.render('ai_bearing_website.privacy_policy_template', {})

    @http.route('/products-15', type='http', auth="public", website=True)
    def products_15(self, **kwargs):
        # Fetch product categories with parent_id = 32
        categories = request.env['product.public.category'].sudo().search([('parent_id', '=', 32)])

        # Render the template and pass the categories to it
        return request.render('ai_bearing_website.products_15_template', {
            'categories': categories,
        })

    @http.route('/jnj-infrastructure', type='http', auth="public", website=True)
    def privacy_policy(self, **kwargs):
        # Render the template
        return request.render('ai_bearing_website.jnj_infrastructure_template', {})

class ContactController(CustomerPortal):
    @http.route(['/contactus'], type='http', auth='public', website=True, sitemap=True)
    def contact_page(self, **kwargs):
        values = self._prepare_portal_layout_values()

        # Get company information
        company = request.env.company
        values.update({
            'company': company,
            'res_company': company,
        })

        # If form is submitted
        if request.httprequest.method == 'POST':
            error = {}
            values = {
                'name': kwargs.get('name', ''),
                'phone': kwargs.get('phone', ''),
                'email_from': kwargs.get('email_from', ''),
                'company': kwargs.get('company', ''),
                'subject': kwargs.get('subject', ''),
                'description': kwargs.get('description', ''),
            }

            # Basic validation
            if not values.get('name'):
                error['name'] = 'Please enter your name'
            if not values.get('email_from'):
                error['email_from'] = 'Please enter your email'
            if not values.get('description'):
                error['description'] = 'Please enter your message'

            if not error:
                # Create contact message
                request.env['mail.mail'].sudo().create({
                    'subject': f"Website Contact Form: {values.get('subject', 'Contact Request')}",
                    'email_from': values.get('email_from'),
                    'email_to': company.email,
                    'body_html': f"""
                        <p>Contact Information:</p>
                        <ul>
                            <li>Name: {values.get('name')}</li>
                            <li>Phone: {values.get('phone')}</li>
                            <li>Email: {values.get('email_from')}</li>
                            <li>Company: {values.get('company')}</li>
                        </ul>
                        <p>Message:</p>
                        <p>{values.get('description')}</p>
                    """,
                })

                # Log the contact in CRM if installed
                if request.env['ir.module.module'].sudo().search([('name', '=', 'crm'), ('state', '=', 'installed')]):
                    request.env['crm.lead'].sudo().create({
                        'name': f"Website Contact: {values.get('subject', 'Contact Request')}",
                        'partner_name': values.get('name'),
                        'email_from': values.get('email_from'),
                        'phone': values.get('phone'),
                        'description': values.get('description'),
                        'type': 'opportunity',
                        'medium_id': request.env.ref('utm.utm_medium_website').id,
                    })

                # Track event in Plausible if enabled
                if request.website.plausible_shared_key:
                    request.env['website.track.event'].create({
                        'event_name': 'Lead Generation',
                        'event_params': {'CTA': 'Contact Us'}
                    })

                # Redirect to thank you page
                return request.redirect('/contactus-thank-you')

            values['error'] = error

        return request.render("ai_bearing_website.contactus_template", values)

    @http.route(['/contactus-thank-you'], type='http', auth='public', website=True, sitemap=False)
    def contact_thank_you(self, **kwargs):
        """Handler for the thank you page"""
        values = {
            'company': request.env.company,
        }
        return request.render("ai_bearing_website.contactus_thanks_template", values)

    @http.route(['/contactus/submit'], type='json', auth="public", website=True)
    def contact_form_submit(self, **kwargs):
        """AJAX submission endpoint for the contact form"""
        return self.contact_page(**kwargs)


