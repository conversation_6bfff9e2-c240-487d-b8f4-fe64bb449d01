# -*- coding: utf-8 -*-
from odoo import models, fields, api
from odoo.addons.http_routing.models.ir_http import slug, unslug
import logging

_logger = logging.getLogger(__name__)


class Product_template(models.Model):
    _inherit = 'product.template'


    x_description = fields.Text(string='Description')
    x_website_url = fields.Char(string='Website URL', compute='_compute_x_website_url', store=True)
    
    @api.depends('name')
    def _compute_x_website_url(self):
        for product in self:
            if product.id:
                # _logger.info(f"Product ID: {"/shop/%s" % slug(product)}")
                product.x_website_url = "/shop/%s" % slug(product)
                
    @api.model
    def create(self, vals):
        product = super(Product_template, self).create(vals)
        product.x_website_url = "/shop/%s" % slug(product)
        return product
  