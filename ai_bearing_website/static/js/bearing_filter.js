

// Category Filter Management
document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    const itemsPerPage = 20;
    let currentPage = 1;
    let currentFilters = {};
    let products = [];
    let headers = [];
    let parentHeaders = [];

    // Cache DOM elements
    const searchFilters = document.getElementById('searchInput123');
    const apply_btn = document.getElementById('applyfilter');
    const reset_btn = document.getElementById('resetfilter');
    const productsTableBody = document.getElementById('productsTableBody');
    const productPagination = document.getElementById('productPagination');
    const loadingOverlay = document.getElementById('loadingOverlay');

    function initializeEventListeners() {
        // Text search inputs
        document.querySelectorAll('.search-input').forEach(input => {
            input.addEventListener('input', debounce(handleFilterChange, 500));
        });
    
        // Checkbox inputs
        document.querySelectorAll('.filter-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', handleFilterChange);
        });
    
        // Apply buttons for each filter group
        document.querySelectorAll('.apply-filter').forEach(button => {
            button.addEventListener('click', function(e) {
                handleFilterChange(e, true); // Pass true for apply button click
            });
        });
    
        // Reset buttons for each filter group
        document.querySelectorAll('.reset-filter').forEach(button => {
            button.addEventListener('click', function(e) {
                const filterGroup = e.target.closest('.number-range-filter');
                if (filterGroup) {
                    const inputs = filterGroup.querySelectorAll('input.min-value, input.max-value');
                    inputs.forEach(input => input.value = '');
                    handleFilterChange(e, true); // Pass true for reset button click
                }
            });
        });
    }

    // Initialize data from window object
    try {
        if (window.INITIAL_DATA) {
            products = window.INITIAL_DATA.products || [];
            headers = window.INITIAL_DATA.headers || [];
            parentHeaders = window.INITIAL_DATA.parent_headers || [];
            
            // Initialize filters and table
            // initializeFilters();
            initializeEventListeners(); // Add this line
            updateProductTable(products);
            updatePopularItemCount();
        } else {
            console.error('Initial data not found');
        }
    } catch (error) {
        console.error('Error initializing data:', error);
    }


    // apply_btn.addEventListener('click', applyRange);
    // reset_btn.addEventListener('click', resetRange);

    
    // Event Listeners
    // if (searchFilters) {
    //     searchFilters.addEventListener('input', debounce(handleFilterChange, 500));
    //     searchFilters.addEventListener('change', handleFilterChange);
    // }

    // if (apply_btn) {
    //     apply_btn.addEventListener('click', applyRange);
    // }
    // if (reset_btn) {
    //     reset_btn.addEventListener('click', resetRange);
    // }

    // if (productPagination) {
    //     productPagination.addEventListener('click', handlePaginationClick);
    // }

    /**
     * Initialize filter components
     */
    function initializeFilters() {
        try {
            // Store original button text
            document.querySelectorAll('.dropdown-toggle').forEach(button => {
                button.setAttribute('data-original-text', button.textContent);
            });

            // Initialize filter groups
            const filterGroups = document.querySelectorAll('.filter-group');
            filterGroups.forEach(group => {
                // const type = group.dataset.type;
                // if (type === 'checkbox') {
                //     initializeCheckboxGroup(group);
                // }
            });
        } catch (error) {
            console.error('Error initializing filters:', error);
        }
    }

    /**
     * Initialize checkbox group with unique values from products
     */
    function initializeCheckboxGroup(group) {
        try {
            const field = group.dataset.field;
            const uniqueValues = [...new Set(products.map(p => p[field]))].filter(Boolean);
            const checkboxContainer = group.querySelector('.checkbox-group');

            if (checkboxContainer) {
                uniqueValues.forEach(value => {
                    const div = document.createElement('div');
                    div.className = 'form-check';
                    div.innerHTML = `
                        <input class="form-check-input filter-checkbox" type="checkbox" value="${value}" 
                               id="${field}_${value}" data-field="${field}"
                               onclick="toggleCheckbox(this.id, 'dropdown_content_${field}')">
                        <label class="form-check-label" for="${field}_${value}">${value}</label>
                    `;
                    checkboxContainer.appendChild(div);
                });
            }
        } catch (error) {
            console.error('Error initializing checkbox group:', error);
        }
    }

    /**
     * Toggle checkbox state and update popular item count
     */
    

    // checkbox_event = document.getElementsByID('form-check');


    /**
     * Update popular item count in dropdown
     */
    function updatePopularItemCount(dropdownId) {
        const dropdown = document.getElementById(dropdownId);
        if (!dropdown) return;

        const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]');
        const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
        
        const button = document.querySelector(`[aria-controls="${dropdownId}"]`);
        if (button) {
            const originalText = button.getAttribute('data-original-text') || button.textContent;
            button.setAttribute('data-original-text', originalText);
            button.textContent = checkedCount ? `${originalText} (${checkedCount})` : originalText;
        }
    }

    /**
     * Reset range inputs
     */
    // function resetRange() {
    //     const rangeInputs = document.querySelectorAll('input[type="number"]');
    //     rangeInputs.forEach(input => {
    //         input.value = '';
    //     });
    //     handleFilterChange({ target: rangeInputs[0] });
    // }

    /**
     * Apply range filter
     */
    // function applyRange() {
        // const rangeInputs = document.querySelectorAll('input[type="number"]');
        // const minValue = parseInt(document.getElementById('minValue').value);
        // console.log(minValue);
        // const maxValue = parseInt(document.getElementById('maxValue').value);
        // console.log(maxValue);
        // if (isNaN(minValue) || isNaN(maxValue)) {
        //   alert("Please enter valid numbers for both min and max values.");
        //   return;
        // }
        // console.log(products);
        // const filteredData = products.filter(product => product.x_shift_dia_mm >= minValue && product.x_shift_dia_mm <= maxValue);
        // console.log(filteredData);
        
        // updateProductTable(filteredData);
        // // console.log(rangeInputs);
        // console.log("apply called");
        // handleFilterChange({ target: rangeInputs[0] });
    // }


    // function applyRange() {
    //     const rangeInputs = document.querySelectorAll('input[type="number"]');
    //     handleFilterChange({ target: rangeInputs[0] });
    // }
    /**
     * Handle filter changes
     */
    function handleFilterChange(event, isNumberFilter = false) {
        try {
            const target = event.target;
            const filterGroup = target.closest('.number-range-filter');
            console.log(filterGroup);
            if (filterGroup) {
                if (!isNumberFilter) {
                    return;
                }
                // When Apply clicked, use the filter group's data-field
                target.dataset.field = filterGroup.dataset.field;
            }
            if (!target.dataset.field) return;
            console.log('Filter change event:', event.target.dataset.field);
            showLoading();
            collectFilters();
            filterProducts();
            hideLoading();
        } catch (error) {
            console.error('Error handling filter change:', error);
            hideLoading();
        }
    }

    /**
     * Collect current filter values
     */
    function collectFilters() {
        currentFilters = {};
        try {
            // Text search filters
            document.querySelectorAll('.search-input').forEach(input => {
                const value = input.value.trim();
                if (value) {
                    currentFilters[input.dataset.field] = {
                        type: 'text',
                        value: value.toLowerCase()
                    };
                    // console.log(currentFilters[input.dataset.field]);
                }
            });

            // Number range filters 1/29/2025 mayank :- fixing the apply btn issue 
            document.querySelectorAll('.number-range-filter').forEach(group => {
                // console.log("num filter called");
                const field = group.dataset.field;
                // console.log(field);
                const min = group.querySelector('#minValue')?.value;
                // console.log(min);
                const max = group.querySelector('#maxValue')?.value;
                // console.log(max);
                if (min || max) {
                    currentFilters[field] = {
                        type: 'number',
                        min: min ? parseFloat(min) : null,
                        max: max ? parseFloat(max) : null
                    };
                    // console.log(currentFilters[field]);
                }
            });

            // Checkbox filters
            document.querySelectorAll('.checkbox-group').forEach(group => {
                const field = group.dataset.field;
                const checked = Array.from(group.querySelectorAll('input:checked')).map(cb => cb.value);
                if (checked.length) {
                    currentFilters[field] = {
                        type: 'checkbox',
                        values: checked
                    };
                }
            });
            console.log('Current filters:', currentFilters);
        } catch (error) {
            console.error('Error collecting filters:', error);
        }
    }

    /**
     * Filter products based on current filters
     */
    function filterProducts() {
        try {
            const filteredProducts = products.filter(product => {
                return Object.entries(currentFilters).every(([field, filter]) => {
                    console.log(currentFilters);
                    const value = product[field];
                    console.log(value);
                    
                    switch (filter.type) {
                        case 'text':
                            return value && value.toString().toLowerCase().includes(filter.value);
                        
                        case 'number':
                            if (!value) return false;
                            const numValue = parseFloat(value);
                            // console.log(numValue);
                            if (isNaN(numValue)) return false;
                            if (filter.min !== null && numValue < filter.min) return false;
                            if (filter.max !== null && numValue > filter.max) return false;
                            // console.log("numValue");
                            // console.log(numValue);
                            return true;
                        
                        case 'checkbox':
                            return filter.values.includes(value);
                        
                        default:
                            return true;
                    }
                });
            });

            currentPage = 1; // Reset to first page when filtering
            updateProductTable(filteredProducts);
            updatePagination(filteredProducts.length);
        } catch (error) {
            console.error('Error filtering products:', error);
            hideLoading();
        }
    }

    /**
     * Update product table with filtered results
     */
    function updateProductTable(filteredProducts) {
        try {
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageProducts = filteredProducts.slice(startIndex, endIndex);
    
            // Clear existing rows
            if (productsTableBody) {
                productsTableBody.innerHTML = '';
    
                // Add new rows
                pageProducts.forEach(product => {
                    const row = document.createElement('tr');
                    // Add cursor pointer and hover effect
                    row.style.cursor = 'pointer';
                    // Add click event to the entire row
                    row.addEventListener('click', () => {
                        webiste_url = product.product_website_url.substring(6);
                        // console.log(webiste_url);
                        window.location.href = `/product/${webiste_url}`;
                    });
                    
                    headers.forEach(header => {
                        const cell = document.createElement('td');
                        cell.className = 'text-center';
                        
                        const value = product[header.name];
                        cell.textContent = value !== undefined && value !== null ? value : '';
                        
                        row.appendChild(cell);
                    });
                    productsTableBody.appendChild(row);
                });
    
                // Update pagination
                updatePagination(filteredProducts.length);
            }
        } catch (error) {
            console.error('Error updating product table:', error);
        }
    }

    /**
     * Update pagination based on number of results
     */
    function updatePagination(totalProducts) {
        try {
            const totalPages = Math.ceil(totalProducts / itemsPerPage);
            productPagination.innerHTML = '';

            if (totalPages <= 1) return;

            // Previous button
            if (currentPage > 1) {
                productPagination.appendChild(
                    addPaginationButton('Previous', currentPage - 1)
                );
            }

            // First page
            productPagination.appendChild(
                addPaginationButton('1', 1, currentPage === 1)
            );

            // Ellipsis and middle pages
            if (totalPages > 7) {
                if (currentPage > 4) {
                    productPagination.appendChild(
                        addPaginationButton('...', Math.floor((currentPage + 1) / 2))
                    );
                }

                for (let i = Math.max(2, currentPage - 2); i <= Math.min(currentPage + 2, totalPages - 1); i++) {
                    if (i <= totalPages - 1) {
                        productPagination.appendChild(
                            addPaginationButton(i.toString(), i, currentPage === i)
                        );
                    }
                }

                if (currentPage < totalPages - 3) {
                    productPagination.appendChild(
                        addPaginationButton('...', Math.floor((currentPage + totalPages) / 2))
                    );
                }
            } else {
                // Show all pages if total pages <= 7
                for (let i = 2; i < totalPages; i++) {
                    productPagination.appendChild(
                        addPaginationButton(i.toString(), i, currentPage === i)
                    );
                }
            }

            // Last page
            if (totalPages > 1) {
                productPagination.appendChild(
                    addPaginationButton(totalPages.toString(), totalPages, currentPage === totalPages)
                );
            }

            // Next button
            if (currentPage < totalPages) {
                productPagination.appendChild(
                    addPaginationButton('Next', currentPage + 1)
                );
            }
        } catch (error) {
            console.error('Error updating pagination:', error);
        }
    }

    /**
     * Add pagination button
     */
    function addPaginationButton(text, page, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item${isActive ? ' active' : ''}`;
        
        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;
        
        if (text === '...') {
            a.style.pointerEvents = 'none';
        } else {
            a.addEventListener('click', (e) => {
                e.preventDefault();
                if (currentPage !== page) {
                    currentPage = page;
                    const filteredProducts = products.filter(product => {
                        return Object.entries(currentFilters).every(([field, filter]) => {
                            const value = product[field];
                            
                            switch (filter.type) {
                                case 'text':
                                    return value && value.toString().toLowerCase().includes(filter.value);
                                
                                case 'number':
                                    if (!value) return false;
                                    const numValue = parseFloat(value);
                                    if (isNaN(numValue)) return false;
                                    if (filter.min !== null && numValue < filter.min) return false;
                                    if (filter.max !== null && numValue > filter.max) return false;
                                    return true;
                                
                                case 'checkbox':
                                    return filter.values.includes(value);
                                
                                default:
                                    return true;
                            }
                        });
                    });
                    updateProductTable(filteredProducts);
                }
            });
        }
        
        li.appendChild(a);
        return li;
    }

    /**
     * Show loading overlay
     */
    async function showLoading() {
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
            await new Promise(resolve => setTimeout(resolve, 0)); // Allow DOM to update
        }
    }

    /**
     * Hide loading overlay
     */
    async function hideLoading() {
        if (loadingOverlay) {
            await new Promise(resolve => setTimeout(resolve, 300)); // Minimum loading time
            loadingOverlay.style.display = 'none';
        }
    }

    /**
     * Debounce function
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    window.toggleCheckbox = function(id, dropdownId) {
        const checkbox = document.getElementById(id);
        console.log(checkbox);
        console.log("toggleCheckbox");
        if (checkbox) {
            // checkbox.checked = checkbox.checked;
            updatePopularItemCount(dropdownId);
            handleFilterChange({ target: checkbox });
        }
    };
});
