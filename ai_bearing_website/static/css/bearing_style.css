/* Global Styles */
:root {
    --primary-color: #76885B;
    --primary-hover: #6C7D50;
    --text-color: #333;
    --border-color: #ccc;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --bg-light: #f8f9fa;
}

/* Hero Section */
.s_cover {
    position: relative;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.s_cover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.s_cover .container {
    position: relative;
    z-index: 2;
}

/* Filter Section */
.filter-section {
    padding: 2rem 0;
    background-color: var(--bg-light);
}

/* Dropdown Styles */
.dropdown .btn-secondary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    width: 100%;
    text-align: left;
    position: relative;
}

.dropdown .btn-secondary::after {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
}

.dropdown-menu {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem var(--shadow-color);
    padding: 1rem;
    min-width: 250px;
}

.dropdown-menu .form-control {
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
}

.dropdown-menu .input-group {
    margin-bottom: 1rem;
}

.dropdown-menu .input-group-text {
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

/* Checkbox Styles */
.form-check {
    padding: 0.5rem 0;
    margin: 0;
}

.form-check-input {
    margin-right: 0.5rem;
}

.form-check-label {
    color: var(--text-color);
    font-size: 0.9rem;
}

/* Button Styles */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--bg-light);
    border-color: var(--border-color);
    color: var(--text-color);
}

/* Table Styles */
.table {
    margin-top: 2rem;
    background-color: white;
    border: 1px solid var(--border-color);
}

.table th {
    background-color: var(--bg-light);
    border-bottom: 2px solid var(--border-color);
    color: var(--text-color);
    font-weight: 600;
    padding: 1rem;
    text-align: center;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pagination */
.pagination {
    margin-top: 2rem;
}

.page-link {
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
}

.page-link:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Animations */
.dropdown-menu {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Styles */
@media (max-width: 768px) {
    .dropdown-menu {
        min-width: 100%;
    }
    
    .table-responsive {
        margin-bottom: 2rem;
    }
    
    .s_cover {
        min-height: 300px;
    }
}

/* Helper Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* Custom Scrollbar */
.dropdown-menu::-webkit-scrollbar {
    width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
    background: var(--bg-light);
}

.dropdown-menu::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: var(--primary-hover);
}
