#!/usr/bin/env python3
"""
Analyze contact pages and forms in profectusaccounts.com
"""

import xmlrpc.client
import re

# Working connection details
url = 'http://localhost:8069'
db = 'profectusaccounts.com'
username = 'demo'
password = 'demo'

def connect_to_odoo():
    """Connect to profectusaccounts.com database"""
    try:
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        if not uid:
            print("❌ Authentication failed")
            return None, None
        
        print(f"✅ Connected to profectusaccounts.com as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        return models, uid
        
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return None, None

def analyze_contact_pages(models, uid):
    """Analyze contact pages specifically"""
    try:
        print("\n🔍 Analyzing Contact Pages...")
        print("-" * 50)
        
        # Get contact pages
        contact_pages = models.execute_kw(db, uid, password, 'website.page', 'search_read',
                                        [[('url', 'ilike', 'contact')]], 
                                        {'fields': ['name', 'url', 'website_published', 'view_id']})
        
        print(f"📋 Found {len(contact_pages)} contact-related pages:")
        
        contact_views = []
        for page in contact_pages:
            status = "✅ Published" if page['website_published'] else "❌ Unpublished"
            view_id = page['view_id'][0] if page['view_id'] else None
            print(f"   • {page['name']} - {page['url']} - {status} - View ID: {view_id}")
            
            if view_id:
                contact_views.append(view_id)
        
        return contact_views
        
    except Exception as e:
        print(f"❌ Error analyzing contact pages: {e}")
        return []

def analyze_view_content(models, uid, view_id):
    """Analyze specific view content for forms"""
    try:
        print(f"\n🔍 Analyzing View ID: {view_id}")
        print("-" * 30)
        
        view = models.execute_kw(db, uid, password, 'ir.ui.view', 'read',
                               [view_id], {'fields': ['name', 'key', 'arch_db', 'type']})
        
        if not view:
            print("   ❌ View not found")
            return None
        
        view = view[0]
        print(f"   📄 View Name: {view['name']}")
        print(f"   🔑 Key: {view.get('key', 'N/A')}")
        print(f"   📝 Type: {view.get('type', 'N/A')}")
        
        arch = view.get('arch_db', '')
        if not arch:
            print("   ❌ No architecture content")
            return None
        
        # Look for forms
        if '<form' in arch.lower():
            print("   ✅ Contains form elements")
            
            # Extract form details
            form_matches = re.findall(r'<form[^>]*>', arch, re.IGNORECASE)
            for i, form_match in enumerate(form_matches):
                print(f"      Form {i+1}: {form_match}")
                
                # Extract action
                action_match = re.search(r'action=["\']([^"\']+)["\']', form_match)
                if action_match:
                    print(f"         Action: {action_match.group(1)}")
                
                # Extract method
                method_match = re.search(r'method=["\']([^"\']+)["\']', form_match)
                if method_match:
                    print(f"         Method: {method_match.group(1)}")
            
            # Find input fields
            input_matches = re.findall(r'<input[^>]*name=["\']([^"\']+)["\'][^>]*>', arch, re.IGNORECASE)
            if input_matches:
                print(f"      Input Fields: {input_matches}")
            
            # Find textarea fields
            textarea_matches = re.findall(r'<textarea[^>]*name=["\']([^"\']+)["\'][^>]*>', arch, re.IGNORECASE)
            if textarea_matches:
                print(f"      Textarea Fields: {textarea_matches}")
            
            # Find submit buttons
            submit_matches = re.findall(r'<button[^>]*type=["\']submit["\'][^>]*>([^<]*)</button>', arch, re.IGNORECASE)
            if submit_matches:
                print(f"      Submit Buttons: {submit_matches}")
            
            return {
                'view_id': view_id,
                'name': view['name'],
                'has_forms': True,
                'arch': arch
            }
        else:
            print("   ❌ No form elements found")
            return {
                'view_id': view_id,
                'name': view['name'],
                'has_forms': False,
                'arch': arch
            }
        
    except Exception as e:
        print(f"   ❌ Error analyzing view {view_id}: {e}")
        return None

def check_form_handling_routes(models, uid):
    """Check for existing form handling routes"""
    try:
        print("\n🔍 Checking Form Handling Routes...")
        print("-" * 50)
        
        # Check for website form builder
        try:
            form_builder = models.execute_kw(db, uid, password, 'website.form', 'search_count', [[]])
            print(f"   ✅ website.form records: {form_builder}")
        except:
            print("   ❌ website.form model not available")
        
        # Check for CRM leads (common for contact forms)
        try:
            crm_leads = models.execute_kw(db, uid, password, 'crm.lead', 'search_count', [[]])
            print(f"   ✅ crm.lead records: {crm_leads}")
        except:
            print("   ❌ crm.lead model not available")
        
        # Check for mail.mail (email handling)
        try:
            mail_records = models.execute_kw(db, uid, password, 'mail.mail', 'search_count', [[]])
            print(f"   ✅ mail.mail records: {mail_records}")
        except:
            print("   ❌ mail.mail model not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking routes: {e}")
        return False

def check_website_project_tasks(models, uid):
    """Check website project tasks (since website_project is installed)"""
    try:
        print("\n🔍 Checking Website Project Tasks...")
        print("-" * 50)
        
        # Check for project.task
        try:
            tasks = models.execute_kw(db, uid, password, 'project.task', 'search_count', [[]])
            print(f"   ✅ project.task records: {tasks}")
            
            # Get recent tasks to see if they're from website forms
            recent_tasks = models.execute_kw(db, uid, password, 'project.task', 'search_read',
                                           [[]], {'fields': ['name', 'description', 'create_date'], 
                                                  'limit': 5, 'order': 'create_date desc'})
            
            if recent_tasks:
                print("   📋 Recent tasks:")
                for task in recent_tasks:
                    print(f"      • {task['name']} - {task['create_date']}")
            
        except Exception as e:
            print(f"   ❌ project.task not available: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking project tasks: {e}")
        return False

def test_website_access():
    """Test if we can access the website directly"""
    try:
        print("\n🔍 Testing Website Access...")
        print("-" * 50)
        
        import urllib.request
        import urllib.error
        
        # Test contact page
        contact_url = f"{url.replace(':8069', '')}/contactus"
        print(f"   Testing: {contact_url}")
        
        try:
            response = urllib.request.urlopen(contact_url, timeout=10)
            print(f"   ✅ Contact page accessible - Status: {response.getcode()}")
            
            # Read a small portion of the content
            content = response.read(1000).decode('utf-8', errors='ignore')
            if '<form' in content.lower():
                print("   ✅ Form elements detected in page content")
            else:
                print("   ❌ No form elements detected in page content")
                
        except urllib.error.URLError as e:
            print(f"   ❌ Cannot access contact page: {e}")
        except Exception as e:
            print(f"   ❌ Error accessing page: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing website access: {e}")
        return False

def main():
    """Main analysis function"""
    print("🚀 Analyzing Contact Pages in profectusaccounts.com")
    print("=" * 60)
    
    # Connect to database
    models, uid = connect_to_odoo()
    if not models:
        return False
    
    # Analyze contact pages
    contact_view_ids = analyze_contact_pages(models, uid)
    
    # Analyze each contact view in detail
    form_views = []
    for view_id in contact_view_ids:
        view_info = analyze_view_content(models, uid, view_id)
        if view_info:
            form_views.append(view_info)
    
    # Check form handling capabilities
    check_form_handling_routes(models, uid)
    check_website_project_tasks(models, uid)
    
    # Test website access
    test_website_access()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 CONTACT PAGES ANALYSIS SUMMARY")
    print("=" * 60)
    print(f"• Contact Pages Found: {len(contact_view_ids)}")
    print(f"• Views with Forms: {len([v for v in form_views if v and v.get('has_forms')])}")
    
    forms_with_issues = []
    for view in form_views:
        if view and view.get('has_forms'):
            print(f"\n📄 {view['name']} (ID: {view['view_id']})")
            print("   ✅ Has form elements")
            
            # Check if form has proper action
            arch = view.get('arch', '')
            if 'action=' not in arch:
                print("   ⚠️ Form may not have proper action URL")
                forms_with_issues.append(view)
            
            if '/website/form/' not in arch and '/contactus' not in arch:
                print("   ⚠️ Form action may not be handled properly")
                forms_with_issues.append(view)
    
    if forms_with_issues:
        print(f"\n🚨 FORMS THAT MAY NEED ATTENTION:")
        for view in forms_with_issues:
            print(f"   • {view['name']} (ID: {view['view_id']})")
    
    print("\n🎯 RECOMMENDATIONS:")
    print("1. Check if contact forms are submitting properly")
    print("2. Verify email notifications are working")
    print("3. Test form validation and error handling")
    print("4. Ensure success page redirects work correctly")

if __name__ == "__main__":
    main()
