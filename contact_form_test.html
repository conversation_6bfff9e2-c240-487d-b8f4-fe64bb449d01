
<!DOCTYPE html>
<html>
<head>
    <title>Contact Form Test - profectusaccounts.com</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .test-info { background: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="form-container">
        <div class="test-info">
            <h3>Contact Form Test for profectusaccounts.com</h3>
            <p>This form tests both the original (mail.mail) and updated (res.partner) approaches.</p>
        </div>
        
        <h2>Test Form 1: Using res.partner (Recommended)</h2>
        <form action="/website/form/" method="post" enctype="multipart/form-data" 
              data-model_name="res.partner" data-success-page="/contactus-thank-you">
            
            <div class="form-group">
                <label for="name1">Name *</label>
                <input type="text" id="name1" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="email1">Email *</label>
                <input type="email" id="email1" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="phone1">Phone</label>
                <input type="tel" id="phone1" name="phone">
            </div>
            
            <div class="form-group">
                <label for="company1">Company</label>
                <input type="text" id="company1" name="parent_name">
            </div>
            
            <div class="form-group">
                <label for="subject1">Subject</label>
                <input type="text" id="subject1" name="function">
            </div>
            
            <div class="form-group">
                <label for="message1">Message *</label>
                <textarea id="message1" name="comment" rows="5" required></textarea>
            </div>
            
            <button type="submit">Submit (res.partner)</button>
        </form>
        
        <hr style="margin: 40px 0;">
        
        <h2>Test Form 2: Using mail.mail (Original)</h2>
        <form action="/website/form/" method="post" enctype="multipart/form-data" 
              data-model_name="mail.mail" data-success-page="/contactus-thank-you">
            
            <div class="form-group">
                <label for="name2">Name *</label>
                <input type="text" id="name2" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="email2">Email From *</label>
                <input type="email" id="email2" name="email_from" required>
            </div>
            
            <div class="form-group">
                <label for="email_to2">Email To</label>
                <input type="email" id="email_to2" name="email_to" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="subject2">Subject</label>
                <input type="text" id="subject2" name="subject">
            </div>
            
            <div class="form-group">
                <label for="message2">Message *</label>
                <textarea id="message2" name="description" rows="5" required></textarea>
            </div>
            
            <button type="submit">Submit (mail.mail)</button>
        </form>
        
        <div class="test-info" style="margin-top: 30px;">
            <h4>Test Instructions:</h4>
            <ol>
                <li>Try submitting both forms with test data</li>
                <li>Check if you get redirected to the success page</li>
                <li>For res.partner form: Check Contacts app for new contact</li>
                <li>For mail.mail form: Check if email is sent/queued</li>
            </ol>
        </div>
    </div>
</body>
</html>
        