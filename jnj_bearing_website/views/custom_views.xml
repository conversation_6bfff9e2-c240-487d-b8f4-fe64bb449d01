<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Enquiry Views -->
        <record id="view_enquiry_tree" model="ir.ui.view">
            <field name="name">Enquiry List</field>
            <field name="model">jnj_bearing_website.enquiry</field>
            <field name="arch" type="xml">
                <list string="Enquiries" decoration-info="status == 'new'" decoration-success="status == 'replied'">
                    <field name="create_date"/>
                    <field name="x_name"/>
                    <field name="x_email"/>
                    <field name="x_company_name"/>
                    <field name="x_subject"/>
                    <field name="status"/>
                </list>
            </field>
        </record>

        <record id="view_enquiry_form" model="ir.ui.view">
            <field name="name">Enquiry Form</field>
            <field name="model">jnj_bearing_website.enquiry</field>
            <field name="arch" type="xml">
                <form string="Enquiry">
                    <header>
                        <field name="status" widget="statusbar" statusbar_visible="new,in_progress,replied,closed"/>
                    </header>
                    <sheet>
                        <group>
                            <group string="Contact Information">
                                <field name="x_name"/>
                                <field name="x_last_name"/>
                                <field name="x_email"/>
                                <field name="x_phone"/>
                                <field name="x_company_name"/>
                            </group>
                            <group string="Address">
                                <field name="x_address"/>
                                <field name="x_city"/>
                                <field name="x_state"/>
                                <field name="x_country"/>
                            </group>
                        </group>
                        <group string="Enquiry Details">
                            <field name="x_subject"/>
                            <field name="x_message" widget="text"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Product Enquiry Views -->
        <record id="view_product_enquiry_tree" model="ir.ui.view">
            <field name="name">Product Enquiry List</field>
            <field name="model">jnj_bearing_website.product_enquiry</field>
            <field name="arch" type="xml">
                <list string="Product Enquiries" decoration-info="status == 'new'" decoration-success="status == 'quoted'">
                    <field name="create_date"/>
                    <field name="x_name"/>
                    <field name="x_email"/>
                    <field name="x_company_name"/>
                    <field name="x_product_id"/>
                    <field name="x_qty"/>
                    <field name="status"/>
                </list>
            </field>
        </record>

        <record id="view_product_enquiry_form" model="ir.ui.view">
            <field name="name">Product Enquiry Form</field>
            <field name="model">jnj_bearing_website.product_enquiry</field>
            <field name="arch" type="xml">
                <form string="Product Enquiry">
                    <header>
                        <field name="status" widget="statusbar" statusbar_visible="new,quoted,converted,lost"/>
                    </header>
                    <sheet>
                        <group>
                            <group string="Contact Information">
                                <field name="x_name"/>
                                <field name="x_email"/>
                                <field name="x_phone"/>
                                <field name="x_company_name"/>
                            </group>
                            <group string="Product Information">
                                <field name="x_product_id"/>
                                <field name="x_qty"/>
                            </group>
                        </group>
                        <group string="Additional Message">
                            <field name="x_message" widget="text" nolabel="1"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Category Headers Views -->
        <record id="view_category_headers_tree" model="ir.ui.view">
            <field name="name">Category Headers List</field>
            <field name="model">jnj_bearing_website.category_headers</field>
            <field name="arch" type="xml">
                <list string="Category Headers" editable="bottom">
                    <field name="x_sequence" widget="handle"/>
                    <field name="x_name"/>
                    <field name="x_label"/>
                    <field name="x_category_id"/>
                    <field name="x_category_parent_header_id"/>
                    <field name="x_search_type"/>
                    <field name="x_is_searchable"/>
                    <field name="x_visible_in_table"/>
                </list>
            </field>
        </record>

        <record id="view_category_headers_form" model="ir.ui.view">
            <field name="name">Category Headers Form</field>
            <field name="model">jnj_bearing_website.category_headers</field>
            <field name="arch" type="xml">
                <form string="Category Header">
                    <sheet>
                        <group>
                            <group>
                                <field name="x_name"/>
                                <field name="x_label"/>
                                <field name="x_sequence"/>
                            </group>
                            <group>
                                <field name="x_category_id"/>
                                <field name="x_category_parent_header_id"/>
                                <field name="x_search_type"/>
                            </group>
                        </group>
                        <group>
                            <field name="x_is_searchable"/>
                            <field name="x_visible_in_table"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Category Parent Headers Views -->
        <record id="view_category_parent_headers_tree" model="ir.ui.view">
            <field name="name">Category Parent Headers List</field>
            <field name="model">jnj_bearing_website.category_parent_headers</field>
            <field name="arch" type="xml">
                <list string="Category Parent Headers" editable="bottom">
                    <field name="x_sequence" widget="handle"/>
                    <field name="x_name"/>
                    <field name="x_category_id"/>
                </list>
            </field>
        </record>

        <record id="view_category_parent_headers_form" model="ir.ui.view">
            <field name="name">Category Parent Headers Form</field>
            <field name="model">jnj_bearing_website.category_parent_headers</field>
            <field name="arch" type="xml">
                <form string="Category Parent Header">
                    <sheet>
                        <group>
                            <field name="x_name"/>
                            <field name="x_category_id"/>
                            <field name="x_sequence"/>
                        </group>
                        <notebook>
                            <page string="Category Headers">
                                <field name="x_category_headers">
                                    <list editable="bottom">
                                        <field name="x_sequence" widget="handle"/>
                                        <field name="x_name"/>
                                        <field name="x_label"/>
                                        <field name="x_search_type"/>
                                        <field name="x_is_searchable"/>
                                        <field name="x_visible_in_table"/>
                                    </list>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Barcode Generation Views -->
        <record id="view_generate_barcode_batch_tree" model="ir.ui.view">
            <field name="name">Generate Barcode Batch List</field>
            <field name="model">jnj_bearing_website.generate_barcode_batch</field>
            <field name="arch" type="xml">
                <list string="Barcode Batches">
                    <field name="x_name"/>
                    <field name="x_product_id"/>
                    <field name="x_number"/>
                    <field name="x_inspec_by"/>
                    <field name="create_date"/>
                </list>
            </field>
        </record>

        <record id="view_generate_barcode_batch_form" model="ir.ui.view">
            <field name="name">Generate Barcode Batch Form</field>
            <field name="model">jnj_bearing_website.generate_barcode_batch</field>
            <field name="arch" type="xml">
                <form string="Generate Barcode Batch">
                    <sheet>
                        <group>
                            <group>
                                <field name="x_name"/>
                                <field name="x_product_id"/>
                                <field name="x_number"/>
                            </group>
                            <group>
                                <field name="x_inspec_by"/>
                                <field name="x_csv_file" filename="csv_filename"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Generated Barcodes">
                                <field name="x_generated_barcodes">
                                    <list>
                                        <field name="x_name"/>
                                        <field name="x_product_code"/>
                                        <field name="x_token"/>
                                        <field name="x_no_of_visits"/>
                                        <field name="x_msg"/>
                                    </list>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Data Modelling Views -->
        <record id="view_data_modelling_tree" model="ir.ui.view">
            <field name="name">Data Modelling List</field>
            <field name="model">jnj_bearing_website.data_modelling</field>
            <field name="arch" type="xml">
                <list string="Data Models">
                    <field name="x_name"/>
                    <field name="x_model_id"/>
                    <field name="x_resolve_relations"/>
                    <field name="create_date"/>
                </list>
            </field>
        </record>

        <record id="view_data_modelling_form" model="ir.ui.view">
            <field name="name">Data Modelling Form</field>
            <field name="model">jnj_bearing_website.data_modelling</field>
            <field name="arch" type="xml">
                <form string="Data Model">
                    <sheet>
                        <group>
                            <group>
                                <field name="x_name"/>
                                <field name="x_model_id"/>
                                <field name="x_resolve_relations"/>
                            </group>
                            <group>
                                <field name="x_skip_keys"/>
                                <field name="x_data_file" filename="data_filename"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Data">
                                <field name="x_data" widget="text"/>
                            </page>
                            <page string="JSON Data">
                                <field name="x_json" widget="text"/>
                            </page>
                            <page string="Data Mapping">
                                <field name="x_data_mapping" widget="text"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Documents Views -->
        <record id="view_documents_tree" model="ir.ui.view">
            <field name="name">Documents List</field>
            <field name="model">jnj_bearing_website.documents</field>
            <field name="arch" type="xml">
                <list string="Documents">
                    <field name="x_name"/>
                    <field name="x_print_name"/>
                    <field name="x_model"/>
                    <field name="x_view"/>
                    <field name="x_report"/>
                </list>
            </field>
        </record>

        <record id="view_documents_form" model="ir.ui.view">
            <field name="name">Documents Form</field>
            <field name="model">jnj_bearing_website.documents</field>
            <field name="arch" type="xml">
                <form string="Document">
                    <sheet>
                        <group>
                            <group>
                                <field name="x_name"/>
                                <field name="x_print_name"/>
                                <field name="x_template_name"/>
                            </group>
                            <group>
                                <field name="x_model"/>
                                <field name="x_view"/>
                                <field name="x_view_name"/>
                                <field name="x_report"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Code">
                                <field name="x_code" widget="text"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
    </data>
</odoo>
