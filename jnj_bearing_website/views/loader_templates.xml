<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <!-- Loader Template with Bearing Housing Assembly -->
    <template id="jnj_loader" name="J&amp;J Bearing Loader">
        <div class="jnj-loader" id="jnjLoader">
            <div class="loader-background-pattern"></div>
            <div class="loader-content">
                <!-- Logo -->
                <div class="loader-logo-container">
                    <img src="/web/image/website/1/logo" 
                         alt="J&amp;J Bearing Logo" 
                         class="loader-logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block'"/>
                    <div class="loader-logo-fallback" style="display: none;">
                        <i class="fas fa-cog fa-3x"></i>
                        <span>J&amp;J Bearing</span>
                    </div>
                </div>
                
                <!-- Company Info -->
                <h2 class="loader-text">Precision Bearing Solutions</h2>
                <p class="loader-subtitle">Initializing Advanced Bearing Systems</p>
                
                <!-- 3D Bearing Housing Assembly Animation -->
                <div class="bearing-housing-container" id="bearingHousingContainer">
                    <!-- Housing Base -->
                    <div class="housing-base">
                        <div class="housing-body"></div>
                        <div class="housing-cap"></div>
                        <div class="housing-bolts">
                            <div class="bolt bolt-1"></div>
                            <div class="bolt bolt-2"></div>
                            <div class="bolt bolt-3"></div>
                            <div class="bolt bolt-4"></div>
                        </div>
                    </div>
                    
                    <!-- Bearing Assembly -->
                    <div class="bearing-assembly">
                        <div class="bearing-outer-ring"></div>
                        <div class="bearing-inner-ring"></div>
                        <div class="bearing-balls">
                            <div class="ball ball-1"></div>
                            <div class="ball ball-2"></div>
                            <div class="ball ball-3"></div>
                            <div class="ball ball-4"></div>
                            <div class="ball ball-5"></div>
                            <div class="ball ball-6"></div>
                            <div class="ball ball-7"></div>
                            <div class="ball ball-8"></div>
                        </div>
                        <div class="bearing-cage"></div>
                    </div>
                    
                    <!-- Shaft -->
                    <div class="shaft-assembly">
                        <div class="shaft-main"></div>
                        <div class="shaft-keyway"></div>
                    </div>
                    
                    <!-- Seals -->
                    <div class="seal-assembly">
                        <div class="seal-left"></div>
                        <div class="seal-right"></div>
                    </div>
                    
                    <!-- Lubrication -->
                    <div class="lubrication-system">
                        <div class="grease-fitting"></div>
                        <div class="oil-level-indicator"></div>
                    </div>
                </div>
                
                <!-- Loading Animation -->
                <div class="loading-animation">
                    <div class="loading-bar">
                        <div class="loading-progress" id="loadingProgress"></div>
                    </div>
                </div>
                
                <!-- Loading Dots -->
                <div class="loading-dots">
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                </div>
                
                <!-- Progress Percentage -->
                <div class="loader-progress" id="loaderProgress">0%</div>
                
                <!-- Loading Status -->
                <div class="loading-status" id="loadingStatus">
                    <span class="status-text">Initializing...</span>
                </div>
            </div>
        </div>
    </template>
</data>
</odoo>
