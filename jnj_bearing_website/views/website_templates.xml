<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="jnj_bearing_website.contact_page" name="Contact Page" website="True">
            <t t-call="website.layout">
                <t t-set="title">Contact Us - J&amp;J Bearing</t>

                <div class="container py-5">
                    <div class="row">
                        <div class="col-lg-8 mx-auto">
                            <h1 class="text-center mb-5">Contact Us</h1>

                            <form action="/contact/submit" method="post" class="row g-3">
                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                <div class="col-md-6">
                                    <label for="name" class="form-label">Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required=""/>
                                </div>

                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" required=""/>
                                </div>

                                <div class="col-12">
                                    <label for="subject" class="form-label">Subject</label>
                                    <input type="text" class="form-control" id="subject" name="subject"/>
                                </div>

                                <div class="col-12">
                                    <label for="message" class="form-label">Message *</label>
                                    <textarea class="form-control" id="message" name="message" rows="5" required=""></textarea>
                                </div>

                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">Send Message</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <template id="jnj_bearing_website.contact_success" name="Contact Success" website="True">
            <t t-call="website.layout">
                <t t-set="title">Thank You - J&amp;J Bearing</t>

                <div class="container py-5">
                    <div class="row">
                        <div class="col-lg-6 mx-auto text-center">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle fa-3x mb-3"></i>
                                <h3>Thank You!</h3>
                                <p>Your message has been sent successfully. We'll get back to you soon.</p>
                                <a href="/" class="btn btn-primary">Back to Home</a>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <template id="jnj_bearing_website.contact_error" name="Contact Error" website="True">
            <t t-call="website.layout">
                <t t-set="title">Error - J&amp;J Bearing</t>

                <div class="container py-5">
                    <div class="row">
                        <div class="col-lg-6 mx-auto text-center">
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                <h3>Oops! Something went wrong</h3>
                                <p>We encountered an error while processing your message. Please try again.</p>
                                <a href="/contact" class="btn btn-primary">Try Again</a>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <template id="jnj_bearing_website.products_page" name="J&amp;J Bearing Products" website="True">
            <t t-call="website.layout">
                <t t-set="title">Products - J&amp;J Bearing</t>

                <div class="container py-5">
                    <div class="row">
                        <div class="col-12">
                            <h1 class="text-center mb-5">Our Products</h1>
                            <p class="text-center lead mb-5">Comprehensive range of precision bearings and bearing housings</p>

                            <div class="row">
                                <t t-foreach="products" t-as="product">
                                    <div class="col-lg-4 col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <h5 class="card-title" t-field="product.name"/>
                                                <p class="card-text" t-field="product.description_sale"/>
                                                <a href="/contact" class="btn btn-primary">Get Quote</a>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
    </data>
</odoo>
