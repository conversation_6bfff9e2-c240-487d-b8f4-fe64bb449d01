<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Actions (must be defined before menus that reference them) -->
        <record id="action_enquiry" model="ir.actions.act_window">
            <field name="name">Enquiries</field>
            <field name="res_model">jnj_bearing_website.enquiry</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No enquiries yet!
                </p>
                <p>
                    Customer enquiries will appear here when submitted through the website.
                </p>
            </field>
        </record>

        <record id="action_product_enquiry" model="ir.actions.act_window">
            <field name="name">Product Enquiries</field>
            <field name="res_model">jnj_bearing_website.product_enquiry</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No product enquiries yet!
                </p>
                <p>
                    Product-specific enquiries will appear here when submitted through the website.
                </p>
            </field>
        </record>

        <record id="action_category_headers" model="ir.actions.act_window">
            <field name="name">Category Headers</field>
            <field name="res_model">jnj_bearing_website.category_headers</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first category header!
                </p>
                <p>
                    Category headers define the searchable and filterable fields for product categories.
                </p>
            </field>
        </record>

        <record id="action_category_parent_headers" model="ir.actions.act_window">
            <field name="name">Category Parent Headers</field>
            <field name="res_model">jnj_bearing_website.category_parent_headers</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first parent header!
                </p>
                <p>
                    Parent headers group related category headers together for better organization.
                </p>
            </field>
        </record>

        <record id="action_generate_barcode_batch" model="ir.actions.act_window">
            <field name="name">Generate Barcode Batch</field>
            <field name="res_model">jnj_bearing_website.generate_barcode_batch</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Generate your first barcode batch!
                </p>
                <p>
                    Create batches of barcodes for products to track and manage inventory.
                </p>
            </field>
        </record>

        <record id="action_generated_barcodes" model="ir.actions.act_window">
            <field name="name">Generated Barcodes</field>
            <field name="res_model">jnj_bearing_website.generated_barcodes</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No barcodes generated yet!
                </p>
                <p>
                    Generated barcodes will appear here after creating barcode batches.
                </p>
            </field>
        </record>

        <record id="action_data_modelling" model="ir.actions.act_window">
            <field name="name">Data Modelling</field>
            <field name="res_model">jnj_bearing_website.data_modelling</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first data model!
                </p>
                <p>
                    Data models help you import and manage data from external sources.
                </p>
            </field>
        </record>

        <record id="action_documents" model="ir.actions.act_window">
            <field name="name">Documents</field>
            <field name="res_model">jnj_bearing_website.documents</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first document template!
                </p>
                <p>
                    Document templates help you generate reports and documents for various models.
                </p>
            </field>
        </record>

        <!-- Main Menu -->
        <menuitem id="menu_jnj_bearing_main"
                  name="J&amp;J Bearing"
                  sequence="10"/>

        <!-- Customer Management -->
        <menuitem id="menu_jnj_bearing_customers"
                  name="Customer Management"
                  parent="menu_jnj_bearing_main"
                  sequence="10"/>

        <menuitem id="menu_enquiries"
                  name="Enquiries"
                  parent="menu_jnj_bearing_customers"
                  action="action_enquiry"
                  sequence="10"/>

        <menuitem id="menu_product_enquiries"
                  name="Product Enquiries"
                  parent="menu_jnj_bearing_customers"
                  action="action_product_enquiry"
                  sequence="20"/>

        <!-- Product Configuration -->
        <menuitem id="menu_jnj_bearing_products"
                  name="Product Configuration"
                  parent="menu_jnj_bearing_main"
                  sequence="20"/>

        <menuitem id="menu_category_headers"
                  name="Category Headers"
                  parent="menu_jnj_bearing_products"
                  action="action_category_headers"
                  sequence="10"/>

        <menuitem id="menu_category_parent_headers"
                  name="Category Parent Headers"
                  parent="menu_jnj_bearing_products"
                  action="action_category_parent_headers"
                  sequence="20"/>

        <!-- Barcode Management -->
        <menuitem id="menu_jnj_bearing_barcodes"
                  name="Barcode Management"
                  parent="menu_jnj_bearing_main"
                  sequence="30"/>

        <menuitem id="menu_generate_barcode_batch"
                  name="Generate Barcode Batch"
                  parent="menu_jnj_bearing_barcodes"
                  action="action_generate_barcode_batch"
                  sequence="10"/>

        <menuitem id="menu_generated_barcodes"
                  name="Generated Barcodes"
                  parent="menu_jnj_bearing_barcodes"
                  action="action_generated_barcodes"
                  sequence="20"/>

        <!-- Data Management -->
        <menuitem id="menu_jnj_bearing_data"
                  name="Data Management"
                  parent="menu_jnj_bearing_main"
                  sequence="40"/>

        <menuitem id="menu_data_modelling"
                  name="Data Modelling"
                  parent="menu_jnj_bearing_data"
                  action="action_data_modelling"
                  sequence="10"/>

        <menuitem id="menu_documents"
                  name="Documents"
                  parent="menu_jnj_bearing_data"
                  action="action_documents"
                  sequence="20"/>

        <!-- Tools -->
        <menuitem id="menu_jnj_bearing_tools"
                  name="Tools"
                  parent="menu_jnj_bearing_main"
                  sequence="50"/>
    </data>
</odoo>
