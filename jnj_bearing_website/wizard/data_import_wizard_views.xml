<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Data Import Wizard Form View -->
        <record id="view_data_import_wizard_form" model="ir.ui.view">
            <field name="name">Data Import Wizard Form</field>
            <field name="model">jnj_bearing_website.data_import_wizard</field>
            <field name="arch" type="xml">
                <form string="Data Import Wizard">
                    <sheet>
                        <group>
                            <group>
                                <field name="name" placeholder="e.g., Product Import 2024"/>
                                <field name="import_type"/>
                                <field name="file_data" filename="file_name"/>
                                <field name="file_name" invisible="1"/>
                            </group>
                            <group>
                                <field name="has_header"/>
                                <field name="update_existing"/>
                            </group>
                        </group>
                        
                        <group string="Import Results" invisible="not context.get('show_results')">
                            <group>
                                <field name="success_count" readonly="1"/>
                                <field name="error_count" readonly="1"/>
                            </group>
                            <field name="import_log" readonly="1" widget="text"/>
                        </group>
                    </sheet>
                    
                    <footer>
                        <button name="action_import_data" 
                                string="Import Data" 
                                type="object" 
                                class="btn-primary"
                                invisible="context.get('show_results')"/>
                        <button string="Close" 
                                class="btn-secondary" 
                                special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Data Import Wizard Action -->
        <record id="action_data_import_wizard" model="ir.actions.act_window">
            <field name="name">Data Import Wizard</field>
            <field name="res_model">jnj_bearing_website.data_import_wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

        <!-- Menu Item -->
        <menuitem id="menu_data_import_wizard"
                  name="Data Import Wizard"
                  parent="menu_jnj_bearing_tools"
                  action="action_data_import_wizard"
                  sequence="10"/>
    </data>
</odoo>
