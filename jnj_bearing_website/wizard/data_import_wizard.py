# -*- coding: utf-8 -*-
from odoo import models, fields, api
import json
import csv
import base64
import io
import logging

_logger = logging.getLogger(__name__)

class DataImportWizard(models.TransientModel):
    """Wizard for importing data into J&J Bearing models"""
    _name = 'jnj_bearing_website.data_import_wizard'
    _description = 'Data Import Wizard'

    name = fields.Char(string='Import Name', required=True)
    import_type = fields.Selection([
        ('products', 'Products'),
        ('categories', 'Categories'),
        ('enquiries', 'Enquiries'),
        ('barcodes', 'Barcodes')
    ], string='Import Type', required=True, default='products')
    
    file_data = fields.Binary(string='CSV File', required=True)
    file_name = fields.Char(string='File Name')
    
    # Options
    has_header = fields.Boolean(string='File has header row', default=True)
    update_existing = fields.Boolean(string='Update existing records', default=False)
    
    # Results
    import_log = fields.Text(string='Import Log', readonly=True)
    success_count = fields.Integer(string='Successful Imports', readonly=True)
    error_count = fields.Integer(string='Errors', readonly=True)

    def action_import_data(self):
        """Execute the data import"""
        self.ensure_one()
        
        try:
            # Decode file data
            file_content = base64.b64decode(self.file_data)
            csv_data = file_content.decode('utf-8')
            
            # Parse CSV
            csv_reader = csv.DictReader(io.StringIO(csv_data))
            
            if self.import_type == 'products':
                return self._import_products(csv_reader)
            elif self.import_type == 'categories':
                return self._import_categories(csv_reader)
            elif self.import_type == 'enquiries':
                return self._import_enquiries(csv_reader)
            elif self.import_type == 'barcodes':
                return self._import_barcodes(csv_reader)
                
        except Exception as e:
            self.import_log = f"Error: {str(e)}"
            self.error_count = 1
            return self._return_wizard()

    def _import_products(self, csv_reader):
        """Import products from CSV"""
        success_count = 0
        error_count = 0
        log_lines = []
        
        for row_num, row in enumerate(csv_reader, start=2):
            try:
                # Map CSV columns to product fields
                product_data = {
                    'name': row.get('name', ''),
                    'default_code': row.get('default_code', ''),
                    'list_price': float(row.get('list_price', 0)),
                    'description_sale': row.get('description_sale', ''),
                    'x_description': row.get('x_description', ''),
                    'website_published': row.get('website_published', 'True').lower() == 'true',
                }
                
                # Add bearing-specific fields
                bearing_fields = [
                    'x_shaft_dia_in', 'x_shift_dia_mm', 'x_housing_desi',
                    'x_housing_dimensions_H', 'x_housing_dimensions_D',
                    'x_bearing_seating_Ca', 'x_bearing_seating_Da'
                ]
                
                for field in bearing_fields:
                    if field in row and row[field]:
                        try:
                            if field.endswith('_in') or field.endswith('_mm'):
                                product_data[field] = float(row[field])
                            elif field.endswith('_H') or field.endswith('_D') or field.endswith('_Ca') or field.endswith('_Da'):
                                product_data[field] = int(row[field])
                            else:
                                product_data[field] = row[field]
                        except (ValueError, TypeError):
                            pass
                
                # Check if product exists
                existing_product = None
                if self.update_existing and product_data.get('default_code'):
                    existing_product = self.env['product.template'].search([
                        ('default_code', '=', product_data['default_code'])
                    ], limit=1)
                
                if existing_product:
                    existing_product.write(product_data)
                    log_lines.append(f"Row {row_num}: Updated product {product_data['name']}")
                else:
                    self.env['product.template'].create(product_data)
                    log_lines.append(f"Row {row_num}: Created product {product_data['name']}")
                
                success_count += 1
                
            except Exception as e:
                error_count += 1
                log_lines.append(f"Row {row_num}: Error - {str(e)}")
        
        self.success_count = success_count
        self.error_count = error_count
        self.import_log = '\n'.join(log_lines)
        
        return self._return_wizard()

    def _import_categories(self, csv_reader):
        """Import categories from CSV"""
        success_count = 0
        error_count = 0
        log_lines = []
        
        for row_num, row in enumerate(csv_reader, start=2):
            try:
                category_data = {
                    'name': row.get('name', ''),
                    'x_category_description': row.get('description', ''),
                }
                
                # Check for parent category
                parent_name = row.get('parent_category', '')
                if parent_name:
                    parent_category = self.env['product.public.category'].search([
                        ('name', '=', parent_name)
                    ], limit=1)
                    if parent_category:
                        category_data['parent_id'] = parent_category.id
                
                # Check if category exists
                existing_category = None
                if self.update_existing:
                    existing_category = self.env['product.public.category'].search([
                        ('name', '=', category_data['name'])
                    ], limit=1)
                
                if existing_category:
                    existing_category.write(category_data)
                    log_lines.append(f"Row {row_num}: Updated category {category_data['name']}")
                else:
                    self.env['product.public.category'].create(category_data)
                    log_lines.append(f"Row {row_num}: Created category {category_data['name']}")
                
                success_count += 1
                
            except Exception as e:
                error_count += 1
                log_lines.append(f"Row {row_num}: Error - {str(e)}")
        
        self.success_count = success_count
        self.error_count = error_count
        self.import_log = '\n'.join(log_lines)
        
        return self._return_wizard()

    def _import_enquiries(self, csv_reader):
        """Import enquiries from CSV"""
        success_count = 0
        error_count = 0
        log_lines = []
        
        for row_num, row in enumerate(csv_reader, start=2):
            try:
                enquiry_data = {
                    'x_name': row.get('name', ''),
                    'x_email': row.get('email', ''),
                    'x_phone': row.get('phone', ''),
                    'x_company_name': row.get('company_name', ''),
                    'x_subject': row.get('subject', ''),
                    'x_message': row.get('message', ''),
                    'x_city': row.get('city', ''),
                    'x_state': row.get('state', ''),
                    'x_country': row.get('country', ''),
                }
                
                self.env['jnj_bearing_website.enquiry'].create(enquiry_data)
                log_lines.append(f"Row {row_num}: Created enquiry from {enquiry_data['x_name']}")
                success_count += 1
                
            except Exception as e:
                error_count += 1
                log_lines.append(f"Row {row_num}: Error - {str(e)}")
        
        self.success_count = success_count
        self.error_count = error_count
        self.import_log = '\n'.join(log_lines)
        
        return self._return_wizard()

    def _import_barcodes(self, csv_reader):
        """Import barcodes from CSV"""
        success_count = 0
        error_count = 0
        log_lines = []
        
        for row_num, row in enumerate(csv_reader, start=2):
            try:
                # Find product
                product_code = row.get('product_code', '')
                product = self.env['product.template'].search([
                    ('default_code', '=', product_code)
                ], limit=1)
                
                if not product:
                    error_count += 1
                    log_lines.append(f"Row {row_num}: Product not found - {product_code}")
                    continue
                
                barcode_data = {
                    'x_name': row.get('name', ''),
                    'x_product_code': product_code,
                    'x_product_id': product.id,
                    'x_token': row.get('token', ''),
                    'x_no_of_visits': int(row.get('visits', 0)),
                }
                
                self.env['jnj_bearing_website.generated_barcodes'].create(barcode_data)
                log_lines.append(f"Row {row_num}: Created barcode {barcode_data['x_name']}")
                success_count += 1
                
            except Exception as e:
                error_count += 1
                log_lines.append(f"Row {row_num}: Error - {str(e)}")
        
        self.success_count = success_count
        self.error_count = error_count
        self.import_log = '\n'.join(log_lines)
        
        return self._return_wizard()

    def _return_wizard(self):
        """Return to wizard with results"""
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'jnj_bearing_website.data_import_wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {'show_results': True}
        }
