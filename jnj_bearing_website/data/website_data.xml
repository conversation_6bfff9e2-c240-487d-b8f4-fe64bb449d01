<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Website Pages -->
        <record id="homepage_page" model="website.page">
            <field name="name">J&amp;J Bearing Homepage</field>
            <field name="url">/</field>
            <field name="view_id" ref="jnj_bearing_website.homepage"/>
            <field name="website_published">True</field>
            <field name="is_homepage">True</field>
        </record>

        <record id="contact_page_record" model="website.page">
            <field name="name">Contact Us</field>
            <field name="url">/contact</field>
            <field name="view_id" ref="jnj_bearing_website.contact_page"/>
            <field name="website_published">True</field>
        </record>

        <!-- Website Menu Items -->
        <record id="menu_home" model="website.menu">
            <field name="name">Home</field>
            <field name="url">/</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence">10</field>
        </record>

        <record id="menu_products" model="website.menu">
            <field name="name">Products</field>
            <field name="url">/products</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence">20</field>
        </record>

        <record id="menu_about" model="website.menu">
            <field name="name">About</field>
            <field name="url">/about</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence">30</field>
        </record>

        <record id="menu_services" model="website.menu">
            <field name="name">Services</field>
            <field name="url">/services</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence">40</field>
        </record>

        <record id="menu_contact" model="website.menu">
            <field name="name">Contact</field>
            <field name="url">/contact</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence">50</field>
        </record>

        <!-- Sample Product Categories -->
        <record id="category_ball_bearings" model="product.category">
            <field name="name">Ball Bearings</field>
            <field name="x_category_description">&lt;p&gt;High-precision ball bearings for various industrial applications with superior load capacity and durability.&lt;/p&gt;</field>
        </record>

        <record id="category_bearing_housings" model="product.category">
            <field name="name">Bearing Housings</field>
            <field name="x_category_description">&lt;p&gt;Robust bearing housings designed for easy installation and maintenance in demanding environments.&lt;/p&gt;</field>
        </record>

        <record id="category_accessories" model="product.category">
            <field name="name">Accessories</field>
            <field name="x_category_description">&lt;p&gt;Complete range of bearing accessories including seals, rings, and mounting hardware.&lt;/p&gt;</field>
        </record>

        <!-- Sample Category Headers -->
        <record id="header_shaft_diameter" model="jnj_bearing_website.category_headers">
            <field name="x_name">shaft_diameter</field>
            <field name="x_label">Shaft Diameter</field>
            <field name="x_search_type">number_filter</field>
            <field name="x_is_searchable">True</field>
            <field name="x_visible_in_table">True</field>
            <field name="x_sequence">10</field>
        </record>

        <record id="header_housing_type" model="jnj_bearing_website.category_headers">
            <field name="x_name">housing_type</field>
            <field name="x_label">Housing Type</field>
            <field name="x_search_type">checkbox_filter</field>
            <field name="x_is_searchable">True</field>
            <field name="x_visible_in_table">True</field>
            <field name="x_sequence">20</field>
        </record>

        <record id="header_bearing_designation" model="jnj_bearing_website.category_headers">
            <field name="x_name">bearing_designation</field>
            <field name="x_label">Bearing Designation</field>
            <field name="x_search_type">search</field>
            <field name="x_is_searchable">True</field>
            <field name="x_visible_in_table">True</field>
            <field name="x_sequence">30</field>
        </record>

        <!-- Sample Category Parent Headers -->
        <record id="parent_header_dimensions" model="jnj_bearing_website.category_parent_headers">
            <field name="x_name">Dimensions</field>
            <field name="x_sequence">10</field>
        </record>

        <record id="parent_header_specifications" model="jnj_bearing_website.category_parent_headers">
            <field name="x_name">Specifications</field>
            <field name="x_sequence">20</field>
        </record>

        <!-- Link headers to parent headers -->
        <record id="header_shaft_diameter" model="jnj_bearing_website.category_headers">
            <field name="x_category_parent_header_id" ref="parent_header_dimensions"/>
        </record>

        <record id="header_housing_type" model="jnj_bearing_website.category_headers">
            <field name="x_category_parent_header_id" ref="parent_header_specifications"/>
        </record>

        <record id="header_bearing_designation" model="jnj_bearing_website.category_headers">
            <field name="x_category_parent_header_id" ref="parent_header_specifications"/>
        </record>
    </data>
</odoo>
