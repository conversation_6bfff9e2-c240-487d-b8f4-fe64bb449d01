# -*- coding: utf-8 -*-
import json
import logging
from odoo import http, fields, _
from odoo.http import request
from odoo.addons.website.controllers.main import Website
from odoo.addons.website_sale.controllers.main import WebsiteSale

_logger = logging.getLogger(__name__)

class JNJBearingWebsite(http.Controller):
    """Main controller for J&J Bearing website functionality"""

    @http.route(['/'], type='http', auth="public", website=True)
    def homepage(self, **kwargs):
        """Homepage with bearing-themed design"""
        return request.render('jnj_bearing_website.homepage', {
            'page_name': 'homepage',
        })

    @http.route(['/products', '/products/page/<int:page>'], type='http', auth="public", website=True)
    def products_page(self, page=1, category=None, search=None, **kwargs):
        """Products listing page with advanced filtering"""
        domain = [('website_published', '=', True)]
        
        # Category filtering
        if category:
            try:
                category_id = int(category)
                domain.append(('public_categ_ids', 'child_of', category_id))
            except (ValueError, TypeError):
                pass
        
        # Search filtering
        if search:
            domain.extend([
                '|', '|', '|',
                ('name', 'ilike', search),
                ('description_sale', 'ilike', search),
                ('x_description', 'ilike', search),
                ('default_code', 'ilike', search)
            ])
        
        # Get products
        Product = request.env['product.template']
        products_count = Product.search_count(domain)
        
        # Pagination
        page_size = 20
        offset = (page - 1) * page_size
        products = Product.search(domain, limit=page_size, offset=offset, order='name')
        
        # Get categories for filtering
        categories = request.env['product.public.category'].search([
            ('parent_id', '=', False)
        ])
        
        # Pagination info
        total_pages = (products_count + page_size - 1) // page_size
        
        return request.render('jnj_bearing_website.products_page', {
            'products': products,
            'categories': categories,
            'current_page': page,
            'total_pages': total_pages,
            'products_count': products_count,
            'search': search or '',
            'current_category': category,
            'page_name': 'products',
        })

    @http.route(['/product/<model("product.template"):product>'], type='http', auth="public", website=True)
    def product_detail(self, product, **kwargs):
        """Product detail page with bearing specifications"""
        if not product.website_published:
            return request.render('website.404')
        
        # Get related products
        related_products = request.env['product.template'].search([
            ('website_published', '=', True),
            ('public_categ_ids', 'in', product.public_categ_ids.ids),
            ('id', '!=', product.id)
        ], limit=4)
        
        return request.render('jnj_bearing_website.product_detail', {
            'product': product,
            'related_products': related_products,
            'page_name': 'product_detail',
        })

    @http.route(['/contact'], type='http', auth="public", website=True)
    def contact_page(self, **kwargs):
        """Contact page"""
        return request.render('jnj_bearing_website.contact_page', {
            'page_name': 'contact',
        })

    @http.route(['/contact/submit'], type='http', auth="public", website=True, methods=['POST'], csrf=False)
    def contact_submit(self, **kwargs):
        """Handle contact form submission"""
        try:
            # Create enquiry record
            enquiry_data = {
                'x_name': kwargs.get('name', ''),
                'x_last_name': kwargs.get('last_name', ''),
                'x_email': kwargs.get('email', ''),
                'x_phone': kwargs.get('phone', ''),
                'x_company_name': kwargs.get('company_name', ''),
                'x_subject': kwargs.get('subject', ''),
                'x_message': kwargs.get('message', ''),
                'x_city': kwargs.get('city', ''),
                'x_state': kwargs.get('state', ''),
                'x_country': kwargs.get('country', ''),
                'x_address': kwargs.get('address', ''),
            }
            
            request.env['jnj_bearing_website.enquiry'].sudo().create(enquiry_data)
            
            return request.render('jnj_bearing_website.contact_success', {
                'page_name': 'contact_success',
            })
            
        except Exception as e:
            _logger.error(f"Error submitting contact form: {e}")
            return request.render('jnj_bearing_website.contact_error', {
                'page_name': 'contact_error',
                'error': str(e),
            })

    @http.route(['/product/enquiry'], type='http', auth="public", website=True, methods=['POST'], csrf=False)
    def product_enquiry_submit(self, **kwargs):
        """Handle product enquiry form submission"""
        try:
            product_id = kwargs.get('product_id')
            if product_id:
                product_id = int(product_id)
            
            enquiry_data = {
                'x_name': kwargs.get('name', ''),
                'x_email': kwargs.get('email', ''),
                'x_phone': kwargs.get('phone', ''),
                'x_company_name': kwargs.get('company_name', ''),
                'x_qty': int(kwargs.get('quantity', 1)),
                'x_product_id': product_id,
                'x_message': kwargs.get('message', ''),
            }
            
            request.env['jnj_bearing_website.product_enquiry'].sudo().create(enquiry_data)
            
            return json.dumps({'success': True, 'message': 'Enquiry submitted successfully!'})
            
        except Exception as e:
            _logger.error(f"Error submitting product enquiry: {e}")
            return json.dumps({'success': False, 'message': 'Error submitting enquiry. Please try again.'})

    @http.route(['/api/products/filter'], type='json', auth="public", website=True)
    def products_filter_api(self, **kwargs):
        """API endpoint for product filtering"""
        try:
            domain = [('website_published', '=', True)]
            
            # Apply filters from kwargs
            if kwargs.get('category_id'):
                domain.append(('public_categ_ids', 'child_of', int(kwargs['category_id'])))
            
            if kwargs.get('search'):
                search_term = kwargs['search']
                domain.extend([
                    '|', '|', '|',
                    ('name', 'ilike', search_term),
                    ('description_sale', 'ilike', search_term),
                    ('x_description', 'ilike', search_term),
                    ('default_code', 'ilike', search_term)
                ])
            
            # Dimension filters
            dimension_filters = ['shaft_dia', 'housing_dim_h', 'housing_dim_d']
            for filter_name in dimension_filters:
                if kwargs.get(filter_name):
                    # Add dimension-specific filtering logic here
                    pass
            
            products = request.env['product.template'].search(domain, limit=50)
            
            product_data = []
            for product in products:
                product_data.append({
                    'id': product.id,
                    'name': product.name,
                    'image_url': f'/web/image/product.template/{product.id}/image_1920',
                    'url': f'/product/{product.id}',
                    'price': product.list_price,
                    'description': product.description_sale or '',
                })
            
            return {
                'success': True,
                'products': product_data,
                'count': len(product_data)
            }
            
        except Exception as e:
            _logger.error(f"Error in products filter API: {e}")
            return {
                'success': False,
                'error': str(e)
            }
