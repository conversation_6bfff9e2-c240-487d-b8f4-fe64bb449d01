# -*- coding: utf-8 -*-
from odoo import models, fields, api
from odoo.addons.http_routing.models.ir_http import slug, unslug
import logging

_logger = logging.getLogger(__name__)

class CategoryHeaders(models.Model):
    """Category Headers for product filtering and search"""
    _name = 'jnj_bearing_website.category_headers'
    _description = 'Category Headers'
    _order = 'x_sequence, x_name'

    x_category_id = fields.Many2one('product.public.category', string='Category', store=True)    
    x_category_parent_header_id = fields.Many2one('jnj_bearing_website.category_parent_headers', string='Category Parent Header', store=True)
    x_is_searchable = fields.Boolean(string='Is Searchable in Website?', store=True, default=True)
    x_label = fields.Char(string='Label', store=True, required=True)
    x_name = fields.Char(string='Name', store=True, required=True)
    x_search_type = fields.Selection([
        ('search', 'Search'),
        ('number_filter', 'Number Filter'),
        ('checkbox_filter', 'Checkbox Filter')
    ], string='Search Type', store=True, default='search')
    x_sequence = fields.Integer(string='Sequence', store=True, default=10)
    x_visible_in_table = fields.Boolean(string='Visible in Table (On Website)?', store=True, default=True)

class CategoryParentHeaders(models.Model):
    """Category Parent Headers for organizing category headers"""
    _name = 'jnj_bearing_website.category_parent_headers'
    _description = 'Category Parent Headers'
    _order = 'x_sequence, x_name'
    
    x_category_headers = fields.One2many(
        'jnj_bearing_website.category_headers',
        'x_category_parent_header_id',
        string='Category Headers',
        store=True
    )
    x_category_id = fields.Many2one('product.public.category', string='Category', store=True)
    x_name = fields.Char(string='Name', store=True, required=True)
    x_sequence = fields.Integer(string='Sequence', store=True, default=10)

class DataModelling(models.Model):
    """Data modeling and import functionality"""
    _name = 'jnj_bearing_website.data_modelling'
    _description = 'Data Modelling'

    x_data = fields.Text(string='Data', store=True)
    x_data_file = fields.Binary(string='Data File', store=True)
    x_data_mapping = fields.Text(string='Data Mapping', store=True)
    x_json = fields.Text(string='JSON Data', store=True)
    x_model_id = fields.Many2one('ir.model', string='Model', store=True)
    x_name = fields.Char(string='Name', store=True, required=True)
    x_resolve_relations = fields.Boolean(string='Resolve Relations?', store=True, default=False)
    x_skip_keys = fields.Char(string='Skip Keys (Comma Separated)', store=True)

class Documents(models.Model):
    """Document management system"""
    _name = 'jnj_bearing_website.documents'
    _description = 'Documents'

    x_code = fields.Text(string='Code', store=True)
    x_model = fields.Many2one('ir.model', string='Model', store=True)
    x_name = fields.Char(string='Name', store=True, required=True)
    x_print_name = fields.Char(string='Print Name', store=True)
    x_report = fields.Many2one('ir.actions.report', string='Report', store=True)
    x_template_name = fields.Char(string='Template Name', store=True)
    x_view = fields.Many2one('ir.ui.view', string='View', store=True)
    x_view_name = fields.Char(string='View Name', store=True)

class Enquiry(models.Model):
    """Customer enquiry management"""
    _name = 'jnj_bearing_website.enquiry'
    _description = 'Customer Enquiry'
    _order = 'create_date desc'

    x_address = fields.Text(string='Address', store=True)
    x_city = fields.Char(string='City', store=True)
    x_company_name = fields.Char(string='Company Name', store=True)
    x_country = fields.Char(string='Country', store=True)
    x_email = fields.Char(string='Email', store=True, required=True)
    x_last_name = fields.Char(string='Last Name', store=True)
    x_message = fields.Text(string='Message', store=True)
    x_name = fields.Char(string='First Name', store=True, required=True)
    x_phone = fields.Char(string='Phone Number', store=True)  # Changed from Integer to Char for better phone handling
    x_state = fields.Char(string='State', store=True)
    x_subject = fields.Char(string='Subject', store=True)
    
    # Additional fields for better enquiry management
    status = fields.Selection([
        ('new', 'New'),
        ('in_progress', 'In Progress'),
        ('replied', 'Replied'),
        ('closed', 'Closed')
    ], string='Status', default='new')
    
    @api.model
    def create(self, vals):
        """Override create to handle enquiry creation"""
        enquiry = super(Enquiry, self).create(vals)
        # You can add email notification logic here
        return enquiry

class GenerateBarcodeBatch(models.Model):
    """Barcode generation batch management"""
    _name = 'jnj_bearing_website.generate_barcode_batch'
    _description = 'Generate Barcode Batch'

    x_csv_file = fields.Binary(string='CSV File', store=True)
    x_generated_barcodes = fields.One2many(
        'jnj_bearing_website.generated_barcodes',
        'x_generate_barcode_batch_id',
        string='Generated Barcodes',
        store=True
    )
    x_inspec_by = fields.Char(string='Inspection By', store=True)
    x_name = fields.Char(string='Name', store=True, required=True)
    x_number = fields.Integer(string='Number of Products', store=True, default=1)
    x_product_id = fields.Many2one('product.template', string='Select Product', store=True)

class GeneratedBarcodes(models.Model):
    """Generated barcodes tracking"""
    _name = 'jnj_bearing_website.generated_barcodes'
    _description = 'Generated Barcodes'

    x_generate_barcode_batch_id = fields.Many2one('jnj_bearing_website.generate_barcode_batch', string='Generate Barcode Batch Ref', store=True)
    x_msg = fields.Boolean(string='Message', store=True, default=False)
    x_name = fields.Char(string='Name', store=True, required=True)
    x_no_of_visits = fields.Integer(string='No Of Visits', store=True, default=0)
    x_product_code = fields.Char(string='Product Code', store=True)
    x_product_id = fields.Many2one('product.template', string='Product Ref', readonly=True, store=True)
    x_token = fields.Char(string='Token', store=True)

class ProductEnquiry(models.Model):
    """Product-specific enquiry management"""
    _name = 'jnj_bearing_website.product_enquiry'
    _description = 'Product Enquiry'
    _order = 'create_date desc'

    x_company_name = fields.Char(string='Company Name', store=True)
    x_email = fields.Char(string='Email', store=True, required=True)
    x_name = fields.Char(string='Name', store=True, required=True)
    x_phone = fields.Char(string='Phone Number', store=True)  # Changed from Integer to Char
    x_qty = fields.Integer(string='Quantity', store=True, default=1)
    x_product_id = fields.Many2one('product.template', string='Product', store=True)
    x_message = fields.Text(string='Additional Message', store=True)
    
    # Status tracking
    status = fields.Selection([
        ('new', 'New'),
        ('quoted', 'Quoted'),
        ('converted', 'Converted'),
        ('lost', 'Lost')
    ], string='Status', default='new')

class ProductTemplate(models.Model):
    """Extended Product Template with bearing-specific fields"""
    _inherit = 'product.template'

    # Website URL computation
    x_website_url = fields.Char(string='Website URL', compute='_compute_x_website_url', store=True)

    @api.depends('name')
    def _compute_x_website_url(self):
        for product in self:
            if product.id:
                product.x_website_url = "/shop/%s" % slug(product)

    @api.model
    def create(self, vals):
        product = super(ProductTemplate, self).create(vals)
        if product.id:
            product.x_website_url = "/shop/%s" % slug(product)
        return product

    # Basic product information
    x_description = fields.Text(string='Detailed Description', store=True)

    # Bearing specifications
    x_adapter_sleeve = fields.Char(string='Adapter Sleeve', store=True)
    x_appro_bearing = fields.Char(string='Appropriate Bearing', store=True)
    x_bearing_adapter_sleeve_designation = fields.Char(string='Bearing And Adapter Sleeve Designation', store=True)
    x_felt_strip = fields.Char(string='Felt Strip', store=True)
    x_housing_desi = fields.Char(string='Housing Designation', store=True)
    x_housing_without_seals = fields.Char(string='Housing Without Seals', store=True)
    x_without_seal_housing = fields.Char(string='Housing Designation Without Seals', store=True)
    x_without_seal_housing_des = fields.Char(string='Designation Without Seals', store=True)

    # Quantity and rings
    x_qty = fields.Char(string='Quantity', store=True)
    x_rings_no_desg = fields.Char(string='Locating rings No. & Designations', store=True)
    x_rubber_orings = fields.Integer(string='2 Rubber O-rings', store=True)

    # Bearing seating dimensions
    x_bearing_seating_Ca = fields.Integer(string='Bearing Seating Ca', store=True)
    x_bearing_seating_Da = fields.Integer(string='Bearing Seating Da', store=True)
    x_bearing_seating_db = fields.Integer(string='Bearing Seating db', store=True)

    # Shaft dimensions
    x_shaft_dia_in = fields.Float(string='Shaft Diameter (Inches)', store=True)
    x_shift_dia_D = fields.Integer(string='Shaft Diameter D', store=True)
    x_shift_dia_d1 = fields.Integer(string='Shaft Diameter d1', store=True)
    x_shift_dia_da = fields.Integer(string='Shaft Diameter da', store=True)
    x_shift_dia_db = fields.Integer(string='Shaft Diameter db', store=True)
    x_shift_dia_mm = fields.Integer(string='Shaft Diameter (mm)', store=True)

    # Housing dimensions - Main dimensions
    x_housing_dimensions_A = fields.Integer(string='Housing Dimension A', store=True)
    x_housing_dimensions_A1 = fields.Integer(string='Housing Dimension A1', store=True)
    x_housing_dimensions_A2 = fields.Integer(string='Housing Dimension A2', store=True)
    x_housing_dimensions_A3 = fields.Integer(string='Housing Dimension A3', store=True)
    x_housing_dimensions_B = fields.Char(string='Housing Dimension B', store=True)
    x_housing_dimensions_Ca = fields.Integer(string='Housing Dimension Ca', store=True)
    x_housing_dimensions_D = fields.Char(string='Housing Dimension D', store=True)
    x_housing_dimensions_Da = fields.Integer(string='Housing Dimension Da', store=True)
    x_housing_dimensions_E = fields.Char(string='Housing Dimension E', store=True)
    x_housing_dimensions_G = fields.Float(string='Housing Dimension G', store=True)
    x_housing_dimensions_H = fields.Integer(string='Housing Dimension H', store=True)
    x_housing_dimensions_H1 = fields.Integer(string='Housing Dimension H1', store=True)
    x_housing_dimensions_H2 = fields.Integer(string='Housing Dimension H2', store=True)
    x_housing_dimensions_I = fields.Integer(string='Housing Dimension I', store=True)
    x_housing_dimensions_J = fields.Integer(string='Housing Dimension J', store=True)
    x_housing_dimensions_J1 = fields.Integer(string='Housing Dimension J1', store=True)
    x_housing_dimensions_L = fields.Integer(string='Housing Dimension L', store=True)
    x_housing_dimensions_N = fields.Integer(string='Housing Dimension N', store=True)
    x_housing_dimensions_N1 = fields.Integer(string='Housing Dimension N1', store=True)
    x_housing_dimensions_S = fields.Char(string='Housing Dimension S', store=True)
    x_housing_dimensions_V = fields.Char(string='Housing Dimension V', store=True)

    # Housing dimensions - Lowercase dimensions
    x_housing_dimensions_a = fields.Integer(string='Housing Dimension a', store=True)
    x_housing_dimensions_b = fields.Integer(string='Housing Dimension b', store=True)
    x_housing_dimensions_c = fields.Integer(string='Housing Dimension c', store=True)
    x_housing_dimensions_h = fields.Integer(string='Housing Dimension h', store=True)
    x_housing_dimensions_m = fields.Integer(string='Housing Dimension m', store=True)
    x_housing_dimensions_n = fields.Integer(string='Housing Dimension n', store=True)
    x_housing_dimensions_s = fields.Integer(string='Housing Dimension s', store=True)
    x_housing_dimensions_s_sd3300 = fields.Char(string='Housing Dimension s 3300', store=True)
    x_housing_dimensions_u = fields.Integer(string='Housing Dimension u', store=True)
    x_housing_dimensions_v = fields.Integer(string='Housing Dimension v', store=True)

    # Width across seal dimensions
    x_width_across_seal_A2 = fields.Integer(string='Width Across Seal A2', store=True)
    x_width_across_seal_A3 = fields.Integer(string='Width Across Seal A3', store=True)
    x_width_across_seal_Da = fields.Integer(string='Width Across Seal Da', store=True)

class ProductPublicCategory(models.Model):
    """Extended Product Public Category with additional fields"""
    _inherit = 'product.public.category'

    x_category_description = fields.Html(string='Category Description', store=True)
    x_diagrams = fields.Many2many(
        'ir.attachment',
        'jnj_category_attachment_rel',  # Updated relation table name
        'category_id',
        'attachment_id',
        string='Technical Diagrams',
        store=True
    )
    x_product_image = fields.Binary(string='Category Image', store=True)

class AccountAnalyticLine(models.Model):
    """Extended Analytic Line for department tracking"""
    _inherit = 'account.analytic.line'

    x_plan2_id = fields.Many2one('account.analytic.account', string='Departments', store=True)
    x_plan3_id = fields.Many2one('account.analytic.account', string='Internal', store=True)
