<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Partner Form View Extension for Services -->
    <record id="view_partner_form_services" model="ir.ui.view">
        <field name="name">res.partner.form.services</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page string="Services" name="services_info">
                    <field name="x_service_ids" nolabel="1">
                        <list editable="bottom">
                            <field name="name"/>
                            <field name="x_task_manager"/>
                            <field name="x_agreed_fee"/>
                            <field name="x_planned_hours"/>
                            <field name="x_nextcall"/>
                            <field name="x_execute_every"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <record id="view_x_services_form" model="ir.ui.view">
        <field name="name">x_services.form</field>
        <field name="model">x_services</field>
        <field name="arch" type="xml">
           <form string="Services">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Service Name..."/>
                        </h1>
                    </div>
                    <group>
                        <group name="services_info" string="Service Information">
                            <field name="x_partner_id"/>
                            <field name="x_task_manager"/>
                            <field name="x_project_id"/>
                            <field name="x_planned_hours"/>
                            <field name="x_agreed_fee"/>
                            <field name="x_assigned_to" widget="many2many_tags"/>
                        </group>
                        <group name="schedule_info" string="Schedule Information">
                            <field name="x_execute_every"/>
                            <field name="x_interval_number"/>
                            <field name="x_numbercall"/>
                            <field name="x_previouscall"/>
                            <field name="x_nextcall"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Task Details" name="task_details">
                            <group>
                                <field name="x_task_description" nolabel="1"/>
                            </group>
                        </page>
                        <page string="Email Configuration" name="email_config">
                            <group>
                                <field name="x_email_subject"/>
                                <field name="x_email_template" nolabel="1"/>
                            </group>
                        </page>
                        <page string="Related Tasks" name="related_tasks">
                            <field name="x_tasks_ref" nolabel="1"/>
                        </page>
                    </notebook>

                </sheet>
                </form>


        </field>
    </record>

    <record id="view_x_services_list" model="ir.ui.view">
        <field name="name">x_services.list</field>
        <field name="model">x_services</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="x_partner_id"/>
                <field name="x_task_manager"/>
                <field name="x_planned_hours"/>
                <field name="x_agreed_fee"/>
                <field name="x_project_id"/>
            </list>
        </field>
    </record>

    <record id="view_x_services_search" model="ir.ui.view">
        <field name="name">x_services.search</field>
        <field name="model">x_services</field>
        <field name="arch" type="xml">
            <search>
                <field name="name" string="Service Name"/>
                <field name="x_partner_id" string="Client"/>
                <field name="x_task_manager" string="Task Manager"/>
                <field name="x_project_id" string="Project"/>
                <filter string="My Services" name="my_services" domain="[('x_task_manager', '=', uid)]"/>
                <group expand="0" string="Group By">
                    <filter string="Client" name="group_client" context="{'group_by': 'x_partner_id'}"/>
                    <filter string="Task Manager" name="group_task_manager" context="{'group_by': 'x_task_manager'}"/>
                    <filter string="Project" name="group_project" context="{'group_by': 'x_project_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <menuitem id="menu_x_services" name="Services" parent="imca_groups.menu_ca_management_root" action="action_x_services" sequence="30"/>

</odoo>
