<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="address_layout_compact" inherit_id="web.address_layout">
        <xpath expr="//div[@name='address']/*[1]" position="before">
            <b>Customer:</b>
        </xpath>
    </template>
    <template id="external_layout_striped_compact">
        <div t-attf-class="header o_company_#{company.id}_layout {{report_type == 'pdf' and 'pt-5'}}">
            <div class="d-flex justify-content-between">
                <div>
                    <img t-if="company.logo" class="o_company_logo_big" t-att-src="image_data_uri(company.logo)" alt="Logo"/>
                    <div t-if="company.report_header" t-field="company.report_header" class="o_company_tagline fw-bold">Company tagline</div>
                </div>
                <div name="company_address" class="text-end">
                    <ul class="list-unstyled" name="company_address_list">
                        <li t-if="company.is_company_details_empty"><span t-field="company.partner_id" t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": true}'>
                            <div class="d-flex flex-column align-items-center justify-content-center border-1 rounded p-4 h-100 w-100 bg-light opacity-75 text-muted text-center">
                                <strong>Company address block</strong>
                                <div>Contains the company address.</div>
                            </div>
                        </span></li>
                        <li t-else="">
                            <span t-field="company.company_details">
                                <div class="d-flex flex-column align-items-center justify-content-center border-1 rounded p-4 h-100 w-100 bg-light opacity-75 text-muted text-center">
                                    <strong>Company details block</strong>
                                    <div>Contains the company details.</div>
                                </div>
                            </span>
                        </li>
                        <li t-if="not forced_vat"/>
                        <li t-else="">
                            <t t-esc="company.country_id.vat_label or 'Tax ID'">Tax ID</t>:
                            <span t-esc="forced_vat">US12345671</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div t-attf-class="article o_report_layout_bubble o_table_striped o_table_striped {{'o_report_layout_background' if company.layout_background != 'Blank' else ''}}"
             t-attf-style="{{ 'background-image: url(%s);' % layout_background_url if layout_background_url else '' }}"
             t-att-data-oe-model="o and o._name"
             t-att-data-oe-id="o and o.id"
             t-att-data-oe-lang="o and o.env.context.get('lang')">
            <div t-att-class="not information_block and 'd-flex justify-content-between align-items-end'">
                <t t-call="web.address_layout">
                    <t t-set="custom_layout_address" t-value="true"/>
                </t>
                <h2 t-attf-class="{{not information_block and 'mb-4'}} text-end" t-out="layout_document_title"/>
            </div>
            <t t-out="0"/>
        </div>

        <div t-attf-class="footer o_company_#{company.id}_layout {{report_type != 'pdf' and 'mt-auto'}}">
            <div class="o_footer_content border-top pt-2 text-center">
                <div t-field="company.report_footer"/>
                <div t-if="report_type == 'pdf' and display_name_in_footer" class="text-muted" t-out="o.name">(document name)</div>
                <div t-if="report_type == 'pdf'" class="text-muted">Page <span class="page"/> / <span class="topage"/></div>
            </div>
        </div>
    </template>
    <template id="report_invoice_document_with_vat_summary" inherit_id="account.report_invoice_document">
        <xpath expr="//div[hasclass('page')]" position="after">
            <table>
                <tr class="no-border-table">
                    <td style="padding-right: 10px;"><strong>Vat Summary</strong></td>
                </tr>
                <tr>
                    <td style="padding-right: 10px;">Tax Code 03 = VAT</td>
                </tr>
            </table>
            <br/>
        </xpath>
    </template>
    <template id="report_invoice_document_with_tax_code" inherit_id="account.report_invoice_document">
        <xpath expr="//th[@name='th_taxes']/span" position="replace">
            <span>Tax Code</span>
        </xpath>
    </template>
</odoo>