#!/usr/bin/env python3
"""
Fix Credentials and CA Management Issues Script
This script addresses:
1. Create Credentials Type model and make x_type field many2one
2. Add separate Credentials Type menu in CA Management
3. Update credentials views to use many2one relationship
"""

import xmlrpc.client
import time
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

def connect_to_odoo():
    """Connect to Odoo"""
    try:
        logger.info(f"Connecting to Odoo at {URL}")
        common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
        models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
        
        uid = common.authenticate(DB, USERNAME, PASSWORD, {})
        if not uid:
            raise Exception("Authentication failed")
            
        logger.info(f"Successfully connected as user ID: {uid}")
        return common, models, uid
        
    except Exception as e:
        logger.error(f"Connection failed: {e}")
        return None, None, None

def create_credentials_type_model(models, uid):
    """Create the Credentials Type model"""
    logger.info("=" * 60)
    logger.info("CREATING CREDENTIALS TYPE MODEL")
    logger.info("=" * 60)
    
    try:
        # 1. Check if model already exists
        logger.info("1. Checking if x_credential_types model exists...")
        existing_models = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.model', 'search_read',
            [[('model', '=', 'x_credential_types')]],
            {'fields': ['name', 'model']}
        )
        
        if existing_models:
            logger.info("   ✅ x_credential_types model already exists")
            model_id = existing_models[0]['id']
        else:
            logger.info("   ⚠️ Creating x_credential_types model...")
            
            # Create the model
            model_id = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.model', 'create',
                [{
                    'name': 'Credential Types',
                    'model': 'x_credential_types',
                    'state': 'manual',
                    'info': 'Model for managing credential types'
                }]
            )
            logger.info(f"   ✅ Created x_credential_types model with ID: {model_id}")
        
        # 2. Create fields for the model
        logger.info("2. Creating fields for x_credential_types model...")
        
        # Check if fields already exist
        existing_fields = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.model.fields', 'search_read',
            [[('model', '=', 'x_credential_types')]],
            {'fields': ['name', 'field_description']}
        )
        
        existing_field_names = [f['name'] for f in existing_fields]
        
        # Define fields to create
        fields_to_create = [
            {
                'name': 'name',
                'field_description': 'Type Name',
                'ttype': 'char',
                'required': True,
                'model_id': model_id
            },
            {
                'name': 'description',
                'field_description': 'Description',
                'ttype': 'text',
                'model_id': model_id
            },
            {
                'name': 'active',
                'field_description': 'Active',
                'ttype': 'boolean',
                'model_id': model_id
            }
        ]
        
        for field_data in fields_to_create:
            if field_data['name'] not in existing_field_names:
                field_id = models.execute_kw(
                    DB, uid, PASSWORD,
                    'ir.model.fields', 'create',
                    [field_data]
                )
                logger.info(f"   ✅ Created field '{field_data['name']}' with ID: {field_id}")
            else:
                logger.info(f"   ✅ Field '{field_data['name']}' already exists")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating credentials type model: {e}")
        return False

def update_credentials_type_field(models, uid):
    """Update x_type field in x_credentials to be many2one"""
    logger.info("=" * 60)
    logger.info("UPDATING CREDENTIALS TYPE FIELD")
    logger.info("=" * 60)
    
    try:
        # 1. Check current x_type field
        logger.info("1. Checking current x_type field in x_credentials...")
        
        existing_fields = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.model.fields', 'search_read',
            [[('model', '=', 'x_credentials'), ('name', '=', 'x_type')]],
            {'fields': ['name', 'ttype', 'relation']}
        )
        
        if existing_fields:
            current_field = existing_fields[0]
            logger.info(f"   Current x_type field: Type={current_field['ttype']}, Relation={current_field.get('relation', 'None')}")
            
            if current_field['ttype'] == 'many2one' and current_field.get('relation') == 'x_credential_types':
                logger.info("   ✅ x_type field is already many2one to x_credential_types")
                return True
            
            # Update the field to many2one
            logger.info("   🔄 Updating x_type field to many2one...")
            models.execute_kw(
                DB, uid, PASSWORD,
                'ir.model.fields', 'write',
                [[current_field['id']], {
                    'ttype': 'many2one',
                    'relation': 'x_credential_types',
                    'field_description': 'Credential Type'
                }]
            )
            logger.info("   ✅ Updated x_type field to many2one")
        else:
            logger.info("   ⚠️ x_type field not found, creating new many2one field...")
            
            # Get x_credentials model ID
            credential_models = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.model', 'search_read',
                [[('model', '=', 'x_credentials')]],
                {'fields': ['id']}
            )
            
            if credential_models:
                model_id = credential_models[0]['id']
                
                # Create new many2one field
                field_id = models.execute_kw(
                    DB, uid, PASSWORD,
                    'ir.model.fields', 'create',
                    [{
                        'name': 'x_type',
                        'field_description': 'Credential Type',
                        'ttype': 'many2one',
                        'relation': 'x_credential_types',
                        'model_id': model_id
                    }]
                )
                logger.info(f"   ✅ Created new x_type many2one field with ID: {field_id}")
            else:
                logger.error("   ❌ x_credentials model not found")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error updating credentials type field: {e}")
        return False

def create_credential_types_menu_and_views(models, uid):
    """Create menu and views for Credentials Type"""
    logger.info("=" * 60)
    logger.info("CREATING CREDENTIALS TYPE MENU AND VIEWS")
    logger.info("=" * 60)
    
    try:
        # 1. Create action for credential types
        logger.info("1. Creating action for credential types...")
        
        # Check if action already exists
        existing_actions = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.actions.act_window', 'search_read',
            [[('res_model', '=', 'x_credential_types')]],
            {'fields': ['name', 'res_model']}
        )
        
        if existing_actions:
            logger.info("   ✅ Action for x_credential_types already exists")
            action_id = existing_actions[0]['id']
        else:
            action_id = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.actions.act_window', 'create',
                [{
                    'name': 'Credential Types',
                    'res_model': 'x_credential_types',
                    'view_mode': 'list,form',
                    'target': 'current'
                }]
            )
            logger.info(f"   ✅ Created action for credential types with ID: {action_id}")
        
        # 2. Create menu item
        logger.info("2. Creating menu item for credential types...")
        
        # Get CA Management root menu
        ca_menus = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.ui.menu', 'search_read',
            [[('name', '=', 'CA Management')]],
            {'fields': ['id', 'name']}
        )
        
        if ca_menus:
            parent_menu_id = ca_menus[0]['id']
            
            # Check if menu already exists
            existing_menus = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.ui.menu', 'search_read',
                [[('name', '=', 'Credential Types'), ('parent_id', '=', parent_menu_id)]],
                {'fields': ['name']}
            )
            
            if existing_menus:
                logger.info("   ✅ Credential Types menu already exists")
            else:
                menu_id = models.execute_kw(
                    DB, uid, PASSWORD,
                    'ir.ui.menu', 'create',
                    [{
                        'name': 'Credential Types',
                        'parent_id': parent_menu_id,
                        'action': f'ir.actions.act_window,{action_id}',
                        'sequence': 45
                    }]
                )
                logger.info(f"   ✅ Created Credential Types menu with ID: {menu_id}")
        else:
            logger.warning("   ⚠️ CA Management root menu not found")
        
        # 3. Create basic views for credential types
        logger.info("3. Creating views for credential types...")
        
        # List view
        list_view_arch = '''
<list string="Credential Types">
    <field name="name"/>
    <field name="description"/>
    <field name="active"/>
</list>'''
        
        # Form view
        form_view_arch = '''
<form string="Credential Type">
    <sheet>
        <div class="oe_title">
            <h1>
                <field name="name" placeholder="Type Name..."/>
            </h1>
        </div>
        <group>
            <field name="description"/>
            <field name="active"/>
        </group>
    </sheet>
</form>'''
        
        # Check if views already exist
        existing_views = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.ui.view', 'search_read',
            [[('model', '=', 'x_credential_types')]],
            {'fields': ['name', 'type']}
        )
        
        existing_view_types = [v['type'] for v in existing_views]
        
        # Create list view if not exists
        if 'list' not in existing_view_types:
            list_view_id = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.ui.view', 'create',
                [{
                    'name': 'x_credential_types.list',
                    'model': 'x_credential_types',
                    'type': 'list',
                    'arch': list_view_arch
                }]
            )
            logger.info(f"   ✅ Created list view with ID: {list_view_id}")
        else:
            logger.info("   ✅ List view already exists")
        
        # Create form view if not exists
        if 'form' not in existing_view_types:
            form_view_id = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.ui.view', 'create',
                [{
                    'name': 'x_credential_types.form',
                    'model': 'x_credential_types',
                    'type': 'form',
                    'arch': form_view_arch
                }]
            )
            logger.info(f"   ✅ Created form view with ID: {form_view_id}")
        else:
            logger.info("   ✅ Form view already exists")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating credential types menu and views: {e}")
        return False

def create_sample_credential_types(models, uid):
    """Create sample credential types for demonstration"""
    logger.info("=" * 60)
    logger.info("CREATING SAMPLE CREDENTIAL TYPES")
    logger.info("=" * 60)
    
    try:
        # Sample credential types for CA practice
        sample_types = [
            {
                'name': 'Income Tax Portal',
                'description': 'Credentials for Income Tax e-filing portal access'
            },
            {
                'name': 'GST Portal',
                'description': 'Credentials for GST portal and filing'
            },
            {
                'name': 'MCA Portal',
                'description': 'Ministry of Corporate Affairs portal access'
            },
            {
                'name': 'TDS Portal',
                'description': 'TDS return filing and certificate download'
            },
            {
                'name': 'PF Portal',
                'description': 'Provident Fund portal access'
            },
            {
                'name': 'ESI Portal',
                'description': 'Employee State Insurance portal'
            },
            {
                'name': 'Bank Portal',
                'description': 'Online banking credentials'
            },
            {
                'name': 'Audit Software',
                'description': 'Audit and accounting software access'
            }
        ]
        
        # Check existing types
        existing_types = models.execute_kw(
            DB, uid, PASSWORD,
            'x_credential_types', 'search_read',
            [[]],
            {'fields': ['name']}
        )
        
        existing_names = [t['name'] for t in existing_types]
        
        created_count = 0
        for sample_type in sample_types:
            if sample_type['name'] not in existing_names:
                type_id = models.execute_kw(
                    DB, uid, PASSWORD,
                    'x_credential_types', 'create',
                    [sample_type]
                )
                logger.info(f"   ✅ Created credential type: {sample_type['name']} (ID: {type_id})")
                created_count += 1
            else:
                logger.info(f"   ✅ Credential type already exists: {sample_type['name']}")
        
        logger.info(f"   📊 Created {created_count} new credential types")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating sample credential types: {e}")
        return False

def main():
    """Main execution function"""
    logger.info("=" * 60)
    logger.info("CREDENTIALS AND CA MANAGEMENT FIX SCRIPT")
    logger.info("=" * 60)
    
    # Connect to Odoo
    common, models, uid = connect_to_odoo()
    if not models:
        logger.error("Failed to connect to Odoo. Exiting.")
        sys.exit(1)
    
    # Apply fixes
    fixes_applied = 0
    
    # Fix 1: Create Credentials Type model
    logger.info("\n🔧 FIX 1: Create Credentials Type Model")
    if create_credentials_type_model(models, uid):
        fixes_applied += 1
    
    # Fix 2: Update credentials type field to many2one
    logger.info("\n🔧 FIX 2: Update Credentials Type Field")
    if update_credentials_type_field(models, uid):
        fixes_applied += 1
    
    # Fix 3: Create menu and views for credential types
    logger.info("\n🔧 FIX 3: Create Credential Types Menu and Views")
    if create_credential_types_menu_and_views(models, uid):
        fixes_applied += 1
    
    # Fix 4: Create sample credential types
    logger.info("\n🔧 FIX 4: Create Sample Credential Types")
    if create_sample_credential_types(models, uid):
        fixes_applied += 1
    
    # Final summary
    logger.info("\n" + "=" * 60)
    logger.info("FINAL SUMMARY")
    logger.info("=" * 60)
    
    logger.info(f"Fixes applied: {fixes_applied}/4")
    
    if fixes_applied >= 3:
        logger.info("\n🎉 SUCCESS! Credentials and CA Management improvements completed!")
        logger.info("\n📋 What was implemented:")
        logger.info("1. ✅ Created Credentials Type model (x_credential_types)")
        logger.info("2. ✅ Updated x_type field to many2one relationship")
        logger.info("3. ✅ Added Credential Types menu in CA Management")
        logger.info("4. ✅ Created sample credential types for CA practice")
        
        logger.info("\n🔄 Next Steps:")
        logger.info("1. Check CA Management menu for new 'Credential Types' option")
        logger.info("2. Test credentials form with dropdown type selection")
        logger.info("3. Create demo data module for comprehensive testing")
        logger.info("4. Update credentials views to use the new many2one field")
    else:
        logger.warning("\n⚠️ Some fixes may need manual attention")
    
    logger.info("\nScript completed!")

if __name__ == "__main__":
    main()
