#!/bin/bash

echo "🚀 Setting up Playwright tests for profectusaccounts.com contact forms"
echo "=================================================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 is not installed"
    exit 1
fi

echo "✅ Python3 found"

# Install Python requirements
echo "📦 Installing Python requirements..."
pip3 install -r requirements.txt

# Install Playwright browsers
echo "🌐 Installing Playwright browsers..."
playwright install chromium

# Make test script executable
chmod +x test_contact_forms.py

echo ""
echo "✅ Setup complete!"
echo ""
echo "🎯 To run the tests:"
echo "   python3 test_contact_forms.py"
echo ""
echo "📋 What the test will do:"
echo "   1. Generate unique test data"
echo "   2. Navigate to /contactus page"
echo "   3. Fill and submit the contact form"
echo "   4. Check if data appears in Odoo database"
echo "   5. Verify form submission success"
echo "   6. Take screenshots of each step"
echo "   7. Clean up test data"
echo ""
echo "🔧 Configuration:"
echo "   • Website URL: http://localhost:8069"
echo "   • Database: profectusaccounts.com"
echo "   • User: demo/demo"
echo ""
echo "⚠️ Note: Make sure Odoo is running and accessible"
