2025-07-15 09:14:35,100 - INFO - ============================================================
2025-07-15 09:14:35,100 - INFO - I<PERSON> Modules Upgrade Script Started
2025-07-15 09:14:35,101 - INFO - ============================================================
2025-07-15 09:14:35,101 - INFO - Connecting to Odoo at https://sdpm.arihantai.com
2025-07-15 09:14:35,640 - INFO - Successfully connected as user ID: 6
2025-07-15 09:14:35,641 - INFO - 
========================================
2025-07-15 09:14:35,641 - INFO - CHECKING INITIAL LOGS
2025-07-15 09:14:35,641 - INFO - ========================================
2025-07-15 09:14:35,641 - INFO - Checking recent logs for errors...
2025-07-15 09:14:35,706 - ERROR - Error executing ir.logging.search: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1717, in search\n    return self.search_fetch(domain, [], offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1749, in search_fetch\n    return self._fetch_query(query, fields_to_fetch)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4183, in _fetch_query\n    fetched = self.browse(query)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6157, in browse\n    if not ids:\n  File "/usr/lib/python3/dist-packages/odoo/tools/query.py", line 261, in __bool__\n    return bool(self.get_result_ids())\n  File "/usr/lib/python3/dist-packages/odoo/tools/query.py", line 225, in get_result_ids\n    self._ids = tuple(id_ for id_, in self._env.execute_query(self.select()))\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 962, in execute_query\n    self.cr.execute(query)\n  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 354, in execute\n    res = self._obj.execute(query, params)\npsycopg2.ProgrammingError: can\'t adapt type \'dict\'\n'>
2025-07-15 09:14:35,707 - ERROR - Error checking logs: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1717, in search\n    return self.search_fetch(domain, [], offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1749, in search_fetch\n    return self._fetch_query(query, fields_to_fetch)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4183, in _fetch_query\n    fetched = self.browse(query)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6157, in browse\n    if not ids:\n  File "/usr/lib/python3/dist-packages/odoo/tools/query.py", line 261, in __bool__\n    return bool(self.get_result_ids())\n  File "/usr/lib/python3/dist-packages/odoo/tools/query.py", line 225, in get_result_ids\n    self._ids = tuple(id_ for id_, in self._env.execute_query(self.select()))\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 962, in execute_query\n    self.cr.execute(query)\n  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 354, in execute\n    res = self._obj.execute(query, params)\npsycopg2.ProgrammingError: can\'t adapt type \'dict\'\n'>
2025-07-15 09:14:35,707 - INFO - 
========================================
2025-07-15 09:14:35,707 - INFO - UPGRADING IMCA MODULES
2025-07-15 09:14:35,707 - INFO - ========================================
2025-07-15 09:14:35,707 - INFO - Starting upgrade for module: imca_groups
2025-07-15 09:14:35,741 - ERROR - Error executing ir.module.module.read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3782, in read\n    self._origin.fetch(fields)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4057, in fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names, ignore_when_in_cache=True)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 09:14:35,741 - ERROR - Error getting module info for imca_groups: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3782, in read\n    self._origin.fetch(fields)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4057, in fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names, ignore_when_in_cache=True)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 09:14:35,741 - ERROR - Module imca_groups not found
2025-07-15 09:14:37,743 - INFO - Starting upgrade for module: imca_services
2025-07-15 09:14:37,774 - ERROR - Error executing ir.module.module.read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3782, in read\n    self._origin.fetch(fields)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4057, in fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names, ignore_when_in_cache=True)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 09:14:37,774 - ERROR - Error getting module info for imca_services: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3782, in read\n    self._origin.fetch(fields)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4057, in fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names, ignore_when_in_cache=True)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 09:14:37,774 - ERROR - Module imca_services not found
2025-07-15 09:14:39,776 - INFO - Starting upgrade for module: imca_client_documents
2025-07-15 09:14:39,834 - ERROR - Error executing ir.module.module.read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3782, in read\n    self._origin.fetch(fields)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4057, in fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names, ignore_when_in_cache=True)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 09:14:39,834 - ERROR - Error getting module info for imca_client_documents: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3782, in read\n    self._origin.fetch(fields)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4057, in fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names, ignore_when_in_cache=True)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 09:14:39,834 - ERROR - Module imca_client_documents not found
2025-07-15 09:14:41,835 - INFO - Starting upgrade for module: imca_crednetials_manager
2025-07-15 09:14:41,926 - ERROR - Error executing ir.module.module.read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3782, in read\n    self._origin.fetch(fields)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4057, in fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names, ignore_when_in_cache=True)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 09:14:41,927 - ERROR - Error getting module info for imca_crednetials_manager: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3782, in read\n    self._origin.fetch(fields)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4057, in fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names, ignore_when_in_cache=True)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 09:14:41,927 - ERROR - Module imca_crednetials_manager not found
2025-07-15 09:14:43,930 - INFO - Starting upgrade for module: imca_dsc_management
2025-07-15 09:14:43,957 - ERROR - Error executing ir.module.module.read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3782, in read\n    self._origin.fetch(fields)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4057, in fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names, ignore_when_in_cache=True)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 09:14:43,957 - ERROR - Error getting module info for imca_dsc_management: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3782, in read\n    self._origin.fetch(fields)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4057, in fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names, ignore_when_in_cache=True)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 09:14:43,957 - ERROR - Module imca_dsc_management not found
2025-07-15 09:14:45,960 - INFO - Waiting 10 seconds for upgrades to complete...
2025-07-15 09:14:55,970 - INFO - 
========================================
2025-07-15 09:14:55,970 - INFO - CHECKING LOGS AFTER UPGRADE
2025-07-15 09:14:55,970 - INFO - ========================================
2025-07-15 09:14:55,971 - INFO - Checking recent logs for errors...
2025-07-15 09:14:55,984 - ERROR - Error executing ir.logging.search: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1717, in search\n    return self.search_fetch(domain, [], offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1749, in search_fetch\n    return self._fetch_query(query, fields_to_fetch)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4183, in _fetch_query\n    fetched = self.browse(query)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6157, in browse\n    if not ids:\n  File "/usr/lib/python3/dist-packages/odoo/tools/query.py", line 261, in __bool__\n    return bool(self.get_result_ids())\n  File "/usr/lib/python3/dist-packages/odoo/tools/query.py", line 225, in get_result_ids\n    self._ids = tuple(id_ for id_, in self._env.execute_query(self.select()))\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 962, in execute_query\n    self.cr.execute(query)\n  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 354, in execute\n    res = self._obj.execute(query, params)\npsycopg2.ProgrammingError: can\'t adapt type \'dict\'\n'>
2025-07-15 09:14:55,985 - ERROR - Error checking logs: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1717, in search\n    return self.search_fetch(domain, [], offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1749, in search_fetch\n    return self._fetch_query(query, fields_to_fetch)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4183, in _fetch_query\n    fetched = self.browse(query)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6157, in browse\n    if not ids:\n  File "/usr/lib/python3/dist-packages/odoo/tools/query.py", line 261, in __bool__\n    return bool(self.get_result_ids())\n  File "/usr/lib/python3/dist-packages/odoo/tools/query.py", line 225, in get_result_ids\n    self._ids = tuple(id_ for id_, in self._env.execute_query(self.select()))\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 962, in execute_query\n    self.cr.execute(query)\n  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 354, in execute\n    res = self._obj.execute(query, params)\npsycopg2.ProgrammingError: can\'t adapt type \'dict\'\n'>
2025-07-15 09:14:55,985 - INFO - 
========================================
2025-07-15 09:14:55,985 - INFO - TESTING PARTNER FORM EXTENSIONS
2025-07-15 09:14:55,985 - INFO - ========================================
2025-07-15 09:14:55,985 - INFO - Testing partner form view extensions...
2025-07-15 09:14:56,019 - WARNING - No partner form extensions found
2025-07-15 09:14:56,019 - INFO - 
============================================================
2025-07-15 09:14:56,019 - INFO - UPGRADE SUMMARY
2025-07-15 09:14:56,019 - INFO - ============================================================
2025-07-15 09:14:56,019 - INFO - Module Upgrade Results:
2025-07-15 09:14:56,019 - INFO -   imca_groups: ❌ FAILED
2025-07-15 09:14:56,019 - INFO -   imca_services: ❌ FAILED
2025-07-15 09:14:56,019 - INFO -   imca_client_documents: ❌ FAILED
2025-07-15 09:14:56,019 - INFO -   imca_crednetials_manager: ❌ FAILED
2025-07-15 09:14:56,019 - INFO -   imca_dsc_management: ❌ FAILED
2025-07-15 09:14:56,019 - INFO - 
Partner Form Extensions: ❌ NOT FOUND
2025-07-15 09:14:56,019 - INFO - Errors Encountered: 7
2025-07-15 09:14:56,019 - INFO - Errors Fixed: 0
2025-07-15 09:14:56,019 - INFO - Error Logs Found: 0
2025-07-15 09:14:56,019 - WARNING - 
⚠️  Some issues detected. Please review the logs above.
2025-07-15 09:14:56,020 - INFO - 
Log file saved to: imca_upgrade_20250715_091435.log
2025-07-15 09:14:56,020 - INFO - 
Upgrade script completed.
