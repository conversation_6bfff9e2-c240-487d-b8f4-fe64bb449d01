<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Gamification Category -->
        <record id="module_category_gamification" model="ir.module.category">
            <field name="name">eCommerce Gamification</field>
            <field name="description">Helps you manage gamification features for eCommerce users</field>
            <field name="sequence">20</field>
        </record>
        
        <!-- Gamification User Group -->
        <record id="group_gamification_user" model="res.groups">
            <field name="name">User</field>
            <field name="category_id" ref="module_category_gamification"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>
        
        <!-- Gamification Manager Group -->
        <record id="group_gamification_manager" model="res.groups">
            <field name="name">Manager</field>
            <field name="category_id" ref="module_category_gamification"/>
            <field name="implied_ids" eval="[(4, ref('group_gamification_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>
        
        <!-- Multi-company Rules -->
        <record id="gamification_reward_comp_rule" model="ir.rule">
            <field name="name">Gamification Reward: multi-company</field>
            <field name="model_id" ref="model_gamification_reward"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        
        <record id="gamification_reward_item_comp_rule" model="ir.rule">
            <field name="name">Gamification Reward Item: multi-company</field>
            <field name="model_id" ref="model_gamification_reward_item"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        
        <record id="gamification_rule_comp_rule" model="ir.rule">
            <field name="name">Gamification Rule: multi-company</field>
            <field name="model_id" ref="model_gamification_rule"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        
        <!-- User Access Rules -->
        <record id="gamification_user_reward_personal_rule" model="ir.rule">
            <field name="name">User Rewards: own rewards only</field>
            <field name="model_id" ref="model_gamification_user_reward"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal')), (4, ref('base.group_user'))]"/>
        </record>
        
        <record id="gamification_wallet_personal_rule" model="ir.rule">
            <field name="name">User Wallet: own wallet only</field>
            <field name="model_id" ref="model_gamification_wallet"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal')), (4, ref('base.group_user'))]"/>
        </record>
        
        <record id="gamification_wallet_transaction_personal_rule" model="ir.rule">
            <field name="name">Wallet Transactions: own transactions only</field>
            <field name="model_id" ref="model_gamification_wallet_transaction"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal')), (4, ref('base.group_user'))]"/>
        </record>
        
        <!-- Manager Access Rules -->
        <record id="gamification_user_reward_manager_rule" model="ir.rule">
            <field name="name">User Rewards: all rewards</field>
            <field name="model_id" ref="model_gamification_user_reward"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_gamification_manager'))]"/>
        </record>
        
        <record id="gamification_wallet_manager_rule" model="ir.rule">
            <field name="name">User Wallet: all wallets</field>
            <field name="model_id" ref="model_gamification_wallet"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_gamification_manager'))]"/>
        </record>
        
        <record id="gamification_wallet_transaction_manager_rule" model="ir.rule">
            <field name="name">Wallet Transactions: all transactions</field>
            <field name="model_id" ref="model_gamification_wallet_transaction"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_gamification_manager'))]"/>
        </record>
    </data>
</odoo>
