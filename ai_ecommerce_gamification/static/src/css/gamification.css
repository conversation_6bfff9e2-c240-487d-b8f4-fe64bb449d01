/**
 * Gamification Styles
 */

/* Scratch Card */
#scratch-card-container {
    width: 100%;
    height: 300px;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#scratch-card-overlay {
    background-color: #AAAAAA;
    z-index: 10;
    cursor: pointer;
}

.scratch-card-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 20;
}

#scratch-card-result {
    background-color: #FFFFFF;
    background-image: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    height: 100%;
}

/* Spin Wheel */
#spin-wheel-container {
    position: relative;
}

#wheel-canvas-container {
    position: relative;
    width: 400px;
    height: 400px;
    margin: 0 auto;
}

#wheel-canvas {
    display: block;
    margin: 0 auto;
}

#wheel-pointer {
    position: absolute;
    top: 0;
    left: 50%;
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 30px solid #FF0000;
    transform: translateX(-50%);
    z-index: 10;
}

/* Reward Cards */
.product-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Reward Notification */
.gamification-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 300px;
    background-color: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    overflow: hidden;
    transform: translateX(120%);
    transition: transform 0.5s ease;
}

.gamification-notification.show {
    transform: translateX(0);
}

.notification-header {
    background-color: #4CAF50;
    color: #FFFFFF;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-header h5 {
    margin: 0;
    font-size: 16px;
}

.close-notification {
    background: none;
    border: none;
    color: #FFFFFF;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.notification-body {
    padding: 15px;
}

.notification-footer {
    padding: 10px 15px;
    background-color: #f5f5f5;
    text-align: right;
}

/* Wallet Balance Card */
.wallet-balance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #FFFFFF;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.wallet-balance-card .card-body {
    padding: 30px;
}

.wallet-balance-card .balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.wallet-balance-card .balance-label {
    font-size: 1rem;
    opacity: 0.8;
}

/* Transaction History */
.transaction-item {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.transaction-item:hover {
    background-color: #f9f9f9;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-date {
    color: #777;
    font-size: 0.85rem;
}

.transaction-amount.credit {
    color: #4CAF50;
}

.transaction-amount.debit {
    color: #F44336;
}

/* Reward Modal */
.reward-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #f0f8ff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
    #scratch-card-container {
        height: 250px;
    }
    
    #wheel-canvas-container {
        width: 300px;
        height: 300px;
    }
    
    #wheel-canvas {
        width: 300px;
        height: 300px;
    }
    
    .gamification-notification {
        width: calc(100% - 40px);
        bottom: 10px;
        right: 10px;
        left: 10px;
    }
}
