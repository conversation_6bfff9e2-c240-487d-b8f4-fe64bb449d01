/**
 * Rewards Implementation
 * 
 * This script provides common functionality for rewards in the gamification module.
 */
odoo.define('ai_ecommerce_gamification.rewards', function (require) {
    'use strict';
    
    var publicWidget = require('web.public.widget');
    var ajax = require('web.ajax');
    var core = require('web.core');
    var _t = core._t;
    
    /**
     * Rewards Widget
     * 
     * Handles common reward functionality across the website
     */
    publicWidget.registry.GamificationRewards = publicWidget.Widget.extend({
        selector: '.oe_website_sale',
        events: {
            'click .claim-reward-btn': '_onClaimRewardClick',
        },
        
        /**
         * @override
         */
        start: function () {
            var self = this;
            return this._super.apply(this, arguments).then(function () {
                self._checkForRewards();
            });
        },
        
        /**
         * Check for available rewards
         * @private
         */
        _checkForRewards: function () {
            var self = this;
            
            // Only check for logged in users
            if (!odoo.session_info.user_id) return;
            
            ajax.jsonRpc('/gamification/api/get-rewards', 'call', {})
                .then(function (result) {
                    if (result.rewards && result.rewards.length > 0) {
                        self._showRewardNotification(result.rewards);
                    }
                })
                .catch(function (error) {
                    console.error('Failed to check for rewards:', error);
                });
        },
        
        /**
         * Show notification for available rewards
         * @private
         * @param {Array} rewards - Available rewards
         */
        _showRewardNotification: function (rewards) {
            if (rewards.length === 0) return;
            
            // Create notification
            var $notification = $('<div class="gamification-notification">' +
                '<div class="notification-header">' +
                    '<h5>' + _t('Rewards Available!') + '</h5>' +
                    '<button type="button" class="close-notification">&times;</button>' +
                '</div>' +
                '<div class="notification-body">' +
                    '<p>' + _t('You have rewards waiting for you!') + '</p>' +
                '</div>' +
                '<div class="notification-footer">' +
                    '<a href="/gamification/rewards" class="btn btn-sm btn-primary">' + _t('View Rewards') + '</a>' +
                '</div>' +
            '</div>');
            
            // Add to page
            $('body').append($notification);
            
            // Show with animation
            setTimeout(function () {
                $notification.addClass('show');
            }, 500);
            
            // Handle close button
            $notification.find('.close-notification').on('click', function () {
                $notification.removeClass('show');
                setTimeout(function () {
                    $notification.remove();
                }, 500);
            });
            
            // Auto-hide after 10 seconds
            setTimeout(function () {
                if ($notification.hasClass('show')) {
                    $notification.removeClass('show');
                    setTimeout(function () {
                        $notification.remove();
                    }, 500);
                }
            }, 10000);
        },
        
        /**
         * Handle claim reward button click
         * @private
         * @param {Event} ev - Click event
         */
        _onClaimRewardClick: function (ev) {
            ev.preventDefault();
            var self = this;
            var $button = $(ev.currentTarget);
            var rewardId = $button.data('reward-id');
            var rewardType = $button.text().trim();
            
            // Handle different reward types
            if (rewardType === 'Scratch Card') {
                window.location.href = '/gamification/scratch-card/' + rewardId;
                return;
            } else if (rewardType === 'Spin Wheel') {
                window.location.href = '/gamification/spin-wheel/' + rewardId;
                return;
            }
            
            // For direct rewards
            $button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>');
            
            ajax.jsonRpc('/gamification/api/claim-reward', 'call', {
                reward_id: rewardId
            })
            .then(function (result) {
                $button.prop('disabled', false).text(rewardType);
                
                if (result.success) {
                    self._showRewardModal(result);
                } else {
                    alert(result.message || _t('Failed to claim reward'));
                }
            })
            .catch(function (error) {
                $button.prop('disabled', false).text(rewardType);
                alert(_t('Failed to claim reward. Please try again later.'));
                console.error('Error claiming reward:', error);
            });
        },
        
        /**
         * Show reward modal
         * @private
         * @param {Object} result - Reward result data
         */
        _showRewardModal: function (result) {
            var $modal = $('#rewardClaimedModal');
            
            if ($modal.length === 0) {
                // Create modal if it doesn't exist
                $modal = $('<div class="modal fade" id="rewardClaimedModal" tabindex="-1" aria-hidden="true">' +
                    '<div class="modal-dialog modal-dialog-centered">' +
                        '<div class="modal-content">' +
                            '<div class="modal-header bg-success text-white">' +
                                '<h5 class="modal-title">Reward Claimed!</h5>' +
                                '<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>' +
                            '</div>' +
                            '<div class="modal-body text-center py-4">' +
                                '<div class="reward-icon mb-3">' +
                                    '<i class="fa fa-gift fa-4x text-success"></i>' +
                                '</div>' +
                                '<h4 id="reward-name" class="mb-3">Your Reward</h4>' +
                                '<p id="reward-message" class="mb-4">You\'ve successfully claimed your reward!</p>' +
                                '<div id="reward-details" class="mb-3"></div>' +
                            '</div>' +
                            '<div class="modal-footer">' +
                                '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>' +
                            '</div>' +
                        '</div>' +
                    '</div>' +
                '</div>');
                
                $('body').append($modal);
            }
            
            // Update modal content
            $modal.find('#reward-name').text(result.reward ? result.reward.name : 'Your Reward');
            $modal.find('#reward-message').text(result.message);
            
            var detailsHtml = '';
            if (result.reward) {
                if (result.reward.item_type === 'coupon' || result.reward.item_type === 'external_coupon') {
                    detailsHtml = '<div class="alert alert-success">' +
                        '<strong>Your Coupon Code:</strong> ' + result.reward.code +
                    '</div>';
                } else if (result.reward.item_type === 'wallet_credit') {
                    detailsHtml = '<div class="alert alert-success">' +
                        '<strong>Amount Added to Wallet:</strong> ' + result.reward.value +
                    '</div>';
                }
            }
            
            $modal.find('#reward-details').html(detailsHtml);
            
            // Show modal
            var modal = new bootstrap.Modal($modal[0]);
            modal.show();
            
            // Reload page after closing modal
            $modal.on('hidden.bs.modal', function () {
                location.reload();
            });
        }
    });
    
    return publicWidget.registry.GamificationRewards;
});
