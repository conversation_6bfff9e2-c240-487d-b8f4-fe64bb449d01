/**
 * Scratch Card Implementation
 * 
 * This script provides functionality for scratch cards in the gamification module.
 */
(function() {
    'use strict';
    
    /**
     * ScratchCard constructor
     * @param {Object} options - Configuration options
     */
    function ScratchCard(options) {
        this.options = Object.assign({
            container: null,
            brushSize: 30,
            coverColor: '#AAAAAA',
            coverImage: null,
            revealPercentage: 50,
            onRevealed: function() {}
        }, options);
        
        this.canvas = null;
        this.ctx = null;
        this.isDrawing = false;
        this.lastPoint = null;
        this.scratchedPixels = 0;
        this.totalPixels = 0;
    }
    
    /**
     * Initialize the scratch card
     */
    ScratchCard.prototype.init = function() {
        if (!this.options.container) {
            console.error('Scratch card container not specified');
            return;
        }
        
        // Create canvas
        this.canvas = document.createElement('canvas');
        this.canvas.width = this.options.container.offsetWidth;
        this.canvas.height = this.options.container.offsetHeight;
        this.canvas.className = 'scratch-card-canvas';
        this.options.container.appendChild(this.canvas);
        
        this.ctx = this.canvas.getContext('2d');
        this.totalPixels = this.canvas.width * this.canvas.height;
        
        // Draw cover
        this.drawCover();
        
        // Add event listeners
        this.addEventListeners();
    };
    
    /**
     * Draw the cover layer
     */
    ScratchCard.prototype.drawCover = function() {
        if (this.options.coverImage) {
            // Load and draw cover image
            const img = new Image();
            img.onload = () => {
                this.ctx.drawImage(img, 0, 0, this.canvas.width, this.canvas.height);
            };
            img.src = this.options.coverImage;
        } else {
            // Draw solid color cover
            this.ctx.fillStyle = this.options.coverColor;
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        }
    };
    
    /**
     * Add event listeners for scratching
     */
    ScratchCard.prototype.addEventListeners = function() {
        // Mouse events
        this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this));
        this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));
        
        // Touch events
        this.canvas.addEventListener('touchstart', this.handleTouchStart.bind(this));
        this.canvas.addEventListener('touchmove', this.handleTouchMove.bind(this));
        this.canvas.addEventListener('touchend', this.handleTouchEnd.bind(this));
    };
    
    /**
     * Handle mouse down event
     */
    ScratchCard.prototype.handleMouseDown = function(e) {
        this.isDrawing = true;
        this.lastPoint = this.getPointFromEvent(e);
        this.scratch(this.lastPoint.x, this.lastPoint.y);
    };
    
    /**
     * Handle mouse move event
     */
    ScratchCard.prototype.handleMouseMove = function(e) {
        if (!this.isDrawing) return;
        
        const currentPoint = this.getPointFromEvent(e);
        this.scratchLine(this.lastPoint.x, this.lastPoint.y, currentPoint.x, currentPoint.y);
        this.lastPoint = currentPoint;
    };
    
    /**
     * Handle mouse up event
     */
    ScratchCard.prototype.handleMouseUp = function() {
        this.isDrawing = false;
        this.checkRevealPercentage();
    };
    
    /**
     * Handle touch start event
     */
    ScratchCard.prototype.handleTouchStart = function(e) {
        e.preventDefault();
        if (e.touches.length === 1) {
            this.isDrawing = true;
            this.lastPoint = this.getPointFromEvent(e.touches[0]);
            this.scratch(this.lastPoint.x, this.lastPoint.y);
        }
    };
    
    /**
     * Handle touch move event
     */
    ScratchCard.prototype.handleTouchMove = function(e) {
        e.preventDefault();
        if (!this.isDrawing || e.touches.length !== 1) return;
        
        const currentPoint = this.getPointFromEvent(e.touches[0]);
        this.scratchLine(this.lastPoint.x, this.lastPoint.y, currentPoint.x, currentPoint.y);
        this.lastPoint = currentPoint;
    };
    
    /**
     * Handle touch end event
     */
    ScratchCard.prototype.handleTouchEnd = function(e) {
        e.preventDefault();
        this.isDrawing = false;
        this.checkRevealPercentage();
    };
    
    /**
     * Get point coordinates from event
     */
    ScratchCard.prototype.getPointFromEvent = function(e) {
        const rect = this.canvas.getBoundingClientRect();
        return {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
    };
    
    /**
     * Scratch at a specific point
     */
    ScratchCard.prototype.scratch = function(x, y) {
        this.ctx.globalCompositeOperation = 'destination-out';
        this.ctx.beginPath();
        this.ctx.arc(x, y, this.options.brushSize / 2, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Update scratched pixels count
        this.updateScratchedPixels();
    };
    
    /**
     * Scratch a line between two points
     */
    ScratchCard.prototype.scratchLine = function(x1, y1, x2, y2) {
        this.ctx.globalCompositeOperation = 'destination-out';
        this.ctx.lineWidth = this.options.brushSize;
        this.ctx.lineCap = 'round';
        this.ctx.beginPath();
        this.ctx.moveTo(x1, y1);
        this.ctx.lineTo(x2, y2);
        this.ctx.stroke();
        
        // Update scratched pixels count
        this.updateScratchedPixels();
    };
    
    /**
     * Update the count of scratched pixels
     */
    ScratchCard.prototype.updateScratchedPixels = function() {
        const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
        const pixels = imageData.data;
        let transparentPixels = 0;
        
        for (let i = 3; i < pixels.length; i += 4) {
            if (pixels[i] === 0) {
                transparentPixels++;
            }
        }
        
        this.scratchedPixels = transparentPixels;
    };
    
    /**
     * Check if enough of the card has been revealed
     */
    ScratchCard.prototype.checkRevealPercentage = function() {
        const percentage = (this.scratchedPixels / this.totalPixels) * 100;
        
        if (percentage >= this.options.revealPercentage) {
            // Enough has been scratched, reveal the reward
            if (typeof this.options.onRevealed === 'function') {
                this.options.onRevealed();
            }
        }
    };
    
    // Make ScratchCard available globally
    window.ScratchCard = ScratchCard;
})();
