/**
 * Spin Wheel Implementation
 * 
 * This script provides functionality for spin wheels in the gamification module.
 */
(function() {
    'use strict';
    
    /**
     * SpinWheel constructor
     * @param {Object} options - Configuration options
     */
    function SpinWheel(options) {
        this.options = Object.assign({
            canvas: null,
            segments: [],
            outerRadius: 200,
            innerRadius: 50,
            textDistance: 160,
            spinDuration: 5000,
            onSpinEnd: function() {}
        }, options);
        
        this.ctx = null;
        this.isSpinning = false;
        this.startAngle = 0;
        this.currentAngle = 0;
        this.targetAngle = 0;
        this.spinStartTime = 0;
    }
    
    /**
     * Initialize the spin wheel
     */
    SpinWheel.prototype.init = function() {
        if (!this.options.canvas) {
            console.error('Spin wheel canvas not specified');
            return;
        }
        
        this.ctx = this.options.canvas.getContext('2d');
        
        // Draw initial wheel
        this.draw();
        
        // Add pointer element if it doesn't exist
        if (!document.getElementById('wheel-pointer')) {
            const pointer = document.createElement('div');
            pointer.id = 'wheel-pointer';
            this.options.canvas.parentNode.appendChild(pointer);
        }
    };
    
    /**
     * Draw the wheel
     */
    SpinWheel.prototype.draw = function() {
        const ctx = this.ctx;
        const canvas = this.options.canvas;
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const segments = this.options.segments;
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw segments
        if (segments.length > 0) {
            const segmentAngle = (2 * Math.PI) / segments.length;
            
            for (let i = 0; i < segments.length; i++) {
                const segment = segments[i];
                const startAngle = this.currentAngle + i * segmentAngle;
                const endAngle = startAngle + segmentAngle;
                
                // Draw segment
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, this.options.outerRadius, startAngle, endAngle);
                ctx.lineTo(centerX, centerY);
                ctx.fillStyle = segment.color || '#' + Math.floor(Math.random() * 16777215).toString(16);
                ctx.fill();
                ctx.stroke();
                
                // Draw text
                ctx.save();
                ctx.translate(centerX, centerY);
                ctx.rotate(startAngle + segmentAngle / 2);
                ctx.textAlign = 'right';
                ctx.fillStyle = '#FFFFFF';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(segment.text, this.options.textDistance, 5);
                ctx.restore();
            }
        } else {
            // Draw placeholder wheel if no segments
            ctx.beginPath();
            ctx.arc(centerX, centerY, this.options.outerRadius, 0, 2 * Math.PI);
            ctx.fillStyle = '#CCCCCC';
            ctx.fill();
            ctx.stroke();
            
            ctx.fillStyle = '#000000';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('No segments defined', centerX, centerY);
        }
        
        // Draw center circle
        ctx.beginPath();
        ctx.arc(centerX, centerY, this.options.innerRadius, 0, 2 * Math.PI);
        ctx.fillStyle = '#FFFFFF';
        ctx.fill();
        ctx.stroke();
    };
    
    /**
     * Spin the wheel to a specific segment
     * @param {number} segmentIndex - Index of the segment to land on
     */
    SpinWheel.prototype.spin = function(segmentIndex) {
        if (this.isSpinning) return;
        
        const segments = this.options.segments;
        if (segments.length === 0) return;
        
        this.isSpinning = true;
        this.spinStartTime = Date.now();
        this.startAngle = this.currentAngle;
        
        // Calculate target angle
        const segmentAngle = (2 * Math.PI) / segments.length;
        const segmentCenter = segmentIndex * segmentAngle;
        
        // Add multiple rotations plus the target segment
        this.targetAngle = this.startAngle + (Math.PI * 8) + segmentCenter;
        
        // Start animation
        this.animate();
    };
    
    /**
     * Animate the wheel spin
     */
    SpinWheel.prototype.animate = function() {
        if (!this.isSpinning) return;
        
        const now = Date.now();
        const elapsed = now - this.spinStartTime;
        const duration = this.options.spinDuration;
        
        if (elapsed < duration) {
            // Calculate current angle using easing function
            const progress = elapsed / duration;
            const easedProgress = this.easeOutCubic(progress);
            this.currentAngle = this.startAngle + (this.targetAngle - this.startAngle) * easedProgress;
            
            // Draw wheel
            this.draw();
            
            // Continue animation
            requestAnimationFrame(this.animate.bind(this));
        } else {
            // Animation complete
            this.currentAngle = this.targetAngle % (2 * Math.PI);
            this.draw();
            this.isSpinning = false;
            
            // Calculate which segment landed at the top
            const segments = this.options.segments;
            const segmentAngle = (2 * Math.PI) / segments.length;
            const normalizedAngle = (this.currentAngle % (2 * Math.PI) + 2 * Math.PI) % (2 * Math.PI);
            const topSegmentIndex = Math.floor(normalizedAngle / segmentAngle) % segments.length;
            
            // Call callback
            if (typeof this.options.onSpinEnd === 'function') {
                this.options.onSpinEnd(topSegmentIndex);
            }
        }
    };
    
    /**
     * Easing function for smooth animation
     * @param {number} t - Progress (0-1)
     * @returns {number} Eased value
     */
    SpinWheel.prototype.easeOutCubic = function(t) {
        return 1 - Math.pow(1 - t, 3);
    };
    
    // Make SpinWheel available globally
    window.SpinWheel = SpinWheel;
})();
