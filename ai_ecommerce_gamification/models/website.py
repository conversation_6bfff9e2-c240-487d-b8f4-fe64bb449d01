# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _
import logging

_logger = logging.getLogger(__name__)

class Website(models.Model):
    _inherit = 'website'
    
    # Gamification settings
    gamification_enabled = fields.<PERSON><PERSON>an('Enable Gamification', default=True)
    
    # Signup reward
    signup_reward_enabled = fields.<PERSON><PERSON>an('Enable Signup Reward', default=True)
    signup_reward_id = fields.Many2one('gamification.reward', string='Signup Reward',
                                      domain=[('active', '=', True)])
    
    # Purchase reward
    purchase_reward_enabled = fields.<PERSON><PERSON><PERSON>('Enable Purchase Reward', default=True)
    purchase_reward_id = fields.Many2one('gamification.reward', string='Purchase Reward',
                                        domain=[('active', '=', True)])
    
    # Display settings
    show_rewards_in_account = fields.<PERSON><PERSON><PERSON>('Show Rewards in My Account', default=True)
    show_wallet_in_account = fields.<PERSON><PERSON><PERSON>('Show Wallet in My Account', default=True)
    
    # Notification settings
    reward_notification_enabled = fields.<PERSON><PERSON><PERSON>('Enable Reward Notifications', default=True)
    
    def get_gamification_rewards(self, user=None):
        """Get available rewards for the current user"""
        if not user:
            if not self.env.user or self.env.user._is_public():
                return []
            user = self.env.user
        
        if not self.gamification_enabled:
            return []
        
        return user.get_available_rewards()
    
    def check_trigger_rewards(self, trigger_type, user=None, **kwargs):
        """Check and trigger rewards based on events"""
        if not self.gamification_enabled:
            return False
        
        if not user:
            if not self.env.user or self.env.user._is_public():
                return False
            user = self.env.user
        
        # Find rewards with rules matching this trigger
        rewards = self.env['gamification.reward'].search([('active', '=', True)])
        triggered_rewards = []
        
        for reward in rewards:
            # Find rules matching this trigger type
            matching_rules = reward.rule_ids.filtered(
                lambda r: r.active and r.rule_type == trigger_type
            )
            
            if not matching_rules:
                continue
            
            # Check if any rule applies
            for rule in matching_rules:
                eligible, message = rule.check_rule(user)
                if eligible:
                    # Claim the reward
                    result, msg = user.claim_reward(reward.id)
                    if result:
                        triggered_rewards.append({
                            'reward_id': reward.id,
                            'name': reward.name,
                            'message': msg,
                        })
                    break  # Only trigger once per reward
        
        return triggered_rewards
