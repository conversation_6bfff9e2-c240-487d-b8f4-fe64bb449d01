# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from odoo.tools.safe_eval import safe_eval
import logging

_logger = logging.getLogger(__name__)

class GamificationRule(models.Model):
    _name = 'gamification.rule'
    _description = 'Gamification Rule'
    _order = 'sequence, id'
    
    name = fields.Char('Name', required=True, translate=True)
    active = fields.Boolean('Active', default=True)
    sequence = fields.Integer('Sequence', default=10)
    description = fields.Text('Description', translate=True)
    
    # Reward Type
    reward_id = fields.Many2one('gamification.reward', string='Reward', required=True, ondelete='cascade')
    
    # Rule Type
    rule_type = fields.Selection([
        ('signup', 'User Signup'),
        ('purchase', 'Purchase Completion'),
        ('order_value', 'Order Value'),
        ('repeat_purchase', 'Repeat Purchase'),
        ('special_date', 'Special Date'),
        ('custom', 'Custom Domain'),
    ], string='Rule Type', required=True, default='signup')
    
    # Domain Filter
    domain = fields.Char('Domain', default='[]',
                        help='Domain filter to determine eligibility')
    
    # Order Value Rule
    min_order_value = fields.Float('Minimum Order Value', default=0.0)
    
    # Repeat Purchase Rule
    min_purchase_count = fields.Integer('Minimum Purchase Count', default=1)
    
    # Special Date Rule
    special_date_type = fields.Selection([
        ('birthday', 'Birthday'),
        ('anniversary', 'Account Anniversary'),
        ('specific', 'Specific Date'),
    ], string='Special Date Type', default='birthday')
    specific_date = fields.Date('Specific Date')
    
    # Validity
    date_from = fields.Datetime('Valid From')
    date_to = fields.Datetime('Valid To')
    
    @api.constrains('domain')
    def _check_domain(self):
        for rule in self:
            try:
                domain = safe_eval(rule.domain or '[]')
                if not isinstance(domain, list):
                    raise ValidationError(_('Domain must be a list of tuples'))
            except Exception as e:
                raise ValidationError(_('Invalid domain: %s') % str(e))
    
    def check_rule(self, user):
        """Check if a user meets this rule's criteria"""
        self.ensure_one()
        
        # Check if rule is active and within valid dates
        if not self.active:
            return False, _("This rule is not active")
        
        now = fields.Datetime.now()
        if self.date_from and self.date_from > now:
            return False, _("This rule is not yet active")
        
        if self.date_to and self.date_to < now:
            return False, _("This rule has expired")
        
        # Check based on rule type
        if self.rule_type == 'signup':
            # Check if user recently signed up (within last 24 hours)
            signup_date = user.create_date
            if not signup_date:
                return False, _("Cannot determine signup date")
            
            hours_since_signup = (now - signup_date).total_seconds() / 3600
            if hours_since_signup <= 24:
                return True, _("Welcome reward for new signup")
            else:
                return False, _("This reward is only for new users")
        
        elif self.rule_type == 'purchase':
            # Check if user has a recently completed order
            SaleOrder = self.env['sale.order']
            recent_order = SaleOrder.search([
                ('partner_id', '=', user.partner_id.id),
                ('state', '=', 'sale'),
                ('date_order', '>=', fields.Datetime.subtract(now, hours=24)),
            ], limit=1)
            
            if recent_order:
                return True, _("Reward for recent purchase")
            else:
                return False, _("No recent purchase found")
        
        elif self.rule_type == 'order_value':
            # Check if user has an order with value above threshold
            SaleOrder = self.env['sale.order']
            qualifying_order = SaleOrder.search([
                ('partner_id', '=', user.partner_id.id),
                ('state', '=', 'sale'),
                ('amount_total', '>=', self.min_order_value),
                ('date_order', '>=', fields.Datetime.subtract(now, hours=24)),
            ], limit=1)
            
            if qualifying_order:
                return True, _("Reward for high-value purchase")
            else:
                return False, _("No qualifying high-value purchase found")
        
        elif self.rule_type == 'repeat_purchase':
            # Check if user has made multiple purchases
            SaleOrder = self.env['sale.order']
            purchase_count = SaleOrder.search_count([
                ('partner_id', '=', user.partner_id.id),
                ('state', '=', 'sale'),
            ])
            
            if purchase_count >= self.min_purchase_count:
                return True, _("Reward for loyal customer")
            else:
                return False, _("Not enough purchases to qualify")
        
        elif self.rule_type == 'special_date':
            # Check if today is a special date for the user
            today = fields.Date.today()
            
            if self.special_date_type == 'birthday' and user.partner_id.birthdate:
                user_birthday = fields.Date.from_string(user.partner_id.birthdate)
                if user_birthday.day == today.day and user_birthday.month == today.month:
                    return True, _("Happy Birthday!")
                else:
                    return False, _("Not your birthday")
            
            elif self.special_date_type == 'anniversary':
                signup_date = fields.Date.from_string(user.create_date)
                if signup_date.day == today.day and signup_date.month == today.month:
                    return True, _("Happy Account Anniversary!")
                else:
                    return False, _("Not your account anniversary")
            
            elif self.special_date_type == 'specific' and self.specific_date:
                if self.specific_date == today:
                    return True, _("Special day reward")
                else:
                    return False, _("Not a special day")
            
            return False, _("No special date configured")
        
        elif self.rule_type == 'custom':
            # Evaluate custom domain
            try:
                domain = safe_eval(self.domain or '[]')
                domain.append(('id', '=', user.id))
                matching_users = self.env['res.users'].search(domain)
                
                if user in matching_users:
                    return True, _("You qualify for this reward")
                else:
                    return False, _("You don't meet the criteria for this reward")
            except Exception as e:
                _logger.error("Error evaluating custom domain: %s", str(e))
                return False, _("Error checking eligibility")
        
        return False, _("Unknown rule type")
