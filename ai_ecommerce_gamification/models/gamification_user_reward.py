# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _
import logging
import uuid

_logger = logging.getLogger(__name__)

class GamificationUserReward(models.Model):
    _name = 'gamification.user.reward'
    _description = 'User Reward'
    _order = 'create_date desc'
    
    name = fields.Char('Name', compute='_compute_name', store=True)
    user_id = fields.Many2one('res.users', string='User', required=True, ondelete='cascade', index=True)
    reward_id = fields.Many2one('gamification.reward', string='Reward', required=True, ondelete='cascade')
    reward_item_id = fields.Many2one('gamification.reward.item', string='Reward Item', ondelete='cascade')
    
    # Tracking
    create_date = fields.Datetime('Created On', readonly=True)
    claim_date = fields.Datetime('Claimed On')
    expiry_date = fields.Datetime('Expires On')
    
    # Status
    state = fields.Selection([
        ('pending', 'Pending'),
        ('claimed', 'Claimed'),
        ('expired', 'Expired'),
        ('failed', 'Failed'),
    ], string='Status', default='pending', required=True, index=True)
    
    # Security
    token = fields.Char('Security Token', default=lambda self: str(uuid.uuid4()), readonly=True)
    
    # Reward Details
    reward_type = fields.Selection(related='reward_id.reward_type', string='Reward Type')
    item_type = fields.Selection(related='reward_item_id.item_type', string='Item Type')
    value = fields.Float('Value', help='Value of the reward (amount for wallet credit, discount percentage, etc.)')
    code = fields.Char('Code', help='Coupon code or other reward code')
    
    # For coupon rewards
    coupon_id = fields.Many2one('coupon.coupon', string='Coupon')
    
    # For product rewards
    product_id = fields.Many2one('product.product', string='Product')
    
    # Notes
    notes = fields.Text('Notes')
    
    @api.depends('reward_id', 'reward_item_id', 'create_date')
    def _compute_name(self):
        for record in self:
            if record.reward_item_id:
                record.name = f"{record.reward_id.name} - {record.reward_item_id.name}"
            else:
                record.name = record.reward_id.name
    
    def claim_reward(self, token=None):
        """Claim a pending reward"""
        self.ensure_one()
        
        # Security check
        if token and token != self.token:
            return False, _("Invalid security token")
        
        # Check if already claimed
        if self.state != 'pending':
            return False, _("This reward has already been %s") % self.state
        
        # Check if expired
        if self.expiry_date and self.expiry_date < fields.Datetime.now():
            self.write({
                'state': 'expired',
            })
            return False, _("This reward has expired")
        
        # Process the reward based on type
        result = True
        message = _("Reward claimed successfully")
        
        # Mark as claimed
        self.write({
            'state': 'claimed',
            'claim_date': fields.Datetime.now(),
        })
        
        return result, message
    
    def generate_reward_data(self):
        """Generate data needed for displaying the reward in the UI"""
        self.ensure_one()
        
        data = {
            'id': self.id,
            'name': self.name,
            'reward_type': self.reward_type,
            'item_type': self.item_type,
            'token': self.token,
        }
        
        # Add type-specific data
        if self.reward_type == 'scratch_card':
            data.update({
                'template': self.reward_id.scratch_card_template,
                'reward_text': self.reward_item_id.name if self.reward_item_id else _("Try your luck!"),
            })
        
        elif self.reward_type == 'spin_wheel':
            data.update({
                'wheel_data': self.reward_id.generate_wheel_data(),
            })
        
        # Add item-specific data
        if self.item_type == 'coupon' and self.code:
            data.update({
                'code': self.code,
            })
        
        elif self.item_type == 'wallet_credit':
            data.update({
                'value': self.value,
            })
        
        elif self.item_type == 'free_product' and self.product_id:
            data.update({
                'product': {
                    'id': self.product_id.id,
                    'name': self.product_id.name,
                    'image': self.product_id.image_1920,
                },
            })
        
        return data
