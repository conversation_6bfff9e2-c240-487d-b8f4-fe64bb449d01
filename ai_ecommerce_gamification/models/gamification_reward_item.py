# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)

class GamificationRewardItem(models.Model):
    _name = 'gamification.reward.item'
    _description = 'Gamification Reward Item'
    _order = 'sequence, id'
    
    name = fields.Char('Name', required=True, translate=True)
    active = fields.Boolean('Active', default=True)
    sequence = fields.Integer('Sequence', default=10)
    description = fields.Text('Description', translate=True)
    
    # Reward Type
    reward_id = fields.Many2one('gamification.reward', string='Reward', required=True, ondelete='cascade')
    
    # Item Type
    item_type = fields.Selection([
        ('coupon', 'Coupon Code'),
        ('external_coupon', 'External Coupon'),
        ('wallet_credit', 'Wallet Credit'),
        ('free_shipping', 'Free Shipping'),
        ('free_product', 'Free Product'),
        ('discount', 'Discount'),
        ('no_reward', 'No Reward'),
    ], string='Item Type', required=True, default='coupon')
    
    # Probability
    probability = fields.Float('Probability (%)', default=10.0, 
                              help='Probability of this item being selected. The sum of all probabilities should be 100%.')
    
    # Value
    value = fields.Float('Value', default=0.0,
                        help='Value of the reward (amount for wallet credit, discount percentage, etc.)')
    
    # Coupon Code
    coupon_code = fields.Char('Coupon Code',
                             help='For external coupons, enter the code here. For Odoo coupons, leave empty to generate automatically.')
    coupon_program_id = fields.Many2one('coupon.program', string='Coupon Program',
                                       help='Odoo coupon program to use for generating coupons')
    
    # Free Product
    product_id = fields.Many2one('product.product', string='Product',
                                help='Product to give for free')
    
    # Quantity
    max_quantity = fields.Integer('Maximum Quantity', default=0,
                                 help='Maximum number of this reward that can be given. 0 means unlimited.')
    remaining_quantity = fields.Integer('Remaining Quantity', compute='_compute_remaining_quantity', store=True)
    
    # Statistics
    total_assigned = fields.Integer('Total Assigned', default=0)
    
    # Display
    icon = fields.Binary('Icon', attachment=True)
    color = fields.Char('Color', default='#FFFFFF')
    
    @api.depends('max_quantity', 'total_assigned')
    def _compute_remaining_quantity(self):
        for item in self:
            if item.max_quantity > 0:
                item.remaining_quantity = max(0, item.max_quantity - item.total_assigned)
            else:
                item.remaining_quantity = 0  # Unlimited
    
    @api.constrains('probability')
    def _check_probability(self):
        for item in self:
            if item.probability < 0 or item.probability > 100:
                raise ValidationError(_('Probability must be between 0 and 100.'))
    
    @api.constrains('probability', 'reward_id')
    def _check_total_probability(self):
        for item in self:
            total_probability = sum(item.reward_id.reward_item_ids.mapped('probability'))
            if abs(total_probability - 100) > 0.01 and len(item.reward_id.reward_item_ids) > 1:
                raise ValidationError(_('The sum of all probabilities must be 100%. Current total: %s%%') % total_probability)
    
    def assign_to_user(self, user_id):
        """Assign this reward item to a user"""
        self.ensure_one()
        
        # Check if there are remaining quantities
        if self.max_quantity > 0 and self.remaining_quantity <= 0:
            return False, _("This reward is no longer available")
        
        # Create user reward record
        user_reward = self.env['gamification.user.reward'].create({
            'user_id': user_id,
            'reward_id': self.reward_id.id,
            'reward_item_id': self.id,
            'state': 'pending',
        })
        
        # Process based on item type
        result = False
        message = _("Reward assigned")
        
        if self.item_type == 'coupon' and self.coupon_program_id:
            # Generate Odoo coupon
            try:
                coupon = self.env['coupon.coupon'].create({
                    'program_id': self.coupon_program_id.id,
                    'partner_id': self.env['res.users'].browse(user_id).partner_id.id,
                })
                user_reward.write({
                    'coupon_id': coupon.id,
                    'code': coupon.code,
                })
                result = True
                message = _("Coupon code %s generated successfully") % coupon.code
            except Exception as e:
                _logger.error("Failed to generate coupon: %s", str(e))
                message = _("Failed to generate coupon")
        
        elif self.item_type == 'external_coupon':
            # Assign external coupon code
            user_reward.write({
                'code': self.coupon_code,
            })
            result = True
            message = _("External coupon code assigned")
        
        elif self.item_type == 'wallet_credit':
            # Add wallet credit
            wallet = self.env['gamification.wallet'].search([('user_id', '=', user_id)], limit=1)
            if not wallet:
                wallet = self.env['gamification.wallet'].create({'user_id': user_id})
            
            wallet.add_credit(self.value, _("Reward from %s") % self.reward_id.name)
            user_reward.write({
                'value': self.value,
            })
            result = True
            message = _("Wallet credited with %s") % self.value
        
        elif self.item_type == 'free_shipping':
            # Create free shipping coupon
            result = True
            message = _("Free shipping reward assigned")
        
        elif self.item_type == 'free_product' and self.product_id:
            # Add free product
            user_reward.write({
                'product_id': self.product_id.id,
            })
            result = True
            message = _("Free product reward assigned")
        
        elif self.item_type == 'discount':
            # Create discount coupon
            user_reward.write({
                'value': self.value,
            })
            result = True
            message = _("Discount reward assigned")
        
        elif self.item_type == 'no_reward':
            # No reward (better luck next time)
            user_reward.write({
                'state': 'claimed',
            })
            result = True
            message = _("No reward this time")
        
        # Update statistics if successful
        if result:
            self.write({
                'total_assigned': self.total_assigned + 1,
            })
            user_reward.write({
                'state': 'claimed',
            })
        else:
            user_reward.write({
                'state': 'failed',
                'notes': message,
            })
        
        return result, message
