# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)

class GamificationWallet(models.Model):
    _name = 'gamification.wallet'
    _description = 'User eWallet'
    _rec_name = 'user_id'
    
    user_id = fields.Many2one('res.users', string='User', required=True, ondelete='cascade', index=True)
    balance = fields.Float('Balance', default=0.0, readonly=True)
    currency_id = fields.Many2one('res.currency', string='Currency', 
                                 default=lambda self: self.env.company.currency_id.id)
    
    # Transactions
    transaction_ids = fields.One2many('gamification.wallet.transaction', 'wallet_id', string='Transactions')
    
    # Statistics
    total_earned = fields.Float('Total Earned', compute='_compute_statistics', store=True)
    total_spent = fields.Float('Total Spent', compute='_compute_statistics', store=True)
    last_transaction_date = fields.Datetime('Last Transaction', compute='_compute_statistics', store=True)
    
    _sql_constraints = [
        ('user_uniq', 'unique(user_id)', 'A user can only have one wallet!'),
    ]
    
    @api.depends('transaction_ids.amount', 'transaction_ids.transaction_type', 'transaction_ids.create_date')
    def _compute_statistics(self):
        for wallet in self:
            wallet.total_earned = sum(wallet.transaction_ids.filtered(lambda t: t.transaction_type == 'credit').mapped('amount'))
            wallet.total_spent = sum(wallet.transaction_ids.filtered(lambda t: t.transaction_type == 'debit').mapped('amount'))
            
            last_transaction = wallet.transaction_ids.sorted('create_date', reverse=True)[:1]
            wallet.last_transaction_date = last_transaction.create_date if last_transaction else False
    
    def add_credit(self, amount, description=None):
        """Add credit to the wallet"""
        self.ensure_one()
        
        if amount <= 0:
            raise ValidationError(_("Credit amount must be positive"))
        
        # Create transaction
        self.env['gamification.wallet.transaction'].create({
            'wallet_id': self.id,
            'amount': amount,
            'description': description or _('Credit'),
            'transaction_type': 'credit',
        })
        
        # Update balance
        self.balance += amount
        
        return True
    
    def deduct_credit(self, amount, description=None):
        """Deduct credit from the wallet"""
        self.ensure_one()
        
        if amount <= 0:
            raise ValidationError(_("Debit amount must be positive"))
        
        if self.balance < amount:
            raise ValidationError(_("Insufficient balance"))
        
        # Create transaction
        self.env['gamification.wallet.transaction'].create({
            'wallet_id': self.id,
            'amount': amount,
            'description': description or _('Debit'),
            'transaction_type': 'debit',
        })
        
        # Update balance
        self.balance -= amount
        
        return True
    
    def get_transaction_history(self, limit=10):
        """Get recent transaction history"""
        self.ensure_one()
        
        transactions = self.transaction_ids.sorted('create_date', reverse=True)[:limit]
        
        result = []
        for transaction in transactions:
            result.append({
                'id': transaction.id,
                'date': transaction.create_date,
                'type': transaction.transaction_type,
                'amount': transaction.amount,
                'description': transaction.description,
            })
        
        return result


class GamificationWalletTransaction(models.Model):
    _name = 'gamification.wallet.transaction'
    _description = 'Wallet Transaction'
    _order = 'create_date desc'
    
    wallet_id = fields.Many2one('gamification.wallet', string='Wallet', required=True, ondelete='cascade')
    user_id = fields.Many2one(related='wallet_id.user_id', string='User', store=True, readonly=True)
    
    amount = fields.Float('Amount', required=True)
    description = fields.Char('Description')
    
    transaction_type = fields.Selection([
        ('credit', 'Credit'),
        ('debit', 'Debit'),
    ], string='Type', required=True)
    
    create_date = fields.Datetime('Date', readonly=True)
    
    # Related document
    model = fields.Char('Related Document Model')
    res_id = fields.Integer('Related Document ID')
    
    currency_id = fields.Many2one(related='wallet_id.currency_id', string='Currency', readonly=True)
    
    def name_get(self):
        result = []
        for record in self:
            name = f"{record.create_date.strftime('%Y-%m-%d %H:%M:%S')} - {record.amount}"
            result.append((record.id, name))
        return result
