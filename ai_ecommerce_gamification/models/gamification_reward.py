# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import random
import json
import logging

_logger = logging.getLogger(__name__)

class GamificationReward(models.Model):
    _name = 'gamification.reward'
    _description = 'Gamification Reward'
    _order = 'sequence, id'
    
    name = fields.Char('Name', required=True, translate=True)
    active = fields.Boolean('Active', default=True)
    sequence = fields.Integer('Sequence', default=10)
    description = fields.Text('Description', translate=True)
    
    # Reward Type
    reward_type = fields.Selection([
        ('scratch_card', 'Scratch Card'),
        ('spin_wheel', 'Spin the Wheel'),
        ('direct', 'Direct Reward'),
    ], string='Reward Type', required=True, default='direct')
    
    # UI Configuration
    ui_theme = fields.Selection([
        ('default', 'Default'),
        ('festive', 'Festive'),
        ('premium', 'Premium'),
        ('birthday', 'Birthday'),
    ], string='UI Theme', default='default')
    
    display_mode = fields.Selection([
        ('popup', 'Popup'),
        ('fullscreen', 'Full Screen'),
        ('sidebar', 'Sidebar'),
    ], string='Display Mode', default='popup')
    
    # Images
    image = fields.Binary('Image', attachment=True)
    background_image = fields.Binary('Background Image', attachment=True)
    
    # Reward Items
    reward_item_ids = fields.One2many('gamification.reward.item', 'reward_id', string='Reward Items')
    
    # Rules
    rule_ids = fields.One2many('gamification.rule', 'reward_id', string='Rules')
    
    # Limits
    max_rewards_per_user = fields.Integer('Max Rewards Per User', default=0, 
                                         help='Maximum number of rewards a user can get from this reward type. 0 means unlimited.')
    max_rewards_per_day = fields.Integer('Max Rewards Per Day', default=0,
                                        help='Maximum number of rewards a user can get per day. 0 means unlimited.')
    max_rewards_total = fields.Integer('Max Total Rewards', default=0,
                                      help='Maximum number of rewards that can be given in total. 0 means unlimited.')
    
    # Validity
    date_from = fields.Datetime('Valid From')
    date_to = fields.Datetime('Valid To')
    
    # Statistics
    total_rewards_given = fields.Integer('Total Rewards Given', compute='_compute_statistics', store=True)
    success_rate = fields.Float('Success Rate (%)', compute='_compute_statistics', store=True)
    
    # For Scratch Card
    scratch_card_template = fields.Selection([
        ('default', 'Default'),
        ('gold', 'Gold'),
        ('silver', 'Silver'),
        ('festive', 'Festive'),
    ], string='Scratch Card Template', default='default')
    
    # For Spin Wheel
    wheel_segments = fields.Integer('Wheel Segments', default=8)
    wheel_colors = fields.Text('Wheel Colors', default='["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF", "#FFA500", "#800080"]',
                              help='JSON array of colors for wheel segments')
    
    @api.depends('reward_item_ids.total_assigned')
    def _compute_statistics(self):
        for reward in self:
            total_given = sum(reward.reward_item_ids.mapped('total_assigned'))
            reward.total_rewards_given = total_given
            
            total_attempts = self.env['gamification.user.reward'].search_count([
                ('reward_id', '=', reward.id)
            ])
            
            reward.success_rate = (total_given / total_attempts * 100) if total_attempts else 0
    
    def get_random_reward_item(self, user_id=None):
        """Get a random reward item based on probabilities"""
        self.ensure_one()
        
        # Filter active reward items with remaining quantity
        valid_items = self.reward_item_ids.filtered(
            lambda r: r.active and (r.max_quantity == 0 or r.remaining_quantity > 0)
        )
        
        if not valid_items:
            return False
        
        # Get probabilities
        probabilities = valid_items.mapped('probability')
        total_probability = sum(probabilities)
        
        # Normalize probabilities if they don't sum to 100
        if total_probability != 100:
            factor = 100 / total_probability
            probabilities = [p * factor for p in probabilities]
        
        # Random selection based on probability
        rand = random.uniform(0, 100)
        cumulative = 0
        
        for i, item in enumerate(valid_items):
            cumulative += probabilities[i]
            if rand <= cumulative:
                return item
        
        # Fallback to the last item if something went wrong
        return valid_items[-1]
    
    def generate_wheel_data(self):
        """Generate data for the spin wheel"""
        self.ensure_one()
        
        if self.reward_type != 'spin_wheel':
            return {}
        
        # Get active reward items
        items = self.reward_item_ids.filtered(lambda r: r.active)
        
        # Prepare wheel segments
        segments = []
        colors = json.loads(self.wheel_colors)
        
        for i, item in enumerate(items):
            color_index = i % len(colors)
            segments.append({
                'id': item.id,
                'text': item.name,
                'color': colors[color_index],
                'probability': item.probability,
            })
        
        return {
            'segments': segments,
            'wheel_id': self.id,
        }
    
    def check_user_eligibility(self, user, rule=None):
        """Check if a user is eligible for this reward"""
        self.ensure_one()
        
        # Check if reward is active and within valid dates
        if not self.active:
            return False, _("This reward is not active")
        
        now = fields.Datetime.now()
        if self.date_from and self.date_from > now:
            return False, _("This reward is not yet available")
        
        if self.date_to and self.date_to < now:
            return False, _("This reward has expired")
        
        # Check max rewards per user
        if self.max_rewards_per_user > 0:
            user_rewards_count = self.env['gamification.user.reward'].search_count([
                ('reward_id', '=', self.id),
                ('user_id', '=', user.id),
                ('state', '=', 'claimed'),
            ])
            
            if user_rewards_count >= self.max_rewards_per_user:
                return False, _("You have reached the maximum number of rewards for this promotion")
        
        # Check max rewards per day
        if self.max_rewards_per_day > 0:
            today_start = fields.Datetime.to_string(fields.Datetime.start_of(fields.Datetime.now(), 'day'))
            today_end = fields.Datetime.to_string(fields.Datetime.end_of(fields.Datetime.now(), 'day'))
            
            day_rewards_count = self.env['gamification.user.reward'].search_count([
                ('reward_id', '=', self.id),
                ('user_id', '=', user.id),
                ('state', '=', 'claimed'),
                ('create_date', '>=', today_start),
                ('create_date', '<=', today_end),
            ])
            
            if day_rewards_count >= self.max_rewards_per_day:
                return False, _("You have reached the maximum number of rewards for today")
        
        # Check max total rewards
        if self.max_rewards_total > 0 and self.total_rewards_given >= self.max_rewards_total:
            return False, _("All rewards have been claimed")
        
        # If a specific rule is provided, check that rule
        if rule:
            return rule.check_rule(user)
        
        # Otherwise check if any rule applies
        for rule in self.rule_ids.filtered(lambda r: r.active):
            eligible, message = rule.check_rule(user)
            if eligible:
                return True, _("You are eligible for this reward")
        
        return False, _("You are not eligible for this reward")
