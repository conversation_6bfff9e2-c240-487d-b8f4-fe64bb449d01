# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _
import logging

_logger = logging.getLogger(__name__)

class ResUsers(models.Model):
    _inherit = 'res.users'
    
    # Gamification fields
    wallet_id = fields.One2many('gamification.wallet', 'user_id', string='eWallet')
    wallet_balance = fields.Float(related='wallet_id.balance', string='Wallet Balance', readonly=True)
    reward_ids = fields.One2many('gamification.user.reward', 'user_id', string='Rewards')
    
    # Statistics
    total_rewards_count = fields.Integer('Total Rewards', compute='_compute_reward_statistics')
    active_rewards_count = fields.Integer('Active Rewards', compute='_compute_reward_statistics')
    
    @api.depends('reward_ids')
    def _compute_reward_statistics(self):
        for user in self:
            user.total_rewards_count = len(user.reward_ids)
            user.active_rewards_count = len(user.reward_ids.filtered(lambda r: r.state == 'pending'))
    
    def get_available_rewards(self):
        """Get rewards available to this user"""
        self.ensure_one()
        
        # Find all active rewards
        rewards = self.env['gamification.reward'].search([('active', '=', True)])
        
        available_rewards = []
        for reward in rewards:
            eligible, message = reward.check_user_eligibility(self)
            if eligible:
                available_rewards.append({
                    'id': reward.id,
                    'name': reward.name,
                    'type': reward.reward_type,
                    'message': message,
                })
        
        return available_rewards
    
    def claim_reward(self, reward_id):
        """Claim a reward for this user"""
        self.ensure_one()
        
        reward = self.env['gamification.reward'].browse(reward_id)
        if not reward.exists():
            return False, _("Reward not found")
        
        # Check eligibility
        eligible, message = reward.check_user_eligibility(self)
        if not eligible:
            return False, message
        
        # Get a random reward item
        reward_item = reward.get_random_reward_item(self.id)
        if not reward_item:
            return False, _("No available rewards")
        
        # Assign to user
        result, message = reward_item.assign_to_user(self.id)
        
        return result, message
    
    def get_wallet(self):
        """Get or create user wallet"""
        self.ensure_one()
        
        wallet = self.env['gamification.wallet'].search([('user_id', '=', self.id)], limit=1)
        if not wallet:
            wallet = self.env['gamification.wallet'].create({'user_id': self.id})
        
        return wallet
    
    def get_wallet_balance(self):
        """Get user wallet balance"""
        self.ensure_one()
        wallet = self.get_wallet()
        return wallet.balance
    
    def get_reward_history(self, limit=10):
        """Get user reward history"""
        self.ensure_one()
        
        rewards = self.reward_ids.sorted('create_date', reverse=True)[:limit]
        
        result = []
        for reward in rewards:
            result.append({
                'id': reward.id,
                'name': reward.name,
                'type': reward.reward_type,
                'state': reward.state,
                'date': reward.create_date,
                'item_type': reward.item_type,
                'value': reward.value,
                'code': reward.code,
            })
        
        return result
