<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Signup Rule -->
        <record id="rule_signup" model="gamification.rule">
            <field name="name">New User Signup</field>
            <field name="reward_id" ref="reward_signup_scratch_card"/>
            <field name="rule_type">signup</field>
            <field name="description">Reward new users when they sign up for an account.</field>
        </record>
        
        <!-- First Purchase Rule -->
        <record id="rule_first_purchase" model="gamification.rule">
            <field name="name">First Purchase</field>
            <field name="reward_id" ref="reward_first_purchase_spin_wheel"/>
            <field name="rule_type">purchase</field>
            <field name="description">Reward users after they complete their first purchase.</field>
        </record>
        
        <!-- High Value Purchase Rule -->
        <record id="rule_high_value_purchase" model="gamification.rule">
            <field name="name">High Value Purchase</field>
            <field name="reward_id" ref="reward_high_value_purchase"/>
            <field name="rule_type">order_value</field>
            <field name="min_order_value">10000</field>
            <field name="description">Reward users who make purchases over ₹10,000.</field>
        </record>
        
        <!-- Birthday Rule -->
        <record id="rule_birthday" model="gamification.rule">
            <field name="name">Birthday Reward</field>
            <field name="reward_id" ref="reward_birthday"/>
            <field name="rule_type">special_date</field>
            <field name="special_date_type">birthday</field>
            <field name="description">Reward users on their birthday.</field>
        </record>
        
        <!-- Loyalty Rule -->
        <record id="rule_loyalty" model="gamification.rule">
            <field name="name">Loyal Customer</field>
            <field name="reward_id" ref="reward_loyalty"/>
            <field name="rule_type">repeat_purchase</field>
            <field name="min_purchase_count">5</field>
            <field name="description">Reward users who have made at least 5 purchases.</field>
        </record>
    </data>
</odoo>
