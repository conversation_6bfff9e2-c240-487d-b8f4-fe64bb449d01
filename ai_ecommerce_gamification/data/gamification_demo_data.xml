<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Website Configuration -->
        <record id="base.main_company" model="res.company">
            <field name="name">Amigo Attires</field>
        </record>

        <record id="website.default_website" model="website">
            <field name="name">Amigo Attires</field>
            <field name="gamification_enabled" eval="True"/>
            <field name="signup_reward_enabled" eval="True"/>
            <field name="signup_reward_id" ref="reward_signup_scratch_card"/>
            <field name="purchase_reward_enabled" eval="True"/>
            <field name="purchase_reward_id" ref="reward_first_purchase_spin_wheel"/>
            <field name="show_rewards_in_account" eval="True"/>
            <field name="show_wallet_in_account" eval="True"/>
            <field name="reward_notification_enabled" eval="True"/>
        </record>

        <!-- Demo Users for Testing -->
        <record id="demo_user_customer" model="res.users">
            <field name="login"><EMAIL></field>
            <field name="password">demo123</field>
            <field name="partner_id" eval="ref('base.partner_demo')"/>
            <field name="groups_id" eval="[(6, 0, [ref('base.group_portal')])]" />
            <field name="name">Demo Customer</field>
            <field name="email"><EMAIL></field>
        </record>

        <record id="demo_user_customer2" model="res.users">
            <field name="login"><EMAIL></field>
            <field name="password">demo123</field>
            <field name="partner_id" eval="ref('base.partner_demo_portal')"/>
            <field name="groups_id" eval="[(6, 0, [ref('base.group_portal')])]" />
            <field name="name">Loyal Customer</field>
            <field name="email"><EMAIL></field>
        </record>

        <!-- Demo User Rewards - Admin User -->
        <record id="demo_user_reward_1" model="gamification.user.reward">
            <field name="user_id" ref="base.user_admin"/>
            <field name="reward_id" ref="reward_signup_scratch_card"/>
            <field name="reward_item_id" ref="reward_item_signup_wallet"/>
            <field name="state">claimed</field>
            <field name="value">100</field>
            <field name="create_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="claim_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="demo_user_reward_2" model="gamification.user.reward">
            <field name="user_id" ref="base.user_admin"/>
            <field name="reward_id" ref="reward_first_purchase_spin_wheel"/>
            <field name="reward_item_id" ref="reward_item_first_purchase_coupon_15"/>
            <field name="state">claimed</field>
            <field name="code">DEMO15OFF</field>
            <field name="create_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="claim_date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="demo_user_reward_3" model="gamification.user.reward">
            <field name="user_id" ref="base.user_admin"/>
            <field name="reward_id" ref="reward_high_value_purchase"/>
            <field name="reward_item_id" ref="reward_item_high_value_wallet_500"/>
            <field name="state">claimed</field>
            <field name="value">500</field>
            <field name="create_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="claim_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <!-- Demo User Rewards - Demo Customer -->
        <record id="demo_customer_reward_1" model="gamification.user.reward">
            <field name="user_id" ref="demo_user_customer"/>
            <field name="reward_id" ref="reward_signup_scratch_card"/>
            <field name="reward_item_id" ref="reward_item_signup_coupon"/>
            <field name="state">claimed</field>
            <field name="code">WELCOME10</field>
            <field name="create_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="claim_date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="demo_customer_reward_2" model="gamification.user.reward">
            <field name="user_id" ref="demo_user_customer"/>
            <field name="reward_id" ref="reward_first_purchase_spin_wheel"/>
            <field name="reward_item_id" ref="reward_item_first_purchase_wallet_200"/>
            <field name="state">claimed</field>
            <field name="value">200</field>
            <field name="create_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="claim_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <!-- Demo User Rewards - Loyal Customer -->
        <record id="demo_loyal_customer_reward_1" model="gamification.user.reward">
            <field name="user_id" ref="demo_user_customer2"/>
            <field name="reward_id" ref="reward_signup_scratch_card"/>
            <field name="reward_item_id" ref="reward_item_signup_free_shipping"/>
            <field name="state">claimed</field>
            <field name="create_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="claim_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="demo_loyal_customer_reward_2" model="gamification.user.reward">
            <field name="user_id" ref="demo_user_customer2"/>
            <field name="reward_id" ref="reward_loyalty"/>
            <field name="reward_item_id" ref="reward_item_loyalty_wallet"/>
            <field name="state">claimed</field>
            <field name="value">300</field>
            <field name="create_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="claim_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="demo_loyal_customer_reward_3" model="gamification.user.reward">
            <field name="user_id" ref="demo_user_customer2"/>
            <field name="reward_id" ref="reward_high_value_purchase"/>
            <field name="reward_item_id" ref="reward_item_high_value_wallet_1000"/>
            <field name="state">claimed</field>
            <field name="value">1000</field>
            <field name="create_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="claim_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <!-- Pending Rewards for Demo -->
        <record id="demo_pending_reward_1" model="gamification.user.reward">
            <field name="user_id" ref="base.user_admin"/>
            <field name="reward_id" ref="reward_birthday"/>
            <field name="state">pending</field>
            <field name="create_date" eval="(DateTime.now()).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <!-- Demo Wallets -->
        <record id="demo_wallet_admin" model="gamification.wallet">
            <field name="user_id" ref="base.user_admin"/>
            <field name="balance">600</field>
        </record>

        <record id="demo_wallet_customer" model="gamification.wallet">
            <field name="user_id" ref="demo_user_customer"/>
            <field name="balance">200</field>
        </record>

        <record id="demo_wallet_loyal" model="gamification.wallet">
            <field name="user_id" ref="demo_user_customer2"/>
            <field name="balance">1300</field>
        </record>

        <!-- Demo Wallet Transactions - Admin -->
        <record id="demo_wallet_transaction_admin_1" model="gamification.wallet.transaction">
            <field name="wallet_id" ref="demo_wallet_admin"/>
            <field name="amount">100</field>
            <field name="description">Welcome reward</field>
            <field name="transaction_type">credit</field>
            <field name="create_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="demo_wallet_transaction_admin_2" model="gamification.wallet.transaction">
            <field name="wallet_id" ref="demo_wallet_admin"/>
            <field name="amount">500</field>
            <field name="description">High value purchase reward</field>
            <field name="transaction_type">credit</field>
            <field name="create_date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <!-- Demo Wallet Transactions - Customer -->
        <record id="demo_wallet_transaction_customer_1" model="gamification.wallet.transaction">
            <field name="wallet_id" ref="demo_wallet_customer"/>
            <field name="amount">200</field>
            <field name="description">First purchase reward</field>
            <field name="transaction_type">credit</field>
            <field name="create_date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <!-- Demo Wallet Transactions - Loyal Customer -->
        <record id="demo_wallet_transaction_loyal_1" model="gamification.wallet.transaction">
            <field name="wallet_id" ref="demo_wallet_loyal"/>
            <field name="amount">300</field>
            <field name="description">Loyalty reward</field>
            <field name="transaction_type">credit</field>
            <field name="create_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="demo_wallet_transaction_loyal_2" model="gamification.wallet.transaction">
            <field name="wallet_id" ref="demo_wallet_loyal"/>
            <field name="amount">1000</field>
            <field name="description">High value purchase reward</field>
            <field name="transaction_type">credit</field>
            <field name="create_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <!-- Demo Coupon Programs for Integration -->
        <record id="demo_coupon_program_welcome" model="coupon.program">
            <field name="name">Welcome Discount</field>
            <field name="promo_code_usage">code_needed</field>
            <field name="program_type">promotion_program</field>
            <field name="discount_type">percentage</field>
            <field name="discount_percentage">10</field>
            <field name="discount_apply_on">on_order</field>
        </record>

        <record id="demo_coupon_program_loyalty" model="coupon.program">
            <field name="name">Loyalty Discount</field>
            <field name="promo_code_usage">code_needed</field>
            <field name="program_type">promotion_program</field>
            <field name="discount_type">percentage</field>
            <field name="discount_percentage">15</field>
            <field name="discount_apply_on">on_order</field>
        </record>

        <record id="demo_coupon_program_birthday" model="coupon.program">
            <field name="name">Birthday Discount</field>
            <field name="promo_code_usage">code_needed</field>
            <field name="program_type">promotion_program</field>
            <field name="discount_type">percentage</field>
            <field name="discount_percentage">25</field>
            <field name="discount_apply_on">on_order</field>
        </record>
    </data>
</odoo>
