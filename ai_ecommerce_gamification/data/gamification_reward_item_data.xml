<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Signup Scratch Card Reward Items -->
        <record id="reward_item_signup_coupon" model="gamification.reward.item">
            <field name="name">10% Off Coupon</field>
            <field name="reward_id" ref="reward_signup_scratch_card"/>
            <field name="item_type">coupon</field>
            <field name="probability">30</field>
            <field name="value">10</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_signup_wallet" model="gamification.reward.item">
            <field name="name">₹100 Wallet Credit</field>
            <field name="reward_id" ref="reward_signup_scratch_card"/>
            <field name="item_type">wallet_credit</field>
            <field name="probability">20</field>
            <field name="value">100</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_signup_free_shipping" model="gamification.reward.item">
            <field name="name">Free Shipping</field>
            <field name="reward_id" ref="reward_signup_scratch_card"/>
            <field name="item_type">free_shipping</field>
            <field name="probability">30</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_signup_no_reward" model="gamification.reward.item">
            <field name="name">Better Luck Next Time</field>
            <field name="reward_id" ref="reward_signup_scratch_card"/>
            <field name="item_type">no_reward</field>
            <field name="probability">20</field>
            <field name="max_quantity">0</field>
        </record>
        
        <!-- First Purchase Spin Wheel Reward Items -->
        <record id="reward_item_first_purchase_coupon_15" model="gamification.reward.item">
            <field name="name">15% Off Coupon</field>
            <field name="reward_id" ref="reward_first_purchase_spin_wheel"/>
            <field name="item_type">coupon</field>
            <field name="probability">15</field>
            <field name="value">15</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_first_purchase_coupon_10" model="gamification.reward.item">
            <field name="name">10% Off Coupon</field>
            <field name="reward_id" ref="reward_first_purchase_spin_wheel"/>
            <field name="item_type">coupon</field>
            <field name="probability">20</field>
            <field name="value">10</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_first_purchase_wallet_200" model="gamification.reward.item">
            <field name="name">₹200 Wallet Credit</field>
            <field name="reward_id" ref="reward_first_purchase_spin_wheel"/>
            <field name="item_type">wallet_credit</field>
            <field name="probability">15</field>
            <field name="value">200</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_first_purchase_wallet_100" model="gamification.reward.item">
            <field name="name">₹100 Wallet Credit</field>
            <field name="reward_id" ref="reward_first_purchase_spin_wheel"/>
            <field name="item_type">wallet_credit</field>
            <field name="probability">20</field>
            <field name="value">100</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_first_purchase_free_shipping" model="gamification.reward.item">
            <field name="name">Free Shipping</field>
            <field name="reward_id" ref="reward_first_purchase_spin_wheel"/>
            <field name="item_type">free_shipping</field>
            <field name="probability">15</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_first_purchase_no_reward" model="gamification.reward.item">
            <field name="name">Better Luck Next Time</field>
            <field name="reward_id" ref="reward_first_purchase_spin_wheel"/>
            <field name="item_type">no_reward</field>
            <field name="probability">15</field>
            <field name="max_quantity">0</field>
        </record>
        
        <!-- High Value Purchase Reward Items -->
        <record id="reward_item_high_value_coupon_20" model="gamification.reward.item">
            <field name="name">20% Off Coupon</field>
            <field name="reward_id" ref="reward_high_value_purchase"/>
            <field name="item_type">coupon</field>
            <field name="probability">20</field>
            <field name="value">20</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_high_value_wallet_500" model="gamification.reward.item">
            <field name="name">₹500 Wallet Credit</field>
            <field name="reward_id" ref="reward_high_value_purchase"/>
            <field name="item_type">wallet_credit</field>
            <field name="probability">20</field>
            <field name="value">500</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_high_value_wallet_1000" model="gamification.reward.item">
            <field name="name">₹1000 Wallet Credit</field>
            <field name="reward_id" ref="reward_high_value_purchase"/>
            <field name="item_type">wallet_credit</field>
            <field name="probability">10</field>
            <field name="value">1000</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_high_value_free_shipping" model="gamification.reward.item">
            <field name="name">Free Express Shipping</field>
            <field name="reward_id" ref="reward_high_value_purchase"/>
            <field name="item_type">free_shipping</field>
            <field name="probability">20</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_high_value_external_coupon" model="gamification.reward.item">
            <field name="name">Partner Store Coupon</field>
            <field name="reward_id" ref="reward_high_value_purchase"/>
            <field name="item_type">external_coupon</field>
            <field name="probability">20</field>
            <field name="coupon_code">PARTNER20</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_high_value_no_reward" model="gamification.reward.item">
            <field name="name">Better Luck Next Time</field>
            <field name="reward_id" ref="reward_high_value_purchase"/>
            <field name="item_type">no_reward</field>
            <field name="probability">10</field>
            <field name="max_quantity">0</field>
        </record>
        
        <!-- Birthday Reward Items -->
        <record id="reward_item_birthday_coupon" model="gamification.reward.item">
            <field name="name">25% Birthday Discount</field>
            <field name="reward_id" ref="reward_birthday"/>
            <field name="item_type">coupon</field>
            <field name="probability">40</field>
            <field name="value">25</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_birthday_wallet" model="gamification.reward.item">
            <field name="name">₹250 Birthday Credit</field>
            <field name="reward_id" ref="reward_birthday"/>
            <field name="item_type">wallet_credit</field>
            <field name="probability">40</field>
            <field name="value">250</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_birthday_free_shipping" model="gamification.reward.item">
            <field name="name">Free Birthday Shipping</field>
            <field name="reward_id" ref="reward_birthday"/>
            <field name="item_type">free_shipping</field>
            <field name="probability">20</field>
            <field name="max_quantity">0</field>
        </record>
        
        <!-- Loyalty Reward Items -->
        <record id="reward_item_loyalty_coupon" model="gamification.reward.item">
            <field name="name">15% Loyalty Discount</field>
            <field name="reward_id" ref="reward_loyalty"/>
            <field name="item_type">coupon</field>
            <field name="probability">50</field>
            <field name="value">15</field>
            <field name="max_quantity">0</field>
        </record>
        
        <record id="reward_item_loyalty_wallet" model="gamification.reward.item">
            <field name="name">₹300 Loyalty Credit</field>
            <field name="reward_id" ref="reward_loyalty"/>
            <field name="item_type">wallet_credit</field>
            <field name="probability">50</field>
            <field name="value">300</field>
            <field name="max_quantity">0</field>
        </record>
    </data>
</odoo>
