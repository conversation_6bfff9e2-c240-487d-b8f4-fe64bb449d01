<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Signup Reward -->
        <record id="reward_signup_scratch_card" model="gamification.reward">
            <field name="name">Welcome Scratch Card</field>
            <field name="reward_type">scratch_card</field>
            <field name="description">Welcome to our store! Scratch this card to reveal your welcome reward.</field>
            <field name="ui_theme">festive</field>
            <field name="display_mode">popup</field>
            <field name="scratch_card_template">default</field>
            <field name="max_rewards_per_user">1</field>
        </record>
        
        <!-- First Purchase Reward -->
        <record id="reward_first_purchase_spin_wheel" model="gamification.reward">
            <field name="name">First Purchase Spin Wheel</field>
            <field name="reward_type">spin_wheel</field>
            <field name="description">Congratulations on your first purchase! Spin the wheel to win a reward.</field>
            <field name="ui_theme">premium</field>
            <field name="display_mode">fullscreen</field>
            <field name="wheel_segments">8</field>
            <field name="wheel_colors">["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF", "#FFA500", "#800080"]</field>
            <field name="max_rewards_per_user">1</field>
        </record>
        
        <!-- High Value Purchase Reward -->
        <record id="reward_high_value_purchase" model="gamification.reward">
            <field name="name">Premium Spin Wheel</field>
            <field name="reward_type">spin_wheel</field>
            <field name="description">Thank you for your premium purchase! Spin the wheel for exclusive rewards.</field>
            <field name="ui_theme">premium</field>
            <field name="display_mode">fullscreen</field>
            <field name="wheel_segments">6</field>
            <field name="wheel_colors">["#FFD700", "#C0C0C0", "#B87333", "#4169E1", "#32CD32", "#FF4500"]</field>
            <field name="max_rewards_per_user">0</field>
        </record>
        
        <!-- Birthday Reward -->
        <record id="reward_birthday" model="gamification.reward">
            <field name="name">Birthday Scratch Card</field>
            <field name="reward_type">scratch_card</field>
            <field name="description">Happy Birthday! Scratch this card to reveal your special birthday reward.</field>
            <field name="ui_theme">birthday</field>
            <field name="display_mode">popup</field>
            <field name="scratch_card_template">festive</field>
            <field name="max_rewards_per_user">1</field>
            <field name="max_rewards_per_day">1</field>
        </record>
        
        <!-- Loyalty Reward -->
        <record id="reward_loyalty" model="gamification.reward">
            <field name="name">Loyalty Reward</field>
            <field name="reward_type">direct</field>
            <field name="description">Thank you for being a loyal customer! Here's a special reward just for you.</field>
            <field name="ui_theme">default</field>
            <field name="display_mode">popup</field>
            <field name="max_rewards_per_user">0</field>
        </record>
    </data>
</odoo>
