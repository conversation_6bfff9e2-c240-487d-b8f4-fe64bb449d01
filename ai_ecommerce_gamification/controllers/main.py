# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import http, _
from odoo.http import request, Response
import json
import logging
import werkzeug

_logger = logging.getLogger(__name__)

class GamificationController(http.Controller):
    
    @http.route(['/gamification/rewards'], type='http', auth='user', website=True)
    def rewards_page(self, **kw):
        """Display user rewards page"""
        user = request.env.user
        
        # Get user rewards
        rewards = user.reward_ids.filtered(lambda r: r.state in ['pending', 'claimed'])
        
        # Get wallet
        wallet = user.get_wallet()
        
        values = {
            'rewards': rewards,
            'wallet': wallet,
            'page_name': 'rewards',
        }
        
        return request.render("ai_ecommerce_gamification.rewards_page", values)
    
    @http.route(['/gamification/wallet'], type='http', auth='user', website=True)
    def wallet_page(self, **kw):
        """Display user wallet page"""
        user = request.env.user
        
        # Get wallet
        wallet = user.get_wallet()
        
        # Get transaction history
        transactions = wallet.transaction_ids.sorted('create_date', reverse=True)
        
        values = {
            'wallet': wallet,
            'transactions': transactions,
            'page_name': 'wallet',
        }
        
        return request.render("ai_ecommerce_gamification.wallet_page", values)
    
    @http.route(['/gamification/scratch-card/<int:reward_id>'], type='http', auth='user', website=True)
    def scratch_card(self, reward_id, **kw):
        """Display scratch card page"""
        user = request.env.user
        reward = request.env['gamification.reward'].browse(reward_id)
        
        if not reward.exists() or reward.reward_type != 'scratch_card':
            return werkzeug.utils.redirect('/gamification/rewards')
        
        # Check eligibility
        eligible, message = reward.check_user_eligibility(user)
        
        values = {
            'reward': reward,
            'eligible': eligible,
            'message': message,
            'page_name': 'scratch_card',
        }
        
        return request.render("ai_ecommerce_gamification.scratch_card_page", values)
    
    @http.route(['/gamification/spin-wheel/<int:reward_id>'], type='http', auth='user', website=True)
    def spin_wheel(self, reward_id, **kw):
        """Display spin wheel page"""
        user = request.env.user
        reward = request.env['gamification.reward'].browse(reward_id)
        
        if not reward.exists() or reward.reward_type != 'spin_wheel':
            return werkzeug.utils.redirect('/gamification/rewards')
        
        # Check eligibility
        eligible, message = reward.check_user_eligibility(user)
        
        # Get wheel data
        wheel_data = reward.generate_wheel_data()
        
        values = {
            'reward': reward,
            'eligible': eligible,
            'message': message,
            'wheel_data': json.dumps(wheel_data),
            'page_name': 'spin_wheel',
        }
        
        return request.render("ai_ecommerce_gamification.spin_wheel_page", values)
    
    # API Endpoints
    
    @http.route(['/gamification/api/claim-reward'], type='json', auth='user', website=True)
    def claim_reward(self, **kw):
        """API to claim a reward"""
        user = request.env.user
        reward_id = kw.get('reward_id')
        
        if not reward_id:
            return {'success': False, 'message': _("No reward specified")}
        
        reward = request.env['gamification.reward'].browse(int(reward_id))
        if not reward.exists():
            return {'success': False, 'message': _("Reward not found")}
        
        # Check eligibility
        eligible, message = reward.check_user_eligibility(user)
        if not eligible:
            return {'success': False, 'message': message}
        
        # Claim reward
        result, message = user.claim_reward(reward.id)
        
        # Get the latest user reward
        user_reward = request.env['gamification.user.reward'].search([
            ('user_id', '=', user.id),
            ('reward_id', '=', reward.id),
        ], order='create_date desc', limit=1)
        
        # Generate reward data for UI
        reward_data = user_reward.generate_reward_data() if user_reward else {}
        
        return {
            'success': result,
            'message': message,
            'reward': reward_data,
        }
    
    @http.route(['/gamification/api/get-rewards'], type='json', auth='user', website=True)
    def get_rewards(self, **kw):
        """API to get available rewards"""
        user = request.env.user
        
        available_rewards = user.get_available_rewards()
        
        return {
            'success': True,
            'rewards': available_rewards,
        }
    
    @http.route(['/gamification/api/get-wallet'], type='json', auth='user', website=True)
    def get_wallet(self, **kw):
        """API to get wallet info"""
        user = request.env.user
        
        wallet = user.get_wallet()
        transactions = wallet.get_transaction_history(limit=5)
        
        return {
            'success': True,
            'balance': wallet.balance,
            'currency': wallet.currency_id.symbol,
            'transactions': transactions,
        }
    
    @http.route(['/gamification/api/scratch-result'], type='json', auth='user', website=True)
    def scratch_result(self, **kw):
        """API to get scratch card result"""
        user = request.env.user
        reward_id = kw.get('reward_id')
        
        if not reward_id:
            return {'success': False, 'message': _("No reward specified")}
        
        # First claim the reward
        result = self.claim_reward(reward_id=reward_id)
        
        if not result.get('success'):
            return result
        
        # Return the reward data
        return result
    
    @http.route(['/gamification/api/spin-result'], type='json', auth='user', website=True)
    def spin_result(self, **kw):
        """API to get spin wheel result"""
        user = request.env.user
        reward_id = kw.get('reward_id')
        
        if not reward_id:
            return {'success': False, 'message': _("No reward specified")}
        
        # First claim the reward
        result = self.claim_reward(reward_id=reward_id)
        
        if not result.get('success'):
            return result
        
        # Return the reward data with segment index for the wheel
        reward_data = result.get('reward', {})
        
        # Find the segment index for the UI
        reward = request.env['gamification.reward'].browse(int(reward_id))
        wheel_data = reward.generate_wheel_data()
        
        if reward_data and 'reward_item_id' in reward_data:
            for i, segment in enumerate(wheel_data.get('segments', [])):
                if segment.get('id') == reward_data['reward_item_id']:
                    reward_data['segment_index'] = i
                    break
        
        result['reward'] = reward_data
        return result
