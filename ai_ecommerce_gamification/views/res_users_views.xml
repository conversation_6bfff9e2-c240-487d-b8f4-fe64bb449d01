<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- User Form View -->
    <record id="view_users_form_gamification" model="ir.ui.view">
        <field name="name">res.users.form.gamification</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form"/>
        <field name="arch" type="xml">
            <notebook position="inside">
                <page string="Gamification" name="gamification" groups="ai_ecommerce_gamification.group_gamification_user">
                    <group>
                        <group string="Wallet">
                            <field name="wallet_balance" widget="monetary"/>
                            <button name="get_wallet" string="View Wallet" type="object" class="oe_link"/>
                        </group>
                        <group string="Rewards">
                            <field name="total_rewards_count"/>
                            <field name="active_rewards_count"/>
                            <button name="%(action_gamification_user_reward)d" string="View Rewards" type="action" class="oe_link" context="{'search_default_user_id': active_id}"/>
                        </group>
                    </group>
                    <group string="Available Rewards">
                        <button name="get_available_rewards" string="Check Available Rewards" type="object" class="oe_highlight"/>
                    </group>
                </page>
            </notebook>
        </field>
    </record>
</odoo>
