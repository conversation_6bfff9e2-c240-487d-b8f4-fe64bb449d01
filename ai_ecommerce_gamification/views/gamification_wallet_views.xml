<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Wallet Form View -->
    <record id="view_gamification_wallet_form" model="ir.ui.view">
        <field name="name">gamification.wallet.form</field>
        <field name="model">gamification.wallet</field>
        <field name="arch" type="xml">
            <form string="User Wallet">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(action_gamification_user_reward)d" type="action" class="oe_stat_button" icon="fa-gift" context="{'search_default_user_id': user_id}">
                            <div class="o_stat_info">
                                <field name="user_id" invisible="1"/>
                                <span class="o_stat_text">Rewards</span>
                            </div>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="user_id" placeholder="User"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="balance" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            <field name="currency_id" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="total_earned" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            <field name="total_spent" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            <field name="last_transaction_date"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Transactions" name="transactions">
                            <field name="transaction_ids" nolabel="1">
                                <tree string="Transactions" decoration-success="transaction_type == 'credit'" decoration-danger="transaction_type == 'debit'">
                                    <field name="create_date"/>
                                    <field name="transaction_type"/>
                                    <field name="amount" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                                    <field name="description"/>
                                    <field name="currency_id" invisible="1"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    
    <!-- Wallet Tree View -->
    <record id="view_gamification_wallet_tree" model="ir.ui.view">
        <field name="name">gamification.wallet.tree</field>
        <field name="model">gamification.wallet</field>
        <field name="arch" type="xml">
            <tree string="User Wallets">
                <field name="user_id"/>
                <field name="balance" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                <field name="total_earned" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                <field name="total_spent" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                <field name="last_transaction_date"/>
                <field name="currency_id" invisible="1"/>
            </tree>
        </field>
    </record>
    
    <!-- Wallet Search View -->
    <record id="view_gamification_wallet_search" model="ir.ui.view">
        <field name="name">gamification.wallet.search</field>
        <field name="model">gamification.wallet</field>
        <field name="arch" type="xml">
            <search string="Search Wallets">
                <field name="user_id"/>
                <filter string="Has Balance" name="has_balance" domain="[('balance', '>', 0)]"/>
                <filter string="No Balance" name="no_balance" domain="[('balance', '=', 0)]"/>
                <filter string="Recent Activity" name="recent_activity" domain="[('last_transaction_date', '>=', (context_today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Currency" name="group_by_currency" domain="[]" context="{'group_by': 'currency_id'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <!-- Wallet Transaction Form View -->
    <record id="view_gamification_wallet_transaction_form" model="ir.ui.view">
        <field name="name">gamification.wallet.transaction.form</field>
        <field name="model">gamification.wallet.transaction</field>
        <field name="arch" type="xml">
            <form string="Wallet Transaction">
                <sheet>
                    <group>
                        <group>
                            <field name="wallet_id"/>
                            <field name="user_id"/>
                            <field name="transaction_type"/>
                        </group>
                        <group>
                            <field name="amount" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            <field name="currency_id" invisible="1"/>
                            <field name="create_date" readonly="1"/>
                        </group>
                    </group>
                    <group>
                        <field name="description" placeholder="Transaction description..."/>
                    </group>
                    <group string="Related Document" attrs="{'invisible': [('model', '=', False)]}">
                        <field name="model"/>
                        <field name="res_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    
    <!-- Wallet Transaction Tree View -->
    <record id="view_gamification_wallet_transaction_tree" model="ir.ui.view">
        <field name="name">gamification.wallet.transaction.tree</field>
        <field name="model">gamification.wallet.transaction</field>
        <field name="arch" type="xml">
            <tree string="Wallet Transactions" decoration-success="transaction_type == 'credit'" decoration-danger="transaction_type == 'debit'">
                <field name="create_date"/>
                <field name="user_id"/>
                <field name="transaction_type"/>
                <field name="amount" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                <field name="description"/>
                <field name="currency_id" invisible="1"/>
            </tree>
        </field>
    </record>
    
    <!-- Wallet Transaction Search View -->
    <record id="view_gamification_wallet_transaction_search" model="ir.ui.view">
        <field name="name">gamification.wallet.transaction.search</field>
        <field name="model">gamification.wallet.transaction</field>
        <field name="arch" type="xml">
            <search string="Search Transactions">
                <field name="user_id"/>
                <field name="description"/>
                <separator/>
                <filter string="Credits" name="credit" domain="[('transaction_type', '=', 'credit')]"/>
                <filter string="Debits" name="debit" domain="[('transaction_type', '=', 'debit')]"/>
                <filter string="Today" name="today" domain="[('create_date', '>=', time.strftime('%Y-%m-%d 00:00:00')), ('create_date', '&lt;=', time.strftime('%Y-%m-%d 23:59:59'))]"/>
                <filter string="This Week" name="this_week" domain="[('create_date', '>=', (context_today() + relativedelta(weeks=-1,days=1,weekday=0)).strftime('%Y-%m-%d')), ('create_date', '&lt;=', (context_today() + relativedelta(weekday=6)).strftime('%Y-%m-%d'))]"/>
                <filter string="This Month" name="this_month" domain="[('create_date', '>=', (context_today() + relativedelta(day=1)).strftime('%Y-%m-%d')), ('create_date', '&lt;=', (context_today() + relativedelta(months=1, day=1, days=-1)).strftime('%Y-%m-%d'))]"/>
                <group expand="0" string="Group By">
                    <filter string="User" name="group_by_user" domain="[]" context="{'group_by': 'user_id'}"/>
                    <filter string="Type" name="group_by_type" domain="[]" context="{'group_by': 'transaction_type'}"/>
                    <filter string="Date" name="group_by_date" domain="[]" context="{'group_by': 'create_date:day'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <!-- Wallet Action -->
    <record id="action_gamification_wallet" model="ir.actions.act_window">
        <field name="name">User Wallets</field>
        <field name="res_model">gamification.wallet</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_gamification_wallet_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No wallets yet
            </p>
            <p>
                Wallets are created automatically when users earn wallet credits through the gamification system.
            </p>
        </field>
    </record>
    
    <!-- Wallet Transaction Action -->
    <record id="action_gamification_wallet_transaction" model="ir.actions.act_window">
        <field name="name">Wallet Transactions</field>
        <field name="res_model">gamification.wallet.transaction</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_gamification_wallet_transaction_search"/>
        <field name="context">{'search_default_group_by_user': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No transactions yet
            </p>
            <p>
                Wallet transactions are created when users earn or spend wallet credits.
            </p>
        </field>
    </record>
</odoo>
