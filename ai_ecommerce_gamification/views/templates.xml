<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add Gamification to My Account Menu -->
    <template id="portal_my_home_gamification" name="Portal My Home : Gamification" inherit_id="portal.portal_my_home">
        <xpath expr="//div[hasclass('o_portal_docs')]" position="inside">
            <t t-if="request.website.gamification_enabled">
                <div t-if="request.website.show_rewards_in_account" class="o_portal_gamification_rewards mb-4">
                    <h3 class="page-header">
                        <a href="/gamification/rewards">Your Rewards</a>
                        <small class="float-end">
                            <t t-set="reward_count" t-value="request.env['gamification.user.reward'].search_count([('user_id', '=', request.env.user.id), ('state', 'in', ['pending', 'claimed'])])"/>
                            <t t-esc="reward_count"/> Rewards
                        </small>
                    </h3>
                    <div t-if="reward_count" class="row">
                        <div class="col-12">
                            <a href="/gamification/rewards" class="btn btn-secondary btn-block">
                                <i class="fa fa-gift"/> View Your Rewards
                            </a>
                        </div>
                    </div>
                    <div t-else="" class="alert alert-info">
                        <p class="mb-0">You don't have any rewards yet.</p>
                    </div>
                </div>
                
                <div t-if="request.website.show_wallet_in_account" class="o_portal_gamification_wallet mb-4">
                    <h3 class="page-header">
                        <a href="/gamification/wallet">Your Wallet</a>
                        <small class="float-end">
                            <t t-set="wallet" t-value="request.env.user.get_wallet()"/>
                            <span t-field="wallet.balance" t-options="{'widget': 'monetary', 'display_currency': wallet.currency_id}"/>
                        </small>
                    </h3>
                    <div class="row">
                        <div class="col-12">
                            <a href="/gamification/wallet" class="btn btn-secondary btn-block">
                                <i class="fa fa-wallet"/> View Your Wallet
                            </a>
                        </div>
                    </div>
                </div>
            </t>
        </xpath>
    </template>
    
    <!-- Add Gamification to Portal Layout -->
    <template id="portal_layout_gamification" name="Portal Layout : Gamification" inherit_id="portal.portal_breadcrumbs">
        <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
            <t t-if="request.website.gamification_enabled">
                <li t-if="page_name == 'rewards'" class="breadcrumb-item">
                    <a t-if="rewards" t-attf-href="/gamification/rewards">Rewards</a>
                    <t t-else="">Rewards</t>
                </li>
                <li t-if="page_name == 'wallet'" class="breadcrumb-item">
                    <a t-if="wallet" t-attf-href="/gamification/wallet">Wallet</a>
                    <t t-else="">Wallet</t>
                </li>
                <li t-if="page_name == 'scratch_card'" class="breadcrumb-item">
                    <a t-attf-href="/gamification/rewards">Rewards</a>
                </li>
                <li t-if="page_name == 'scratch_card'" class="breadcrumb-item active">
                    Scratch Card
                </li>
                <li t-if="page_name == 'spin_wheel'" class="breadcrumb-item">
                    <a t-attf-href="/gamification/rewards">Rewards</a>
                </li>
                <li t-if="page_name == 'spin_wheel'" class="breadcrumb-item active">
                    Spin the Wheel
                </li>
            </t>
        </xpath>
    </template>
    
    <!-- Rewards Page Template -->
    <template id="rewards_page" name="Rewards Page">
        <t t-call="portal.portal_layout">
            <div class="container mt-4 mb-5">
                <div class="row">
                    <div class="col-12">
                        <h1 class="mb-4">Your Rewards</h1>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Wallet Balance</h5>
                            </div>
                            <div class="card-body text-center">
                                <h2 class="display-4 mb-3">
                                    <span t-field="wallet.balance" t-options="{'widget': 'monetary', 'display_currency': wallet.currency_id}"/>
                                </h2>
                                <a href="/gamification/wallet" class="btn btn-outline-primary">
                                    <i class="fa fa-wallet me-2"></i> View Wallet
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-8 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">Available Rewards</h5>
                            </div>
                            <div class="card-body">
                                <div class="row" id="available-rewards-container">
                                    <div class="col-12 text-center py-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2">Loading available rewards...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">Your Reward History</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Reward</th>
                                                <th>Type</th>
                                                <th>Value</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <t t-if="rewards">
                                                <t t-foreach="rewards" t-as="reward">
                                                    <tr>
                                                        <td><span t-field="reward.create_date" t-options="{'widget': 'datetime'}"/></td>
                                                        <td><span t-field="reward.name"/></td>
                                                        <td>
                                                            <span t-if="reward.item_type == 'coupon'" class="badge bg-primary">Coupon</span>
                                                            <span t-elif="reward.item_type == 'external_coupon'" class="badge bg-info">External Coupon</span>
                                                            <span t-elif="reward.item_type == 'wallet_credit'" class="badge bg-success">Wallet Credit</span>
                                                            <span t-elif="reward.item_type == 'free_shipping'" class="badge bg-warning">Free Shipping</span>
                                                            <span t-elif="reward.item_type == 'free_product'" class="badge bg-danger">Free Product</span>
                                                            <span t-elif="reward.item_type == 'discount'" class="badge bg-secondary">Discount</span>
                                                            <span t-else="" class="badge bg-dark">Other</span>
                                                        </td>
                                                        <td>
                                                            <t t-if="reward.item_type == 'wallet_credit'">
                                                                <span t-field="reward.value" t-options="{'widget': 'monetary', 'display_currency': wallet.currency_id}"/>
                                                            </t>
                                                            <t t-elif="reward.item_type == 'discount'">
                                                                <span t-field="reward.value"/>%
                                                            </t>
                                                            <t t-elif="reward.item_type in ['coupon', 'external_coupon']">
                                                                <span t-field="reward.code"/>
                                                            </t>
                                                            <t t-elif="reward.item_type == 'free_product'">
                                                                <span t-field="reward.product_id.name"/>
                                                            </t>
                                                            <t t-else="">
                                                                -
                                                            </t>
                                                        </td>
                                                        <td>
                                                            <span t-if="reward.state == 'pending'" class="badge bg-warning">Pending</span>
                                                            <span t-elif="reward.state == 'claimed'" class="badge bg-success">Claimed</span>
                                                            <span t-elif="reward.state == 'expired'" class="badge bg-danger">Expired</span>
                                                            <span t-elif="reward.state == 'failed'" class="badge bg-dark">Failed</span>
                                                        </td>
                                                        <td>
                                                            <t t-if="reward.state == 'pending'">
                                                                <t t-if="reward.reward_type == 'scratch_card'">
                                                                    <a t-att-href="'/gamification/scratch-card/%s' % reward.reward_id.id" class="btn btn-sm btn-primary">
                                                                        Scratch Card
                                                                    </a>
                                                                </t>
                                                                <t t-elif="reward.reward_type == 'spin_wheel'">
                                                                    <a t-att-href="'/gamification/spin-wheel/%s' % reward.reward_id.id" class="btn btn-sm btn-primary">
                                                                        Spin Wheel
                                                                    </a>
                                                                </t>
                                                                <t t-else="">
                                                                    <button class="btn btn-sm btn-primary claim-reward-btn" t-att-data-reward-id="reward.reward_id.id">
                                                                        Claim
                                                                    </button>
                                                                </t>
                                                            </t>
                                                        </td>
                                                    </tr>
                                                </t>
                                            </t>
                                            <tr t-if="not rewards">
                                                <td colspan="6" class="text-center py-4">
                                                    <p class="text-muted mb-0">You don't have any rewards yet.</p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Reward Claimed Modal -->
            <div class="modal fade" id="rewardClaimedModal" tabindex="-1" aria-labelledby="rewardClaimedModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title" id="rewardClaimedModalLabel">Reward Claimed!</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body text-center py-4">
                            <div class="reward-icon mb-3">
                                <i class="fa fa-gift fa-4x text-success"></i>
                            </div>
                            <h4 id="reward-name" class="mb-3">Your Reward</h4>
                            <p id="reward-message" class="mb-4">You've successfully claimed your reward!</p>
                            <div id="reward-details" class="mb-3">
                                <!-- Reward details will be inserted here -->
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <script type="text/javascript">
                $(document).ready(function() {
                    // Load available rewards
                    function loadAvailableRewards() {
                        $.ajax({
                            url: '/gamification/api/get-rewards',
                            type: 'POST',
                            dataType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                jsonrpc: "2.0",
                                method: "call",
                                params: {},
                                id: Math.floor(Math.random() * 1000000)
                            }),
                            success: function(data) {
                                if (data.result && data.result.success) {
                                    var rewards = data.result.rewards;
                                    var container = $('#available-rewards-container');
                                    container.empty();
                                    
                                    if (rewards.length > 0) {
                                        rewards.forEach(function(reward) {
                                            var card = $('<div class="col-md-6 col-lg-4 mb-3">' +
                                                '<div class="card h-100">' +
                                                    '<div class="card-body text-center">' +
                                                        '<h5 class="card-title">' + reward.name + '</h5>' +
                                                        '<p class="card-text">' + reward.message + '</p>' +
                                                    '</div>' +
                                                    '<div class="card-footer bg-white border-top-0 text-center">' +
                                                        '<button class="btn btn-primary claim-reward-btn" data-reward-id="' + reward.id + '">' +
                                                            (reward.type === 'scratch_card' ? 'Scratch Card' : 
                                                             reward.type === 'spin_wheel' ? 'Spin Wheel' : 'Claim Reward') +
                                                        '</button>' +
                                                    '</div>' +
                                                '</div>' +
                                            '</div>');
                                            container.append(card);
                                        });
                                    } else {
                                        container.html('<div class="col-12 text-center py-3">' +
                                            '<p class="text-muted mb-0">No rewards available at the moment.</p>' +
                                        '</div>');
                                    }
                                    
                                    // Attach event handlers to new buttons
                                    attachClaimButtonHandlers();
                                }
                            },
                            error: function() {
                                $('#available-rewards-container').html('<div class="col-12 text-center py-3">' +
                                    '<p class="text-danger mb-0">Failed to load rewards. Please try again later.</p>' +
                                '</div>');
                            }
                        });
                    }
                    
                    // Handle claim reward button clicks
                    function attachClaimButtonHandlers() {
                        $('.claim-reward-btn').off('click').on('click', function() {
                            var rewardId = $(this).data('reward-id');
                            var rewardType = $(this).text().trim();
                            
                            if (rewardType === 'Scratch Card') {
                                window.location.href = '/gamification/scratch-card/' + rewardId;
                                return;
                            } else if (rewardType === 'Spin Wheel') {
                                window.location.href = '/gamification/spin-wheel/' + rewardId;
                                return;
                            }
                            
                            // For direct rewards
                            $.ajax({
                                url: '/gamification/api/claim-reward',
                                type: 'POST',
                                dataType: 'json',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    jsonrpc: "2.0",
                                    method: "call",
                                    params: {
                                        reward_id: rewardId
                                    },
                                    id: Math.floor(Math.random() * 1000000)
                                }),
                                success: function(data) {
                                    if (data.result) {
                                        showRewardModal(data.result);
                                        // Reload the page after closing the modal
                                        $('#rewardClaimedModal').on('hidden.bs.modal', function() {
                                            location.reload();
                                        });
                                    }
                                },
                                error: function() {
                                    alert('Failed to claim reward. Please try again later.');
                                }
                            });
                        });
                    }
                    
                    // Show reward modal
                    function showRewardModal(result) {
                        $('#reward-name').text(result.reward ? result.reward.name : 'Your Reward');
                        $('#reward-message').text(result.message);
                        
                        var detailsHtml = '';
                        if (result.reward) {
                            if (result.reward.item_type === 'coupon' || result.reward.item_type === 'external_coupon') {
                                detailsHtml = '<div class="alert alert-success">' +
                                    '<strong>Your Coupon Code:</strong> ' + result.reward.code +
                                '</div>';
                            } else if (result.reward.item_type === 'wallet_credit') {
                                detailsHtml = '<div class="alert alert-success">' +
                                    '<strong>Amount Added to Wallet:</strong> ' + result.reward.value +
                                '</div>';
                            }
                        }
                        
                        $('#reward-details').html(detailsHtml);
                        var modal = new bootstrap.Modal(document.getElementById('rewardClaimedModal'));
                        modal.show();
                    }
                    
                    // Load rewards on page load
                    loadAvailableRewards();
                    
                    // Attach handlers to existing buttons
                    attachClaimButtonHandlers();
                });
            </script>
        </t>
    </template>
    
    <!-- Wallet Page Template -->
    <template id="wallet_page" name="Wallet Page">
        <t t-call="portal.portal_layout">
            <div class="container mt-4 mb-5">
                <div class="row">
                    <div class="col-12">
                        <h1 class="mb-4">Your Wallet</h1>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Current Balance</h5>
                            </div>
                            <div class="card-body text-center">
                                <h2 class="display-4 mb-3">
                                    <span t-field="wallet.balance" t-options="{'widget': 'monetary', 'display_currency': wallet.currency_id}"/>
                                </h2>
                                <div class="row mt-4">
                                    <div class="col-6">
                                        <h5>Total Earned</h5>
                                        <p class="text-success">
                                            <span t-field="wallet.total_earned" t-options="{'widget': 'monetary', 'display_currency': wallet.currency_id}"/>
                                        </p>
                                    </div>
                                    <div class="col-6">
                                        <h5>Total Spent</h5>
                                        <p class="text-danger">
                                            <span t-field="wallet.total_spent" t-options="{'widget': 'monetary', 'display_currency': wallet.currency_id}"/>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-8 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">Transaction History</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Type</th>
                                                <th>Amount</th>
                                                <th>Description</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <t t-if="transactions">
                                                <t t-foreach="transactions" t-as="transaction">
                                                    <tr t-attf-class="{{ 'table-success' if transaction.transaction_type == 'credit' else 'table-danger' }}">
                                                        <td><span t-field="transaction.create_date" t-options="{'widget': 'datetime'}"/></td>
                                                        <td>
                                                            <span t-if="transaction.transaction_type == 'credit'" class="badge bg-success">Credit</span>
                                                            <span t-else="" class="badge bg-danger">Debit</span>
                                                        </td>
                                                        <td>
                                                            <span t-field="transaction.amount" t-options="{'widget': 'monetary', 'display_currency': transaction.currency_id}"/>
                                                        </td>
                                                        <td><span t-field="transaction.description"/></td>
                                                    </tr>
                                                </t>
                                            </t>
                                            <tr t-if="not transactions">
                                                <td colspan="4" class="text-center py-4">
                                                    <p class="text-muted mb-0">No transactions yet.</p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">How to Earn More</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3 mb-md-0">
                                        <div class="text-center">
                                            <i class="fa fa-shopping-cart fa-3x mb-3 text-primary"></i>
                                            <h5>Make Purchases</h5>
                                            <p>Earn wallet credits with every purchase you make.</p>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3 mb-md-0">
                                        <div class="text-center">
                                            <i class="fa fa-gift fa-3x mb-3 text-primary"></i>
                                            <h5>Claim Rewards</h5>
                                            <p>Check available rewards regularly for wallet credit opportunities.</p>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center">
                                            <i class="fa fa-user-plus fa-3x mb-3 text-primary"></i>
                                            <h5>Refer Friends</h5>
                                            <p>Invite friends and earn credits when they make their first purchase.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>
