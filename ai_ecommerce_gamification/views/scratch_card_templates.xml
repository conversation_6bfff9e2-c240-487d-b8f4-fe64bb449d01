<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Scratch Card Page Template -->
    <template id="scratch_card_page" name="Scratch Card">
        <t t-call="portal.portal_layout">
            <div class="container mt-4 mb-5">
                <div class="row">
                    <div class="col-12 text-center">
                        <h1 class="mb-4">Scratch Card</h1>
                        <p class="lead mb-5">Scratch the card below to reveal your reward!</p>
                    </div>
                </div>
                
                <div class="row justify-content-center">
                    <div class="col-md-8 col-lg-6">
                        <div class="card">
                            <div class="card-body p-0">
                                <div t-if="eligible" id="scratch-card-container" class="position-relative" t-att-data-reward-id="reward.id">
                                    <div id="scratch-card-overlay" class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                                        <div class="text-center p-5">
                                            <h3 class="mb-4">Scratch Here to Reveal Your Reward</h3>
                                            <p>Use your finger or mouse to scratch the card</p>
                                        </div>
                                    </div>
                                    <div id="scratch-card-result" class="position-relative text-center p-5" style="display: none;">
                                        <div class="reward-icon mb-4">
                                            <i class="fa fa-gift fa-4x text-success"></i>
                                        </div>
                                        <h3 id="reward-name" class="mb-3">Your Reward</h3>
                                        <p id="reward-message" class="mb-4">Scratching to reveal your reward...</p>
                                        <div id="reward-details" class="mb-3">
                                            <!-- Reward details will be inserted here -->
                                        </div>
                                    </div>
                                </div>
                                <div t-if="not eligible" class="text-center p-5">
                                    <div class="alert alert-warning">
                                        <h4 class="alert-heading">Not Eligible</h4>
                                        <p t-esc="message"></p>
                                    </div>
                                    <a href="/gamification/rewards" class="btn btn-primary mt-3">
                                        Back to Rewards
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <link rel="stylesheet" href="/ai_ecommerce_gamification/static/src/css/gamification.css"/>
            <script type="text/javascript" src="/ai_ecommerce_gamification/static/src/js/scratch_card.js"></script>
            
            <script type="text/javascript">
                $(document).ready(function() {
                    if ($('#scratch-card-container').length) {
                        initScratchCard();
                    }
                    
                    function initScratchCard() {
                        var container = document.getElementById('scratch-card-container');
                        var overlay = document.getElementById('scratch-card-overlay');
                        var result = document.getElementById('scratch-card-result');
                        var rewardId = container.dataset.rewardId;
                        
                        // Initialize scratch card
                        var scratchCard = new ScratchCard({
                            container: overlay,
                            brushSize: 40,
                            coverColor: '#AAAAAA',
                            coverImage: '/ai_ecommerce_gamification/static/src/img/scratch_card_cover.jpg',
                            revealPercentage: 50,
                            onRevealed: function() {
                                // When enough is scratched, reveal the reward
                                revealReward();
                            }
                        });
                        
                        scratchCard.init();
                        
                        function revealReward() {
                            // Show the result
                            $(overlay).fadeOut(500, function() {
                                $(result).fadeIn(500);
                                
                                // Call API to get the reward
                                $.ajax({
                                    url: '/gamification/api/scratch-result',
                                    type: 'POST',
                                    dataType: 'json',
                                    contentType: 'application/json',
                                    data: JSON.stringify({
                                        jsonrpc: "2.0",
                                        method: "call",
                                        params: {
                                            reward_id: rewardId
                                        },
                                        id: Math.floor(Math.random() * 1000000)
                                    }),
                                    success: function(data) {
                                        if (data.result) {
                                            updateRewardDisplay(data.result);
                                        }
                                    },
                                    error: function() {
                                        $('#reward-message').text('Failed to claim reward. Please try again later.');
                                    }
                                });
                            });
                        }
                        
                        function updateRewardDisplay(result) {
                            $('#reward-name').text(result.reward ? result.reward.name : 'Your Reward');
                            $('#reward-message').text(result.message);
                            
                            var detailsHtml = '';
                            if (result.reward) {
                                if (result.reward.item_type === 'coupon' || result.reward.item_type === 'external_coupon') {
                                    detailsHtml = '<div class="alert alert-success">' +
                                        '<strong>Your Coupon Code:</strong> ' + result.reward.code +
                                    '</div>';
                                } else if (result.reward.item_type === 'wallet_credit') {
                                    detailsHtml = '<div class="alert alert-success">' +
                                        '<strong>Amount Added to Wallet:</strong> ' + result.reward.value +
                                    '</div>';
                                }
                            }
                            
                            $('#reward-details').html(detailsHtml);
                            
                            // Add a button to go back to rewards
                            $('#reward-details').append('<a href="/gamification/rewards" class="btn btn-primary mt-3">Back to Rewards</a>');
                        }
                    }
                });
            </script>
        </t>
    </template>
</odoo>
