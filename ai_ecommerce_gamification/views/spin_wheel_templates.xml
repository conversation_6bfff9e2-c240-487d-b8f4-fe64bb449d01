<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Spin Wheel Page Template -->
    <template id="spin_wheel_page" name="Spin the Wheel">
        <t t-call="portal.portal_layout">
            <div class="container mt-4 mb-5">
                <div class="row">
                    <div class="col-12 text-center">
                        <h1 class="mb-4">Spin the Wheel</h1>
                        <p class="lead mb-5">Spin the wheel below to win exciting rewards!</p>
                    </div>
                </div>
                
                <div class="row justify-content-center">
                    <div class="col-md-10 col-lg-8">
                        <div class="card">
                            <div class="card-body p-0">
                                <div t-if="eligible" id="spin-wheel-container" class="position-relative text-center p-4" t-att-data-reward-id="reward.id" t-att-data-wheel-data="wheel_data">
                                    <div id="wheel-canvas-container" class="mb-4">
                                        <canvas id="wheel-canvas" width="400" height="400"></canvas>
                                        <div id="wheel-pointer"></div>
                                    </div>
                                    
                                    <button id="spin-button" class="btn btn-lg btn-primary px-5 py-3">
                                        <i class="fa fa-sync-alt me-2"></i> Spin the Wheel
                                    </button>
                                    
                                    <div id="spin-result" class="mt-4" style="display: none;">
                                        <div class="alert alert-success">
                                            <h4 id="result-title" class="alert-heading">Congratulations!</h4>
                                            <p id="result-message">You've won a reward!</p>
                                        </div>
                                        <div id="result-details">
                                            <!-- Reward details will be inserted here -->
                                        </div>
                                        <a href="/gamification/rewards" class="btn btn-secondary mt-3">
                                            Back to Rewards
                                        </a>
                                    </div>
                                </div>
                                <div t-if="not eligible" class="text-center p-5">
                                    <div class="alert alert-warning">
                                        <h4 class="alert-heading">Not Eligible</h4>
                                        <p t-esc="message"></p>
                                    </div>
                                    <a href="/gamification/rewards" class="btn btn-primary mt-3">
                                        Back to Rewards
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <link rel="stylesheet" href="/ai_ecommerce_gamification/static/src/css/gamification.css"/>
            <script type="text/javascript" src="/ai_ecommerce_gamification/static/src/js/spin_wheel.js"></script>
            
            <script type="text/javascript">
                $(document).ready(function() {
                    if ($('#spin-wheel-container').length) {
                        initSpinWheel();
                    }
                    
                    function initSpinWheel() {
                        var container = document.getElementById('spin-wheel-container');
                        var rewardId = container.dataset.rewardId;
                        var wheelData = JSON.parse(container.dataset.wheelData || '{}');
                        
                        // Initialize spin wheel
                        var spinWheel = new SpinWheel({
                            canvas: document.getElementById('wheel-canvas'),
                            segments: wheelData.segments || [],
                            onSpinEnd: function(segmentIndex) {
                                // When spin ends, show the reward
                                showReward(segmentIndex);
                            }
                        });
                        
                        spinWheel.init();
                        
                        // Handle spin button click
                        $('#spin-button').on('click', function() {
                            $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin me-2"></i> Spinning...');
                            
                            // Call API to get the reward
                            $.ajax({
                                url: '/gamification/api/spin-result',
                                type: 'POST',
                                dataType: 'json',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    jsonrpc: "2.0",
                                    method: "call",
                                    params: {
                                        reward_id: rewardId
                                    },
                                    id: Math.floor(Math.random() * 1000000)
                                }),
                                success: function(data) {
                                    if (data.result && data.result.success) {
                                        // Spin to the winning segment
                                        var segmentIndex = data.result.reward.segment_index || 0;
                                        spinWheel.spin(segmentIndex);
                                        
                                        // Store the result for later
                                        container.dataset.spinResult = JSON.stringify(data.result);
                                    } else {
                                        // Error handling
                                        $('#spin-button').prop('disabled', false).html('<i class="fa fa-sync-alt me-2"></i> Spin the Wheel');
                                        alert('Failed to spin: ' + (data.result.message || 'Unknown error'));
                                    }
                                },
                                error: function() {
                                    $('#spin-button').prop('disabled', false).html('<i class="fa fa-sync-alt me-2"></i> Spin the Wheel');
                                    alert('Failed to communicate with the server. Please try again later.');
                                }
                            });
                        });
                        
                        function showReward(segmentIndex) {
                            // Get the stored result
                            var result = JSON.parse(container.dataset.spinResult || '{}');
                            
                            // Update the result display
                            $('#result-title').text('Congratulations!');
                            $('#result-message').text(result.message || 'You won a reward!');
                            
                            var detailsHtml = '';
                            if (result.reward) {
                                if (result.reward.item_type === 'coupon' || result.reward.item_type === 'external_coupon') {
                                    detailsHtml = '<div class="alert alert-info">' +
                                        '<strong>Your Coupon Code:</strong> ' + result.reward.code +
                                    '</div>';
                                } else if (result.reward.item_type === 'wallet_credit') {
                                    detailsHtml = '<div class="alert alert-info">' +
                                        '<strong>Amount Added to Wallet:</strong> ' + result.reward.value +
                                    '</div>';
                                } else if (result.reward.item_type === 'free_product') {
                                    detailsHtml = '<div class="alert alert-info">' +
                                        '<strong>Free Product:</strong> ' + (result.reward.product ? result.reward.product.name : 'Product') +
                                    '</div>';
                                } else if (result.reward.item_type === 'no_reward') {
                                    detailsHtml = '<div class="alert alert-warning">' +
                                        'Better luck next time!' +
                                    '</div>';
                                }
                            }
                            
                            $('#result-details').html(detailsHtml);
                            
                            // Show the result
                            $('#spin-result').fadeIn();
                        }
                    }
                });
            </script>
        </t>
    </template>
</odoo>
