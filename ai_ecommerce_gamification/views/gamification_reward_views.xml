<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Reward Form View -->
    <record id="view_gamification_reward_form" model="ir.ui.view">
        <field name="name">gamification.reward.form</field>
        <field name="model">gamification.reward</field>
        <field name="arch" type="xml">
            <form string="Reward">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                        </button>
                    </div>
                    <field name="image" widget="image" class="oe_avatar"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Reward Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="reward_type"/>
                            <field name="sequence"/>
                            <field name="total_rewards_given"/>
                            <field name="success_rate" widget="percentage"/>
                        </group>
                        <group>
                            <field name="ui_theme"/>
                            <field name="display_mode"/>
                            <field name="date_from"/>
                            <field name="date_to"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" placeholder="Describe the reward..."/>
                        </page>
                        <page string="Reward Items" name="reward_items">
                            <field name="reward_item_ids" nolabel="1">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="item_type"/>
                                    <field name="probability"/>
                                    <field name="value"/>
                                    <field name="max_quantity"/>
                                    <field name="remaining_quantity"/>
                                    <field name="total_assigned"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Rules" name="rules">
                            <field name="rule_ids" nolabel="1">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="rule_type"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Limits" name="limits">
                            <group>
                                <group>
                                    <field name="max_rewards_per_user"/>
                                    <field name="max_rewards_per_day"/>
                                    <field name="max_rewards_total"/>
                                </group>
                            </group>
                        </page>
                        <page string="UI Configuration" name="ui_config">
                            <group>
                                <group attrs="{'invisible': [('reward_type', '!=', 'scratch_card')]}">
                                    <field name="scratch_card_template" attrs="{'required': [('reward_type', '=', 'scratch_card')]}"/>
                                    <field name="background_image" widget="image"/>
                                </group>
                                <group attrs="{'invisible': [('reward_type', '!=', 'spin_wheel')]}">
                                    <field name="wheel_segments" attrs="{'required': [('reward_type', '=', 'spin_wheel')]}"/>
                                    <field name="wheel_colors" attrs="{'required': [('reward_type', '=', 'spin_wheel')]}"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    
    <!-- Reward Tree View -->
    <record id="view_gamification_reward_tree" model="ir.ui.view">
        <field name="name">gamification.reward.tree</field>
        <field name="model">gamification.reward</field>
        <field name="arch" type="xml">
            <tree string="Rewards" decoration-muted="not active">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="reward_type"/>
                <field name="total_rewards_given"/>
                <field name="success_rate" widget="percentage"/>
                <field name="date_from"/>
                <field name="date_to"/>
                <field name="active" invisible="1"/>
            </tree>
        </field>
    </record>
    
    <!-- Reward Search View -->
    <record id="view_gamification_reward_search" model="ir.ui.view">
        <field name="name">gamification.reward.search</field>
        <field name="model">gamification.reward</field>
        <field name="arch" type="xml">
            <search string="Search Rewards">
                <field name="name"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Scratch Cards" name="scratch_card" domain="[('reward_type', '=', 'scratch_card')]"/>
                <filter string="Spin Wheels" name="spin_wheel" domain="[('reward_type', '=', 'spin_wheel')]"/>
                <filter string="Direct Rewards" name="direct" domain="[('reward_type', '=', 'direct')]"/>
                <group expand="0" string="Group By">
                    <filter string="Reward Type" name="group_by_type" domain="[]" context="{'group_by': 'reward_type'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <!-- Reward Action -->
    <record id="action_gamification_reward" model="ir.actions.act_window">
        <field name="name">Rewards</field>
        <field name="res_model">gamification.reward</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_gamification_reward_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new reward
            </p>
            <p>
                Create different types of rewards like scratch cards, spin wheels, or direct rewards.
            </p>
        </field>
    </record>
</odoo>
