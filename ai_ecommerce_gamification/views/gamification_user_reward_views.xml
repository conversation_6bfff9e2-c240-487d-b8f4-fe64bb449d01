<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- User Reward Form View -->
    <record id="view_gamification_user_reward_form" model="ir.ui.view">
        <field name="name">gamification.user.reward.form</field>
        <field name="model">gamification.user.reward</field>
        <field name="arch" type="xml">
            <form string="User Reward">
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="pending,claimed,expired,failed"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="user_id"/>
                            <field name="reward_id"/>
                            <field name="reward_item_id"/>
                            <field name="reward_type"/>
                            <field name="item_type"/>
                        </group>
                        <group>
                            <field name="create_date" readonly="1"/>
                            <field name="claim_date" readonly="1"/>
                            <field name="expiry_date"/>
                            <field name="token" groups="base.group_system"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Reward Details" name="reward_details">
                            <group>
                                <group>
                                    <field name="value" attrs="{'invisible': [('item_type', 'not in', ['wallet_credit', 'discount'])]}"/>
                                    <field name="code" attrs="{'invisible': [('item_type', 'not in', ['coupon', 'external_coupon'])]}"/>
                                    <field name="coupon_id" attrs="{'invisible': [('item_type', '!=', 'coupon')]}"/>
                                    <field name="product_id" attrs="{'invisible': [('item_type', '!=', 'free_product')]}"/>
                                </group>
                            </group>
                        </page>
                        <page string="Notes" name="notes">
                            <field name="notes" placeholder="Additional notes..."/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    
    <!-- User Reward Tree View -->
    <record id="view_gamification_user_reward_tree" model="ir.ui.view">
        <field name="name">gamification.user.reward.tree</field>
        <field name="model">gamification.user.reward</field>
        <field name="arch" type="xml">
            <tree string="User Rewards" decoration-success="state == 'claimed'" decoration-danger="state == 'failed'" decoration-muted="state == 'expired'">
                <field name="create_date"/>
                <field name="name"/>
                <field name="user_id"/>
                <field name="reward_id"/>
                <field name="reward_type"/>
                <field name="item_type"/>
                <field name="value"/>
                <field name="code"/>
                <field name="state"/>
            </tree>
        </field>
    </record>
    
    <!-- User Reward Search View -->
    <record id="view_gamification_user_reward_search" model="ir.ui.view">
        <field name="name">gamification.user.reward.search</field>
        <field name="model">gamification.user.reward</field>
        <field name="arch" type="xml">
            <search string="Search User Rewards">
                <field name="name"/>
                <field name="user_id"/>
                <field name="reward_id"/>
                <field name="code"/>
                <separator/>
                <filter string="Pending" name="pending" domain="[('state', '=', 'pending')]"/>
                <filter string="Claimed" name="claimed" domain="[('state', '=', 'claimed')]"/>
                <filter string="Expired" name="expired" domain="[('state', '=', 'expired')]"/>
                <filter string="Failed" name="failed" domain="[('state', '=', 'failed')]"/>
                <separator/>
                <filter string="Scratch Cards" name="scratch_card" domain="[('reward_type', '=', 'scratch_card')]"/>
                <filter string="Spin Wheels" name="spin_wheel" domain="[('reward_type', '=', 'spin_wheel')]"/>
                <filter string="Direct Rewards" name="direct" domain="[('reward_type', '=', 'direct')]"/>
                <separator/>
                <filter string="Coupons" name="coupon" domain="[('item_type', '=', 'coupon')]"/>
                <filter string="Wallet Credits" name="wallet_credit" domain="[('item_type', '=', 'wallet_credit')]"/>
                <group expand="0" string="Group By">
                    <filter string="User" name="group_by_user" domain="[]" context="{'group_by': 'user_id'}"/>
                    <filter string="Reward" name="group_by_reward" domain="[]" context="{'group_by': 'reward_id'}"/>
                    <filter string="Status" name="group_by_state" domain="[]" context="{'group_by': 'state'}"/>
                    <filter string="Date" name="group_by_date" domain="[]" context="{'group_by': 'create_date:day'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <!-- User Reward Action -->
    <record id="action_gamification_user_reward" model="ir.actions.act_window">
        <field name="name">User Rewards</field>
        <field name="res_model">gamification.user.reward</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_gamification_user_reward_search"/>
        <field name="context">{'search_default_group_by_user': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No user rewards yet
            </p>
            <p>
                User rewards are created when users earn rewards through the gamification system.
            </p>
        </field>
    </record>
</odoo>
