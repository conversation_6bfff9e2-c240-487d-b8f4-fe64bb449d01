<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Reward Item Form View -->
    <record id="view_gamification_reward_item_form" model="ir.ui.view">
        <field name="name">gamification.reward.item.form</field>
        <field name="model">gamification.reward.item</field>
        <field name="arch" type="xml">
            <form string="Reward Item">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                        </button>
                    </div>
                    <field name="icon" widget="image" class="oe_avatar"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Reward Item Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="reward_id"/>
                            <field name="item_type"/>
                            <field name="probability"/>
                            <field name="color" widget="color"/>
                        </group>
                        <group>
                            <field name="sequence"/>
                            <field name="max_quantity"/>
                            <field name="remaining_quantity"/>
                            <field name="total_assigned"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" placeholder="Describe the reward item..."/>
                        </page>
                        <page string="Item Configuration" name="item_config">
                            <group>
                                <group attrs="{'invisible': [('item_type', 'not in', ['wallet_credit', 'discount'])]}">
                                    <field name="value" attrs="{'required': [('item_type', 'in', ['wallet_credit', 'discount'])]}"/>
                                </group>
                                <group attrs="{'invisible': [('item_type', 'not in', ['coupon', 'external_coupon'])]}">
                                    <field name="coupon_program_id" attrs="{'invisible': [('item_type', '!=', 'coupon')], 'required': [('item_type', '=', 'coupon')]}"/>
                                    <field name="coupon_code" attrs="{'invisible': [('item_type', '!=', 'external_coupon')], 'required': [('item_type', '=', 'external_coupon')]}"/>
                                </group>
                                <group attrs="{'invisible': [('item_type', '!=', 'free_product')]}">
                                    <field name="product_id" attrs="{'required': [('item_type', '=', 'free_product')]}"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    
    <!-- Reward Item Tree View -->
    <record id="view_gamification_reward_item_tree" model="ir.ui.view">
        <field name="name">gamification.reward.item.tree</field>
        <field name="model">gamification.reward.item</field>
        <field name="arch" type="xml">
            <tree string="Reward Items" decoration-muted="not active">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="reward_id"/>
                <field name="item_type"/>
                <field name="probability"/>
                <field name="value"/>
                <field name="max_quantity"/>
                <field name="remaining_quantity"/>
                <field name="total_assigned"/>
                <field name="active" invisible="1"/>
            </tree>
        </field>
    </record>
    
    <!-- Reward Item Search View -->
    <record id="view_gamification_reward_item_search" model="ir.ui.view">
        <field name="name">gamification.reward.item.search</field>
        <field name="model">gamification.reward.item</field>
        <field name="arch" type="xml">
            <search string="Search Reward Items">
                <field name="name"/>
                <field name="reward_id"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Coupons" name="coupon" domain="[('item_type', '=', 'coupon')]"/>
                <filter string="External Coupons" name="external_coupon" domain="[('item_type', '=', 'external_coupon')]"/>
                <filter string="Wallet Credits" name="wallet_credit" domain="[('item_type', '=', 'wallet_credit')]"/>
                <filter string="Free Products" name="free_product" domain="[('item_type', '=', 'free_product')]"/>
                <filter string="Discounts" name="discount" domain="[('item_type', '=', 'discount')]"/>
                <filter string="No Rewards" name="no_reward" domain="[('item_type', '=', 'no_reward')]"/>
                <group expand="0" string="Group By">
                    <filter string="Reward" name="group_by_reward" domain="[]" context="{'group_by': 'reward_id'}"/>
                    <filter string="Item Type" name="group_by_type" domain="[]" context="{'group_by': 'item_type'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <!-- Reward Item Action -->
    <record id="action_gamification_reward_item" model="ir.actions.act_window">
        <field name="name">Reward Items</field>
        <field name="res_model">gamification.reward.item</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_gamification_reward_item_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new reward item
            </p>
            <p>
                Reward items are the actual prizes that users can win, such as coupons, wallet credits, or free products.
            </p>
        </field>
    </record>
</odoo>
