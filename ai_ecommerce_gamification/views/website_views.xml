<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Website Form View -->
    <record id="view_website_form_gamification" model="ir.ui.view">
        <field name="name">website.form.gamification</field>
        <field name="model">website</field>
        <field name="inherit_id" ref="website.view_website_form"/>
        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page string="Gamification" name="gamification">
                    <group>
                        <field name="gamification_enabled"/>
                    </group>
                    <group attrs="{'invisible': [('gamification_enabled', '=', False)]}">
                        <group string="Reward Settings">
                            <field name="signup_reward_enabled"/>
                            <field name="signup_reward_id" attrs="{'invisible': [('signup_reward_enabled', '=', False)], 'required': [('signup_reward_enabled', '=', True)]}"/>
                            <field name="purchase_reward_enabled"/>
                            <field name="purchase_reward_id" attrs="{'invisible': [('purchase_reward_enabled', '=', False)], 'required': [('purchase_reward_enabled', '=', True)]}"/>
                        </group>
                        <group string="Display Settings">
                            <field name="show_rewards_in_account"/>
                            <field name="show_wallet_in_account"/>
                            <field name="reward_notification_enabled"/>
                        </group>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
