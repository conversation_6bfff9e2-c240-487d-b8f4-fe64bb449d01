<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Rule Form View -->
    <record id="view_gamification_rule_form" model="ir.ui.view">
        <field name="name">gamification.rule.form</field>
        <field name="model">gamification.rule</field>
        <field name="arch" type="xml">
            <form string="Rule">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Rule Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="reward_id"/>
                            <field name="rule_type"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="date_from"/>
                            <field name="date_to"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" placeholder="Describe the rule..."/>
                        </page>
                        <page string="Rule Configuration" name="rule_config">
                            <group>
                                <group attrs="{'invisible': [('rule_type', '!=', 'order_value')]}">
                                    <field name="min_order_value" attrs="{'required': [('rule_type', '=', 'order_value')]}"/>
                                </group>
                                <group attrs="{'invisible': [('rule_type', '!=', 'repeat_purchase')]}">
                                    <field name="min_purchase_count" attrs="{'required': [('rule_type', '=', 'repeat_purchase')]}"/>
                                </group>
                                <group attrs="{'invisible': [('rule_type', '!=', 'special_date')]}">
                                    <field name="special_date_type" attrs="{'required': [('rule_type', '=', 'special_date')]}"/>
                                    <field name="specific_date" attrs="{'invisible': [('special_date_type', '!=', 'specific')], 'required': [('special_date_type', '=', 'specific')]}"/>
                                </group>
                                <group attrs="{'invisible': [('rule_type', '!=', 'custom')]}">
                                    <field name="domain" attrs="{'required': [('rule_type', '=', 'custom')]}"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    
    <!-- Rule Tree View -->
    <record id="view_gamification_rule_tree" model="ir.ui.view">
        <field name="name">gamification.rule.tree</field>
        <field name="model">gamification.rule</field>
        <field name="arch" type="xml">
            <tree string="Rules" decoration-muted="not active">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="reward_id"/>
                <field name="rule_type"/>
                <field name="date_from"/>
                <field name="date_to"/>
                <field name="active" invisible="1"/>
            </tree>
        </field>
    </record>
    
    <!-- Rule Search View -->
    <record id="view_gamification_rule_search" model="ir.ui.view">
        <field name="name">gamification.rule.search</field>
        <field name="model">gamification.rule</field>
        <field name="arch" type="xml">
            <search string="Search Rules">
                <field name="name"/>
                <field name="reward_id"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Signup Rules" name="signup" domain="[('rule_type', '=', 'signup')]"/>
                <filter string="Purchase Rules" name="purchase" domain="[('rule_type', '=', 'purchase')]"/>
                <filter string="Order Value Rules" name="order_value" domain="[('rule_type', '=', 'order_value')]"/>
                <filter string="Repeat Purchase Rules" name="repeat_purchase" domain="[('rule_type', '=', 'repeat_purchase')]"/>
                <filter string="Special Date Rules" name="special_date" domain="[('rule_type', '=', 'special_date')]"/>
                <filter string="Custom Rules" name="custom" domain="[('rule_type', '=', 'custom')]"/>
                <group expand="0" string="Group By">
                    <filter string="Reward" name="group_by_reward" domain="[]" context="{'group_by': 'reward_id'}"/>
                    <filter string="Rule Type" name="group_by_type" domain="[]" context="{'group_by': 'rule_type'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <!-- Rule Action -->
    <record id="action_gamification_rule" model="ir.actions.act_window">
        <field name="name">Rules</field>
        <field name="res_model">gamification.rule</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_gamification_rule_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new rule
            </p>
            <p>
                Rules determine when rewards are triggered for users, such as on signup, after purchase, or on special dates.
            </p>
        </field>
    </record>
</odoo>
