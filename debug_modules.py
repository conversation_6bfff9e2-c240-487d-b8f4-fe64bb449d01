#!/usr/bin/env python3

import xmlrpc.client
import sys
import traceback

URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

try:
    print("Starting debug script...")
    
    # Test connection
    print("Connecting to Odoo...")
    common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
    models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
    
    print("Authenticating...")
    uid = common.authenticate(DB, USERNAME, PASSWORD, {})
    
    if not uid:
        print("Authentication failed!")
        sys.exit(1)
    
    print(f"Connected successfully as user {uid}")
    
    # Test basic model access
    print("\nTesting basic model access...")
    
    # Test partner count
    try:
        partner_count = models.execute_kw(
            DB, uid, PASSWORD,
            'res.partner', 'search_count', [[]]
        )
        print(f"✅ Partners: {partner_count} records")
    except Exception as e:
        print(f"❌ Partner test failed: {e}")
    
    # Test IMCA modules
    modules_to_test = ['imca_groups', 'imca_services', 'imca_client_documents', 'imca_crednetials_manager', 'imca_dsc_management']
    
    for module_name in modules_to_test:
        try:
            print(f"\nTesting {module_name}...")
            
            # Check if module exists in module list
            module_ids = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.module.module', 'search',
                [('name', '=', module_name)]
            )
            
            if module_ids:
                module_data = models.execute_kw(
                    DB, uid, PASSWORD,
                    'ir.module.module', 'read',
                    [module_ids[0]], {'fields': ['name', 'state']}
                )[0]
                print(f"  📦 Module found: {module_data['name']} - State: {module_data['state']}")
                
                # If installed, try to access the model
                if module_data['state'] == 'installed':
                    model_name = None
                    if module_name == 'imca_groups':
                        model_name = 'x_groups'
                    elif module_name == 'imca_services':
                        model_name = 'x_services'
                    elif module_name == 'imca_client_documents':
                        model_name = 'x_client_documents'
                    elif module_name == 'imca_crednetials_manager':
                        model_name = 'x_credentials'
                    elif module_name == 'imca_dsc_management':
                        model_name = 'x_dsc'
                    
                    if model_name:
                        try:
                            count = models.execute_kw(
                                DB, uid, PASSWORD,
                                model_name, 'search_count', [[]]
                            )
                            print(f"  ✅ Model {model_name}: {count} records")
                        except Exception as e:
                            print(f"  ❌ Model {model_name} error: {e}")
                
                # Try to install/upgrade if not installed
                elif module_data['state'] == 'uninstalled':
                    print(f"  🔄 Installing {module_name}...")
                    try:
                        models.execute_kw(
                            DB, uid, PASSWORD,
                            'ir.module.module', 'button_immediate_install',
                            [module_ids[0]]
                        )
                        print(f"  ✅ Installation initiated for {module_name}")
                    except Exception as e:
                        print(f"  ❌ Installation failed: {e}")
                        
            else:
                print(f"  ❌ Module {module_name} not found in module list")
                
        except Exception as e:
            print(f"  ❌ Error testing {module_name}: {e}")
            traceback.print_exc()
    
    print("\nDebug script completed.")
    
except Exception as e:
    print(f"Fatal error: {e}")
    traceback.print_exc()
    sys.exit(1)
