// =============================================================================
// OMISOL THEME - SECONDARY VARIABLES
// =============================================================================
// This file contains computed variables and mixins based on primary variables
// These are derived from the primary variables and provide additional styling options

// =============================================================================
// BOOTSTRAP GRID SYSTEM VARIABLES (Required)
// =============================================================================

// Grid system
$grid-columns: 12 !default;
$grid-gutter-width: 1.5rem !default;
$grid-row-columns: 6 !default;

// Container max widths
$container-max-widths: (
  sm: 33.75rem,  // 540px
  md: 45rem,     // 720px
  lg: 60rem,     // 960px
  xl: 71.25rem,  // 1140px
  xxl: 82.5rem   // 1320px
) !default;

// Breakpoints
$grid-breakpoints: (
  xs: 0,
  sm: 36rem,    // 576px
  md: 48rem,    // 768px
  lg: 62rem,    // 992px
  xl: 75rem,    // 1200px
  xxl: 87.5rem  // 1400px
) !default;

// Container padding
$container-padding-x: $grid-gutter-width * 0.5 !default;

// =============================================================================
// BOOTSTRAP HELPER FUNCTIONS (Required)
// =============================================================================

// Note: Using calc() instead of custom functions to avoid unit conflicts
// Bootstrap has its own add/subtract functions that we should not override

// Lighten function wrapper (ensure it's available)
@function lighten($color, $amount) {
  @return mix(white, $color, $amount);
}

// Darken function wrapper (ensure it's available)
@function darken($color, $amount) {
  @return mix(black, $color, $amount);
}

// =============================================================================
// BOOTSTRAP COMPONENT VARIABLES (Required)
// =============================================================================

// Card variables
$card-spacer-y: 1rem !default;
$card-spacer-x: 1rem !default;
$card-title-spacer-y: 0.5rem !default;
$card-border-width: 0.125rem !default; // 2px converted to rem
$card-border-color: rgba(0, 0, 0, 0.125) !default;
$card-border-radius: 0.5rem !default; // 8px converted to rem
$card-box-shadow: null !default;
$card-inner-border-radius: calc(#{$card-border-radius} - #{$card-border-width}) !default;
$card-cap-padding-y: $card-spacer-y * 0.5 !default;
$card-cap-padding-x: $card-spacer-x !default;
$card-cap-bg: rgba(0, 0, 0, 0.03) !default;
$card-cap-color: null !default;
$card-height: null !default;
$card-color: null !default;
$card-bg: #fff !default;
$card-img-overlay-padding: 1.25rem !default;
$card-group-margin: $grid-gutter-width * 0.5 !default;

// Button variables
$btn-padding-y: 0.375rem !default;
$btn-padding-x: 0.75rem !default;
// $btn-font-family: null !default;
$btn-font-size: 1rem !default;
$btn-line-height: 1.5 !default;
$btn-white-space: null !default;
$btn-padding-y-sm: 0.25rem !default;
$btn-padding-x-sm: 0.5rem !default;
$btn-font-size-sm: 0.875rem !default;
$btn-padding-y-lg: 0.5rem !default;
$btn-padding-x-lg: 1rem !default;
$btn-font-size-lg: 1.25rem !default;
$btn-border-width: 0.125rem !default; // 2px converted to rem
$btn-font-weight: 400 !default;
$btn-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075) !default;
$btn-focus-width: 0.25rem !default;
$btn-focus-box-shadow: 0 0 0 $btn-focus-width rgba(0, 123, 255, 0.25) !default;
$btn-disabled-opacity: 0.65 !default;
$btn-active-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125) !default;

// Form variables
$input-padding-y: 0.375rem !default;
$input-padding-x: 0.75rem !default;
// $input-font-family: null !default;
$input-font-size: 1rem !default;
$input-font-weight: 400 !default;
$input-line-height: 1.5 !default;
$input-color: #212529 !default;
$input-bg: #fff !default;
$input-disabled-bg: #e9ecef !default;
$input-disabled-border-color: null !default;
$input-border-color: #ced4da !default;
$input-border-width: 0.0625rem !default; // 1px converted to rem
$input-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075) !default;
$input-border-radius: 0.375rem !default;
$input-focus-bg: #fff !default;
$input-focus-border-color: lighten(#007bff, 25%) !default;
$input-focus-color: #212529 !default;
$input-focus-width: 0.25rem !default;
$input-focus-box-shadow: 0 0 0 $input-focus-width rgba(0, 123, 255, 0.25) !default;

// Navbar variables
$navbar-padding-y: 0.5rem !default;
$navbar-padding-x: null !default;
$navbar-nav-link-padding-x: 0.5rem !default;
$navbar-brand-font-size: 1.25rem !default;
$navbar-brand-height: null !default;
$navbar-brand-padding-y: 0.3125rem !default;
$navbar-brand-margin-end: 1rem !default;
$navbar-toggler-padding-y: 0.25rem !default;
$navbar-toggler-padding-x: 0.75rem !default;
$navbar-toggler-font-size: 1.25rem !default;
$navbar-toggler-border-radius: 0.375rem !default;
$navbar-toggler-focus-width: 0.25rem !default;
$navbar-toggler-transition: box-shadow 0.15s ease-in-out !default;

// Navigation divider variables (missing Bootstrap variables)
$nav-divider-color: #dee2e6 !default;
$nav-divider-margin-y: 0.5rem !default;

// Dropdown variables (additional Bootstrap variables)
$dropdown-min-width: 10rem !default;
$dropdown-padding-x: 0 !default;
$dropdown-padding-y: 0.5rem !default;
$dropdown-spacer: 0.125rem !default;
$dropdown-font-size: 1rem !default;
$dropdown-color: #212529 !default;
$dropdown-bg: #fff !default;
$dropdown-border-color: rgba(0, 0, 0, 0.15) !default;
$dropdown-border-radius: 0.5rem !default; // 8px converted to rem (matches $border-radius)
$dropdown-border-width: 0.125rem !default; // 2px converted to rem
$dropdown-inner-border-radius: calc(#{$dropdown-border-radius} - #{$dropdown-border-width}) !default;
$dropdown-divider-bg: #e9ecef !default;
$dropdown-divider-margin-y: $nav-divider-margin-y !default;
$dropdown-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175) !default;

// Link variables
$link-color: #0d6efd !default;
$link-decoration: none !default;
$link-shade-percentage: 20% !default;
$link-hover-color: darken($link-color, $link-shade-percentage) !default;
$link-hover-decoration: null !default;

// =============================================================================
// COMPUTED COLOR VARIABLES
// =============================================================================

// Generate color variations using Odoo's color functions
$o-color-1-variations: (
    'base': o-color('o-color-1'),
    'light': lighten(o-color('o-color-1'), 15%),
    'lighter': lighten(o-color('o-color-1'), 30%),
    'dark': darken(o-color('o-color-1'), 15%),
    'darker': darken(o-color('o-color-1'), 30%),
    'alpha-10': rgba(o-color('o-color-1'), 0.1),
    'alpha-20': rgba(o-color('o-color-1'), 0.2),
    'alpha-30': rgba(o-color('o-color-1'), 0.3),
    'alpha-50': rgba(o-color('o-color-1'), 0.5),
);

// Secondary color variations
$secondary-variations: (
    'base': o-website-value('secondary-color'),
    'light': o-website-value('secondary-light'),
    'dark': o-website-value('secondary-dark'),
    'alpha-10': rgba(o-website-value('secondary-color'), 0.1),
    'alpha-20': rgba(o-website-value('secondary-color'), 0.2),
    'alpha-30': rgba(o-website-value('secondary-color'), 0.3),
);

// Accent color variations
$accent-variations: (
    'base': o-website-value('accent-orange'),
    'light': o-website-value('accent-orange-light'),
    'dark': o-website-value('accent-orange-dark'),
    'alpha-10': rgba(o-website-value('accent-orange'), 0.1),
    'alpha-20': rgba(o-website-value('accent-orange'), 0.2),
    'alpha-30': rgba(o-website-value('accent-orange'), 0.3),
);

// =============================================================================
// COMPONENT-SPECIFIC VARIABLES
// =============================================================================

// Header & Navigation computed values
$header-height: 5rem; // 80px
$header-height-mobile: 3.75rem; // 60px
$navbar-transition: o-website-value('transition-base');
$navbar-shadow: 0 4px 20px rgba(o-color('o-color-1'), 0.15);

// Button computed values
$btn-primary-shadow: 0 4px 8px rgba(o-color('o-color-1'), 0.3);
$btn-primary-hover-shadow: 0 6px 12px rgba(o-color('o-color-1'), 0.4);
$btn-secondary-shadow: 0 4px 8px rgba(o-website-value('secondary-color'), 0.2);
$btn-secondary-hover-shadow: 0 6px 12px rgba(o-website-value('secondary-color'), 0.3);

// Card computed values
$card-hover-transform: translateY(-3px);
$card-transition: o-website-value('transition-base');
$card-hover-shadow: 0 8px 25px rgba(o-color('o-color-1'), 0.25);

// Hero section computed values
$hero-min-height: 70vh;
$hero-padding-y: 7.5rem; // 120px
$hero-padding-y-mobile: 5rem; // 80px
$hero-overlay-pattern: linear-gradient(45deg, rgba(o-website-value('accent-orange'), 0.1) 25%, transparent 25%),
                       linear-gradient(-45deg, rgba(o-website-value('accent-orange'), 0.1) 25%, transparent 25%),
                       linear-gradient(45deg, transparent 75%, rgba(o-website-value('accent-orange'), 0.1) 75%),
                       linear-gradient(-45deg, transparent 75%, rgba(o-website-value('accent-orange'), 0.1) 75%);

// Footer computed values
$footer-padding-y: 2.5rem; // 40px
$footer-padding-y-bottom: 1.25rem; // 20px
$footer-border-width: 0.3125rem; // 5px

// =============================================================================
// RESPONSIVE BREAKPOINT VARIABLES
// =============================================================================

$breakpoint-xs: 0;
$breakpoint-sm: 36rem;    // 576px
$breakpoint-md: 48rem;    // 768px
$breakpoint-lg: 62rem;    // 992px
$breakpoint-xl: 75rem;    // 1200px
$breakpoint-xxl: 87.5rem; // 1400px

// =============================================================================
// SPACING SCALE
// =============================================================================

$spacing-scale: (
    'xs': 0.25rem,    // 4px
    'sm': 0.5rem,     // 8px
    'md': 1rem,       // 16px
    'lg': 1.5rem,     // 24px
    'xl': 2rem,       // 32px
    '2xl': 3rem,      // 48px
    '3xl': 4rem,      // 64px
    '4xl': 6rem,      // 96px
    '5xl': 8rem,      // 128px
);

// =============================================================================
// TYPOGRAPHY SCALE
// =============================================================================

$font-size-scale: (
    'xs': 0.75rem,    // 12px
    'sm': 0.875rem,   // 14px
    'base': 1rem,     // 16px
    'lg': 1.125rem,   // 18px
    'xl': 1.25rem,    // 20px
    '2xl': 1.5rem,    // 24px
    '3xl': 1.875rem,  // 30px
    '4xl': 2.25rem,   // 36px
    '5xl': 3rem,      // 48px
    '6xl': 3.75rem,   // 60px
    '7xl': 4.5rem,    // 72px
    '8xl': 6rem,      // 96px
    '9xl': 8rem,      // 128px
);

// =============================================================================
// MIXINS
// =============================================================================

// Button mixin for consistent button styling
@mixin btn-style($bg-color, $border-color, $text-color, $hover-bg, $hover-border, $hover-text) {
    background: $bg-color;
    border: 0.125rem solid $border-color; // 2px
    color: $text-color;
    padding: o-website-value('btn-padding-y') o-website-value('btn-padding-x');
    font-weight: o-website-value('buttons-font-weight');
    // font-family: o-website-value('buttons-font');
    border-radius: o-website-value('border-radius-sm');
    transition: o-website-value('transition-base');
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-transform: uppercase;
    letter-spacing: o-website-value('letter-spacing-buttons');
    
    &:hover {
        background: $hover-bg;
        border-color: $hover-border;
        color: $hover-text;
        text-decoration: none;
        transform: translateY(-2px);
    }
    
    &:active {
        transform: translateY(0);
    }
}

// Card mixin for consistent card styling
@mixin card-style($border-color: o-color('o-color-1'), $shadow-color: rgba(o-color('o-color-1'), 0.15)) {
    background: o-website-value('card-bg');
    border: 0.1875rem solid $border-color; // 3px
    border-radius: o-website-value('border-radius-base');
    overflow: hidden;
    transition: o-website-value('transition-base');
    box-shadow: 0 8px 25px $shadow-color;
    position: relative;
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 0.25rem; // 4px
        background: o-website-value('accent-orange');
        z-index: 1;
    }
    
    &:hover {
        border-color: $border-color;
        box-shadow: 0 8px 25px rgba($shadow-color, 0.25);
        transform: translateY(-3px);
    }
}

// Section mixin for consistent section styling
@mixin section-style($bg-color: transparent, $padding-y: null) {
    $padding: if($padding-y, $padding-y, o-website-value('section-padding-y'));
    
    padding: $padding 0;
    position: relative;
    background: $bg-color;
    
    @media (max-width: $breakpoint-lg) {
        padding: o-website-value('section-padding-y-sm') 0;
    }
    
    @media (max-width: $breakpoint-md) {
        padding: o-website-value('section-padding-y-xs') 0;
    }
}

// Text style mixin
@mixin text-style($size: 'base', $weight: null, $color: null, $line-height: null) {
    font-size: map-get($font-size-scale, $size);
    // font-family: o-website-value('body-font');
    
    @if $weight {
        font-weight: $weight;
    }
    
    @if $color {
        color: $color;
    }
    
    @if $line-height {
        line-height: $line-height;
    } @else {
        line-height: o-website-value('line-height-base');
    }
}

// Heading style mixin
@mixin heading-style($size: '2xl', $weight: null, $color: null) {
    font-size: map-get($font-size-scale, $size);
    // font-family: o-website-value('headings-font');
    font-weight: if($weight, $weight, o-website-value('headings-font-weight'));
    line-height: o-website-value('line-height-headings');
    letter-spacing: o-website-value('letter-spacing-headings');
    color: if($color, $color, o-website-value('text-primary'));
    margin-bottom: 1rem;
}

// Responsive mixin
@mixin responsive($breakpoint) {
    @if $breakpoint == 'xs' {
        @media (max-width: #{$breakpoint-sm - 1px}) { @content; }
    }
    @if $breakpoint == 'sm' {
        @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) { @content; }
    }
    @if $breakpoint == 'md' {
        @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1px}) { @content; }
    }
    @if $breakpoint == 'lg' {
        @media (min-width: #{$breakpoint-lg}) and (max-width: #{$breakpoint-xl - 1px}) { @content; }
    }
    @if $breakpoint == 'xl' {
        @media (min-width: #{$breakpoint-xl}) { @content; }
    }
}

// Animation mixin
@mixin animate($property: all, $duration: 0.3s, $timing: ease, $delay: 0s) {
    transition: $property $duration $timing $delay;
}

// Gradient mixin
@mixin omisol-gradient-bg($gradient-name) {
    background: o-website-value($gradient-name);
}

// Shadow mixin
@mixin shadow($level: 'md') {
    @if $level == 'sm' {
        box-shadow: o-website-value('shadow-sm');
    } @else if $level == 'md' {
        box-shadow: o-website-value('shadow-md');
    } @else if $level == 'lg' {
        box-shadow: o-website-value('shadow-lg');
    } @else if $level == 'xl' {
        box-shadow: o-website-value('shadow-xl');
    }
}

// =============================================================================
// UTILITY CLASSES GENERATION
// =============================================================================

// Generate spacing utility classes
@each $name, $value in $spacing-scale {
    .m-#{$name} { margin: $value !important; }
    .mt-#{$name} { margin-top: $value !important; }
    .mr-#{$name} { margin-right: $value !important; }
    .mb-#{$name} { margin-bottom: $value !important; }
    .ml-#{$name} { margin-left: $value !important; }
    .mx-#{$name} { margin-left: $value !important; margin-right: $value !important; }
    .my-#{$name} { margin-top: $value !important; margin-bottom: $value !important; }
    
    .p-#{$name} { padding: $value !important; }
    .pt-#{$name} { padding-top: $value !important; }
    .pr-#{$name} { padding-right: $value !important; }
    .pb-#{$name} { padding-bottom: $value !important; }
    .pl-#{$name} { padding-left: $value !important; }
    .px-#{$name} { padding-left: $value !important; padding-right: $value !important; }
    .py-#{$name} { padding-top: $value !important; padding-bottom: $value !important; }
}

// Generate font size utility classes
@each $name, $value in $font-size-scale {
    .text-#{$name} { font-size: $value !important; }
}
