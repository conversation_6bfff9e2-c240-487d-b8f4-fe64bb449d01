// =============================================================================
// OMISOL THEME - NON-CRITICAL CSS
// =============================================================================
// Styles that can be loaded after the initial page render for better performance
// This includes below-the-fold content, complex animations, and secondary features

// =============================================================================
// ADVANCED ANIMATIONS
// =============================================================================

// Complex keyframe animations that aren't critical for initial load
@keyframes complexPulse {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    25% {
        transform: scale(1.1) rotate(90deg);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.2) rotate(180deg);
        opacity: 0.6;
    }
    75% {
        transform: scale(1.1) rotate(270deg);
        opacity: 0.8;
    }
}

@keyframes morphing {
    0%, 100% {
        border-radius: 50% 50% 50% 50%;
        transform: rotate(0deg);
    }
    25% {
        border-radius: 60% 40% 60% 40%;
        transform: rotate(90deg);
    }
    50% {
        border-radius: 40% 60% 40% 60%;
        transform: rotate(180deg);
    }
    75% {
        border-radius: 50% 50% 40% 60%;
        transform: rotate(270deg);
    }
}

@keyframes textReveal {
    0% {
        clip-path: polygon(0 0, 0 0, 0 100%, 0% 100%);
    }
    100% {
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    }
}

@keyframes slideInFromEdge {
    0% {
        transform: translateX(-100vw) rotate(-180deg);
        opacity: 0;
    }
    50% {
        transform: translateX(10px) rotate(-90deg);
        opacity: 0.5;
    }
    100% {
        transform: translateX(0) rotate(0deg);
        opacity: 1;
    }
}

// =============================================================================
// ADVANCED HOVER EFFECTS
// =============================================================================

.hover-complex {
    position: relative;
    overflow: hidden;
    @include animate(all, 0.5s, cubic-bezier(0.25, 0.46, 0.45, 0.94));
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }
    
    &:hover::before {
        left: 100%;
    }
    
    &:hover {
        transform: scale(1.05) rotateY(5deg);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }
}

.hover-tilt {
    transform-style: preserve-3d;
    @include animate(transform, 0.3s, ease-out);
    
    &:hover {
        transform: perspective(1000px) rotateX(10deg) rotateY(10deg) scale(1.05);
    }
}

.hover-magnetic {
    @include animate(transform, 0.3s, cubic-bezier(0.25, 0.46, 0.45, 0.94));
    
    &:hover {
        transform: translate(var(--mouse-x, 0), var(--mouse-y, 0)) scale(1.1);
    }
}

// =============================================================================
// ADVANCED CARD STYLES
// =============================================================================

.card-advanced {
    position: relative;
    background: linear-gradient(145deg, #ffffff, #f0f0f0);
    border: none;
    border-radius: 20px;
    overflow: hidden;
    @include animate(all, 0.4s, cubic-bezier(0.25, 0.46, 0.45, 0.94));
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, o-color('o-color-1'), o-website-value('accent-orange'));
        transform: scaleX(0);
        transform-origin: left;
        @include animate(transform, 0.3s, ease-out);
    }
    
    &:hover::before {
        transform: scaleX(1);
    }
    
    &:hover {
        transform: translateY(-10px) rotateX(5deg);
        box-shadow: 
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(o-color('o-color-1'), 0.1);
    }
}

.card-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    @include animate(all, 0.3s, ease);
    
    &:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(o-color('o-color-1'), 0.3);
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }
}

// =============================================================================
// ADVANCED FORM STYLES
// =============================================================================

.form-floating-advanced {
    position: relative;
    
    .form-control {
        background: transparent;
        border: none;
        border-bottom: 2px solid o-website-value('border-color');
        border-radius: 0;
        padding: 1rem 0 0.5rem 0;
        @include animate(border-color, 0.3s, ease);
        
        &:focus {
            border-bottom-color: o-color('o-color-1');
            box-shadow: none;
            
            + label {
                transform: translateY(-1.5rem) scale(0.85);
                color: o-color('o-color-1');
            }
        }
        
        &:not(:placeholder-shown) + label {
            transform: translateY(-1.5rem) scale(0.85);
            color: o-website-value('text-secondary');
        }
    }
    
    label {
        position: absolute;
        top: 1rem;
        left: 0;
        color: o-website-value('text-muted');
        pointer-events: none;
        @include animate(all, 0.3s, ease);
        transform-origin: left top;
    }
    
    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background: o-color('o-color-1');
        @include animate(width, 0.3s, ease);
    }
    
    .form-control:focus ~ &::after {
        width: 100%;
    }
}

// =============================================================================
// ADVANCED NAVIGATION EFFECTS
// =============================================================================

.nav-advanced {
    .nav-link {
        position: relative;
        overflow: hidden;
        
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(o-color('o-color-1'), 0.1), transparent);
            @include animate(left, 0.5s, ease);
        }
        
        &:hover::before {
            left: 100%;
        }
        
        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, o-color('o-color-1'), o-website-value('accent-orange'));
            @include animate(all, 0.3s, ease);
            transform: translateX(-50%);
        }
        
        &:hover::after,
        &.active::after {
            width: 100%;
        }
    }
}

// =============================================================================
// ADVANCED LOADING STATES
// =============================================================================

.skeleton-loader {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
}

@keyframes skeleton-loading {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.loading-wave {
    display: inline-flex;
    gap: 4px;
    
    .wave-bar {
        width: 4px;
        height: 20px;
        background: o-color('o-color-1');
        border-radius: 2px;
        animation: wave-animation 1.2s infinite ease-in-out;
        
        &:nth-child(1) { animation-delay: -1.2s; }
        &:nth-child(2) { animation-delay: -1.1s; }
        &:nth-child(3) { animation-delay: -1.0s; }
        &:nth-child(4) { animation-delay: -0.9s; }
        &:nth-child(5) { animation-delay: -0.8s; }
    }
}

@keyframes wave-animation {
    0%, 40%, 100% {
        transform: scaleY(0.4);
    }
    20% {
        transform: scaleY(1.0);
    }
}

// =============================================================================
// ADVANCED SCROLL EFFECTS
// =============================================================================

.parallax-advanced {
    transform-style: preserve-3d;
    
    &[data-parallax="slow"] {
        transform: translateZ(-1px) scale(2);
    }
    
    &[data-parallax="medium"] {
        transform: translateZ(-2px) scale(3);
    }
    
    &[data-parallax="fast"] {
        transform: translateZ(-3px) scale(4);
    }
}

.scroll-reveal-advanced {
    opacity: 0;
    transform: translateY(50px) rotateX(15deg);
    @include animate(all, 0.8s, cubic-bezier(0.25, 0.46, 0.45, 0.94));
    
    &.revealed {
        opacity: 1;
        transform: translateY(0) rotateX(0);
    }
    
    &.revealed-delayed {
        transition-delay: 0.2s;
    }
}

// =============================================================================
// ADVANCED INTERACTIVE ELEMENTS
// =============================================================================

.interactive-blob {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, o-color('o-color-1'), o-website-value('accent-orange'));
    border-radius: 50%;
    position: relative;
    animation: morphing 8s infinite;
    filter: blur(1px);
    
    &::before,
    &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: inherit;
        border-radius: inherit;
        animation: morphing 6s infinite reverse;
    }
    
    &::before {
        top: -10px;
        left: -10px;
        opacity: 0.7;
    }
    
    &::after {
        bottom: -10px;
        right: -10px;
        opacity: 0.5;
        animation-delay: -2s;
    }
}

.floating-action-button {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    background: o-color('o-color-1');
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 8px 25px rgba(o-color('o-color-1'), 0.3);
    @include animate(all, 0.3s, cubic-bezier(0.25, 0.46, 0.45, 0.94));
    z-index: 1000;
    
    &:hover {
        transform: scale(1.1) rotate(15deg);
        box-shadow: 0 12px 35px rgba(o-color('o-color-1'), 0.4);
        background: o-color('o-color-2');
    }
    
    &:active {
        transform: scale(0.95);
    }
}

// =============================================================================
// ADVANCED MEDIA QUERIES
// =============================================================================

// High DPI displays
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .high-dpi-optimized {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
    .auto-dark {
        background: #1a1a1a;
        color: #ffffff;
        
        .card {
            background: #2a2a2a;
            border-color: #404040;
        }
        
        .form-control {
            background: #2a2a2a;
            border-color: #404040;
            color: #ffffff;
            
            &::placeholder {
                color: #888888;
            }
        }
    }
}

// Print optimizations
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break-before {
        page-break-before: always;
    }
    
    .print-break-after {
        page-break-after: always;
    }
    
    .print-break-inside-avoid {
        page-break-inside: avoid;
    }
}

// =============================================================================
// ADVANCED ACCESSIBILITY
// =============================================================================

// Focus indicators for keyboard navigation
.focus-advanced:focus {
    outline: 3px solid o-color('o-color-1');
    outline-offset: 2px;
    border-radius: 4px;
}

// Screen reader only content
.sr-only-advanced {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

// Skip links
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: o-color('o-color-1');
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
    
    &:focus {
        top: 6px;
    }
}

// =============================================================================
// PERFORMANCE OPTIMIZATIONS
// =============================================================================

// GPU acceleration for complex animations
.gpu-optimized {
    transform: translateZ(0);
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
}

// Contain layout for better performance
.layout-contained {
    contain: layout style paint;
}

// Optimize repaints
.repaint-optimized {
    contain: paint;
    will-change: auto;
}

// =============================================================================
// UTILITY CLASSES FOR ADVANCED FEATURES
// =============================================================================

.cursor-pointer { cursor: pointer; }
.cursor-grab { cursor: grab; }
.cursor-grabbing { cursor: grabbing; }
.cursor-zoom-in { cursor: zoom-in; }
.cursor-zoom-out { cursor: zoom-out; }

.user-select-none { user-select: none; }
.user-select-all { user-select: all; }
.user-select-text { user-select: text; }

.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

.position-sticky { position: sticky; }
.position-fixed { position: fixed; }
.position-absolute { position: absolute; }
.position-relative { position: relative; }
