// =============================================================================
// OMISOL THEME - COLOR SYSTEM
// =============================================================================
// Comprehensive color system using Odoo's color palette and variables

// =============================================================================
// COLOR UTILITY CLASSES
// =============================================================================

// Primary brand colors
.text-o-color-1 { color: o-color('o-color-1') !important; }
.text-o-color-2 { color: o-color('o-color-2') !important; }
.text-o-color-3 { color: o-color('o-color-3') !important; }
.text-o-color-4 { color: o-color('o-color-4') !important; }
.text-o-color-5 { color: o-color('o-color-5') !important; }

.bg-o-color-1 { background-color: o-color('o-color-1') !important; }
.bg-o-color-2 { background-color: o-color('o-color-2') !important; }
.bg-o-color-3 { background-color: o-color('o-color-3') !important; }
.bg-o-color-4 { background-color: o-color('o-color-4') !important; }
.bg-o-color-5 { background-color: o-color('o-color-5') !important; }

// Secondary colors
.text-secondary-color { color: o-website-value('secondary-color') !important; }
.text-secondary-light { color: o-website-value('secondary-light') !important; }
.text-secondary-dark { color: o-website-value('secondary-dark') !important; }

.bg-secondary-color { background-color: o-website-value('secondary-color') !important; }
.bg-secondary-light { background-color: o-website-value('secondary-light') !important; }
.bg-secondary-dark { background-color: o-website-value('secondary-dark') !important; }

// Accent colors
.text-accent { color: o-website-value('accent-orange') !important; }
.text-accent-light { color: o-website-value('accent-orange-light') !important; }
.text-accent-dark { color: o-website-value('accent-orange-dark') !important; }

.bg-accent { background-color: o-website-value('accent-orange') !important; }
.bg-accent-light { background-color: o-website-value('accent-orange-light') !important; }
.bg-accent-dark { background-color: o-website-value('accent-orange-dark') !important; }

// Neutral colors
.text-neutral-100 { color: o-website-value('neutral-100') !important; }
.text-neutral-200 { color: o-website-value('neutral-200') !important; }
.text-neutral-300 { color: o-website-value('neutral-300') !important; }
.text-neutral-400 { color: o-website-value('neutral-400') !important; }
.text-neutral-500 { color: o-website-value('neutral-500') !important; }
.text-neutral-600 { color: o-website-value('neutral-600') !important; }
.text-neutral-700 { color: o-website-value('neutral-700') !important; }
.text-neutral-800 { color: o-website-value('neutral-800') !important; }
.text-neutral-900 { color: o-website-value('neutral-900') !important; }

.bg-neutral-100 { background-color: o-website-value('neutral-100') !important; }
.bg-neutral-200 { background-color: o-website-value('neutral-200') !important; }
.bg-neutral-300 { background-color: o-website-value('neutral-300') !important; }
.bg-neutral-400 { background-color: o-website-value('neutral-400') !important; }
.bg-neutral-500 { background-color: o-website-value('neutral-500') !important; }
.bg-neutral-600 { background-color: o-website-value('neutral-600') !important; }
.bg-neutral-700 { background-color: o-website-value('neutral-700') !important; }
.bg-neutral-800 { background-color: o-website-value('neutral-800') !important; }
.bg-neutral-900 { background-color: o-website-value('neutral-900') !important; }

// Semantic colors
.text-success { color: o-website-value('success-color') !important; }
.text-warning { color: o-website-value('warning-color') !important; }
.text-danger { color: o-website-value('danger-color') !important; }
.text-info { color: o-website-value('info-color') !important; }

.bg-success { background-color: o-website-value('success-color') !important; }
.bg-warning { background-color: o-website-value('warning-color') !important; }
.bg-danger { background-color: o-website-value('danger-color') !important; }
.bg-info { background-color: o-website-value('info-color') !important; }

// Border colors
.border-primary { border-color: o-color('o-color-1') !important; }
.border-secondary { border-color: o-website-value('secondary-color') !important; }
.border-accent { border-color: o-website-value('accent-orange') !important; }
.border-light { border-color: o-website-value('border-light') !important; }
.border-dark { border-color: o-website-value('border-dark') !important; }

// =============================================================================
// COMPONENT COLOR OVERRIDES
// =============================================================================

// Links with consistent colors - No underlines
a {
    color: o-color('o-color-1');
    text-decoration: none;
    @include animate(color);

    &:hover {
        color: o-color('o-color-2');
        text-decoration: none;
    }
    
    &:focus {
        color: o-color('o-color-2');
        outline: 2px solid rgba(o-color('o-color-1'), 0.3);
        outline-offset: 2px;
    }
    
    &:visited {
        color: o-color('o-color-4');
    }
}

// Button color overrides
.btn {
    @include animate(all, 0.3s, ease);
    
    &:focus {
        box-shadow: 0 0 0 0.2rem rgba(o-color('o-color-1'), 0.25);
    }
}

.btn-primary {
    background-color: o-color('o-color-1');
    border-color: o-color('o-color-1');
    color: white;
    
    &:hover {
        background-color: o-color('o-color-2');
        border-color: o-color('o-color-2');
        color: white;
    }
    
    &:focus, &.focus {
        background-color: o-color('o-color-2');
        border-color: o-color('o-color-2');
        box-shadow: 0 0 0 0.2rem rgba(o-color('o-color-1'), 0.5);
    }
    
    &:active, &.active {
        background-color: o-color('o-color-4');
        border-color: o-color('o-color-4');
    }
    
    &.disabled, &:disabled {
        background-color: o-website-value('neutral-400');
        border-color: o-website-value('neutral-400');
        opacity: 0.65;
    }
}

.btn-secondary {
    background-color: transparent;
    border-color: o-website-value('secondary-color');
    color: o-website-value('secondary-color');
    
    &:hover {
        background-color: o-website-value('secondary-color');
        border-color: o-website-value('secondary-color');
        color: white;
    }
    
    &:focus, &.focus {
        background-color: o-website-value('secondary-color');
        border-color: o-website-value('secondary-color');
        color: white;
        box-shadow: 0 0 0 0.2rem rgba(o-website-value('secondary-color'), 0.5);
    }
    
    &:active, &.active {
        background-color: o-website-value('secondary-dark');
        border-color: o-website-value('secondary-dark');
        color: white;
    }
}

.btn-success {
    background-color: o-website-value('success-color');
    border-color: o-website-value('success-color');
    
    &:hover {
        background-color: darken(o-website-value('success-color'), 10%);
        border-color: darken(o-website-value('success-color'), 10%);
    }
}

.btn-warning {
    background-color: o-website-value('warning-color');
    border-color: o-website-value('warning-color');
    color: o-website-value('neutral-800');
    
    &:hover {
        background-color: darken(o-website-value('warning-color'), 10%);
        border-color: darken(o-website-value('warning-color'), 10%);
        color: o-website-value('neutral-800');
    }
}

.btn-danger {
    background-color: o-website-value('danger-color');
    border-color: o-website-value('danger-color');
    
    &:hover {
        background-color: darken(o-website-value('danger-color'), 10%);
        border-color: darken(o-website-value('danger-color'), 10%);
    }
}

.btn-info {
    background-color: o-website-value('info-color');
    border-color: o-website-value('info-color');
    
    &:hover {
        background-color: darken(o-website-value('info-color'), 10%);
        border-color: darken(o-website-value('info-color'), 10%);
    }
}

.btn-light {
    background-color: o-website-value('neutral-100');
    border-color: o-website-value('neutral-300');
    color: o-website-value('neutral-800');
    
    &:hover {
        background-color: o-website-value('neutral-200');
        border-color: o-website-value('neutral-400');
        color: o-website-value('neutral-800');
    }
}

.btn-dark {
    background-color: o-website-value('neutral-800');
    border-color: o-website-value('neutral-800');
    
    &:hover {
        background-color: o-website-value('neutral-900');
        border-color: o-website-value('neutral-900');
    }
}

// =============================================================================
// FORM CONTROL COLORS
// =============================================================================

.form-control {
    border-color: o-website-value('border-color');
    color: o-website-value('text-primary');
    
    &:focus {
        border-color: o-color('o-color-3');
        box-shadow: 0 0 0 0.2rem rgba(o-color('o-color-1'), 0.25);
        color: o-website-value('text-primary');
    }
    
    &::placeholder {
        color: o-website-value('text-muted');
    }
    
    &:disabled, &[readonly] {
        background-color: o-website-value('neutral-100');
        border-color: o-website-value('neutral-300');
        color: o-website-value('text-muted');
    }
}

.form-select {
    border-color: o-website-value('border-color');
    color: o-website-value('text-primary');
    
    &:focus {
        border-color: o-color('o-color-3');
        box-shadow: 0 0 0 0.2rem rgba(o-color('o-color-1'), 0.25);
    }
}

.form-check-input {
    border-color: o-website-value('border-color');
    
    &:checked {
        background-color: o-color('o-color-1');
        border-color: o-color('o-color-1');
    }
    
    &:focus {
        box-shadow: 0 0 0 0.25rem rgba(o-color('o-color-1'), 0.25);
    }
}

// =============================================================================
// ALERT COLORS
// =============================================================================

.alert {
    border-width: 2px;
    border-radius: o-website-value('border-radius-base');
}

.alert-primary {
    background-color: rgba(o-color('o-color-1'), 0.1);
    border-color: o-color('o-color-1');
    color: o-color('o-color-4');
}

.alert-secondary {
    background-color: rgba(o-website-value('secondary-color'), 0.1);
    border-color: o-website-value('secondary-color');
    color: o-website-value('secondary-dark');
}

.alert-success {
    background-color: rgba(o-website-value('success-color'), 0.1);
    border-color: o-website-value('success-color');
    color: darken(o-website-value('success-color'), 20%);
}

.alert-warning {
    background-color: rgba(o-website-value('warning-color'), 0.1);
    border-color: o-website-value('warning-color');
    color: darken(o-website-value('warning-color'), 30%);
}

.alert-danger {
    background-color: rgba(o-website-value('danger-color'), 0.1);
    border-color: o-website-value('danger-color');
    color: darken(o-website-value('danger-color'), 10%);
}

.alert-info {
    background-color: rgba(o-website-value('info-color'), 0.1);
    border-color: o-website-value('info-color');
    color: darken(o-website-value('info-color'), 20%);
}

// =============================================================================
// BADGE COLORS
// =============================================================================

.badge {
    font-weight: 600;
    border-radius: o-website-value('border-radius-pill');
}

.badge-primary {
    background-color: o-color('o-color-1');
    color: white;
}

.badge-secondary {
    background-color: o-website-value('secondary-color');
    color: white;
}

.badge-success {
    background-color: o-website-value('success-color');
    color: white;
}

.badge-warning {
    background-color: o-website-value('warning-color');
    color: o-website-value('neutral-800');
}

.badge-danger {
    background-color: o-website-value('danger-color');
    color: white;
}

.badge-info {
    background-color: o-website-value('info-color');
    color: white;
}

.badge-light {
    background-color: o-website-value('neutral-200');
    color: o-website-value('neutral-800');
}

.badge-dark {
    background-color: o-website-value('neutral-800');
    color: white;
}

// =============================================================================
// CARD COLORS
// =============================================================================

.card {
    border-color: o-website-value('border-color');
    background-color: o-website-value('bg-primary');
    
    &.border-primary {
        border-color: o-color('o-color-1');
    }
    
    &.border-secondary {
        border-color: o-website-value('secondary-color');
    }
    
    &.border-accent {
        border-color: o-website-value('accent-orange');
    }
}

.card-header {
    background-color: rgba(o-website-value('neutral-100'), 0.5);
    border-bottom-color: o-website-value('border-color');
    color: o-website-value('text-primary');
}

.card-footer {
    background-color: rgba(o-website-value('neutral-100'), 0.5);
    border-top-color: o-website-value('border-color');
    color: o-website-value('text-secondary');
}

// =============================================================================
// DROPDOWN COLORS
// =============================================================================

.dropdown-menu {
    border-color: o-color('o-color-1');
    background-color: o-website-value('bg-primary');
    @include shadow('lg');
}

.dropdown-item {
    color: o-website-value('text-primary');
    
    &:hover, &:focus {
        background-color: o-color('o-color-1');
        color: white;
    }
    
    &.active, &:active {
        background-color: o-color('o-color-2');
        color: white;
    }
    
    &.disabled, &:disabled {
        color: o-website-value('text-muted');
    }
}

.dropdown-divider {
    border-top-color: o-website-value('border-color');
}

.dropdown-header {
    color: o-website-value('text-secondary');
}

// =============================================================================
// NAVIGATION COLORS
// =============================================================================

.nav-link {
    color: o-website-value('text-primary');
    
    &:hover, &:focus {
        color: o-color('o-color-1');
    }
    
    &.active {
        color: o-color('o-color-1');
    }
    
    &.disabled {
        color: o-website-value('text-muted');
    }
}

.nav-tabs {
    border-bottom-color: o-website-value('border-color');
    
    .nav-link {
        border-color: transparent;
        
        &:hover, &:focus {
            border-color: o-website-value('border-light') o-website-value('border-light') o-website-value('border-color');
        }
        
        &.active {
            background-color: o-website-value('bg-primary');
            border-color: o-website-value('border-color') o-website-value('border-color') o-website-value('bg-primary');
            color: o-color('o-color-1');
        }
    }
}

.nav-pills {
    .nav-link {
        &.active {
            background-color: o-color('o-color-1');
            color: white;
        }
    }
}

// =============================================================================
// PAGINATION COLORS
// =============================================================================

.page-link {
    color: o-color('o-color-1');
    border-color: o-website-value('border-color');
    
    &:hover {
        color: o-color('o-color-2');
        background-color: o-website-value('neutral-100');
        border-color: o-website-value('border-color');
    }
    
    &:focus {
        box-shadow: 0 0 0 0.2rem rgba(o-color('o-color-1'), 0.25);
    }
}

.page-item.active .page-link {
    background-color: o-color('o-color-1');
    border-color: o-color('o-color-1');
    color: white;
}

.page-item.disabled .page-link {
    color: o-website-value('text-muted');
    background-color: o-website-value('neutral-100');
    border-color: o-website-value('border-light');
}

// =============================================================================
// PROGRESS COLORS
// =============================================================================

.progress {
    background-color: o-website-value('neutral-200');
}

.progress-bar {
    background-color: o-color('o-color-1');
}

.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

// =============================================================================
// TABLE COLORS
// =============================================================================

.table {
    color: o-website-value('text-primary');
    
    th, td {
        border-top-color: o-website-value('border-color');
    }
    
    thead th {
        border-bottom-color: o-website-value('border-dark');
        color: o-website-value('text-primary');
    }
    
    tbody + tbody {
        border-top-color: o-website-value('border-dark');
    }
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(o-website-value('neutral-100'), 0.5);
}

.table-hover tbody tr:hover {
    background-color: rgba(o-color('o-color-1'), 0.075);
}

.table-primary {
    background-color: rgba(o-color('o-color-1'), 0.1);
    border-color: rgba(o-color('o-color-1'), 0.2);
}

.table-secondary {
    background-color: rgba(o-website-value('secondary-color'), 0.1);
    border-color: rgba(o-website-value('secondary-color'), 0.2);
}

// =============================================================================
// MODAL COLORS
// =============================================================================

.modal-content {
    background-color: o-website-value('bg-primary');
    border-color: rgba(o-website-value('neutral-800'), 0.2);
}

.modal-header {
    border-bottom-color: o-website-value('border-color');
    
    .modal-title {
        color: o-website-value('text-primary');
    }
    
    .btn-close {
        filter: invert(1) grayscale(100%) brightness(200%);
    }
}

.modal-footer {
    border-top-color: o-website-value('border-color');
}

.modal-backdrop {
    background-color: o-website-value('neutral-900');
}
