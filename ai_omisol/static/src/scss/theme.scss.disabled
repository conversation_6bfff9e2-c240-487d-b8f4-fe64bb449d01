// =============================================================================
// OMISOL THEME - MAIN THEME STYLES
// =============================================================================
// This file contains the main theme styles using Odoo variables and mixins

// =============================================================================
// BASE STYLES
// =============================================================================

body {
    font-family: o-website-value('body-font');
    font-weight: o-website-value('body-font-weight');
    line-height: o-website-value('line-height-base');
    color: o-website-value('text-primary');
    background: o-website-value('bg-tertiary');
    
    // Subtle background pattern for industrial feel
    background-image:
        linear-gradient(45deg, rgba(o-color('o-color-1'), 0.03) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(o-color('o-color-1'), 0.03) 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, rgba(o-color('o-color-1'), 0.03) 75%),
        linear-gradient(-45deg, transparent 75%, rgba(o-color('o-color-1'), 0.03) 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    min-height: 100vh;
}

// =============================================================================
// TYPOGRAPHY
// =============================================================================

h1, h2, h3, h4, h5, h6 {
    font-family: o-website-value('headings-font');
    font-weight: o-website-value('headings-font-weight');
    line-height: o-website-value('line-height-headings');
    letter-spacing: o-website-value('letter-spacing-headings');
    color: o-website-value('text-primary');
    margin-bottom: 1rem;
}

h1 {
    @include heading-style('5xl', 800);
}

h2 {
    @include heading-style('4xl', 700);
}

h3 {
    @include heading-style('3xl', 600);
}

h4 {
    @include heading-style('2xl', 600);
}

h5 {
    @include heading-style('xl', 600);
}

h6 {
    @include heading-style('lg', 600);
}

p {
    @include text-style('base', null, o-website-value('text-primary'));
    margin-bottom: 1rem;
}

.lead {
    @include text-style('lg', 500, o-website-value('text-primary'), 1.7);
}

.text-muted {
    color: o-website-value('text-muted') !important;
}

// Links - No underlines
a {
    color: o-color('o-color-1');
    text-decoration: none;
    @include animate(color);

    &:hover {
        color: o-color('o-color-2');
        text-decoration: none;
    }
}

// =============================================================================
// HEADER & NAVIGATION
// =============================================================================

.omisol-header {
    @include omisol-gradient-bg('menu-gradient');
    border-bottom: 2px solid o-color('o-color-1'); // Reduced border thickness
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: $navbar-shadow;
    backdrop-filter: blur(10px);
    padding: 0.5rem 0;

    &::after {
        content: '';
        position: absolute;
        bottom: -2px; // Adjusted for thinner border
        left: 0;
        right: 0;
        height: 2px; // Reduced height
        background: o-website-value('accent-orange');
    }
}

.omisol-navbar {
    padding: 1.25rem 0;
}

.omisol-logo {
    height: 50px;
    width: auto;
    max-width: 200px;
    @include animate(transform);
    
    &:hover {
        transform: scale(1.05);
    }
}

.omisol-nav-link {
    color: o-website-value('navbar-link-color');
    font-weight: o-website-value('navbar-font-weight');
    font-family: o-website-value('navbar-font');
    text-decoration: none !important;
    padding: 0.75rem 1.25rem;
    @include animate();
    position: relative;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: o-website-value('letter-spacing-navbar');
    border-radius: o-website-value('border-radius-sm');
    border: none; // Remove any borders

    &:hover {
        color: o-website-value('navbar-link-hover');
        background: linear-gradient(135deg, rgba(o-color('o-color-1'), 0.08) 0%, rgba(o-website-value('accent-orange'), 0.08) 100%); // Subtle background
        transform: translateY(-1px); // Reduced movement for subtlety
        text-decoration: none !important;
    }

    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px; // Reduced height
        background: linear-gradient(90deg, o-color('o-color-1') 0%, o-website-value('accent-orange') 100%);
        @include animate(width);
        border-radius: 1px;
    }

    &:hover::after {
        width: 100%;
    }

    &:focus {
        outline: none;
        text-decoration: none !important;
    }

    i {
        color: o-color('o-color-1');
        font-size: 0.9rem;
        font-family: 'Font Awesome 6 Free'; // Ensure icon font is specified
    }
}

// Dropdown styling
.omisol-dropdown {
    border: 1px solid o-color('o-color-1'); // Reduced border thickness
    border-radius: o-website-value('border-radius-base');
    @include shadow('lg');
    background: o-website-value('navbar-dropdown-bg');
    margin-top: 0.5rem;

    .dropdown-item {
        padding: 0.75rem 1.25rem;
        color: o-website-value('text-primary');
        font-weight: 500;
        font-family: o-website-value('navbar-font');
        @include animate();
        display: flex;
        align-items: center;
        text-decoration: none !important;
        border: none; // Remove borders

        &:hover {
            background: linear-gradient(135deg, o-color('o-color-1') 0%, o-color('o-color-2') 100%); // Gradient background
            color: white;
            transform: translateX(3px); // Reduced movement for subtlety
            text-decoration: none !important;
        }

        &:focus {
            outline: none;
            text-decoration: none !important;
        }

        i {
            color: o-color('o-color-1');
            @include animate(color);
            font-family: 'Font Awesome 6 Free';
        }

        &:hover i {
            color: white;
        }
    }
}

// Contact button in header
.btn-omisol-contact {
    @include btn-style(
        o-color('o-color-1'),
        o-color('o-color-1'),
        white,
        o-website-value('accent-orange'),
        o-website-value('accent-orange'),
        white
    );
    @include shadow('sm');
    
    &:hover {
        @include shadow('md');
    }
    
    i {
        color: white;
    }
}

// =============================================================================
// HERO SECTION
// =============================================================================

.omisol-hero {
    @include omisol-gradient-bg('hero-gradient');
    color: o-website-value('text-white');
    padding: $hero-padding-y 0;
    position: relative;
    overflow: hidden;
    min-height: $hero-min-height;
    display: flex;
    align-items: center;
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: $hero-overlay-pattern;
        background-size: 40px 40px;
        background-position: 0 0, 0 20px, 20px -20px, -20px 0px;
        opacity: 0.3;
    }
    
    &::after {
        content: '';
        position: absolute;
        top: 0;
        right: -10%;
        width: 40%;
        height: 100%;
        background: radial-gradient(ellipse at center, rgba(255,255,255,0.1) 0%, transparent 70%);
        opacity: 0.6;
    }
    
    @include responsive('md') {
        padding: $hero-padding-y-mobile 0;
        text-align: center;
    }
}

.omisol-hero-content {
    position: relative;
    z-index: 2;
}

.omisol-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    font-weight: 800;
    color: white;
    
    @include responsive('lg') {
        font-size: 3rem;
    }
    
    @include responsive('md') {
        font-size: 2.5rem;
    }
    
    @include responsive('xs') {
        font-size: 2.25rem;
    }
}

.omisol-hero p {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    color: #e9ecef;
    font-weight: 400;
    line-height: 1.7;
    
    @include responsive('md') {
        font-size: 1rem;
        margin-bottom: 2rem;
    }
    
    @include responsive('xs') {
        font-size: 0.95rem;
    }
}

// =============================================================================
// BUTTONS
// =============================================================================

.btn-omisol-primary {
    background: linear-gradient(135deg, o-color('o-color-1') 0%, o-color('o-color-2') 100%);
    border: none !important; // Remove borders
    color: white;
    padding: 16px 40px;
    font-size: 0.9rem;
    font-family: o-website-value('buttons-font');
    font-weight: o-website-value('buttons-font-weight');
    text-transform: uppercase;
    letter-spacing: o-website-value('letter-spacing-buttons');
    border-radius: o-website-value('border-radius-sm');
    box-shadow: 0 4px 15px rgba(o-color('o-color-1'), 0.25); // Subtle shadow
    position: relative;
    z-index: 10;
    @include animate(all, 0.3s, cubic-bezier(0.25, 0.46, 0.45, 0.94));
    text-decoration: none !important;

    &:hover {
        background: linear-gradient(135deg, o-color('o-color-2') 0%, o-color('o-color-4') 100%);
        box-shadow: 0 6px 20px rgba(o-color('o-color-1'), 0.35); // Subtle increase
        transform: translateY(-2px); // Subtle lift
        color: white;
        text-decoration: none !important;
    }

    &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(o-color('o-color-1'), 0.2);
        text-decoration: none !important;
    }

    &:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(o-color('o-color-1'), 0.3);
    }
}

.btn-omisol-secondary {
    background: linear-gradient(135deg, white 0%, rgba(o-website-value('secondary-color'), 0.05) 100%);
    border: 1px solid o-website-value('secondary-color') !important; // Thin border
    color: o-website-value('secondary-color');
    padding: 14px 38px;
    font-size: 0.9rem;
    font-family: o-website-value('buttons-font');
    font-weight: o-website-value('buttons-font-weight');
    text-transform: uppercase;
    letter-spacing: o-website-value('letter-spacing-buttons');
    border-radius: o-website-value('border-radius-sm');
    box-shadow: 0 4px 15px rgba(o-website-value('secondary-color'), 0.15); // Subtle shadow
    position: relative;
    z-index: 1;
    @include animate(all, 0.3s, cubic-bezier(0.25, 0.46, 0.45, 0.94));
    text-decoration: none !important;

    &:hover {
        background: linear-gradient(135deg, o-website-value('secondary-color') 0%, o-website-value('secondary-dark') 100%);
        color: white;
        box-shadow: 0 6px 20px rgba(o-website-value('secondary-color'), 0.25); // Subtle increase
        transform: translateY(-2px); // Subtle lift
        text-decoration: none !important;
    }

    &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(o-website-value('secondary-color'), 0.2);
        text-decoration: none !important;
    }

    &:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(o-website-value('secondary-color'), 0.2);
    }
}

// =============================================================================
// SECTIONS
// =============================================================================

.omisol-section {
    @include section-style();

    &:nth-child(even) {
        background: o-website-value('card-gradient');
        border-top: 1px solid rgba(o-color('o-color-1'), 0.1); // Reduced border thickness
        border-bottom: 1px solid rgba(o-website-value('accent-orange'), 0.1);
    }

    &:nth-child(odd) {
        background: o-website-value('bg-primary');
    }

    // Industrial accent bars
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px; // Reduced height
        background: o-website-value('accent-orange');
        z-index: 1;
    }

    // Industrial grid pattern overlay
    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            linear-gradient(rgba(o-color('o-color-1'), 0.01) 1px, transparent 1px), // More subtle
            linear-gradient(90deg, rgba(o-color('o-color-1'), 0.01) 1px, transparent 1px);
        background-size: 20px 20px;
        pointer-events: none;
        z-index: 0;
    }
}

.omisol-section-title {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    color: o-color('o-color-1');
    font-family: o-website-value('headings-font');
    font-weight: o-website-value('headings-font-weight');

    &::after {
        content: '';
        position: absolute;
        bottom: -15px;
        left: 50%;
        width: 80px;
        height: 3px; // Reduced height
        background: o-website-value('accent-orange');
        transform: translateX(-50%);
        border-radius: 2px;
    }
}

// Statistics section
.omisol-stats {
    background: o-website-value('stats-gradient');
    position: relative;

    .omisol-stat-item {
        text-align: center;
        padding: 2rem 1rem;
        @include animate(transform, 0.3s, ease);

        &:hover {
            transform: translateY(-5px);
        }

        i {
            display: block;
            margin-bottom: 1rem;
            @include animate(transform, 0.3s, ease);
        }

        &:hover i {
            transform: scale(1.1);
        }
    }

    .omisol-stat-number {
        display: block;
        font-family: o-website-value('headings-font');
        font-weight: 800;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .omisol-stat-label {
        display: block;
        font-family: o-website-value('body-font');
        font-weight: 600;
        line-height: 1.2;
    }
}

// =============================================================================
// CARDS
// =============================================================================

.omisol-product-card {
    @include card-style();
    margin-bottom: 2rem;
    border: 1px solid o-website-value('border-color') !important; // Thin modern border

    .product-card-link {
        text-decoration: none !important;
        color: inherit;
        display: block;
        @include animate();

        &:hover {
            text-decoration: none !important;
            color: inherit;
            transform: translateY(-5px);
        }
    }
}

.omisol-product-image {
    width: 100%;
    height: 280px;
    object-fit: contain;
    border-bottom: 1px solid o-website-value('border-color'); // Reduced border thickness
    cursor: default;
}

.product-image-container {
    overflow: hidden;
    position: relative;
    border-radius: o-website-value('border-radius-base') o-website-value('border-radius-base') 0 0;
}

.omisol-product-content {
    padding: 2rem;
    background: o-website-value('neutral-100');
}

.omisol-product-title {
    font-family: o-website-value('headings-font');
    font-size: 1.375rem;
    font-weight: 700;
    color: o-website-value('text-primary');
    text-transform: uppercase;
    letter-spacing: 0.025em;
    margin-bottom: 0.75rem;
    line-height: o-website-value('line-height-headings');
}

.omisol-product-description {
    font-family: o-website-value('body-font');
    font-size: 1rem;
    font-weight: 400;
    color: o-website-value('text-primary');
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.omisol-product-category {
    background: o-color('o-color-1');
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    font-weight: 700;
    font-family: o-website-value('buttons-font');
    display: inline-block;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    border-radius: o-website-value('border-radius-pill');
    border: none; // Remove border
}

// =============================================================================
// RESPONSIVE DESIGN
// =============================================================================

@include responsive('xs') {
    .btn-omisol-primary,
    .btn-omisol-secondary {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
        text-align: center;
        padding: 16px 20px;
    }
    
    .omisol-navbar {
        padding: 1rem 0;
    }
    
    .omisol-logo {
        height: 40px;
    }
    
    // Mobile menu styles
    .navbar-collapse {
        background: white;
        border: 2px solid o-website-value('border-color');
        margin-top: 1rem;
        padding: 1rem;
        border-radius: o-website-value('border-radius-base');
    }
    
    .omisol-nav-link {
        border-bottom: 1px solid o-website-value('border-color');
        margin-bottom: 0.5rem;
        padding: 1rem;
        
        &:last-child {
            border-bottom: none;
        }
    }
}
