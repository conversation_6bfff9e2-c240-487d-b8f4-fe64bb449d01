// =============================================================================
// OMISOL THEME - PRIMARY VARIABLES
// =============================================================================
// This file defines the primary theme variables following Odoo's theming system
// These variables will be available in the Website Builder for customization

// =============================================================================
// FONTS CONFIGURATION
// =============================================================================

$o-website-values-palettes: (
    (
        // Typography - Professional Industrial Theme
        'headings-font': ('Roboto', 'Arial', 'Helvetica', sans-serif),
        'body-font': ('Roboto', 'Arial', 'Helvetica', sans-serif),
        'buttons-font': ('Roboto', 'Arial', 'Helvetica', sans-serif),
        'navbar-font': ('Roboto', 'Arial', 'Helvetica', sans-serif),
        
        // Font weights
        'headings-font-weight': 700,
        'body-font-weight': 400,
        'buttons-font-weight': 600,
        'navbar-font-weight': 600,
        
        // Font sizes (using rem units for scalability)
        'font-size-base': 1rem,
        'font-size-lg': 1.125rem,
        'font-size-sm': 0.875rem,
        
        // Line heights
        'line-height-base': 1.6,
        'line-height-headings': 1.2,
        
        // Letter spacing (converted to rem for consistency)
        'letter-spacing-headings': -0.025rem,
        'letter-spacing-buttons': 0.05rem,
        'letter-spacing-navbar': 0.05rem,
        
        // =============================================================================
        // COLOR PALETTE - INDUSTRIAL PROFESSIONAL
        // =============================================================================
        
        // Primary Colors (Company Brand Blue)
        'o-color-1': #007bff,        // Primary Blue
        'o-color-2': #0056b3,        // Primary Blue Dark
        'o-color-3': #4dabf7,        // Primary Blue Light
        'o-color-4': #003d82,        // Primary Blue Darker
        'o-color-5': #1e293b,        // Text Primary (Dark Gray)
        
        // Secondary Colors (Industrial Theme)
        'secondary-color': #1e40af,   // Industrial Blue
        'secondary-light': #3b82f6,   // Industrial Blue Light
        'secondary-dark': #1e3a8a,    // Industrial Blue Dark
        
        // Accent Colors
        'accent-orange': #f97316,     // Orange Accent
        'accent-orange-light': #fb923c,
        'accent-orange-dark': #ea580c,
        
        // Neutral Colors
        'neutral-100': #f8fafc,       // Very Light Gray
        'neutral-200': #f1f5f9,       // Light Gray
        'neutral-300': #e2e8f0,       // Medium Light Gray
        'neutral-400': #94a3b8,       // Medium Gray
        'neutral-500': #64748b,       // Gray
        'neutral-600': #475569,       // Dark Gray
        'neutral-700': #334155,       // Very Dark Gray
        'neutral-800': #1e293b,       // Almost Black
        'neutral-900': #0f172a,       // Black
        
        // Semantic Colors
        'success-color': #16a34a,     // Green
        'warning-color': #eab308,     // Yellow
        'danger-color': #dc2626,      // Red
        'info-color': #0ea5e9,        // Light Blue
        
        // Background Colors
        'bg-primary': #ffffff,        // White
        'bg-secondary': #f8fafc,      // Very Light Gray
        'bg-tertiary': #f1f5f9,       // Light Gray
        'bg-dark': #1e293b,           // Dark Background
        
        // Text Colors
        'text-primary': #1e293b,      // Primary Text
        'text-secondary': #64748b,    // Secondary Text
        'text-muted': #94a3b8,        // Muted Text
        'text-white': #ffffff,        // White Text
        
        // Border Colors
        'border-color': #e2e8f0,      // Default Border
        'border-light': #f1f5f9,      // Light Border
        'border-dark': #94a3b8,       // Dark Border
        
        // =============================================================================
        // COMPONENT SPECIFIC COLORS
        // =============================================================================
        
        // Header & Navigation
        'header-bg': linear-gradient(135deg, #ffffff 0%, #f8fafc 100%),
        'header-border': #007bff,
        'navbar-link-color': #1e293b,
        'navbar-link-hover': #007bff,
        'navbar-dropdown-bg': #ffffff,
        
        // Buttons
        'btn-primary-bg': #007bff,
        'btn-primary-border': #0056b3,
        'btn-primary-hover': #0056b3,
        'btn-secondary-bg': transparent,
        'btn-secondary-border': #1e40af,
        'btn-secondary-color': #1e40af,
        'btn-secondary-hover': #1e40af,
        
        // Cards & Components
        'card-bg': #ffffff,
        'card-border': #007bff,
        'card-shadow': rgba(0, 123, 255, 0.15),
        'card-hover-shadow': rgba(0, 123, 255, 0.25),
        
        // Footer
        'footer-bg': linear-gradient(135deg, #0056b3 0%, #003d82 100%),
        'footer-text': #e2e8f0,
        'footer-link': #e2e8f0,
        'footer-link-hover': #f97316,
        'footer-border': #f97316,
        
        // =============================================================================
        // SPACING & SIZING
        // =============================================================================
        
        // Section padding
        'section-padding-y': 6.25rem,  // 100px
        'section-padding-y-sm': 3.75rem,  // 60px
        'section-padding-y-xs': 2.5rem,  // 40px

        // Container max-widths
        'container-max-width': 75rem,  // 1200px
        'container-max-width-lg': 87.5rem,  // 1400px
        
        // Border radius
        'border-radius-base': 0.5rem, // 8px
        'border-radius-sm': 0.25rem, // 4px
        'border-radius-lg': 0.75rem, // 12px
        'border-radius-pill': 3.125rem, // 50px
        
        // Box shadows
        'shadow-sm': 0 2px 4px rgba(0, 0, 0, 0.1),
        'shadow-md': 0 4px 8px rgba(0, 0, 0, 0.1),
        'shadow-lg': 0 8px 25px rgba(0, 0, 0, 0.15),
        'shadow-xl': 0 15px 35px rgba(0, 0, 0, 0.2),
        
        // Transitions
        'transition-base': all 0.3s ease,
        'transition-fast': all 0.2s ease,
        'transition-slow': all 0.5s ease,
        
        // =============================================================================
        // GRADIENTS
        // =============================================================================
        
        'menu-gradient': linear-gradient(135deg, #ffffff 0%, #f8fafc 100%),
        'header-boxed-gradient': linear-gradient(135deg, #007bff 0%, #0056b3 70%, #475569 100%),
        'footer-gradient': linear-gradient(135deg, #0056b3 0%, #003d82 100%),
        'copyright-gradient': linear-gradient(135deg, #003d82 0%, #1e293b 100%),
        'hero-gradient': linear-gradient(135deg, #007bff 0%, #0056b3 70%, #475569 100%),
        'card-gradient': linear-gradient(135deg, rgba(0, 123, 255, 0.03) 0%, rgba(249, 115, 22, 0.03) 100%),
        'stats-gradient': linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(249, 115, 22, 0.1) 100%),
    ),
);

// =============================================================================
// BOOTSTRAP VARIABLE OVERRIDES
// =============================================================================
// These variables override Bootstrap defaults to match our theme

// Typography
// $font-family-sans-serif: 'Roboto', 'Arial', 'Helvetica', sans-serif !default;
// $font-family-base: $font-family-sans-serif !default;
// $headings-font-family: $font-family-sans-serif !default;

// Font sizes
$font-size-base: 1rem !default;
$font-size-lg: 1.125rem !default;
$font-size-sm: 0.875rem !default;

// Line heights
$line-height-base: 1.6 !default;
$headings-line-height: 1.2 !default;

// Colors - Using Odoo color functions
$primary: #007bff !default;
$secondary: #1e40af !default;
$success: #16a34a !default;
$info: #0ea5e9 !default;
$warning: #eab308 !default;
$danger: #dc2626 !default;
$light: #f8fafc !default;
$dark: #1e293b !default;

// Body
$body-bg: #f1f5f9 !default;
$body-color: #1e293b !default;

// Links
$link-color: #007bff !default;
$link-hover-color: #0056b3 !default;

// Borders
$border-color: #e2e8f0 !default;
$border-radius: 0.5rem !default; // 8px
$border-radius-sm: 0.25rem !default; // 4px
$border-radius-lg: 0.75rem !default; // 12px

// Shadows
$box-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1) !default;
$box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !default;
$box-shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15) !default;

// Navbar
$navbar-padding-y: 1rem !default;
$navbar-nav-link-padding-x: 1.25rem !default;
$navbar-brand-font-size: 1.5rem !default;

// Buttons
$btn-padding-y: 0.75rem !default;
$btn-padding-x: 1.5rem !default;
$btn-font-weight: 600 !default;
$btn-border-radius: 0.375rem !default; // 6px

// Cards
$card-border-width: 0.125rem !default; // 2px
$card-border-color: #007bff !default;
$card-border-radius: 0.5rem !default; // 8px
$card-box-shadow: 0 4px 8px rgba(0, 123, 255, 0.15) !default;

// Grid breakpoints (if needed to customize)
$grid-breakpoints: (
  xs: 0,
  sm: 36rem,    // 576px
  md: 48rem,    // 768px
  lg: 62rem,    // 992px
  xl: 75rem,    // 1200px
  xxl: 87.5rem  // 1400px
) !default;

// Container max widths
$container-max-widths: (
  sm: 33.75rem,  // 540px
  md: 45rem,     // 720px
  lg: 60rem,     // 960px
  xl: 75rem,     // 1200px
  xxl: 87.5rem   // 1400px
) !default;
