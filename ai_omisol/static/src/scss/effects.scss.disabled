// =============================================================================
// OMISOL THEME - VISUAL EFFECTS & ANIMATIONS
// =============================================================================
// Consistent hover effects, transitions, and animations using Odoo's patterns

// =============================================================================
// ANIMATION KEYFRAMES
// =============================================================================

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(o-color('o-color-1'), 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(o-color('o-color-1'), 0.6), 0 0 30px rgba(o-color('o-color-1'), 0.4);
    }
}

// =============================================================================
// ANIMATION UTILITY CLASSES
// =============================================================================

.animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out forwards;
}

.animate-slide-in-up {
    animation: slideInUp 0.5s ease-out forwards;
}

.animate-slide-in-down {
    animation: slideInDown 0.5s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-rotate {
    animation: rotate 2s linear infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
}

// Animation delays
.animate-delay-1 { animation-delay: 0.1s; }
.animate-delay-2 { animation-delay: 0.2s; }
.animate-delay-3 { animation-delay: 0.3s; }
.animate-delay-4 { animation-delay: 0.4s; }
.animate-delay-5 { animation-delay: 0.5s; }

// Animation durations
.animate-fast { animation-duration: 0.3s; }
.animate-slow { animation-duration: 1s; }
.animate-slower { animation-duration: 2s; }

// =============================================================================
// TRANSITION UTILITY CLASSES
// =============================================================================

.transition-all {
    transition: all o-website-value('transition-base');
}

.transition-colors {
    transition: color o-website-value('transition-base'), background-color o-website-value('transition-base'), border-color o-website-value('transition-base');
}

.transition-opacity {
    transition: opacity o-website-value('transition-base');
}

.transition-transform {
    transition: transform o-website-value('transition-base');
}

.transition-shadow {
    transition: box-shadow o-website-value('transition-base');
}

.transition-fast {
    transition-duration: o-website-value('transition-fast');
}

.transition-slow {
    transition-duration: o-website-value('transition-slow');
}

// =============================================================================
// HOVER EFFECTS
// =============================================================================

// Lift effect
.hover-lift {
    @include animate(transform);
    
    &:hover {
        transform: translateY(-5px);
    }
}

.hover-lift-lg {
    @include animate(transform);
    
    &:hover {
        transform: translateY(-10px);
    }
}

// Scale effects
.hover-scale {
    @include animate(transform);
    
    &:hover {
        transform: scale(1.05);
    }
}

.hover-scale-lg {
    @include animate(transform);
    
    &:hover {
        transform: scale(1.1);
    }
}

// Shadow effects
.hover-shadow {
    @include animate(box-shadow);
    
    &:hover {
        @include shadow('md');
    }
}

.hover-shadow-lg {
    @include animate(box-shadow);
    
    &:hover {
        @include shadow('lg');
    }
}

.hover-shadow-xl {
    @include animate(box-shadow);
    
    &:hover {
        @include shadow('xl');
    }
}

// Color effects
.hover-primary {
    @include animate(color);
    
    &:hover {
        color: o-color('o-color-1') !important;
    }
}

.hover-secondary {
    @include animate(color);
    
    &:hover {
        color: o-website-value('secondary-color') !important;
    }
}

.hover-accent {
    @include animate(color);
    
    &:hover {
        color: o-website-value('accent-orange') !important;
    }
}

// Background hover effects
.hover-bg-primary {
    @include animate(background-color);
    
    &:hover {
        background-color: o-color('o-color-1') !important;
        color: white !important;
    }
}

.hover-bg-secondary {
    @include animate(background-color);
    
    &:hover {
        background-color: o-website-value('secondary-color') !important;
        color: white !important;
    }
}

.hover-bg-accent {
    @include animate(background-color);
    
    &:hover {
        background-color: o-website-value('accent-orange') !important;
        color: white !important;
    }
}

.hover-bg-light {
    @include animate(background-color);
    
    &:hover {
        background-color: o-website-value('neutral-100') !important;
    }
}

// Border hover effects
.hover-border-primary {
    @include animate(border-color);
    
    &:hover {
        border-color: o-color('o-color-1') !important;
    }
}

.hover-border-secondary {
    @include animate(border-color);
    
    &:hover {
        border-color: o-website-value('secondary-color') !important;
    }
}

.hover-border-accent {
    @include animate(border-color);
    
    &:hover {
        border-color: o-website-value('accent-orange') !important;
    }
}

// =============================================================================
// FOCUS EFFECTS
// =============================================================================

.focus-ring {
    &:focus {
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(o-color('o-color-1'), 0.25);
    }
}

.focus-ring-secondary {
    &:focus {
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(o-website-value('secondary-color'), 0.25);
    }
}

.focus-ring-accent {
    &:focus {
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(o-website-value('accent-orange'), 0.25);
    }
}

// =============================================================================
// LOADING EFFECTS
// =============================================================================

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(o-color('o-color-1'), 0.3);
    border-radius: 50%;
    border-top-color: o-color('o-color-1');
    animation: rotate 1s ease-in-out infinite;
}

.loading-dots {
    display: inline-block;
    position: relative;
    width: 40px;
    height: 10px;
    
    &::before,
    &::after,
    & {
        content: '';
        position: absolute;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: o-color('o-color-1');
        animation: pulse 1.4s infinite ease-in-out both;
    }
    
    &::before {
        left: 0;
        animation-delay: -0.32s;
    }
    
    &::after {
        left: 17px;
        animation-delay: -0.16s;
    }
}

.loading-pulse {
    background: linear-gradient(90deg, transparent, rgba(o-color('o-color-1'), 0.2), transparent);
    background-size: 200% 100%;
    animation: loading-pulse 1.5s infinite;
}

@keyframes loading-pulse {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

// =============================================================================
// SCROLL ANIMATIONS
// =============================================================================

.scroll-fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
    
    &.in-view {
        opacity: 1;
        transform: translateY(0);
    }
}

.scroll-slide-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
    
    &.in-view {
        opacity: 1;
        transform: translateX(0);
    }
}

.scroll-slide-right {
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
    
    &.in-view {
        opacity: 1;
        transform: translateX(0);
    }
}

.scroll-scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
    
    &.in-view {
        opacity: 1;
        transform: scale(1);
    }
}

// =============================================================================
// SPECIAL EFFECTS
// =============================================================================

// Gradient text effect
.text-gradient {
    background: linear-gradient(135deg, o-color('o-color-1') 0%, o-website-value('accent-orange') 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

// Glitch effect
.glitch {
    position: relative;
    
    &::before,
    &::after {
        content: attr(data-text);
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
    
    &::before {
        animation: glitch-1 0.5s infinite;
        color: o-website-value('accent-orange');
        z-index: -1;
    }
    
    &::after {
        animation: glitch-2 0.5s infinite;
        color: o-color('o-color-1');
        z-index: -2;
    }
}

@keyframes glitch-1 {
    0%, 14%, 15%, 49%, 50%, 99%, 100% {
        transform: translate(0);
    }
    15%, 49% {
        transform: translate(-2px, -1px);
    }
    50%, 99% {
        transform: translate(1px, -2px);
    }
}

@keyframes glitch-2 {
    0%, 20%, 21%, 62%, 63%, 99%, 100% {
        transform: translate(0);
    }
    21%, 62% {
        transform: translate(2px, 1px);
    }
    63%, 99% {
        transform: translate(-1px, 2px);
    }
}

// Typewriter effect
.typewriter {
    overflow: hidden;
    border-right: 2px solid o-color('o-color-1');
    white-space: nowrap;
    margin: 0 auto;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: o-color('o-color-1');
    }
}

// Parallax effect
.parallax {
    transform: translateZ(0);
    will-change: transform;
}

// Glass morphism effect
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: o-website-value('border-radius-lg');
}

// Neon glow effect
.neon {
    color: o-color('o-color-1');
    text-shadow: 
        0 0 5px o-color('o-color-1'),
        0 0 10px o-color('o-color-1'),
        0 0 15px o-color('o-color-1'),
        0 0 20px o-color('o-color-1');
    animation: neon-flicker 1.5s infinite alternate;
}

@keyframes neon-flicker {
    0%, 18%, 22%, 25%, 53%, 57%, 100% {
        text-shadow: 
            0 0 5px o-color('o-color-1'),
            0 0 10px o-color('o-color-1'),
            0 0 15px o-color('o-color-1'),
            0 0 20px o-color('o-color-1');
    }
    20%, 24%, 55% {
        text-shadow: none;
    }
}

// =============================================================================
// PERFORMANCE OPTIMIZATIONS
// =============================================================================

// Hardware acceleration for smooth animations
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

// Reduce motion for accessibility
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
