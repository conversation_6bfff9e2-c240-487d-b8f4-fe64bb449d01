// =============================================================================
// OMISOL WEBSITE LOADER
// =============================================================================
// Professional website loader with dynamic company logo
// Inspired by modern industrial design patterns

// =============================================================================
// LOADER CONTAINER
// =============================================================================

.omisol-website-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(26, 54, 93, 0.95) 0%,
        rgba(45, 90, 135, 0.9) 50%,
        rgba(255, 138, 0, 0.85) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.8s ease-out, visibility 0.8s ease-out;
    
    // Animated background pattern
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: 
            radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
        animation: backgroundPulse 4s ease-in-out infinite;
        z-index: 1;
    }
    
    // Hidden state
    &.loaded {
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
    }
}

// =============================================================================
// LOADER CONTENT
// =============================================================================

.omisol-loader-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 400px;
    padding: 2rem;
}

// =============================================================================
// LOGO CONTAINER
// =============================================================================

.omisol-loader-logo {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 3px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    animation: logoFloat 3s ease-in-out infinite;
    
    // Rotating border effect
    &::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        border-radius: 50%;
        background: conic-gradient(
            from 0deg,
            transparent,
            rgba(255, 138, 0, 0.8),
            transparent,
            rgba(26, 54, 93, 0.6),
            transparent
        );
        animation: rotateBorder 2s linear infinite;
        z-index: -1;
    }
    
    // Inner glow effect
    &::after {
        content: '';
        position: absolute;
        top: 10px;
        left: 10px;
        right: 10px;
        bottom: 10px;
        border-radius: 50%;
        background: radial-gradient(
            circle,
            rgba(255, 255, 255, 0.2) 0%,
            transparent 70%
        );
        animation: innerGlow 2.5s ease-in-out infinite alternate;
    }
    
    // Logo image
    img {
        width: 70px;
        height: 70px;
        object-fit: contain;
        filter: brightness(1.2) contrast(1.1);
        animation: logoScale 2s ease-in-out infinite alternate;
        position: relative;
        z-index: 1;
    }
    
    // Fallback for text logo
    .logo-text {
        // font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-size: 1.5rem;
        font-weight: 700;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 1;
    }
}

// =============================================================================
// LOADING PROGRESS
// =============================================================================

.omisol-loader-progress {
    position: relative;
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    margin: 2rem auto 1rem;
    overflow: hidden;
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 138, 0, 0.8),
            rgba(255, 255, 255, 0.9),
            rgba(255, 138, 0, 0.8),
            transparent
        );
        animation: progressSlide 2s ease-in-out infinite;
    }
}

// =============================================================================
// LOADING TEXT
// =============================================================================

.omisol-loader-text {
    color: white;
    // font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 1.1rem;
    font-weight: 500;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 0.5rem;
    animation: textFade 2s ease-in-out infinite alternate;
}

.omisol-loader-subtext {
    color: rgba(255, 255, 255, 0.8);
    // font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 0.9rem;
    font-weight: 400;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    animation: subtextFade 2.5s ease-in-out infinite alternate;
}

// =============================================================================
// LOADING DOTS
// =============================================================================

.omisol-loader-dots {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1rem;
    gap: 0.5rem;
    
    .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        animation: dotBounce 1.4s ease-in-out infinite both;
        
        &:nth-child(1) { animation-delay: -0.32s; }
        &:nth-child(2) { animation-delay: -0.16s; }
        &:nth-child(3) { animation-delay: 0s; }
    }
}

// =============================================================================
// ANIMATIONS
// =============================================================================

@keyframes backgroundPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-10px) scale(1.05); }
}

@keyframes rotateBorder {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes innerGlow {
    0% { opacity: 0.3; }
    100% { opacity: 0.7; }
}

@keyframes logoScale {
    0% { transform: scale(1); }
    100% { transform: scale(1.1); }
}

@keyframes progressSlide {
    0% { left: -100%; }
    50% { left: 0%; }
    100% { left: 100%; }
}

@keyframes textFade {
    0% { opacity: 0.8; }
    100% { opacity: 1; }
}

@keyframes subtextFade {
    0% { opacity: 0.6; }
    100% { opacity: 0.9; }
}

@keyframes dotBounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

// =============================================================================
// RESPONSIVE DESIGN
// =============================================================================

@media (max-width: 768px) {
    .omisol-loader-content {
        padding: 1rem;
        max-width: 300px;
    }
    
    .omisol-loader-logo {
        width: 100px;
        height: 100px;
        margin-bottom: 1.5rem;
        
        img {
            width: 60px;
            height: 60px;
        }
        
        .logo-text {
            font-size: 1.2rem;
        }
    }
    
    .omisol-loader-progress {
        width: 150px;
    }
    
    .omisol-loader-text {
        font-size: 1rem;
    }
    
    .omisol-loader-subtext {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .omisol-loader-logo {
        width: 80px;
        height: 80px;
        
        img {
            width: 50px;
            height: 50px;
        }
        
        .logo-text {
            font-size: 1rem;
        }
    }
    
    .omisol-loader-progress {
        width: 120px;
    }
}
