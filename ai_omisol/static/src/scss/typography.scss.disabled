// =============================================================================
// OMISOL THEME - TYPOGRAPHY SYSTEM
// =============================================================================
// Comprehensive typography system using Odoo's font size classes and variables

// =============================================================================
// FONT LOADING & OPTIMIZATION
// =============================================================================

// Preload critical fonts for better performance
// @font-face {
//     font-family: 'Roboto';
//     font-style: normal;
//     font-weight: 300;
//     font-display: swap;
//     src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmSU5fBBc4.woff2') format('woff2');
//     unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
// }

// @font-face {
//     font-family: 'Roboto';
//     font-style: normal;
//     font-weight: 400;
//     font-display: swap;
//     src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2') format('woff2');
//     unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
// }

// @font-face {
//     font-family: 'Roboto';
//     font-style: normal;
//     font-weight: 500;
//     font-display: swap;
//     src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmEU9fBBc4.woff2') format('woff2');
//     unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
// }

// @font-face {
//     font-family: 'Roboto';
//     font-style: normal;
//     font-weight: 600;
//     font-display: swap;
//     src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBBc4.woff2') format('woff2');
//     unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
// }

// @font-face {
//     font-family: 'Roboto';
//     font-style: normal;
//     font-weight: 700;
//     font-display: swap;
//     src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBBc4.woff2') format('woff2');
//     unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
// }

// =============================================================================
// BASE TYPOGRAPHY STYLES
// =============================================================================

// Root font size for rem calculations
html {
    font-size: 16px; // Base font size
    
    @include responsive('lg') {
        font-size: 15px;
    }
    
    @include responsive('md') {
        font-size: 14px;
    }
}

// Body typography
body {
    font-family: o-website-value('body-font');
    font-weight: o-website-value('body-font-weight');
    font-size: o-website-value('font-size-base');
    line-height: o-website-value('line-height-base');
    color: o-website-value('text-primary');
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

// =============================================================================
// HEADING STYLES WITH ODOO SIZE CLASSES
// =============================================================================

// Base heading styles
%heading-base {
    font-family: o-website-value('headings-font');
    font-weight: o-website-value('headings-font-weight');
    line-height: o-website-value('line-height-headings');
    letter-spacing: o-website-value('letter-spacing-headings');
    color: o-website-value('text-primary');
    margin-bottom: 1rem;
    margin-top: 0;
}

// Heading elements
h1, .h1 {
    @extend %heading-base;
    font-size: 2.5rem;
    font-weight: 800;
    
    @include responsive('lg') {
        font-size: 2.25rem;
    }
    
    @include responsive('md') {
        font-size: 2rem;
    }
    
    @include responsive('xs') {
        font-size: 1.875rem;
    }
}

h2, .h2 {
    @extend %heading-base;
    font-size: 2rem;
    font-weight: 700;
    
    @include responsive('lg') {
        font-size: 1.875rem;
    }
    
    @include responsive('md') {
        font-size: 1.75rem;
    }
    
    @include responsive('xs') {
        font-size: 1.625rem;
    }
}

h3, .h3 {
    @extend %heading-base;
    font-size: 1.5rem;
    font-weight: 600;
    
    @include responsive('md') {
        font-size: 1.375rem;
    }
    
    @include responsive('xs') {
        font-size: 1.25rem;
    }
}

h4, .h4 {
    @extend %heading-base;
    font-size: 1.25rem;
    font-weight: 600;
    
    @include responsive('xs') {
        font-size: 1.125rem;
    }
}

h5, .h5 {
    @extend %heading-base;
    font-size: 1.125rem;
    font-weight: 600;
}

h6, .h6 {
    @extend %heading-base;
    font-size: 1rem;
    font-weight: 600;
}

// =============================================================================
// DISPLAY HEADINGS (ODOO STYLE)
// =============================================================================

.display-1, .display-1-fs {
    @extend %heading-base;
    font-size: 4rem;
    font-weight: 800;
    
    @include responsive('lg') {
        font-size: 3.5rem;
    }
    
    @include responsive('md') {
        font-size: 3rem;
    }
    
    @include responsive('xs') {
        font-size: 2.5rem;
    }
}

.display-2, .display-2-fs {
    @extend %heading-base;
    font-size: 3.5rem;
    font-weight: 800;
    
    @include responsive('lg') {
        font-size: 3rem;
    }
    
    @include responsive('md') {
        font-size: 2.5rem;
    }
    
    @include responsive('xs') {
        font-size: 2.25rem;
    }
}

.display-3, .display-3-fs {
    @extend %heading-base;
    font-size: 3rem;
    font-weight: 700;
    
    @include responsive('lg') {
        font-size: 2.5rem;
    }
    
    @include responsive('md') {
        font-size: 2.25rem;
    }
    
    @include responsive('xs') {
        font-size: 2rem;
    }
}

.display-4, .display-4-fs {
    @extend %heading-base;
    font-size: 2.5rem;
    font-weight: 700;
    
    @include responsive('lg') {
        font-size: 2.25rem;
    }
    
    @include responsive('md') {
        font-size: 2rem;
    }
    
    @include responsive('xs') {
        font-size: 1.875rem;
    }
}

// =============================================================================
// FONT SIZE UTILITY CLASSES (ODOO COMPATIBLE)
// =============================================================================

// Heading font size classes
.h1-fs { font-size: 2.5rem !important; }
.h2-fs { font-size: 2rem !important; }
.h3-fs { font-size: 1.5rem !important; }
.h4-fs { font-size: 1.25rem !important; }
.h5-fs { font-size: 1.125rem !important; }
.h6-fs { font-size: 1rem !important; }

// Base and small font sizes
.base-fs { font-size: 1rem !important; }
.o_small-fs, .small-fs { font-size: 0.875rem !important; }

// Large text
.text-lg { font-size: 1.125rem !important; }
.text-xl { font-size: 1.25rem !important; }

// =============================================================================
// TEXT STYLES & UTILITIES
// =============================================================================

// Lead text (introduction paragraphs)
.lead {
    font-size: 1.25rem;
    font-weight: 500;
    line-height: 1.7;
    color: o-website-value('text-primary');
    margin-bottom: 1.5rem;
    
    @include responsive('md') {
        font-size: 1.125rem;
    }
    
    @include responsive('xs') {
        font-size: 1rem;
    }
}

// Small text
.small, small, .o_small {
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.4;
    color: o-website-value('text-secondary');
}

// Text weight utilities
.fw-light { font-weight: 300 !important; }
.fw-normal { font-weight: 400 !important; }
.fw-medium { font-weight: 500 !important; }
.fw-semibold { font-weight: 600 !important; }
.fw-bold { font-weight: 700 !important; }
.fw-bolder { font-weight: 800 !important; }

// Text color utilities
.text-primary { color: o-website-value('text-primary') !important; }
.text-secondary { color: o-website-value('text-secondary') !important; }
.text-muted { color: o-website-value('text-muted') !important; }
.text-white { color: o-website-value('text-white') !important; }
.text-brand { color: o-color('o-color-1') !important; }
.text-accent { color: o-website-value('accent-orange') !important; }

// Text alignment
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

// Text transform
.text-lowercase { text-transform: lowercase !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-capitalize { text-transform: capitalize !important; }

// Line height utilities
.lh-1 { line-height: 1 !important; }
.lh-sm { line-height: 1.25 !important; }
.lh-base { line-height: 1.6 !important; }
.lh-lg { line-height: 1.8 !important; }

// Letter spacing utilities (using rem for consistency)
.ls-tight { letter-spacing: -0.025rem !important; }
.ls-normal { letter-spacing: 0 !important; }
.ls-wide { letter-spacing: 0.025rem !important; }
.ls-wider { letter-spacing: 0.05rem !important; }
.ls-widest { letter-spacing: 0.1rem !important; }

// =============================================================================
// SPECIALIZED TEXT STYLES
// =============================================================================

// Section titles
.section-title {
    @extend %heading-base;
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    border-bottom: 4px solid o-color('o-color-2');
    padding-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    color: o-color('o-color-1');
    
    i {
        margin-right: 0.75rem;
        color: o-website-value('accent-orange');
        font-size: 1.4rem;
        background: o-website-value('accent-orange');
        color: white;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }
}

// Product titles
.product-title {
    @extend %heading-base;
    font-size: 1.375rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.025rem;
    color: o-website-value('text-primary');
}

// Card titles
.card-title {
    @extend %heading-base;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: o-website-value('text-primary');
}

// Navbar text
.navbar-text {
    font-family: o-website-value('navbar-font');
    font-weight: o-website-value('navbar-font-weight');
    font-size: 0.875rem;
    letter-spacing: o-website-value('letter-spacing-navbar');
    text-transform: uppercase;
}

// Button text
.btn-text {
    font-family: o-website-value('buttons-font');
    font-weight: o-website-value('buttons-font-weight');
    letter-spacing: o-website-value('letter-spacing-buttons');
    text-transform: uppercase;
}

// =============================================================================
// RESPONSIVE TYPOGRAPHY
// =============================================================================

// Responsive text sizes
@include responsive('xs') {
    .display-1, .display-1-fs { font-size: 2.5rem !important; }
    .display-2, .display-2-fs { font-size: 2.25rem !important; }
    .display-3, .display-3-fs { font-size: 2rem !important; }
    .display-4, .display-4-fs { font-size: 1.875rem !important; }
    
    h1, .h1 { font-size: 1.875rem !important; }
    h2, .h2 { font-size: 1.625rem !important; }
    h3, .h3 { font-size: 1.25rem !important; }
    
    .lead { font-size: 1rem !important; }
    .section-title { font-size: 1.25rem !important; }
    .product-title { font-size: 1.125rem !important; }
}

// =============================================================================
// PRINT STYLES
// =============================================================================

@media print {
    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
    }
    
    h1, h2, h3, h4, h5, h6 {
        color: #000;
        page-break-after: avoid;
    }
    
    p {
        orphans: 3;
        widows: 3;
    }
    
    .display-1, .display-2, .display-3, .display-4 {
        font-size: 18pt;
    }
}
