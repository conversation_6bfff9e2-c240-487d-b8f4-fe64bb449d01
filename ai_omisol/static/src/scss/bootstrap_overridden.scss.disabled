// =============================================================================
// OMISOL THEME - BOOTSTRAP OVERRIDES
// =============================================================================
// This file contains Bootstrap variable overrides that cannot be handled
// through Odoo's primary variables system

// =============================================================================
// TYPOGRAPHY OVERRIDES
// =============================================================================

// Font family stack - Professional industrial look
$font-family-sans-serif: 'Roboto', 'Arial', 'Helvetica Neue', 'Helvetica', sans-serif !default;
$font-family-monospace: 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace !default;

// Font sizes - Improved hierarchy
$font-size-base: 1rem !default; // 16px
$font-size-lg: 1.125rem !default; // 18px
$font-size-sm: 0.875rem !default; // 14px

// Font weights
$font-weight-lighter: 300 !default;
$font-weight-light: 400 !default;
$font-weight-normal: 400 !default;
$font-weight-medium: 500 !default;
$font-weight-semibold: 600 !default;
$font-weight-bold: 700 !default;
$font-weight-bolder: 800 !default;

// Line heights
$line-height-base: 1.6 !default;
$line-height-sm: 1.4 !default;
$line-height-lg: 1.8 !default;

// Headings
$headings-font-family: $font-family-sans-serif !default;
$headings-font-weight: 700 !default;
$headings-line-height: 1.2 !default;
$headings-color: inherit !default;

// Heading sizes - Better scale
$h1-font-size: 2.5rem !default; // 40px
$h2-font-size: 2rem !default; // 32px
$h3-font-size: 1.5rem !default; // 24px
$h4-font-size: 1.25rem !default; // 20px
$h5-font-size: 1.125rem !default; // 18px
$h6-font-size: 1rem !default; // 16px

// Display headings
$display1-size: 4rem !default; // 64px
$display2-size: 3.5rem !default; // 56px
$display3-size: 3rem !default; // 48px
$display4-size: 2.5rem !default; // 40px

// =============================================================================
// COLOR SYSTEM OVERRIDES
// =============================================================================

// Primary brand colors
$blue: #007bff !default;
$indigo: #6610f2 !default;
$purple: #6f42c1 !default;
$pink: #e83e8c !default;
$red: #dc2626 !default;
$orange: #f97316 !default;
$yellow: #eab308 !default;
$green: #16a34a !default;
$teal: #20c997 !default;
$cyan: #0ea5e9 !default;

// Grayscale
$white: #ffffff !default;
$gray-100: #f8fafc !default;
$gray-200: #f1f5f9 !default;
$gray-300: #e2e8f0 !default;
$gray-400: #94a3b8 !default;
$gray-500: #64748b !default;
$gray-600: #475569 !default;
$gray-700: #334155 !default;
$gray-800: #1e293b !default;
$gray-900: #0f172a !default;
$black: #000000 !default;

// Theme colors
$primary: $blue !default;
$secondary: #1e40af !default;
$success: $green !default;
$info: $cyan !default;
$warning: $yellow !default;
$danger: $red !default;
$light: $gray-100 !default;
$dark: $gray-800 !default;

// =============================================================================
// BODY & LAYOUT OVERRIDES
// =============================================================================

$body-bg: #f1f5f9 !default;
$body-color: #1e293b !default;

// Links - Remove underlines
$link-color: $primary !default;
$link-decoration: none !default;
$link-hover-color: darken($primary, 15%) !default;
$link-hover-decoration: none !default;

// =============================================================================
// COMPONENT OVERRIDES
// =============================================================================

// Borders
$border-width: 0.0625rem !default; // 1px converted to rem
$border-color: #e2e8f0 !default;
$border-radius: 0.5rem !default; // 8px converted to rem
$border-radius-sm: 0.25rem !default; // 4px converted to rem
$border-radius-lg: 0.75rem !default; // 12px converted to rem
$border-radius-pill: 50rem !default;

// Box shadows
$box-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1) !default;
$box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !default;
$box-shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15) !default;
$box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075) !default;

// =============================================================================
// BUTTON OVERRIDES
// =============================================================================

$btn-padding-y: 0.75rem !default;
$btn-padding-x: 1.5rem !default;
$btn-font-family: $font-family-sans-serif !default;
$btn-font-size: 0.9rem !default;
$btn-font-weight: 600 !default;
$btn-line-height: 1.4 !default;
$btn-white-space: nowrap !default;

$btn-padding-y-sm: 0.5rem !default;
$btn-padding-x-sm: 1rem !default;
$btn-font-size-sm: 0.8rem !default;

$btn-padding-y-lg: 1rem !default;
$btn-padding-x-lg: 2rem !default;
$btn-font-size-lg: 1rem !default;

// $btn-border-width is defined in secondary_variables.scss
$btn-border-radius: 0.375rem !default; // 6px converted to rem
$btn-border-radius-sm: 0.25rem !default; // 4px converted to rem
$btn-border-radius-lg: 0.5rem !default; // 8px converted to rem

$btn-transition: all 0.3s ease !default;
$btn-focus-width: 0.2rem !default;
$btn-focus-box-shadow: 0 0 0 $btn-focus-width rgba($primary, 0.25) !default;
$btn-disabled-opacity: 0.65 !default;
$btn-active-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125) !default;

// =============================================================================
// NAVBAR OVERRIDES - REMOVED TO USE ODOO'S DEFAULT NAVBAR
// =============================================================================

// Removed all navbar customizations to use Odoo's default navbar styling

// =============================================================================
// CARD OVERRIDES
// =============================================================================

$card-spacer-y: 1.5rem !default;
$card-spacer-x: 1.5rem !default;
// $card-border-width and $card-border-radius are defined in secondary_variables.scss
$card-border-color: $primary !default;
$card-inner-border-radius: calc(#{$card-border-radius} - #{$card-border-width}) !default;
$card-cap-padding-y: $card-spacer-y * 0.5 !default;
$card-cap-padding-x: $card-spacer-x !default;
$card-cap-bg: rgba($black, 0.03) !default;
$card-cap-color: null !default;
$card-height: auto !default;
$card-color: null !default;
$card-bg: $white !default;
$card-img-overlay-padding: 1.25rem !default;
$card-group-margin: $grid-gutter-width * 0.5 !default;

// =============================================================================
// FORM OVERRIDES
// =============================================================================

$input-padding-y: 0.75rem !default;
$input-padding-x: 1rem !default;
$input-font-family: $font-family-sans-serif !default;
$input-font-size: $font-size-base !default;
$input-font-weight: $font-weight-normal !default;
$input-line-height: $line-height-base !default;

$input-padding-y-sm: 0.5rem !default;
$input-padding-x-sm: 0.75rem !default;
$input-font-size-sm: $font-size-sm !default;

$input-padding-y-lg: 1rem !default;
$input-padding-x-lg: 1.25rem !default;
$input-font-size-lg: $font-size-lg !default;

$input-bg: $white !default;
$input-disabled-bg: $gray-200 !default;
$input-disabled-border-color: null !default;

$input-color: $body-color !default;
$input-border-color: $gray-300 !default;
$input-border-width: $border-width !default;
$input-box-shadow: $box-shadow-inset !default;

$input-border-radius: $border-radius !default;
$input-border-radius-sm: $border-radius-sm !default;
$input-border-radius-lg: $border-radius-lg !default;

$input-focus-bg: $input-bg !default;
$input-focus-border-color: lighten($primary, 25%) !default;
$input-focus-color: $input-color !default;
$input-focus-width: 0.2rem !default;
$input-focus-box-shadow: 0 0 0 $input-focus-width rgba($primary, 0.25) !default;

$input-placeholder-color: $gray-500 !default;
$input-plaintext-color: $body-color !default;

// =============================================================================
// DROPDOWN OVERRIDES
// =============================================================================

$dropdown-min-width: 12rem !default;
$dropdown-padding-x: 0 !default;
$dropdown-padding-y: 0.5rem !default;
$dropdown-spacer: 0.125rem !default;
$dropdown-font-size: $font-size-base !default;
$dropdown-color: $body-color !default;
$dropdown-bg: $white !default;
$dropdown-border-color: $primary !default;
// $dropdown-border-radius, $dropdown-border-width, and $dropdown-inner-border-radius are defined in secondary_variables.scss
$dropdown-divider-bg: $gray-200 !default;
$dropdown-divider-margin-y: $nav-divider-margin-y !default;
$dropdown-box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !default;

$dropdown-link-color: $gray-800 !default;
$dropdown-link-hover-color: $white !default;
$dropdown-link-hover-bg: $primary !default;

$dropdown-link-active-color: $white !default;
$dropdown-link-active-bg: $primary !default;

$dropdown-link-disabled-color: $gray-500 !default;

$dropdown-item-padding-y: 0.75rem !default;
$dropdown-item-padding-x: 1.25rem !default;

$dropdown-header-color: $gray-600 !default;
$dropdown-header-padding: $dropdown-padding-y $dropdown-item-padding-x !default;

// =============================================================================
// MODAL OVERRIDES
// =============================================================================

$modal-inner-padding: 1.5rem !default;
$modal-footer-margin-between: 0.5rem !default;
$modal-dialog-margin: 1rem !default;
$modal-dialog-margin-y-sm-up: 2rem !default;
$modal-title-line-height: $line-height-base !default;
$modal-content-color: null !default;
$modal-content-bg: $white !default;
$modal-content-border-color: rgba($black, 0.2) !default;
$modal-content-border-width: $border-width !default;
$modal-content-border-radius: $border-radius-lg !default;
$modal-content-inner-border-radius: calc(#{$modal-content-border-radius} - #{$modal-content-border-width}) !default;
$modal-content-box-shadow-xs: 0 0.25rem 0.5rem rgba($black, 0.5) !default;
$modal-content-box-shadow-sm-up: 0 0.5rem 1rem rgba($black, 0.5) !default;
$modal-backdrop-bg: $black !default;
$modal-backdrop-opacity: 0.5 !default;
$modal-header-border-color: $border-color !default;
$modal-footer-border-color: $modal-header-border-color !default;
$modal-header-border-width: $modal-content-border-width !default;
$modal-footer-border-width: $modal-header-border-width !default;
$modal-header-padding-y: 1.5rem !default;
$modal-header-padding-x: 1.5rem !default;
$modal-header-padding: $modal-header-padding-y $modal-header-padding-x !default;

// =============================================================================
// GRID SYSTEM OVERRIDES
// =============================================================================

$grid-columns: 12 !default;
$grid-gutter-width: 2rem !default;
$grid-row-columns: 6 !default;

$container-padding-x: $grid-gutter-width * 0.5 !default;

$container-max-widths: (
  sm: 33.75rem, // 540px
  md: 45rem,    // 720px
  lg: 60rem,    // 960px
  xl: 75rem,    // 1200px
  xxl: 87.5rem  // 1400px
) !default;

// =============================================================================
// SPACING OVERRIDES
// =============================================================================

$spacer: 1rem !default;
$spacers: (
  0: 0,
  1: $spacer * 0.25,
  2: $spacer * 0.5,
  3: $spacer,
  4: $spacer * 1.5,
  5: $spacer * 3,
  6: $spacer * 4,
  7: $spacer * 6,
  8: $spacer * 8
) !default;
