// =============================================================================
// OMISOL THEME - MODERN EFFECTS
// =============================================================================
// Modern industrial theme effects including cursor follow-through, smooth hovers, and loaders

// =============================================================================
// MODERN LINK STYLES (NO UNDERLINES)
// =============================================================================

// Remove all underlines and add modern hover effects
a {
    text-decoration: none !important;
    color: o-website-value('text-primary');
    position: relative;
    @include animate(color, 0.3s, cubic-bezier(0.25, 0.46, 0.45, 0.94));
    
    &:hover {
        color: o-color('o-color-1');
        text-decoration: none !important;
    }
    
    &:focus {
        outline: none;
        text-decoration: none !important;
    }
    
    &:visited {
        text-decoration: none !important;
    }
}

// Modern link hover effects
.modern-link {
    position: relative;
    display: inline-block;
    overflow: hidden;
    
    &::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background: linear-gradient(90deg, o-color('o-color-1'), o-website-value('accent-orange'));
        @include animate(width, 0.3s, cubic-bezier(0.25, 0.46, 0.45, 0.94));
    }
    
    &:hover::before {
        width: 100%;
    }
    
    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(o-color('o-color-1'), 0.1), transparent);
        @include animate(left, 0.5s, ease);
    }
    
    &:hover::after {
        left: 100%;
    }
}

// Navigation links modern style
.omisol-nav-link {
    position: relative;
    overflow: hidden;
    
    &::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background: linear-gradient(90deg, o-color('o-color-1'), o-website-value('accent-orange'));
        @include animate(all, 0.3s, cubic-bezier(0.25, 0.46, 0.45, 0.94));
        transform: translateX(-50%);
    }
    
    &:hover::before,
    &.active::before {
        width: 80%;
    }
    
    // Subtle background glow on hover
    &:hover {
        background: linear-gradient(135deg, rgba(o-color('o-color-1'), 0.05) 0%, rgba(o-website-value('accent-orange'), 0.05) 100%);
        transform: translateY(-1px);
    }
}

// Footer links
.omisol-footer a {
    position: relative;
    
    &::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 1px;
        background: o-website-value('accent-orange');
        @include animate(width, 0.3s, ease);
    }
    
    &:hover::before {
        width: 100%;
    }
}

// =============================================================================
// MODERN BUTTON EFFECTS
// =============================================================================

.btn {
    position: relative;
    overflow: hidden;
    border: none !important;
    @include animate(all, 0.3s, cubic-bezier(0.25, 0.46, 0.45, 0.94));
    
    // Remove default borders
    &:focus {
        box-shadow: 0 0 0 3px rgba(o-color('o-color-1'), 0.2) !important;
        border: none !important;
    }
    
    &:active {
        transform: translateY(1px);
    }
    
    // Ripple effect
    &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        @include animate(all, 0.6s, ease);
        z-index: 1;
    }
    
    &:active::before {
        width: 300px;
        height: 300px;
    }
    
    // Shimmer effect
    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        @include animate(left, 0.6s, ease);
        z-index: 2;
    }
    
    &:hover::after {
        left: 100%;
    }
}

.btn-omisol-primary {
    background: linear-gradient(135deg, o-color('o-color-1') 0%, o-color('o-color-2') 100%);
    box-shadow: 0 4px 15px rgba(o-color('o-color-1'), 0.3);
    
    &:hover {
        background: linear-gradient(135deg, o-color('o-color-2') 0%, o-color('o-color-4') 100%);
        box-shadow: 0 6px 20px rgba(o-color('o-color-1'), 0.4);
        transform: translateY(-2px);
    }
}

.btn-omisol-secondary {
    background: linear-gradient(135deg, transparent 0%, rgba(o-website-value('secondary-color'), 0.1) 100%);
    border: 2px solid o-website-value('secondary-color') !important;
    color: o-website-value('secondary-color');
    box-shadow: 0 4px 15px rgba(o-website-value('secondary-color'), 0.2);
    
    &:hover {
        background: linear-gradient(135deg, o-website-value('secondary-color') 0%, o-website-value('secondary-dark') 100%);
        color: white;
        box-shadow: 0 6px 20px rgba(o-website-value('secondary-color'), 0.4);
        transform: translateY(-2px);
    }
}

// =============================================================================
// CURSOR FOLLOW-THROUGH SYSTEM
// =============================================================================

.cursor-follow {
    position: fixed;
    width: 20px;
    height: 20px;
    background: o-color('o-color-1');
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    @include animate(all, 0.1s, ease-out);
    opacity: 0;
    
    &.active {
        opacity: 1;
    }
    
    &.hover {
        width: 40px;
        height: 40px;
        background: rgba(o-color('o-color-1'), 0.3);
        border: 2px solid o-color('o-color-1');
        backdrop-filter: blur(5px);
    }
    
    &.click {
        width: 60px;
        height: 60px;
        background: rgba(o-website-value('accent-orange'), 0.5);
        border: 3px solid o-website-value('accent-orange');
    }
}

.cursor-magnetic {
    &:hover {
        cursor: none;
    }
}

// Hide default cursor on interactive elements
.cursor-hide {
    cursor: none !important;
    
    * {
        cursor: none !important;
    }
}

// =============================================================================
// WEBSITE LOADER
// =============================================================================

.website-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, o-color('o-color-1') 0%, o-color('o-color-2') 50%, o-website-value('secondary-color') 100%);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    @include animate(opacity, 0.5s, ease);
    
    &.loaded {
        opacity: 0;
        pointer-events: none;
    }
}

.loader-content {
    text-align: center;
    color: white;
}

.loader-logo {
    width: 120px;
    height: auto;
    margin-bottom: 2rem;
    animation: logoFloat 2s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

.loader-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loader-text {
    font-family: o-website-value('headings-font');
    font-size: 1.2rem;
    font-weight: 600;
    letter-spacing: 0.1rem;
    text-transform: uppercase;
    margin-bottom: 1rem;
    animation: textPulse 1.5s ease-in-out infinite;
}

@keyframes textPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.loader-progress {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.loader-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, white, o-website-value('accent-orange'));
    border-radius: 2px;
    width: 0%;
    animation: progressLoad 3s ease-in-out forwards;
}

@keyframes progressLoad {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

// Industrial loader variant
.loader-industrial {
    .loader-spinner {
        border: none;
        width: 80px;
        height: 80px;
        position: relative;
        
        &::before,
        &::after {
            content: '';
            position: absolute;
            border: 4px solid transparent;
            border-radius: 50%;
        }
        
        &::before {
            width: 80px;
            height: 80px;
            border-top-color: white;
            border-right-color: white;
            animation: industrialSpin 2s linear infinite;
        }
        
        &::after {
            width: 60px;
            height: 60px;
            top: 10px;
            left: 10px;
            border-bottom-color: o-website-value('accent-orange');
            border-left-color: o-website-value('accent-orange');
            animation: industrialSpin 1.5s linear infinite reverse;
        }
    }
}

@keyframes industrialSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

// =============================================================================
// SMOOTH SCROLL BEHAVIOR
// =============================================================================

html {
    scroll-behavior: smooth;
}

// Custom scrollbar
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: o-website-value('neutral-200');
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, o-color('o-color-1'), o-website-value('accent-orange'));
    border-radius: 4px;
    
    &:hover {
        background: linear-gradient(180deg, o-color('o-color-2'), o-website-value('accent-orange-dark'));
    }
}

// =============================================================================
// CARD HOVER EFFECTS
// =============================================================================

.omisol-product-card {
    border: 1px solid o-website-value('border-color') !important;
    @include animate(all, 0.4s, cubic-bezier(0.25, 0.46, 0.45, 0.94));
    
    &:hover {
        border-color: o-color('o-color-1') !important;
        box-shadow: 0 10px 30px rgba(o-color('o-color-1'), 0.15);
        transform: translateY(-5px);
    }
    
    // Subtle glow effect
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, o-color('o-color-1'), o-website-value('accent-orange'));
        transform: scaleX(0);
        transform-origin: left;
        @include animate(transform, 0.3s, ease);
    }
    
    &:hover::before {
        transform: scaleX(1);
    }
}

// =============================================================================
// FORM MODERN EFFECTS
// =============================================================================

.form-control {
    border: 1px solid o-website-value('border-color') !important;
    @include animate(all, 0.3s, ease);
    
    &:focus {
        border-color: o-color('o-color-1') !important;
        box-shadow: 0 0 0 3px rgba(o-color('o-color-1'), 0.1) !important;
    }
}

// =============================================================================
// RESPONSIVE CURSOR EFFECTS
// =============================================================================

@media (max-width: 768px) {
    .cursor-follow {
        display: none;
    }
    
    .cursor-magnetic {
        cursor: pointer !important;
    }
    
    .cursor-hide {
        cursor: pointer !important;
        
        * {
            cursor: pointer !important;
        }
    }
}

// =============================================================================
// ACCESSIBILITY IMPROVEMENTS
// =============================================================================

@media (prefers-reduced-motion: reduce) {
    .cursor-follow {
        display: none;
    }
    
    .website-loader {
        animation: none;
        
        .loader-spinner,
        .loader-logo,
        .loader-text,
        .loader-progress-bar {
            animation: none;
        }
    }
    
    .btn::before,
    .btn::after,
    .modern-link::before,
    .modern-link::after {
        display: none;
    }
}

// Focus indicators for keyboard navigation
.btn:focus-visible,
.modern-link:focus-visible,
.omisol-nav-link:focus-visible {
    outline: 2px solid o-color('o-color-1');
    outline-offset: 2px;
    border-radius: 4px;
}

// =============================================================================
// FLOATING ACTION BUTTON
// =============================================================================

.floating-action-button {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, o-color('o-color-1') 0%, o-color('o-color-2') 100%);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 8px 25px rgba(o-color('o-color-1'), 0.3);
    @include animate(all, 0.3s, cubic-bezier(0.25, 0.46, 0.45, 0.94));
    z-index: 1000;
    cursor: pointer;
    opacity: 0;
    transform: translateY(100px);
    text-decoration: none !important;

    &.fab-visible {
        opacity: 1;
        transform: translateY(0);
    }

    &.fab-hidden {
        opacity: 0.7;
        transform: scale(0.9);
    }

    &:hover {
        transform: scale(1.1);
        box-shadow: 0 12px 35px rgba(o-color('o-color-1'), 0.4);
        background: linear-gradient(135deg, o-color('o-color-2') 0%, o-color('o-color-4') 100%);
        color: white;
        text-decoration: none !important;
    }

    &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(o-color('o-color-1'), 0.3);
        text-decoration: none !important;
    }

    &:active,
    &.fab-clicked {
        transform: scale(0.95);
    }

    i {
        font-family: 'Font Awesome 6 Free';
        @include animate(transform, 0.3s, ease);
    }

    &:hover i {
        transform: translateY(-2px);
    }
}
