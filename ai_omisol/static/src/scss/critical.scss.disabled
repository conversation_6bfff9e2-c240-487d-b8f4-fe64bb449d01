// =============================================================================
// OMISOL THEME - CRITICAL CSS
// =============================================================================
// Critical above-the-fold styles for faster initial page load
// This file contains only the essential styles needed for the initial viewport

// =============================================================================
// CRITICAL TYPOGRAPHY
// =============================================================================

// Only the most essential font styles
body {
    font-family: var(--o-we-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif);
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.6;
    color: #1e293b;
    margin: 0;
    padding: 0;
}

h1, h2, h3 {
    font-family: var(--o-we-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif);
    font-weight: 700;
    line-height: 1.2;
    margin: 0 0 1rem 0;
    color: #1e293b;
}

h1 {
    font-size: 2.5rem;
    font-weight: 800;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
    font-weight: 600;
}

// =============================================================================
// CRITICAL LAYOUT
// =============================================================================

// Container for proper layout
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.container-fluid {
    width: 100%;
    padding: 0 1rem;
}

// Basic grid system
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
}

.col, .col-12, .col-md-6, .col-lg-4, .col-lg-3 {
    flex: 1;
    padding: 0 0.75rem;
}

.col-12 {
    flex: 0 0 100%;
    max-width: 100%;
}

@media (min-width: 768px) {
    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

@media (min-width: 992px) {
    .col-lg-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }
    
    .col-lg-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }
}

// =============================================================================
// CRITICAL HEADER STYLES
// =============================================================================

.omisol-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 4px solid #007bff;
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 0.5rem 0;
    box-shadow: 0 4px 20px rgba(0, 123, 255, 0.15);
}

.omisol-navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem 0;
}

.omisol-logo {
    height: 50px;
    width: auto;
    max-width: 200px;
}

.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-item {
    margin: 0 0.5rem;
}

.omisol-nav-link {
    color: #1e293b;
    text-decoration: none;
    font-weight: 600;
    padding: 0.75rem 1.25rem;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.05rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.omisol-nav-link:hover {
    color: #007bff;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(249, 115, 22, 0.1) 100%);
    text-decoration: none;
}

// Mobile menu toggle
.navbar-toggler {
    display: none;
    background: none;
    border: 2px solid #007bff;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
}

.navbar-toggler-icon {
    width: 24px;
    height: 18px;
    position: relative;
    display: block;
}

.navbar-toggler-icon::before,
.navbar-toggler-icon::after,
.navbar-toggler-icon {
    background: #007bff;
    height: 2px;
    border-radius: 1px;
}

.navbar-toggler-icon::before,
.navbar-toggler-icon::after {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
}

.navbar-toggler-icon::before {
    top: -6px;
}

.navbar-toggler-icon::after {
    bottom: -6px;
}

// =============================================================================
// CRITICAL HERO SECTION
// =============================================================================

.omisol-hero {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 70%, #475569 100%);
    color: white;
    padding: 120px 0;
    position: relative;
    overflow: hidden;
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.omisol-hero-content {
    position: relative;
    z-index: 2;
}

.omisol-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    font-weight: 800;
    color: white;
}

.omisol-hero p {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    color: #e9ecef;
    font-weight: 400;
    line-height: 1.7;
}

// =============================================================================
// CRITICAL BUTTONS
// =============================================================================

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
}

.btn-omisol-primary {
    background: #007bff;
    border-color: #0056b3;
    color: white;
    padding: 16px 40px;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-omisol-primary:hover {
    background: #0056b3;
    border-color: #003d82;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 123, 255, 0.4);
}

.btn-omisol-secondary {
    background: white;
    border-color: #1e40af;
    color: #1e40af;
    padding: 14px 38px;
    box-shadow: 0 4px 8px rgba(30, 64, 175, 0.2);
}

.btn-omisol-secondary:hover {
    background: #1e40af;
    border-color: #1e3a8a;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(30, 64, 175, 0.3);
}

// =============================================================================
// CRITICAL UTILITIES
// =============================================================================

// Text alignment
.text-center {
    text-align: center !important;
}

.text-left {
    text-align: left !important;
}

.text-right {
    text-align: right !important;
}

// Display utilities
.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: flex !important;
}

// Spacing utilities (only most critical ones)
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-3 { padding-bottom: 1rem !important; }
.pb-4 { padding-bottom: 1.5rem !important; }
.pb-5 { padding-bottom: 3rem !important; }

.pt-0 { padding-top: 0 !important; }
.pt-3 { padding-top: 1rem !important; }
.pt-4 { padding-top: 1.5rem !important; }
.pt-5 { padding-top: 3rem !important; }

// =============================================================================
// CRITICAL RESPONSIVE DESIGN
// =============================================================================

@media (max-width: 991.98px) {
    .navbar-toggler {
        display: block;
    }
    
    .navbar-nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        margin-top: 1rem;
        padding: 1rem;
        flex-direction: column;
    }
    
    .navbar-nav.show {
        display: flex;
    }
    
    .nav-item {
        margin: 0.5rem 0;
        width: 100%;
    }
    
    .omisol-nav-link {
        display: block;
        width: 100%;
        text-align: center;
        border-bottom: 1px solid #e2e8f0;
        padding: 1rem;
    }
    
    .omisol-nav-link:last-child {
        border-bottom: none;
    }
}

@media (max-width: 767.98px) {
    .omisol-hero {
        padding: 80px 0;
        text-align: center;
    }
    
    .omisol-hero h1 {
        font-size: 2.5rem;
    }
    
    .omisol-hero p {
        font-size: 1rem;
        margin-bottom: 2rem;
    }
    
    .btn-omisol-primary,
    .btn-omisol-secondary {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
        text-align: center;
        padding: 16px 20px;
    }
    
    .omisol-navbar {
        padding: 1rem 0;
    }
    
    .omisol-logo {
        height: 40px;
    }
}

@media (max-width: 575.98px) {
    .container {
        padding: 0 0.75rem;
    }
    
    .omisol-hero h1 {
        font-size: 2.25rem;
    }
    
    .omisol-hero p {
        font-size: 0.95rem;
    }
}

// =============================================================================
// CRITICAL PERFORMANCE OPTIMIZATIONS
// =============================================================================

// Prevent layout shifts
img {
    max-width: 100%;
    height: auto;
}

// Optimize font loading
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2') format('woff2');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBBc4.woff2') format('woff2');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBBc4.woff2') format('woff2');
}

// Reduce motion for accessibility
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
