/**
 * Animated Grid Background with Random Light Beams
 * Creates random horizontal and vertical light beams on grid lines
 */

(function() {
    'use strict';

    let activeBeams = 0;
    const maxBeams = 2;
    let beamContainer = null;

    // Initialize grid animation
    function initGridAnimation() {
        const heroBackground = document.querySelector('.hero-background');
        if (!heroBackground) return;

        // Create beam container
        beamContainer = document.createElement('div');
        beamContainer.className = 'beam-container';
        beamContainer.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        `;
        heroBackground.appendChild(beamContainer);

        // Start beam generation
        startBeamGeneration();
    }

    // Start generating random beams
    function startBeamGeneration() {
        function generateBeam() {
            if (activeBeams < maxBeams) {
                const isHorizontal = Math.random() > 0.3; // 70% horizontal, 30% vertical
                createBeam(isHorizontal);
            }
            
            // Schedule next beam
            const delay = Math.random() * 8000 + 6000; // 6-14 seconds
            setTimeout(generateBeam, delay);
        }

        // Start first beam after a short delay
        setTimeout(generateBeam, 1000);
    }

    // Create a light beam
    function createBeam(isHorizontal) {
        if (!beamContainer) return;

        activeBeams++;
        
        const beam = document.createElement('div');
        beam.className = 'light-beam';
        
        if (isHorizontal) {
            createHorizontalBeam(beam);
        } else {
            createVerticalBeam(beam);
        }

        beamContainer.appendChild(beam);

        // Remove beam after animation
        setTimeout(() => {
            if (beam.parentNode) {
                beam.parentNode.removeChild(beam);
            }
            activeBeams--;
        }, 15000);
    }

    // Create horizontal beam
    function createHorizontalBeam(beam) {
        const gridSize = 50;
        const randomRow = Math.floor(Math.random() * 20) * gridSize; // Random grid row

        beam.style.cssText = `
            position: absolute;
            top: ${randomRow}px;
            left: -150px;
            width: 150px;
            height: 1.5px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(0, 123, 255, 0.1) 15%,
                rgba(0, 123, 255, 0.7) 50%,
                rgba(0, 123, 255, 0.1) 85%,
                transparent 100%
            );
            border-radius: 1px;
            animation: slideHorizontal 12s linear forwards;
            opacity: 0;
        `;

        // Add CSS animation
        addHorizontalAnimation();
    }

    // Create vertical beam
    function createVerticalBeam(beam) {
        const gridSize = 50;
        const randomCol = Math.floor(Math.random() * 30) * gridSize; // Random grid column

        beam.style.cssText = `
            position: absolute;
            top: -150px;
            left: ${randomCol}px;
            width: 1.5px;
            height: 150px;
            background: linear-gradient(0deg,
                transparent 0%,
                rgba(0, 123, 255, 0.1) 15%,
                rgba(0, 123, 255, 0.6) 50%,
                rgba(0, 123, 255, 0.1) 85%,
                transparent 100%
            );
            border-radius: 1px;
            animation: slideVertical 15s linear forwards;
            opacity: 0;
        `;

        // Add CSS animation
        addVerticalAnimation();
    }

    // Add horizontal animation CSS
    function addHorizontalAnimation() {
        if (document.getElementById('horizontal-beam-animation')) return;

        const style = document.createElement('style');
        style.id = 'horizontal-beam-animation';
        style.textContent = `
            @keyframes slideHorizontal {
                0% {
                    left: -150px;
                    opacity: 0;
                }
                8% {
                    opacity: 1;
                }
                92% {
                    opacity: 1;
                }
                100% {
                    left: calc(100% + 50px);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Add vertical animation CSS
    function addVerticalAnimation() {
        if (document.getElementById('vertical-beam-animation')) return;

        const style = document.createElement('style');
        style.id = 'vertical-beam-animation';
        style.textContent = `
            @keyframes slideVertical {
                0% {
                    top: -150px;
                    opacity: 0;
                }
                8% {
                    opacity: 1;
                }
                92% {
                    opacity: 1;
                }
                100% {
                    top: calc(100% + 50px);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Initialize when DOM is ready
    function init() {
        initGridAnimation();
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Re-initialize on page changes
    window.addEventListener('load', init);

})();
