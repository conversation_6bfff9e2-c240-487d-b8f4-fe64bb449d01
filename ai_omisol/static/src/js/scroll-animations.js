/**
 * Scroll Animations for Omisol Website
 * Implements smooth scroll effects and parallax animations
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, observerOptions);

    // Observe all elements with scroll-animate class
    document.querySelectorAll('.scroll-animate').forEach(el => {
        observer.observe(el);
    });

    // Parallax effect on scroll
    let ticking = false;
    
    function updateParallax() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.parallax-element');
        
        parallaxElements.forEach(element => {
            const speed = element.dataset.speed || 0.5;
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
        
        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    }

    // Throttled scroll event
    window.addEventListener('scroll', requestTick);

    // Counter animation
    function animateCounters() {
        const counters = document.querySelectorAll('.counter');
        
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            const duration = 2000; // 2 seconds
            const increment = target / (duration / 16); // 60fps
            let current = 0;
            
            const updateCounter = () => {
                if (current < target) {
                    current += increment;
                    counter.textContent = Math.ceil(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };
            
            updateCounter();
        });
    }

    // Observe counters
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounters();
                counterObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    document.querySelectorAll('.counter').forEach(counter => {
        counterObserver.observe(counter);
    });

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            const href = this.getAttribute('href');
            // Skip empty or invalid hrefs
            if (!href || href === '#' || href.length <= 1) {
                return;
            }

            e.preventDefault();
            try {
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            } catch (error) {
                console.warn('Invalid selector:', href);
            }
        });
    });

    // Add scroll animations to existing elements
    function addScrollAnimations() {
        // Product cards
        document.querySelectorAll('.product-card-enhanced').forEach((card, index) => {
            card.classList.add('scroll-animate', 'fade-up', `stagger-${(index % 6) + 1}`);
        });

        // Feature cards
        document.querySelectorAll('.feature-card-enhanced').forEach((card, index) => {
            card.classList.add('scroll-animate', 'fade-up', `stagger-${(index % 3) + 1}`);
        });

        // Testimonial cards
        document.querySelectorAll('.testimonial-card').forEach((card, index) => {
            card.classList.add('scroll-animate', 'scale-in', `stagger-${(index % 3) + 1}`);
        });

        // Section titles
        document.querySelectorAll('.section-title-professional, .section-title').forEach(title => {
            title.classList.add('scroll-animate', 'fade-up');
        });

        // Contact cards
        document.querySelectorAll('.contact-card').forEach((card, index) => {
            card.classList.add('scroll-animate', 'fade-up', `stagger-${index + 1}`);
        });

        // FAQ items
        document.querySelectorAll('.faq-item').forEach((item, index) => {
            item.classList.add('scroll-animate', 'fade-left', `stagger-${(index % 5) + 1}`);
        });

        // Course video cards
        document.querySelectorAll('.video-card').forEach((card, index) => {
            card.classList.add('scroll-animate', 'scale-in', `stagger-${(index % 3) + 1}`);
        });
    }

    // Initialize animations
    addScrollAnimations();

    // Navbar scroll effect
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    }
});

// CSS for navbar scroll effect
const style = document.createElement('style');
style.textContent = `
    .navbar.scrolled {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    
    .navbar.scrolled .navbar-brand,
    .navbar.scrolled .nav-link {
        color: #2f496e !important;
    }
`;
document.head.appendChild(style);
