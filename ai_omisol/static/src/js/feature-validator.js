/** @odoo-module **/

/**
 * OMISOL FEATURE VALIDATOR
 * ========================
 * Validates all new features are working correctly
 */

import publicWidget from "@web/legacy/js/public/public_widget";
import { _t } from "@web/core/l10n/translation";

var FeatureValidator = publicWidget.Widget.extend({
    selector: 'body',

    /**
     * @override
     */
    start: function () {
        this._super.apply(this, arguments);
        
        // Only run validation in development mode
        if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
            this._validateAllFeatures();
        }
        
        return this._super.apply(this, arguments);
    },

    /**
     * Validate all features
     * @private
     */
    _validateAllFeatures: function () {
        console.group('🚀 OMISOL Feature Validation');
        
        this._validateThemeConsistency();
        this._validateJavaScriptModules();
        this._validateWhatsAppIntegration();
        this._validateLiveChatSystem();
        this._validateFAQFunctionality();
        this._validateCoursePageElements();
        this._validateNavigationLinks();
        this._validateResponsiveDesign();
        
        console.groupEnd();
    },

    /**
     * Validate theme consistency
     * @private
     */
    _validateThemeConsistency: function () {
        console.group('🎨 Theme Consistency');
        
        var issues = [];
        
        // Check for consistent font usage
        var $elements = $('h1, h2, h3, h4, h5, h6, p, a, button');
        $elements.each(function () {
            var fontFamily = $(this).css('font-family');
            if (!fontFamily.includes('Roboto') && !fontFamily.includes('inherit')) {
                issues.push('Non-Roboto font found: ' + this.tagName + ' - ' + fontFamily);
            }
        });
        
        // Check for underlined links
        $('a').each(function () {
            var textDecoration = $(this).css('text-decoration');
            if (textDecoration.includes('underline')) {
                issues.push('Underlined link found: ' + this.href);
            }
        });
        
        // Check for cursor magnetic elements
        var magneticElements = $('.cursor-magnetic').length;
        if (magneticElements === 0) {
            issues.push('No cursor magnetic elements found');
        }
        
        if (issues.length === 0) {
            console.log('✅ Theme consistency validated');
        } else {
            console.warn('⚠️ Theme issues found:', issues);
        }
        
        console.groupEnd();
    },

    /**
     * Validate JavaScript modules
     * @private
     */
    _validateJavaScriptModules: function () {
        console.group('📜 JavaScript Modules');
        
        var issues = [];
        var modules = [
            'WebsiteLoader',
            'CursorFollowThrough',
            'WhatsAppIntegration',
            'AuthenticatedLiveChat',
            'FAQWidget'
        ];
        
        modules.forEach(function (moduleName) {
            if (typeof window[moduleName] === 'undefined' && 
                !publicWidget.registry[moduleName]) {
                issues.push('Module not found: ' + moduleName);
            }
        });
        
        // Check if cursor follow is working
        if (!$('#cursorFollow').length && window.innerWidth > 768) {
            issues.push('Cursor follow-through not initialized');
        }
        
        if (issues.length === 0) {
            console.log('✅ JavaScript modules validated');
        } else {
            console.warn('⚠️ JavaScript issues found:', issues);
        }
        
        console.groupEnd();
    },

    /**
     * Validate WhatsApp integration
     * @private
     */
    _validateWhatsAppIntegration: function () {
        console.group('💬 WhatsApp Integration');
        
        var issues = [];
        
        // Check for WhatsApp floating button
        var $floatingButton = $('.whatsapp-floating-button');
        if ($floatingButton.length === 0) {
            issues.push('WhatsApp floating button not found');
        }
        
        // Check for WhatsApp widgets
        var $whatsappWidgets = $('.whatsapp-widget');
        if ($whatsappWidgets.length === 0) {
            issues.push('No WhatsApp widgets found');
        }
        
        // Check for phone number data
        $whatsappWidgets.each(function () {
            var phone = $(this).data('phone');
            if (!phone) {
                issues.push('WhatsApp widget missing phone number');
            }
        });
        
        if (issues.length === 0) {
            console.log('✅ WhatsApp integration validated');
        } else {
            console.warn('⚠️ WhatsApp issues found:', issues);
        }
        
        console.groupEnd();
    },

    /**
     * Validate live chat system
     * @private
     */
    _validateLiveChatSystem: function () {
        console.group('💬 Live Chat System');
        
        var issues = [];
        
        // Check for live chat widget
        var $liveChatWidget = $('.omisol-livechat-widget');
        if ($liveChatWidget.length === 0) {
            issues.push('Live chat widget not found');
        }
        
        // Check for authentication check
        if (typeof window.checkLiveChatAuth !== 'function') {
            // This would be implemented in the live chat widget
            console.log('Live chat authentication check not exposed globally (this is normal)');
        }
        
        if (issues.length === 0) {
            console.log('✅ Live chat system validated');
        } else {
            console.warn('⚠️ Live chat issues found:', issues);
        }
        
        console.groupEnd();
    },

    /**
     * Validate FAQ functionality
     * @private
     */
    _validateFAQFunctionality: function () {
        console.group('❓ FAQ Functionality');
        
        var issues = [];
        
        // Check if we're on FAQ page
        if (window.location.pathname === '/faq') {
            // Check for FAQ search
            var $faqSearch = $('#faqSearch');
            if ($faqSearch.length === 0) {
                issues.push('FAQ search input not found');
            }
            
            // Check for FAQ categories
            var $categories = $('.faq-category-section');
            if ($categories.length === 0) {
                issues.push('No FAQ categories found');
            }
            
            // Check for accordion functionality
            var $accordions = $('.accordion-item');
            if ($accordions.length === 0) {
                issues.push('No FAQ accordion items found');
            }
            
            // Check for helpful buttons
            if (typeof window.markHelpful !== 'function') {
                issues.push('markHelpful function not found');
            }
            
            if (typeof window.markNotHelpful !== 'function') {
                issues.push('markNotHelpful function not found');
            }
        } else {
            console.log('Not on FAQ page, skipping FAQ-specific validation');
        }
        
        if (issues.length === 0) {
            console.log('✅ FAQ functionality validated');
        } else {
            console.warn('⚠️ FAQ issues found:', issues);
        }
        
        console.groupEnd();
    },

    /**
     * Validate course page elements
     * @private
     */
    _validateCoursePageElements: function () {
        console.group('🎓 Course Page Elements');
        
        var issues = [];
        
        // Check if we're on course page
        if (window.location.pathname === '/course') {
            // Check for course modules
            var $modules = $('.course-module-card');
            if ($modules.length === 0) {
                issues.push('No course modules found');
            }
            
            // Check for course stats
            var $stats = $('.stat-item');
            if ($stats.length === 0) {
                issues.push('No course statistics found');
            }
            
            // Check for certification section
            var $certification = $('.certification-section');
            if ($certification.length === 0) {
                issues.push('Certification section not found');
            }
        } else {
            console.log('Not on course page, skipping course-specific validation');
        }
        
        if (issues.length === 0) {
            console.log('✅ Course page elements validated');
        } else {
            console.warn('⚠️ Course page issues found:', issues);
        }
        
        console.groupEnd();
    },

    /**
     * Validate navigation links
     * @private
     */
    _validateNavigationLinks: function () {
        console.group('🧭 Navigation Links');
        
        var issues = [];
        var requiredLinks = ['/faq', '/course'];
        
        requiredLinks.forEach(function (link) {
            var $link = $('a[href="' + link + '"]');
            if ($link.length === 0) {
                issues.push('Navigation link not found: ' + link);
            }
        });
        
        // Check for magnetic navigation links
        var $navLinks = $('.omisol-nav-link');
        $navLinks.each(function () {
            if (!$(this).hasClass('cursor-magnetic')) {
                issues.push('Navigation link missing cursor-magnetic class: ' + this.href);
            }
        });
        
        if (issues.length === 0) {
            console.log('✅ Navigation links validated');
        } else {
            console.warn('⚠️ Navigation issues found:', issues);
        }
        
        console.groupEnd();
    },

    /**
     * Validate responsive design
     * @private
     */
    _validateResponsiveDesign: function () {
        console.group('📱 Responsive Design');
        
        var issues = [];
        
        // Check viewport meta tag
        var $viewport = $('meta[name="viewport"]');
        if ($viewport.length === 0) {
            issues.push('Viewport meta tag missing');
        }
        
        // Check for responsive classes
        var responsiveElements = $('[class*="col-"], [class*="d-"], [class*="text-"]').length;
        if (responsiveElements === 0) {
            issues.push('No responsive classes found');
        }
        
        // Check for mobile-specific styles
        if (window.innerWidth <= 768) {
            // Check if cursor effects are disabled on mobile
            var $cursor = $('#cursorFollow');
            if ($cursor.length > 0 && $cursor.is(':visible')) {
                issues.push('Cursor effects should be disabled on mobile');
            }
        }
        
        if (issues.length === 0) {
            console.log('✅ Responsive design validated');
        } else {
            console.warn('⚠️ Responsive design issues found:', issues);
        }
        
        console.groupEnd();
    }
});

// Register widget only in development
if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
    publicWidget.registry.FeatureValidator = FeatureValidator;
}
