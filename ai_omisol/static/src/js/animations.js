/**
 * OMISOL THEME - ANIMATIONS & EFFECTS
 * ===================================
 * JavaScript for handling scroll animations, interactive effects, and performance optimizations
 */

/** @odoo-module **/

import publicWidget from "@web/legacy/js/public/public_widget";
import { _t } from "@web/core/l10n/translation";

    // =============================================================================
    // SCROLL ANIMATIONS WIDGET
    // =============================================================================

    var ScrollAnimations = publicWidget.Widget.extend({
        selector: 'body',
        events: {
            'scroll window': '_onScroll',
            'resize window': '_onResize',
        },

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._initScrollAnimations();
            this._initIntersectionObserver();
            this._initParallaxElements();
            this._bindHoverEffects();
            return this._super.apply(this, arguments);
        },

        /**
         * Initialize scroll-based animations
         * @private
         */
        _initScrollAnimations: function () {
            this.scrollElements = this.$('.scroll-fade-in, .scroll-slide-left, .scroll-slide-right, .scroll-scale-in');
            this.parallaxElements = this.$('.parallax');
            this.animatedElements = new Set();
            
            // Throttle scroll events for better performance
            this._throttledScroll = this._throttle(this._handleScroll.bind(this), 16);
            this._throttledParallax = this._throttle(this._handleParallax.bind(this), 16);
        },

        /**
         * Initialize Intersection Observer for better performance
         * @private
         */
        _initIntersectionObserver: function () {
            if (!window.IntersectionObserver) {
                // Fallback for older browsers
                this._handleScrollFallback();
                return;
            }

            var self = this;
            var options = {
                root: null,
                rootMargin: '0px 0px -100px 0px',
                threshold: 0.1
            };

            this.observer = new IntersectionObserver(function (entries) {
                entries.forEach(function (entry) {
                    if (entry.isIntersecting) {
                        self._animateElement(entry.target);
                        self.observer.unobserve(entry.target);
                    }
                });
            }, options);

            // Observe all scroll animation elements
            this.scrollElements.each(function () {
                self.observer.observe(this);
            });
        },

        /**
         * Initialize parallax elements
         * @private
         */
        _initParallaxElements: function () {
            if (this.parallaxElements.length > 0) {
                this._handleParallax();
            }
        },

        /**
         * Bind hover effects with performance optimization
         * @private
         */
        _bindHoverEffects: function () {
            var self = this;
            
            // Add GPU acceleration to hover elements
            this.$('.hover-lift, .hover-lift-lg, .hover-scale, .hover-scale-lg').each(function () {
                $(this).addClass('gpu-accelerated');
            });

            // Optimize button hover effects
            this.$('.btn').on('mouseenter', function () {
                $(this).addClass('gpu-accelerated');
            }).on('mouseleave', function () {
                var $this = $(this);
                setTimeout(function () {
                    $this.removeClass('gpu-accelerated');
                }, 300);
            });
        },

        /**
         * Handle scroll events
         * @private
         */
        _onScroll: function () {
            this._throttledScroll();
            this._throttledParallax();
        },

        /**
         * Handle resize events
         * @private
         */
        _onResize: function () {
            this._handleParallax();
        },

        /**
         * Handle scroll animations
         * @private
         */
        _handleScroll: function () {
            if (!this.observer) {
                this._handleScrollFallback();
            }
        },

        /**
         * Fallback scroll handling for older browsers
         * @private
         */
        _handleScrollFallback: function () {
            var self = this;
            var windowTop = $(window).scrollTop();
            var windowHeight = $(window).height();

            this.scrollElements.each(function () {
                var $element = $(this);
                var elementTop = $element.offset().top;

                if (elementTop < windowTop + windowHeight - 100 && !self.animatedElements.has(this)) {
                    self._animateElement(this);
                }
            });
        },

        /**
         * Handle parallax effects
         * @private
         */
        _handleParallax: function () {
            if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                return;
            }

            var scrollTop = $(window).scrollTop();
            
            this.parallaxElements.each(function () {
                var $element = $(this);
                var speed = $element.data('parallax-speed') || 0.5;
                var yPos = -(scrollTop * speed);
                
                $element.css('transform', 'translate3d(0, ' + yPos + 'px, 0)');
            });
        },

        /**
         * Animate element when it comes into view
         * @private
         * @param {Element} element
         */
        _animateElement: function (element) {
            var $element = $(element);
            
            if (this.animatedElements.has(element)) {
                return;
            }

            this.animatedElements.add(element);
            $element.addClass('in-view');

            // Add stagger delay for multiple elements
            var delay = $element.data('animation-delay') || 0;
            if (delay > 0) {
                $element.css('animation-delay', delay + 'ms');
            }
        },

        /**
         * Throttle function for performance
         * @private
         * @param {Function} func
         * @param {Number} limit
         * @returns {Function}
         */
        _throttle: function (func, limit) {
            var inThrottle;
            return function () {
                var args = arguments;
                var context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(function () {
                        inThrottle = false;
                    }, limit);
                }
            };
        },

        /**
         * Clean up on destroy
         * @override
         */
        destroy: function () {
            if (this.observer) {
                this.observer.disconnect();
            }
            this._super.apply(this, arguments);
        }
    });

    // =============================================================================
    // LOADING ANIMATIONS WIDGET
    // =============================================================================

    var LoadingAnimations = publicWidget.Widget.extend({
        selector: '.loading-spinner, .loading-dots, .loading-pulse',

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._initLoadingAnimations();
            return this._super.apply(this, arguments);
        },

        /**
         * Initialize loading animations
         * @private
         */
        _initLoadingAnimations: function () {
            var self = this;
            
            // Auto-hide loading animations after a timeout
            this.$el.each(function () {
                var $loader = $(this);
                var timeout = $loader.data('timeout') || 5000;
                
                setTimeout(function () {
                    $loader.fadeOut(300);
                }, timeout);
            });
        }
    });

    // =============================================================================
    // TYPEWRITER EFFECT WIDGET
    // =============================================================================

    var TypewriterEffect = publicWidget.Widget.extend({
        selector: '.typewriter',

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._initTypewriter();
            return this._super.apply(this, arguments);
        },

        /**
         * Initialize typewriter effect
         * @private
         */
        _initTypewriter: function () {
            var self = this;
            
            this.$el.each(function () {
                var $element = $(this);
                var text = $element.text();
                var speed = $element.data('typewriter-speed') || 50;
                
                $element.text('');
                self._typeText($element, text, speed);
            });
        },

        /**
         * Type text character by character
         * @private
         * @param {jQuery} $element
         * @param {String} text
         * @param {Number} speed
         */
        _typeText: function ($element, text, speed) {
            var i = 0;
            var timer = setInterval(function () {
                if (i < text.length) {
                    $element.text($element.text() + text.charAt(i));
                    i++;
                } else {
                    clearInterval(timer);
                }
            }, speed);
        }
    });

    // =============================================================================
    // COUNTER ANIMATION WIDGET
    // =============================================================================

    var CounterAnimation = publicWidget.Widget.extend({
        selector: '.counter',

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._initCounters();
            return this._super.apply(this, arguments);
        },

        /**
         * Initialize counter animations
         * @private
         */
        _initCounters: function () {
            var self = this;
            
            if (window.IntersectionObserver) {
                var observer = new IntersectionObserver(function (entries) {
                    entries.forEach(function (entry) {
                        if (entry.isIntersecting) {
                            self._animateCounter($(entry.target));
                            observer.unobserve(entry.target);
                        }
                    });
                });

                this.$el.each(function () {
                    observer.observe(this);
                });
            } else {
                // Fallback for older browsers
                $(window).on('scroll', function () {
                    self.$el.each(function () {
                        var $counter = $(this);
                        if (self._isInViewport($counter) && !$counter.hasClass('counted')) {
                            self._animateCounter($counter);
                        }
                    });
                });
            }
        },

        /**
         * Animate counter from 0 to target value
         * @private
         * @param {jQuery} $counter
         */
        _animateCounter: function ($counter) {
            var target = parseInt($counter.data('target') || $counter.text());
            var duration = $counter.data('duration') || 2000;
            var start = 0;
            var increment = target / (duration / 16);
            
            $counter.addClass('counted');
            
            var timer = setInterval(function () {
                start += increment;
                $counter.text(Math.floor(start));
                
                if (start >= target) {
                    $counter.text(target);
                    clearInterval(timer);
                }
            }, 16);
        },

        /**
         * Check if element is in viewport
         * @private
         * @param {jQuery} $element
         * @returns {Boolean}
         */
        _isInViewport: function ($element) {
            var elementTop = $element.offset().top;
            var elementBottom = elementTop + $element.outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();
            
            return elementBottom > viewportTop && elementTop < viewportBottom;
        }
    });

    // =============================================================================
    // SMOOTH SCROLL WIDGET
    // =============================================================================

    var SmoothScroll = publicWidget.Widget.extend({
        selector: 'a[href^="#"]',
        events: {
            'click': '_onSmoothScrollClick',
        },

        /**
         * Handle smooth scroll clicks
         * @private
         * @param {Event} ev
         */
        _onSmoothScrollClick: function (ev) {
            var href = $(ev.currentTarget).attr('href');
            var $target = $(href);
            
            if ($target.length) {
                ev.preventDefault();
                
                var offset = $target.data('scroll-offset') || 80;
                var duration = $target.data('scroll-duration') || 800;
                
                $('html, body').animate({
                    scrollTop: $target.offset().top - offset
                }, duration, 'easeInOutCubic');
            }
        }
    });

    // =============================================================================
    // PERFORMANCE MONITOR
    // =============================================================================

    var PerformanceMonitor = publicWidget.Widget.extend({
        selector: 'body',

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._monitorPerformance();
            return this._super.apply(this, arguments);
        },

        /**
         * Monitor performance and disable animations if needed
         * @private
         */
        _monitorPerformance: function () {
            // Check for reduced motion preference
            if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                $('body').addClass('reduced-motion');
                return;
            }

            // Monitor frame rate
            var frameCount = 0;
            var lastTime = performance.now();
            
            var checkFrameRate = function () {
                frameCount++;
                var currentTime = performance.now();
                
                if (currentTime - lastTime >= 1000) {
                    var fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                    
                    if (fps < 30) {
                        $('body').addClass('low-performance');
                        console.warn('Low performance detected, reducing animations');
                    }
                    
                    frameCount = 0;
                    lastTime = currentTime;
                }
                
                requestAnimationFrame(checkFrameRate);
            };
            
            requestAnimationFrame(checkFrameRate);
        }
    });

// Register widgets
publicWidget.registry.ScrollAnimations = ScrollAnimations;
publicWidget.registry.LoadingAnimations = LoadingAnimations;
publicWidget.registry.TypewriterEffect = TypewriterEffect;
publicWidget.registry.CounterAnimation = CounterAnimation;
publicWidget.registry.SmoothScroll = SmoothScroll;
publicWidget.registry.PerformanceMonitor = PerformanceMonitor;
