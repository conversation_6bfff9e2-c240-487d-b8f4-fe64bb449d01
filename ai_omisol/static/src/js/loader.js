/**
 * Omisol Website Loader
 * Industrial-themed loading animation
 */

(function() {
    'use strict';

    let loadingProgress = 0;
    let loadingInterval;
    let minLoadingTime = 2000; // Minimum 2 seconds
    let startTime = Date.now();

    function createLoader() {
        // Create loader HTML structure
        const loaderHTML = `
            <div class="omisol-loader" id="omisolLoader">
                <div class="loader-background-pattern"></div>
                <div class="loader-content">
                    <img src="/web/image/website/1/logo" 
                         alt="Omisol Logo" 
                         class="loader-logo"
                         onerror="this.src='/ai_omisol/static/src/img/omisol-logo.png'">
                    <h2 class="loader-text">Tank Monitoring System</h2>
                    <p class="loader-subtitle">Initializing Advanced Gauging Solutions</p>
                    
                    <div class="tank-container">
                        <div class="tank-body">
                            <div class="tank-liquid"></div>
                            <div class="tank-gauge-line"></div>
                            <div class="tank-gauge-line"></div>
                            <div class="tank-gauge-line"></div>
                        </div>
                        <div class="tank-pipes">
                            <div class="pipe-inlet"></div>
                            <div class="pipe-outlet"></div>
                        </div>
                    </div>
                    
                    <div class="loading-animation">
                        <div class="loading-bar"></div>
                    </div>
                    
                    <div class="loading-dots">
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                    </div>
                    
                    <div class="loader-progress" id="loaderProgress">0%</div>
                </div>
            </div>
        `;

        // Insert loader at the beginning of body
        document.body.insertAdjacentHTML('afterbegin', loaderHTML);
    }

    function updateProgress() {
        const progressElement = document.getElementById('loaderProgress');
        if (progressElement) {
            progressElement.textContent = Math.round(loadingProgress) + '%';
        }
    }

    function simulateLoading() {
        loadingInterval = setInterval(() => {
            // Simulate realistic loading progress
            if (loadingProgress < 30) {
                loadingProgress += Math.random() * 15;
            } else if (loadingProgress < 60) {
                loadingProgress += Math.random() * 10;
            } else if (loadingProgress < 90) {
                loadingProgress += Math.random() * 5;
            } else if (loadingProgress < 95) {
                loadingProgress += Math.random() * 2;
            }

            // Cap at 95% until page is actually loaded
            if (loadingProgress > 95) {
                loadingProgress = 95;
            }

            updateProgress();
        }, 100);
    }

    function completeLoading() {
        // Complete the loading animation
        loadingProgress = 100;
        updateProgress();

        // Wait a bit then fade out
        setTimeout(() => {
            const loader = document.getElementById('omisolLoader');
            if (loader) {
                loader.classList.add('fade-out');
                
                // Remove loader after fade animation
                setTimeout(() => {
                    loader.remove();
                }, 500);
            }
        }, 300);

        // Clear interval
        if (loadingInterval) {
            clearInterval(loadingInterval);
        }
    }

    function checkPageLoad() {
        const currentTime = Date.now();
        const elapsedTime = currentTime - startTime;

        // Check if page is loaded and minimum time has passed
        if (document.readyState === 'complete' && elapsedTime >= minLoadingTime) {
            completeLoading();
        } else {
            // Check again in 100ms
            setTimeout(checkPageLoad, 100);
        }
    }

    function initLoader() {
        // Only show loader on initial page load, not on navigation
        if (performance.navigation.type === performance.navigation.TYPE_RELOAD || 
            performance.navigation.type === performance.navigation.TYPE_NAVIGATE) {
            
            createLoader();
            simulateLoading();
            
            // Start checking for page load completion
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', checkPageLoad);
            } else {
                checkPageLoad();
            }

            // Fallback: ensure loader is removed after maximum time
            setTimeout(() => {
                const loader = document.getElementById('omisolLoader');
                if (loader) {
                    completeLoading();
                }
            }, 8000); // Maximum 8 seconds
        }
    }

    // Initialize loader immediately
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initLoader);
    } else {
        initLoader();
    }

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            // Page is hidden, pause animations if needed
        } else {
            // Page is visible again
        }
    });

    // Expose global function for manual control if needed
    window.OmisolLoader = {
        hide: completeLoading,
        show: initLoader
    };

})();
