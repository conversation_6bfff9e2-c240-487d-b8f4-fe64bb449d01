/** @odoo-module **/

/**
 * OMISOL LOADER DEMO FUNCTIONALITY
 * =================================
 * Demo functions for testing the dynamic website loader
 */

import publicWidget from "@web/legacy/js/public/public_widget";

var LoaderDemo = publicWidget.Widget.extend({
    selector: '.loader-demo-page',
    
    /**
     * @override
     */
    start: function () {
        this._super.apply(this, arguments);
        
        // Make demo functions available globally
        window.showCustomLoader = this.showCustomLoader.bind(this);
        window.showQuickLoader = this.showQuickLoader.bind(this);
        
        return this._super.apply(this, arguments);
    },

    /**
     * Show custom loader demo
     */
    showCustomLoader: function() {
        if (typeof window.OmisolLoader !== 'undefined') {
            window.OmisolLoader.show({
                text: 'Custom Loading...',
                subtext: 'Demonstrating custom options',
                duration: 4000
            });
        } else {
            console.warn('OmisolLoader not available');
            alert('OmisolLoader is not available. Please ensure the dynamic loader is properly loaded.');
        }
    },
    
    /**
     * Show quick loader demo
     */
    showQuickLoader: function() {
        if (typeof window.OmisolLoader !== 'undefined') {
            window.OmisolLoader.show({
                text: 'Quick Load!',
                subtext: 'Fast demonstration',
                duration: 1500
            });
        } else {
            console.warn('OmisolLoader not available');
            alert('OmisolLoader is not available. Please ensure the dynamic loader is properly loaded.');
        }
    }
});

// Register the widget
publicWidget.registry.LoaderDemo = LoaderDemo;

export default LoaderDemo;
