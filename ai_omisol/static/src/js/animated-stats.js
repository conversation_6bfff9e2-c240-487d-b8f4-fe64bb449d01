/**
 * Animated Statistics Counter
 * Professional number animation for statistics section
 */

(function() {
    'use strict';

    // Counter animation function
    function animateCounter(element, target, duration = 2000) {
        const start = 0;
        const increment = target / (duration / 16); // 60fps
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // Format number with commas for large numbers
            const displayValue = Math.floor(current);
            element.textContent = displayValue.toLocaleString();
            
            // Add animation class
            element.classList.add('animate');
        }, 16);
    }

    // Intersection Observer for triggering animations
    function initStatsAnimation() {
        const statsElements = document.querySelectorAll('.stat-number-animated, .stat-number-inline, .stat-number-pro');

        if (statsElements.length === 0) return;

        // Set initial values to prevent vanishing
        statsElements.forEach(element => {
            const target = parseInt(element.getAttribute('data-target'));
            if (!element.classList.contains('animated')) {
                element.textContent = '0';
            }
        });

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !entry.target.classList.contains('animated')) {
                    const target = parseInt(entry.target.getAttribute('data-target'));
                    const duration = 2500; // 2.5 seconds

                    // Mark as animated to prevent re-triggering
                    entry.target.classList.add('animated');

                    // Add animate class to parent for different stat types
                    const parentItem = entry.target.closest('.stat-inline-item, .stat-professional-item');
                    if (parentItem) {
                        parentItem.classList.add('animate');
                    }

                    // Start animation
                    animateCounter(entry.target, target, duration);
                }
            });
        }, {
            threshold: 0.3,
            rootMargin: '0px 0px -50px 0px'
        });

        statsElements.forEach(element => {
            observer.observe(element);
        });
    }

    // Enhanced stats hover effects for all formats
    function initStatsHoverEffects() {
        const statItems = document.querySelectorAll('.stat-inline-item, .stat-professional-item');

        statItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                // Add subtle pulse effect to number
                const numberElement = this.querySelector('.stat-number-inline, .stat-number-pro');
                if (numberElement) {
                    numberElement.style.transform = 'scale(1.05)';
                    numberElement.style.transition = 'transform 0.3s ease';
                }
            });

            item.addEventListener('mouseleave', function() {
                // Reset number scale
                const numberElement = this.querySelector('.stat-number-inline, .stat-number-pro');
                if (numberElement) {
                    numberElement.style.transform = 'scale(1)';
                }
            });
        });
    }

    // Staggered animation for all stat items
    function initStaggeredItemAnimation() {
        const statItems = document.querySelectorAll('.stat-inline-item, .stat-professional-item');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting && !entry.target.classList.contains('item-animated')) {
                    // Add staggered delay
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                        entry.target.classList.add('item-animated');
                    }, index * 150); // 150ms delay between items
                }
            });
        }, {
            threshold: 0.3
        });

        statItems.forEach((item, index) => {
            // Set initial state
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            item.style.transition = 'all 0.6s ease';

            observer.observe(item);
        });
    }

    // Initialize everything when DOM is ready
    function init() {
        // Wait a bit for DOM to be fully ready
        setTimeout(() => {
            initStatsAnimation();
            initStatsHoverEffects();
            initStaggeredItemAnimation();
        }, 100);
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Re-initialize on page changes (for SPA-like behavior)
    window.addEventListener('load', init);

})();
