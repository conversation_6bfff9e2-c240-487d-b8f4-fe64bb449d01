/**
 * OMISOL THEME - LAZY ANIMATIONS
 * ==============================
 * Advanced animations and effects that are loaded after initial page load
 * for better performance and progressive enhancement
 */

/** @odoo-module **/

import publicWidget from "@web/legacy/js/public/public_widget";
import { _t } from "@web/core/l10n/translation";

    // =============================================================================
    // ADVANCED SCROLL ANIMATIONS
    // =============================================================================

    var AdvancedScrollAnimations = publicWidget.Widget.extend({
        selector: 'body',
        
        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._initAdvancedScrollEffects();
            this._initParallaxAdvanced();
            this._initMorphingElements();
            return this._super.apply(this, arguments);
        },

        /**
         * Initialize advanced scroll effects
         * @private
         */
        _initAdvancedScrollEffects: function () {
            var self = this;
            
            // Advanced reveal animations
            this.$('.scroll-reveal-advanced').each(function (index) {
                var $element = $(this);
                var delay = index * 100; // Stagger animation
                
                $element.css('transition-delay', delay + 'ms');
                
                if (self._isInViewport($element)) {
                    setTimeout(function () {
                        $element.addClass('revealed');
                    }, delay);
                }
            });

            // Intersection Observer for advanced animations
            if (window.IntersectionObserver) {
                var observer = new IntersectionObserver(function (entries) {
                    entries.forEach(function (entry) {
                        if (entry.isIntersecting) {
                            var $element = $(entry.target);
                            var delay = $element.data('reveal-delay') || 0;
                            
                            setTimeout(function () {
                                $element.addClass('revealed');
                                if ($element.hasClass('revealed-delayed')) {
                                    $element.addClass('revealed-delayed');
                                }
                            }, delay);
                            
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                });

                this.$('.scroll-reveal-advanced').each(function () {
                    observer.observe(this);
                });
            }
        },

        /**
         * Initialize advanced parallax effects
         * @private
         */
        _initParallaxAdvanced: function () {
            if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                return;
            }

            var self = this;
            var parallaxElements = this.$('.parallax-advanced');
            
            if (parallaxElements.length === 0) {
                return;
            }

            var updateParallax = this._throttle(function () {
                var scrollTop = $(window).scrollTop();
                var windowHeight = $(window).height();
                
                parallaxElements.each(function () {
                    var $element = $(this);
                    var elementTop = $element.offset().top;
                    var elementHeight = $element.outerHeight();
                    var speed = $element.data('parallax-speed') || 0.5;
                    var direction = $element.data('parallax-direction') || 'up';
                    
                    // Only animate if element is in viewport
                    if (elementTop < scrollTop + windowHeight && elementTop + elementHeight > scrollTop) {
                        var yPos = (scrollTop - elementTop) * speed;
                        
                        if (direction === 'down') {
                            yPos = -yPos;
                        }
                        
                        $element.css('transform', 'translate3d(0, ' + yPos + 'px, 0)');
                    }
                });
            }, 16);

            $(window).on('scroll', updateParallax);
            updateParallax(); // Initial call
        },

        /**
         * Initialize morphing elements
         * @private
         */
        _initMorphingElements: function () {
            this.$('.interactive-blob').each(function () {
                var $blob = $(this);
                var colors = $blob.data('colors') || ['#007bff', '#f97316'];
                var duration = $blob.data('duration') || 8000;
                
                // Animate color changes
                setInterval(function () {
                    var randomColor1 = colors[Math.floor(Math.random() * colors.length)];
                    var randomColor2 = colors[Math.floor(Math.random() * colors.length)];
                    
                    $blob.css('background', 'linear-gradient(45deg, ' + randomColor1 + ', ' + randomColor2 + ')');
                }, duration);
            });
        },

        /**
         * Check if element is in viewport
         * @private
         * @param {jQuery} $element
         * @returns {Boolean}
         */
        _isInViewport: function ($element) {
            var elementTop = $element.offset().top;
            var elementBottom = elementTop + $element.outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();
            
            return elementBottom > viewportTop && elementTop < viewportBottom;
        },

        /**
         * Throttle function for performance
         * @private
         */
        _throttle: function (func, limit) {
            var inThrottle;
            return function () {
                var args = arguments;
                var context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(function () {
                        inThrottle = false;
                    }, limit);
                }
            };
        }
    });

    // =============================================================================
    // MAGNETIC HOVER EFFECTS
    // =============================================================================

    var MagneticHover = publicWidget.Widget.extend({
        selector: '.hover-magnetic',
        events: {
            'mousemove': '_onMouseMove',
            'mouseleave': '_onMouseLeave',
        },

        /**
         * Handle mouse move for magnetic effect
         * @private
         * @param {Event} ev
         */
        _onMouseMove: function (ev) {
            var $element = $(ev.currentTarget);
            var rect = ev.currentTarget.getBoundingClientRect();
            var x = ev.clientX - rect.left - rect.width / 2;
            var y = ev.clientY - rect.top - rect.height / 2;
            
            var strength = $element.data('magnetic-strength') || 0.3;
            var maxDistance = $element.data('magnetic-distance') || 50;
            
            // Limit the magnetic effect distance
            x = Math.max(-maxDistance, Math.min(maxDistance, x * strength));
            y = Math.max(-maxDistance, Math.min(maxDistance, y * strength));
            
            $element.css({
                '--mouse-x': x + 'px',
                '--mouse-y': y + 'px'
            });
        },

        /**
         * Reset position on mouse leave
         * @private
         */
        _onMouseLeave: function (ev) {
            $(ev.currentTarget).css({
                '--mouse-x': '0px',
                '--mouse-y': '0px'
            });
        }
    });

    // =============================================================================
    // TILT EFFECTS
    // =============================================================================

    var TiltEffect = publicWidget.Widget.extend({
        selector: '.hover-tilt',
        events: {
            'mousemove': '_onTiltMove',
            'mouseleave': '_onTiltLeave',
        },

        /**
         * Handle mouse move for tilt effect
         * @private
         * @param {Event} ev
         */
        _onTiltMove: function (ev) {
            var $element = $(ev.currentTarget);
            var rect = ev.currentTarget.getBoundingClientRect();
            var x = ev.clientX - rect.left;
            var y = ev.clientY - rect.top;
            
            var centerX = rect.width / 2;
            var centerY = rect.height / 2;
            
            var rotateX = (y - centerY) / centerY * -10; // Max 10 degrees
            var rotateY = (x - centerX) / centerX * 10;
            
            var maxTilt = $element.data('max-tilt') || 10;
            rotateX = Math.max(-maxTilt, Math.min(maxTilt, rotateX));
            rotateY = Math.max(-maxTilt, Math.min(maxTilt, rotateY));
            
            $element.css('transform', 
                'perspective(1000px) rotateX(' + rotateX + 'deg) rotateY(' + rotateY + 'deg) scale(1.05)'
            );
        },

        /**
         * Reset tilt on mouse leave
         * @private
         */
        _onTiltLeave: function (ev) {
            $(ev.currentTarget).css('transform', 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1)');
        }
    });

    // =============================================================================
    // FLOATING ACTION BUTTON
    // =============================================================================

    var FloatingActionButton = publicWidget.Widget.extend({
        selector: '.floating-action-button',
        events: {
            'click': '_onFabClick',
        },

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._initFabBehavior();
            return this._super.apply(this, arguments);
        },

        /**
         * Initialize FAB behavior
         * @private
         */
        _initFabBehavior: function () {
            var self = this;
            var $fab = this.$el;
            var hideTimeout;
            
            // Auto-hide on scroll
            $(window).on('scroll', function () {
                $fab.addClass('fab-hidden');
                
                clearTimeout(hideTimeout);
                hideTimeout = setTimeout(function () {
                    $fab.removeClass('fab-hidden');
                }, 150);
            });
            
            // Show/hide based on scroll position
            $(window).on('scroll', this._throttle(function () {
                var scrollTop = $(window).scrollTop();
                var showThreshold = $fab.data('show-threshold') || 300;
                
                if (scrollTop > showThreshold) {
                    $fab.addClass('fab-visible');
                } else {
                    $fab.removeClass('fab-visible');
                }
            }, 100));
        },

        /**
         * Handle FAB click
         * @private
         * @param {Event} ev
         */
        _onFabClick: function (ev) {
            ev.preventDefault();
            
            var action = this.$el.data('action');
            
            switch (action) {
                case 'scroll-top':
                    $('html, body').animate({ scrollTop: 0 }, 800, 'easeInOutCubic');
                    break;
                case 'contact':
                    // Trigger contact modal or scroll to contact section
                    var $contactSection = $('#contact');
                    if ($contactSection.length) {
                        $('html, body').animate({
                            scrollTop: $contactSection.offset().top - 80
                        }, 800, 'easeInOutCubic');
                    }
                    break;
                default:
                    // Custom action
                    this.trigger_up('fab_clicked', { action: action });
            }
            
            // Add click animation
            this.$el.addClass('fab-clicked');
            setTimeout(() => {
                this.$el.removeClass('fab-clicked');
            }, 200);
        },

        /**
         * Throttle function
         * @private
         */
        _throttle: function (func, limit) {
            var inThrottle;
            return function () {
                var args = arguments;
                var context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(function () {
                        inThrottle = false;
                    }, limit);
                }
            };
        }
    });

    // =============================================================================
    // SKELETON LOADER
    // =============================================================================

    var SkeletonLoader = publicWidget.Widget.extend({
        selector: '.skeleton-loader',

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._initSkeletonLoader();
            return this._super.apply(this, arguments);
        },

        /**
         * Initialize skeleton loader
         * @private
         */
        _initSkeletonLoader: function () {
            var self = this;
            
            this.$el.each(function () {
                var $skeleton = $(this);
                var duration = $skeleton.data('duration') || 3000;
                var targetSelector = $skeleton.data('target');
                
                if (targetSelector) {
                    // Hide skeleton when target content is loaded
                    var checkContent = function () {
                        var $target = $(targetSelector);
                        if ($target.length && $target.is(':visible')) {
                            $skeleton.fadeOut(300, function () {
                                $skeleton.remove();
                            });
                        } else {
                            setTimeout(checkContent, 100);
                        }
                    };
                    
                    setTimeout(checkContent, 500);
                } else {
                    // Auto-hide after duration
                    setTimeout(function () {
                        $skeleton.fadeOut(300, function () {
                            $skeleton.remove();
                        });
                    }, duration);
                }
            });
        }
    });

    // =============================================================================
    // LAZY LOADING IMAGES
    // =============================================================================

    var LazyImages = publicWidget.Widget.extend({
        selector: 'img[data-src]',

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._initLazyLoading();
            return this._super.apply(this, arguments);
        },

        /**
         * Initialize lazy loading
         * @private
         */
        _initLazyLoading: function () {
            if ('IntersectionObserver' in window) {
                var imageObserver = new IntersectionObserver(function (entries) {
                    entries.forEach(function (entry) {
                        if (entry.isIntersecting) {
                            var img = entry.target;
                            var $img = $(img);
                            
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            img.classList.add('lazy-loaded');
                            
                            // Add fade-in effect
                            $img.on('load', function () {
                                $img.addClass('fade-in');
                            });
                            
                            imageObserver.unobserve(img);
                        }
                    });
                });

                this.$el.each(function () {
                    imageObserver.observe(this);
                });
            } else {
                // Fallback for older browsers
                this.$el.each(function () {
                    var img = this;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    img.classList.add('lazy-loaded');
                });
            }
        }
    });

// Register widgets
publicWidget.registry.AdvancedScrollAnimations = AdvancedScrollAnimations;
publicWidget.registry.MagneticHover = MagneticHover;
publicWidget.registry.TiltEffect = TiltEffect;
publicWidget.registry.FloatingActionButton = FloatingActionButton;
publicWidget.registry.SkeletonLoader = SkeletonLoader;
publicWidget.registry.LazyImages = LazyImages;
