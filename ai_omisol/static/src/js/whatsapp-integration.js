/** @odoo-module **/

/**
 * OMISOL WHATSAPP INTEGRATION
 * ===========================
 * WhatsApp integration for customer communication
 */

import publicWidget from "@web/legacy/js/public/public_widget";
import { _t } from "@web/core/l10n/translation";

var WhatsAppIntegration = publicWidget.Widget.extend({
    selector: '.whatsapp-button, .whatsapp-widget',
    events: {
        'click': '_onWhatsAppClick',
    },

    /**
     * Handle WhatsApp button click
     * @private
     * @param {Event} ev
     */
    _onWhatsAppClick: function (ev) {
        ev.preventDefault();
        
        var $button = $(ev.currentTarget);
        var phoneNumber = $button.data('phone') || $button.data('whatsapp-number');
        var message = $button.data('message') || '';
        var customerName = $button.data('customer-name') || '';
        var username = $button.data('username') || '';
        var password = $button.data('password') || '';
        
        if (!phoneNumber) {
            this._showError(_('WhatsApp number is not available.'));
            return;
        }
        
        // Format phone number (remove non-digits)
        var cleanNumber = phoneNumber.replace(/\D/g, '');
        
        // Add country code if not present (assuming India +91 as default)
        if (cleanNumber && !cleanNumber.startsWith('91') && cleanNumber.length === 10) {
            cleanNumber = '91' + cleanNumber;
        }
        
        // Prepare message
        var whatsappMessage = message;
        if (!whatsappMessage && username && password) {
            whatsappMessage = this._getDefaultCredentialsMessage(customerName, username, password);
        } else if (!whatsappMessage) {
            whatsappMessage = this._getDefaultMessage(customerName);
        }
        
        // URL encode the message
        var encodedMessage = encodeURIComponent(whatsappMessage);
        
        // Create WhatsApp URL
        var whatsappUrl = 'https://wa.me/' + cleanNumber + '?text=' + encodedMessage;
        
        // Add loading state
        this._setLoadingState($button, true);
        
        // Open WhatsApp
        window.open(whatsappUrl, '_blank');
        
        // Remove loading state after a short delay
        setTimeout(() => {
            this._setLoadingState($button, false);
        }, 1000);
        
        // Track analytics if available
        this._trackWhatsAppClick(cleanNumber, customerName);
    },

    /**
     * Get default credentials message
     * @private
     * @param {string} customerName
     * @param {string} username
     * @param {string} password
     * @returns {string}
     */
    _getDefaultCredentialsMessage: function (customerName, username, password) {
        var baseUrl = window.location.origin;
        
        return `Hello ${customerName || 'there'},

Your Omisol account credentials:
👤 Username: ${username}
🔑 Password: ${password}

🌐 Login at: ${baseUrl}/web/login

Please keep these credentials safe and consider changing your password after first login.

Best regards,
Omisol Team`;
    },

    /**
     * Get default message
     * @private
     * @param {string} customerName
     * @returns {string}
     */
    _getDefaultMessage: function (customerName) {
        return `Hello ${customerName || 'there'},

Thank you for your interest in Omisol!

We're here to help you with our industrial solutions, chemical products, and specialized equipment.

How can we assist you today?

Best regards,
Omisol Team`;
    },

    /**
     * Set loading state for button
     * @private
     * @param {jQuery} $button
     * @param {boolean} loading
     */
    _setLoadingState: function ($button, loading) {
        if (loading) {
            $button.addClass('loading').prop('disabled', true);
            var originalText = $button.text();
            $button.data('original-text', originalText);
            $button.html('<i class="fa fa-spinner fa-spin me-2"></i>Opening WhatsApp...');
        } else {
            $button.removeClass('loading').prop('disabled', false);
            var originalText = $button.data('original-text');
            if (originalText) {
                $button.text(originalText);
            }
        }
    },

    /**
     * Show error message
     * @private
     * @param {string} message
     */
    _showError: function (message) {
        // Create a simple toast notification
        var $toast = $(`
            <div class="toast-notification error" style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: #dc3545;
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                z-index: 9999;                
                font-size: 14px;
                max-width: 300px;
            ">
                <i class="fa fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `);
        
        $('body').append($toast);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            $toast.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    },

    /**
     * Track WhatsApp click for analytics
     * @private
     * @param {string} phoneNumber
     * @param {string} customerName
     */
    _trackWhatsAppClick: function (phoneNumber, customerName) {
        // Track with Google Analytics if available
        if (typeof gtag !== 'undefined') {
            gtag('event', 'whatsapp_click', {
                'event_category': 'communication',
                'event_label': customerName || 'unknown',
                'value': 1
            });
        }
        
        // Track with Odoo analytics if available
        if (typeof odoo !== 'undefined' && odoo.session) {
            this._rpc({
                route: '/web/dataset/call_kw',
                params: {
                    model: 'omisol.customer',
                    method: 'track_whatsapp_click',
                    args: [phoneNumber, customerName],
                    kwargs: {}
                }
            }).catch(() => {
                // Ignore errors for analytics
            });
        }
    }
});

/**
 * WhatsApp Floating Button Widget
 */
var WhatsAppFloatingButton = publicWidget.Widget.extend({
    selector: 'body',

    /**
     * @override
     */
    start: function () {
        this._super.apply(this, arguments);
        this._createFloatingButton();
        return this._super.apply(this, arguments);
    },

    /**
     * Create floating WhatsApp button
     * @private
     */
    _createFloatingButton: function () {
        // Check if floating button should be shown
        var showFloatingButton = $('body').data('show-whatsapp-float') !== false;
        var whatsappNumber = $('body').data('whatsapp-number') || '+919876543210'; // Default number
        
        if (!showFloatingButton) {
            return;
        }
        
        var $floatingButton = $(`
            <div class="whatsapp-floating-button cursor-magnetic" 
                 data-phone="${whatsappNumber}"
                 data-magnetic-strength="0.4"
                 style="
                     position: fixed;
                     bottom: 100px;
                     right: 2rem;
                     width: 60px;
                     height: 60px;
                     background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
                     border-radius: 50%;
                     display: flex;
                     align-items: center;
                     justify-content: center;
                     color: white;
                     font-size: 24px;
                     box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
                     cursor: pointer;
                     z-index: 999;
                     transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                     text-decoration: none;
                     animation: whatsappPulse 2s infinite;
                 "
                 title="Chat with us on WhatsApp">
                <i class="fab fa-whatsapp"></i>
            </div>
        `);
        
        // Add CSS animation
        if (!$('#whatsapp-animations').length) {
            $('head').append(`
                <style id="whatsapp-animations">
                    @keyframes whatsappPulse {
                        0%, 100% {
                            transform: scale(1);
                            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
                        }
                        50% {
                            transform: scale(1.05);
                            box-shadow: 0 12px 35px rgba(37, 211, 102, 0.6);
                        }
                    }
                    
                    .whatsapp-floating-button:hover {
                        transform: scale(1.1) !important;
                        box-shadow: 0 12px 35px rgba(37, 211, 102, 0.6) !important;
                        animation-play-state: paused;
                    }
                    
                    .whatsapp-floating-button:active {
                        transform: scale(0.95) !important;
                    }
                    
                    @media (max-width: 768px) {
                        .whatsapp-floating-button {
                            bottom: 80px !important;
                            right: 1rem !important;
                            width: 50px !important;
                            height: 50px !important;
                            font-size: 20px !important;
                        }
                    }
                </style>
            `);
        }
        
        $('body').append($floatingButton);
        
        // Make it a WhatsApp widget
        $floatingButton.addClass('whatsapp-widget');
    }
});

// Register widgets
publicWidget.registry.WhatsAppIntegration = WhatsAppIntegration;
publicWidget.registry.WhatsAppFloatingButton = WhatsAppFloatingButton;
