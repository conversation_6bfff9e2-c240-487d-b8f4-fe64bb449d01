/**
 * Custom Cursor Effects for Omisol Website - COMPLETELY DISABLED
 * Custom cursor functionality disabled for better user experience
 */

(function() {
    'use strict';

    // Custom cursor completely disabled
    document.body.style.cursor = 'auto';
    return;

    let mouseX = 0;
    let mouseY = 0;
    let cursorOuterX = 0;
    let cursorOuterY = 0;
    let cursorInnerX = 0;
    let cursorInnerY = 0;

    // Initialize cursor elements
    function initCursor() {
        // Show the custom cursor elements
        const style = document.createElement('style');
        style.textContent = `
            body::before,
            body::after {
                display: block !important;
            }
        `;
        document.head.appendChild(style);
    }

    // Update mouse position
    function updateMousePosition(e) {
        mouseX = e.clientX;
        mouseY = e.clientY;
    }

    // Animate cursor elements
    function animateCursor() {
        // Smooth following for outer cursor (slower)
        const outerSpeed = 0.15;
        cursorOuterX += (mouseX - cursorOuterX) * outerSpeed;
        cursorOuterY += (mouseY - cursorOuterY) * outerSpeed;

        // Faster following for inner cursor
        const innerSpeed = 0.8;
        cursorInnerX += (mouseX - cursorInnerX) * innerSpeed;
        cursorInnerY += (mouseY - cursorInnerY) * innerSpeed;

        // Apply positions using CSS custom properties
        document.documentElement.style.setProperty('--cursor-outer-x', cursorOuterX + 'px');
        document.documentElement.style.setProperty('--cursor-outer-y', cursorOuterY + 'px');
        document.documentElement.style.setProperty('--cursor-inner-x', cursorInnerX + 'px');
        document.documentElement.style.setProperty('--cursor-inner-y', cursorInnerY + 'px');

        requestAnimationFrame(animateCursor);
    }

    // Update CSS to use custom properties
    function updateCursorCSS() {
        const style = document.createElement('style');
        style.textContent = `
            body::before {
                left: var(--cursor-outer-x, 0px);
                top: var(--cursor-outer-y, 0px);
            }
            body::after {
                left: var(--cursor-inner-x, 0px);
                top: var(--cursor-inner-y, 0px);
            }
        `;
        document.head.appendChild(style);
    }

    // Handle hover effects on interactive elements
    function handleHoverEffects() {
        // Only apply big circle effect to buttons with background
        const buttonElements = document.querySelectorAll('.btn, button, .cursor-magnetic, [class*="btn-"]');
        const linkElements = document.querySelectorAll('a:not(.btn):not([class*="btn-"])');

        // Big circle effect for buttons
        buttonElements.forEach(element => {
            element.addEventListener('mouseenter', function() {
                document.documentElement.style.setProperty('--cursor-hover', '1');
                document.body.classList.add('cursor-hovering');
            });

            element.addEventListener('mouseleave', function() {
                document.documentElement.style.setProperty('--cursor-hover', '0');
                document.body.classList.remove('cursor-hovering');
            });
        });

        // Small effect for simple links
        linkElements.forEach(element => {
            element.addEventListener('mouseenter', function() {
                document.body.classList.add('cursor-link-hover');
            });

            element.addEventListener('mouseleave', function() {
                document.body.classList.remove('cursor-link-hover');
            });
        });
    }

    // Enhanced CSS for hover states
    function addHoverCSS() {
        const style = document.createElement('style');
        style.textContent = `
            body.cursor-hovering::before {
                border-color: rgba(0, 123, 255, 0.02) !important;
                transform: translate(-50%, -50%) scale(1.1) !important;
                background-color: rgba(0, 123, 255, 0.02) !important;
                animation: rippleEffect 0.6s ease-out !important;
            }
            body.cursor-hovering::after {
                opacity: 0 !important;
                transform: translate(-50%, -50%) scale(0) !important;
            }

            body.cursor-link-hover::before {
                border-color: var(--primary-blue, #007bff) !important;
                transform: translate(-50%, -50%) scale(1.2) !important;
            }
            body.cursor-link-hover::after {
                background-color: var(--primary-blue, #007bff) !important;
                transform: translate(-50%, -50%) scale(1.1) !important;
            }

            @keyframes rippleEffect {
                0% {
                    background-color: rgba(0, 123, 255, 0.02);
                    transform: translate(-50%, -50%) scale(1);
                }
                50% {
                    background-color: rgba(0, 123, 255, 0.02);
                    transform: translate(-50%, -50%) scale(1.05);
                }
                100% {
                    background-color: rgba(0, 123, 255, 0.02);
                    transform: translate(-50%, -50%) scale(1.1);
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Initialize everything when DOM is ready
    function init() {
        initCursor();
        updateCursorCSS();
        addHoverCSS();
        
        // Start mouse tracking
        document.addEventListener('mousemove', updateMousePosition);
        
        // Start animation loop
        requestAnimationFrame(animateCursor);
        
        // Set up hover effects
        handleHoverEffects();
        
        // Re-apply hover effects when new content is loaded (for dynamic content)
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length > 0) {
                    handleHoverEffects();
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Handle page visibility changes
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            // Page is hidden, pause animations
            document.body.style.cursor = 'none';
        } else {
            // Page is visible, resume animations
            handleHoverEffects();
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        // Re-initialize hover effects after resize
        setTimeout(handleHoverEffects, 100);
    });

})();
