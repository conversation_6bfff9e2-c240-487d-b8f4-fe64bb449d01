/** @odoo-module **/

/**
 * OMISOL DYNAMIC WEBSITE LOADER
 * =============================
 * Professional website loader with dynamic company logo and smooth animations
 * Inspired by modern industrial design patterns
 */

import publicWidget from "@web/legacy/js/public/public_widget";
import { _t } from "@web/core/l10n/translation";

var DynamicWebsiteLoader = publicWidget.Widget.extend({
    selector: 'body',
    
    /**
     * @override
     */
    start: function () {
        this._super.apply(this, arguments);
        
        // Only show loader on initial page load
        if (this._shouldShowLoader()) {
            this._initializeLoader();
        }
        
        return this._super.apply(this, arguments);
    },

    /**
     * Check if loader should be shown
     * @private
     */
    _shouldShowLoader: function () {
        // Don't show loader in backend or if already shown in this session
        if (window.location.pathname.startsWith('/web') || 
            sessionStorage.getItem('omisol_loader_shown')) {
            return false;
        }
        
        // Don't show on AJAX requests
        if (window.performance && window.performance.navigation.type === 1) {
            return false; // Reload
        }
        
        return true;
    },

    /**
     * Initialize and show the loader
     * @private
     */
    _initializeLoader: function () {
        this._createLoaderHTML();
        this._startLoadingSequence();
        
        // Mark loader as shown for this session
        sessionStorage.setItem('omisol_loader_shown', 'true');
    },

    /**
     * Create loader HTML structure
     * @private
     */
    _createLoaderHTML: function () {
        const loaderHTML = `
            <div class="omisol-website-loader" id="omisolLoader">
                <div class="omisol-loader-content">
                    <div class="omisol-loader-logo">
                        ${this._getLogoHTML()}
                    </div>
                    <div class="omisol-loader-progress"></div>
                    <div class="omisol-loader-text">${_t('Loading Excellence...')}</div>
                    <div class="omisol-loader-subtext">${_t('Preparing your industrial solutions')}</div>
                    <div class="omisol-loader-dots">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                </div>
            </div>
        `;
        
        // Insert loader at the beginning of body
        document.body.insertAdjacentHTML('afterbegin', loaderHTML);
    },

    /**
     * Get dynamic logo HTML
     * @private
     */
    _getLogoHTML: function () {
        // Try to get company logo from website settings
        const logoImg = document.querySelector('.navbar-brand img');
        const logoText = document.querySelector('.navbar-brand');
        
        if (logoImg && logoImg.src) {
            return `<img src="${logoImg.src}" alt="Company Logo" />`;
        } else if (logoText && logoText.textContent) {
            const companyName = logoText.textContent.trim();
            return `<div class="logo-text">${companyName}</div>`;
        } else {
            // Fallback to Omisol branding
            return `<div class="logo-text">OMISOL</div>`;
        }
    },

    /**
     * Start the loading sequence
     * @private
     */
    _startLoadingSequence: function () {
        const loader = document.getElementById('omisolLoader');
        if (!loader) return;

        // Simulate loading progress
        this._simulateLoading();
        
        // Wait for page to be fully loaded
        if (document.readyState === 'complete') {
            this._hideLoader();
        } else {
            window.addEventListener('load', () => {
                // Add a minimum display time for better UX
                setTimeout(() => {
                    this._hideLoader();
                }, 1500);
            });
        }
        
        // Fallback: hide loader after maximum time
        setTimeout(() => {
            this._hideLoader();
        }, 8000);
    },

    /**
     * Simulate loading progress with text changes
     * @private
     */
    _simulateLoading: function () {
        const textElement = document.querySelector('.omisol-loader-text');
        const subtextElement = document.querySelector('.omisol-loader-subtext');
        
        if (!textElement || !subtextElement) return;
        
        const loadingSteps = [
            {
                text: _t('Initializing Systems...'),
                subtext: _t('Setting up industrial solutions')
            },
            {
                text: _t('Loading Components...'),
                subtext: _t('Preparing advanced features')
            },
            {
                text: _t('Optimizing Performance...'),
                subtext: _t('Ensuring smooth operation')
            },
            {
                text: _t('Almost Ready...'),
                subtext: _t('Finalizing your experience')
            }
        ];
        
        let currentStep = 0;
        
        const updateText = () => {
            if (currentStep < loadingSteps.length) {
                const step = loadingSteps[currentStep];
                
                // Fade out
                textElement.style.opacity = '0';
                subtextElement.style.opacity = '0';
                
                setTimeout(() => {
                    // Update text
                    textElement.textContent = step.text;
                    subtextElement.textContent = step.subtext;
                    
                    // Fade in
                    textElement.style.opacity = '1';
                    subtextElement.style.opacity = '1';
                    
                    currentStep++;
                }, 300);
            }
        };
        
        // Update text every 1.5 seconds
        const textInterval = setInterval(() => {
            updateText();
            
            if (currentStep >= loadingSteps.length) {
                clearInterval(textInterval);
            }
        }, 1500);
    },

    /**
     * Hide the loader with smooth animation
     * @private
     */
    _hideLoader: function () {
        const loader = document.getElementById('omisolLoader');
        if (!loader) return;
        
        // Add loaded class for CSS transition
        loader.classList.add('loaded');
        
        // Remove from DOM after animation
        setTimeout(() => {
            if (loader.parentNode) {
                loader.parentNode.removeChild(loader);
            }
        }, 800);
        
        // Trigger custom event for other scripts
        window.dispatchEvent(new CustomEvent('omisolLoaderHidden'));
    },

    /**
     * Public method to manually hide loader
     */
    hideLoader: function () {
        this._hideLoader();
    },

    /**
     * Public method to show loader (for SPA navigation)
     */
    showLoader: function (options = {}) {
        // Remove existing loader
        const existingLoader = document.getElementById('omisolLoader');
        if (existingLoader) {
            existingLoader.remove();
        }
        
        // Create new loader
        this._createLoaderHTML();
        
        // Update text if provided
        if (options.text) {
            const textElement = document.querySelector('.omisol-loader-text');
            if (textElement) {
                textElement.textContent = options.text;
            }
        }
        
        if (options.subtext) {
            const subtextElement = document.querySelector('.omisol-loader-subtext');
            if (subtextElement) {
                subtextElement.textContent = options.subtext;
            }
        }
        
        // Auto-hide after specified time or default
        const hideAfter = options.duration || 2000;
        setTimeout(() => {
            this._hideLoader();
        }, hideAfter);
    }
});

// Register the widget
publicWidget.registry.DynamicWebsiteLoader = DynamicWebsiteLoader;

// Create global API for manual usage (Odoo 18 compatible)
const OmisolLoaderAPI = {
    show: function(options = {}) {
        try {
            // Create a temporary widget instance for API calls
            const widget = new DynamicWebsiteLoader();
            widget.setElement(document.body);
            widget.showLoader(options);
        } catch (error) {
            console.error('Error showing Omisol loader:', error);
        }
    },
    hide: function() {
        try {
            // Create a temporary widget instance for API calls
            const widget = new DynamicWebsiteLoader();
            widget.setElement(document.body);
            widget.hideLoader();
        } catch (error) {
            console.error('Error hiding Omisol loader:', error);
        }
    }
};

// Export to global scope
if (typeof window !== 'undefined') {
    window.OmisolLoader = OmisolLoaderAPI;
}

export default DynamicWebsiteLoader;
