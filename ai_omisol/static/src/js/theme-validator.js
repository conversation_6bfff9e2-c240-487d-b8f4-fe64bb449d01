/**
 * OMISOL THEME - THEME VALIDATOR
 * ==============================
 * Validates that all theme components are working correctly
 */

/** @odoo-module **/

import publicWidget from "@web/legacy/js/public/public_widget";
import { _t } from "@web/core/l10n/translation";

    var ThemeValidator = publicWidget.Widget.extend({
        selector: 'body',

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            
            // Only run validation in development mode
            if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
                this._validateTheme();
            }
            
            return this._super.apply(this, arguments);
        },

        /**
         * Validate theme implementation
         * @private
         */
        _validateTheme: function () {
            console.group('🎨 OMISOL Theme Validation');
            
            this._validateFonts();
            this._validateColors();
            this._validateAnimations();
            this._validateResponsiveness();
            this._validateAccessibility();
            this._validatePerformance();
            
            console.groupEnd();
        },

        /**
         * Validate font usage
         * @private
         */
        _validateFonts: function () {
            console.group('📝 Font Validation');
            
            var fontIssues = [];
            
            // Check if Roboto is loaded
            if (!document.fonts.check('1rem Roboto')) {
                fontIssues.push('Roboto font not loaded properly');
            }
            
            // Check for inline font styles
            $('[style*="font-family"], [style*="font-size"], [style*="font-weight"]').each(function () {
                fontIssues.push('Inline font style found on: ' + this.tagName + (this.className ? '.' + this.className : ''));
            });
            
            // Check for non-Odoo font classes
            var validFontClasses = ['h1-fs', 'h2-fs', 'h3-fs', 'h4-fs', 'h5-fs', 'h6-fs', 'base-fs', 'small-fs', 'text-lg', 'text-xl'];
            $('[class*="font-"], [class*="text-"]').each(function () {
                var classes = this.className.split(' ');
                classes.forEach(function (cls) {
                    if ((cls.includes('font-') || cls.includes('text-')) && !validFontClasses.includes(cls)) {
                        // Check if it's a valid utility class
                        if (!cls.match(/^(text-(primary|secondary|muted|white|brand|accent|neutral-\d+)|fw-(light|normal|medium|semibold|bold|bolder))$/)) {
                            fontIssues.push('Non-standard font class: ' + cls);
                        }
                    }
                });
            });
            
            if (fontIssues.length === 0) {
                console.log('✅ All fonts are using Odoo variables');
            } else {
                console.warn('⚠️ Font issues found:', fontIssues);
            }
            
            console.groupEnd();
        },

        /**
         * Validate color usage
         * @private
         */
        _validateColors: function () {
            console.group('🎨 Color Validation');
            
            var colorIssues = [];
            
            // Check for inline color styles
            $('[style*="color"], [style*="background"]').each(function () {
                var style = this.getAttribute('style');
                if (style && (style.includes('color:') || style.includes('background:'))) {
                    colorIssues.push('Inline color style found on: ' + this.tagName + (this.className ? '.' + this.className : ''));
                }
            });
            
            // Check for hardcoded hex colors in classes
            var validColorClasses = [
                'text-o-color-1', 'text-o-color-2', 'text-o-color-3', 'text-o-color-4', 'text-o-color-5',
                'bg-o-color-1', 'bg-o-color-2', 'bg-o-color-3', 'bg-o-color-4', 'bg-o-color-5',
                'text-primary', 'text-secondary', 'text-accent', 'text-white', 'text-muted',
                'bg-primary', 'bg-secondary', 'bg-accent', 'bg-light', 'bg-dark'
            ];
            
            if (colorIssues.length === 0) {
                console.log('✅ All colors are using Odoo variables');
            } else {
                console.warn('⚠️ Color issues found:', colorIssues);
            }
            
            console.groupEnd();
        },

        /**
         * Validate animations and effects
         * @private
         */
        _validateAnimations: function () {
            console.group('✨ Animation Validation');
            
            var animationIssues = [];
            
            // Check if cursor follow-through is working
            if (!$('#cursorFollow').length && window.innerWidth > 768) {
                animationIssues.push('Cursor follow-through not initialized');
            }
            
            // Check if loader was shown
            if (!$('#websiteLoader').length && !window.loaderShown) {
                animationIssues.push('Website loader not shown');
            }
            
            // Check for elements with animation classes
            var animationElements = $('.animate-fade-in, .scroll-fade-in, .hover-lift, .cursor-magnetic').length;
            if (animationElements === 0) {
                animationIssues.push('No animation classes found');
            }
            
            // Check for reduced motion compliance
            if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                var hasReducedMotionStyles = $('body').hasClass('reduced-motion');
                if (!hasReducedMotionStyles) {
                    animationIssues.push('Reduced motion preference not respected');
                }
            }
            
            if (animationIssues.length === 0) {
                console.log('✅ All animations are working correctly');
            } else {
                console.warn('⚠️ Animation issues found:', animationIssues);
            }
            
            console.groupEnd();
        },

        /**
         * Validate responsiveness
         * @private
         */
        _validateResponsiveness: function () {
            console.group('📱 Responsiveness Validation');
            
            var responsiveIssues = [];
            
            // Check for viewport meta tag
            if (!$('meta[name="viewport"]').length) {
                responsiveIssues.push('Viewport meta tag missing');
            }
            
            // Check for responsive classes
            var responsiveElements = $('[class*="col-"], [class*="d-"], [class*="text-"]').length;
            if (responsiveElements === 0) {
                responsiveIssues.push('No responsive classes found');
            }
            
            // Check for fixed widths in styles
            $('[style*="width:"]').each(function () {
                var style = this.getAttribute('style');
                if (style && style.includes('width:') && style.includes('px')) {
                    responsiveIssues.push('Fixed width found on: ' + this.tagName);
                }
            });
            
            if (responsiveIssues.length === 0) {
                console.log('✅ Responsive design implemented correctly');
            } else {
                console.warn('⚠️ Responsive issues found:', responsiveIssues);
            }
            
            console.groupEnd();
        },

        /**
         * Validate accessibility
         * @private
         */
        _validateAccessibility: function () {
            console.group('♿ Accessibility Validation');
            
            var a11yIssues = [];
            
            // Check for alt attributes on images
            $('img:not([alt])').each(function () {
                a11yIssues.push('Image missing alt attribute: ' + (this.src || 'unknown'));
            });
            
            // Check for proper heading hierarchy
            var headings = $('h1, h2, h3, h4, h5, h6');
            var lastLevel = 0;
            headings.each(function () {
                var level = parseInt(this.tagName.charAt(1));
                if (level > lastLevel + 1) {
                    a11yIssues.push('Heading hierarchy skip: ' + this.tagName + ' after h' + lastLevel);
                }
                lastLevel = level;
            });
            
            // Check for focus indicators
            var focusableElements = $('a, button, input, select, textarea, [tabindex]');
            if (focusableElements.length > 0) {
                // This is a basic check - in a real scenario, you'd test actual focus behavior
                console.log('Found ' + focusableElements.length + ' focusable elements');
            }
            
            // Check for ARIA labels where needed
            $('button:not([aria-label]):not(:has(text))').each(function () {
                if (!$(this).text().trim()) {
                    a11yIssues.push('Button without accessible text');
                }
            });
            
            if (a11yIssues.length === 0) {
                console.log('✅ Basic accessibility checks passed');
            } else {
                console.warn('⚠️ Accessibility issues found:', a11yIssues);
            }
            
            console.groupEnd();
        },

        /**
         * Validate performance
         * @private
         */
        _validatePerformance: function () {
            console.group('⚡ Performance Validation');
            
            var performanceIssues = [];
            
            // Check for will-change properties
            var animatedElements = $('.hover-lift, .hover-scale, .cursor-magnetic');
            animatedElements.each(function () {
                var willChange = $(this).css('will-change');
                if (willChange === 'auto') {
                    performanceIssues.push('Element missing will-change optimization: ' + this.className);
                }
            });
            
            // Check for large images without lazy loading
            $('img').each(function () {
                if (this.naturalWidth > 1200 && !this.hasAttribute('loading')) {
                    performanceIssues.push('Large image without lazy loading: ' + this.src);
                }
            });
            
            // Check for unused CSS (basic check)
            var stylesheets = document.styleSheets.length;
            if (stylesheets > 10) {
                performanceIssues.push('Many stylesheets loaded (' + stylesheets + '), consider bundling');
            }
            
            if (performanceIssues.length === 0) {
                console.log('✅ Basic performance checks passed');
            } else {
                console.warn('⚠️ Performance issues found:', performanceIssues);
            }
            
            console.groupEnd();
        }
    });

// Register widget only in development
if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
    publicWidget.registry.ThemeValidator = ThemeValidator;
}
