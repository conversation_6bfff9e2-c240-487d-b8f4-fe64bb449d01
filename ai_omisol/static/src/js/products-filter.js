/**
 * Products Page Advanced Filtering and Search
 */

(function() {
    'use strict';

    let allProducts = [];
    let filteredProducts = [];
    let currentPage = 1;
    const itemsPerPage = 9;

    function initProductFilters() {
        const productItems = document.querySelectorAll('.product-item');
        const searchInput = document.getElementById('productSearch');
        const categoryFilter = document.getElementById('categoryFilter');
        const sortFilter = document.getElementById('sortFilter');
        const resetButton = document.getElementById('resetFilters');

        if (!productItems.length) return;

        // Store all products
        allProducts = Array.from(productItems);
        filteredProducts = [...allProducts];

        // Search functionality
        if (searchInput) {
            searchInput.addEventListener('input', debounce(handleSearch, 300));
        }

        // Category filter
        if (categoryFilter) {
            categoryFilter.addEventListener('change', handleCategoryFilter);
        }

        // Sort filter
        if (sortFilter) {
            sortFilter.addEventListener('change', handleSort);
        }

        // Reset filters button
        if (resetButton) {
            resetButton.addEventListener('click', handleResetFilters);
        }

        // Initialize pagination
        updatePagination();
        showPage(1);
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function handleSearch() {
        const searchTerm = document.getElementById('productSearch').value.toLowerCase();

        filteredProducts = allProducts.filter(item => {
            const name = item.getAttribute('data-name') || '';
            const category = item.getAttribute('data-category') || '';
            return name.includes(searchTerm) || category.toLowerCase().includes(searchTerm);
        });

        applyFilters();
    }

    function handleCategoryFilter() {
        const selectedCategory = document.getElementById('categoryFilter').value;

        if (selectedCategory === '') {
            filteredProducts = [...allProducts];
        } else {
            filteredProducts = allProducts.filter(item => {
                return item.getAttribute('data-category') === selectedCategory;
            });
        }

        // Apply search if there's a search term
        const searchTerm = document.getElementById('productSearch').value.toLowerCase();
        if (searchTerm) {
            filteredProducts = filteredProducts.filter(item => {
                const name = item.getAttribute('data-name') || '';
                const category = item.getAttribute('data-category') || '';
                return name.includes(searchTerm) || category.toLowerCase().includes(searchTerm);
            });
        }

        applyFilters();
    }

    function handleSort() {
        const sortBy = document.getElementById('sortFilter').value;

        filteredProducts.sort((a, b) => {
            switch (sortBy) {
                case 'name':
                    const nameA = a.getAttribute('data-name') || '';
                    const nameB = b.getAttribute('data-name') || '';
                    return nameA.localeCompare(nameB);

                case 'category':
                    const catA = a.getAttribute('data-category') || '';
                    const catB = b.getAttribute('data-category') || '';
                    return catA.localeCompare(catB);

                case 'featured':
                    const featA = a.getAttribute('data-featured') === 'true';
                    const featB = b.getAttribute('data-featured') === 'true';
                    return featB - featA; // Featured first

                default:
                    return 0;
            }
        });

        applyFilters();
    }

    function applyFilters() {
        // Hide all products first
        allProducts.forEach(item => {
            item.style.display = 'none';
        });

        // Reset pagination
        currentPage = 1;
        updatePagination();
        showPage(1);
    }

    function showPage(page) {
        currentPage = page;
        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;

        // Hide all products
        allProducts.forEach(item => {
            item.style.display = 'none';
        });

        // Show products for current page
        const pageProducts = filteredProducts.slice(startIndex, endIndex);
        pageProducts.forEach(item => {
            item.style.display = 'block';
            item.classList.add('show');
        });

        // Update pagination active state
        updatePaginationActive(page);
    }

    function updatePagination() {
        const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
        const paginationContainer = document.getElementById('productsPagination');

        if (!paginationContainer) return;

        let paginationHTML = '';

        if (totalPages > 1) {
            // Previous button
            paginationHTML += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `;

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" data-page="${i}">${i}</a>
                        </li>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            // Next button
            paginationHTML += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;
        }

        paginationContainer.innerHTML = paginationHTML;

        // Add click events to pagination links
        paginationContainer.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = parseInt(this.getAttribute('data-page'));
                if (page && page !== currentPage && page >= 1 && page <= totalPages) {
                    showPage(page);
                    // Scroll to top of products section
                    document.querySelector('.products-listing-section').scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    function updatePaginationActive(page) {
        const paginationContainer = document.getElementById('productsPagination');
        if (!paginationContainer) return;

        paginationContainer.querySelectorAll('.page-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeLink = paginationContainer.querySelector(`[data-page="${page}"]`);
        if (activeLink) {
            activeLink.closest('.page-item').classList.add('active');
        }
    }

    function handleResetFilters() {
        // Reset all form inputs
        const searchInput = document.getElementById('productSearch');
        const categoryFilter = document.getElementById('categoryFilter');
        const sortFilter = document.getElementById('sortFilter');

        if (searchInput) searchInput.value = '';
        if (categoryFilter) categoryFilter.value = '';
        if (sortFilter) sortFilter.value = 'name';

        // Reset filtered products to all products
        filteredProducts = [...allProducts];
        currentPage = 1;

        // Update display
        updatePagination();
        showPage(1);

        // Add visual feedback
        const resetButton = document.getElementById('resetFilters');
        if (resetButton) {
            resetButton.innerHTML = '<i class="fas fa-check"></i>';
            setTimeout(() => {
                resetButton.innerHTML = '<i class="fas fa-undo"></i>';
            }, 1000);
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initProductFilters);
    } else {
        initProductFilters();
    }

})();
