/**
 * OMISOL THEME - MODERN EFFECTS
 * ==============================
 * Modern cursor follow-through, website loader, and smooth interactions
 */

/** @odoo-module **/

import publicWidget from "@web/legacy/js/public/public_widget";
import { _t } from "@web/core/l10n/translation";

    // =============================================================================
    // WEBSITE LOADER
    // =============================================================================

    var WebsiteLoader = publicWidget.Widget.extend({
        selector: 'body',

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._initLoader();
            return this._super.apply(this, arguments);
        },

        /**
         * Initialize website loader
         * @private
         */
        _initLoader: function () {
            // Create loader HTML
            var loaderHTML = `
                <div class="website-loader" id="websiteLoader">
                    <div class="loader-content">
                        <div class="loader-logo">
                            <img src="/ai_omisol/static/src/img/omisol-logo.png" alt="Omisol" class="loader-logo" 
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block'"/>
                            <div style="display:none; color: white; font-size: 2rem; font-weight: bold;">OMISOL</div>
                        </div>
                        <div class="loader-spinner loader-industrial"></div>
                        <div class="loader-text">Loading Excellence...</div>
                        <div class="loader-progress">
                            <div class="loader-progress-bar"></div>
                        </div>
                    </div>
                </div>
            `;
            
            $('body').prepend(loaderHTML);
            
            // Simulate loading progress
            this._simulateLoading();
            
            // Hide loader when page is fully loaded
            $(window).on('load', this._hideLoader.bind(this));
            
            // Fallback: hide loader after maximum time
            setTimeout(this._hideLoader.bind(this), 5000);
        },

        /**
         * Simulate loading progress
         * @private
         */
        _simulateLoading: function () {
            var progress = 0;
            var $progressBar = $('.loader-progress-bar');
            var $loaderText = $('.loader-text');
            
            var messages = [
                'Initializing Systems...',
                'Loading Industrial Solutions...',
                'Preparing Chemical Products...',
                'Setting up Equipment...',
                'Finalizing Experience...',
                'Ready for Excellence!'
            ];
            
            var interval = setInterval(function () {
                progress += Math.random() * 15 + 5;
                
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                }
                
                $progressBar.css('width', progress + '%');
                
                // Update loading message
                var messageIndex = Math.floor((progress / 100) * (messages.length - 1));
                $loaderText.text(messages[messageIndex]);
                
            }, 200);
        },

        /**
         * Hide loader with animation
         * @private
         */
        _hideLoader: function () {
            var $loader = $('#websiteLoader');
            
            setTimeout(function () {
                $loader.addClass('loaded');
                
                setTimeout(function () {
                    $loader.remove();
                }, 500);
            }, 500);
        }
    });

    // =============================================================================
    // CURSOR FOLLOW-THROUGH
    // =============================================================================

    var CursorFollowThrough = publicWidget.Widget.extend({
        selector: 'body',

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._initCursor();
            return this._super.apply(this, arguments);
        },

        /**
         * Initialize cursor follow-through
         * @private
         */
        _initCursor: function () {
            // Skip on mobile devices
            if (window.innerWidth <= 768 || 'ontouchstart' in window) {
                return;
            }

            // Skip if user prefers reduced motion
            if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                return;
            }

            // Create cursor element
            var cursorHTML = '<div class="cursor-follow" id="cursorFollow"></div>';
            $('body').append(cursorHTML);
            
            var $cursor = $('#cursorFollow');
            var $body = $('body');
            
            // Add cursor hide class to body
            $body.addClass('cursor-hide');
            
            // Mouse move handler
            $(document).on('mousemove', function (e) {
                $cursor.css({
                    left: e.clientX - 10,
                    top: e.clientY - 10
                }).addClass('active');
            });
            
            // Mouse enter/leave handlers for interactive elements
            $(document).on('mouseenter', 'a, button, .btn, .cursor-magnetic', function () {
                $cursor.addClass('hover');
                
                // Get element colors for mixing
                var $element = $(this);
                var bgColor = $element.css('background-color');
                var color = $element.css('color');
                
                if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                    $cursor.css('background', bgColor);
                } else if (color) {
                    $cursor.css('background', color);
                }
            });
            
            $(document).on('mouseleave', 'a, button, .btn, .cursor-magnetic', function () {
                $cursor.removeClass('hover').css('background', '');
            });
            
            // Click handler
            $(document).on('mousedown', function () {
                $cursor.addClass('click');
            });
            
            $(document).on('mouseup', function () {
                $cursor.removeClass('click');
            });
            
            // Hide cursor when leaving window
            $(document).on('mouseleave', function () {
                $cursor.removeClass('active');
            });
            
            $(document).on('mouseenter', function () {
                $cursor.addClass('active');
            });
        }
    });

    // =============================================================================
    // MAGNETIC ELEMENTS
    // =============================================================================

    var MagneticElements = publicWidget.Widget.extend({
        selector: '.cursor-magnetic',
        events: {
            'mousemove': '_onMouseMove',
            'mouseleave': '_onMouseLeave',
        },

        /**
         * Handle magnetic mouse move
         * @private
         * @param {Event} ev
         */
        _onMouseMove: function (ev) {
            if (window.innerWidth <= 768) return;
            
            var $element = $(ev.currentTarget);
            var rect = ev.currentTarget.getBoundingClientRect();
            var x = ev.clientX - rect.left - rect.width / 2;
            var y = ev.clientY - rect.top - rect.height / 2;
            
            var strength = $element.data('magnetic-strength') || 0.2;
            var maxDistance = $element.data('magnetic-distance') || 30;
            
            // Limit magnetic effect
            x = Math.max(-maxDistance, Math.min(maxDistance, x * strength));
            y = Math.max(-maxDistance, Math.min(maxDistance, y * strength));
            
            $element.css('transform', 'translate(' + x + 'px, ' + y + 'px)');
        },

        /**
         * Reset magnetic position
         * @private
         */
        _onMouseLeave: function (ev) {
            $(ev.currentTarget).css('transform', 'translate(0px, 0px)');
        }
    });

    // =============================================================================
    // SMOOTH SCROLL ENHANCEMENTS
    // =============================================================================

    var SmoothScrollEnhancements = publicWidget.Widget.extend({
        selector: 'a[href^="#"]',
        events: {
            'click': '_onSmoothScroll',
        },

        /**
         * Enhanced smooth scroll
         * @private
         * @param {Event} ev
         */
        _onSmoothScroll: function (ev) {
            var href = $(ev.currentTarget).attr('href');
            var $target = $(href);
            
            if ($target.length) {
                ev.preventDefault();
                
                var offset = $target.data('scroll-offset') || 80;
                var duration = $target.data('scroll-duration') || 1000;
                
                // Custom easing function
                $.easing.easeInOutCubic = function (x, t, b, c, d) {
                    if ((t /= d / 2) < 1) return c / 2 * t * t * t + b;
                    return c / 2 * ((t -= 2) * t * t + 2) + b;
                };
                
                $('html, body').animate({
                    scrollTop: $target.offset().top - offset
                }, duration, 'easeInOutCubic');
            }
        }
    });

    // =============================================================================
    // MODERN LINK EFFECTS
    // =============================================================================

    var ModernLinkEffects = publicWidget.Widget.extend({
        selector: 'body',

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._initModernLinks();
            return this._super.apply(this, arguments);
        },

        /**
         * Initialize modern link effects
         * @private
         */
        _initModernLinks: function () {
            // Add modern-link class to navigation and footer links
            $('.omisol-nav-link, .omisol-footer a').addClass('modern-link');
            
            // Add ripple effect to buttons
            $(document).on('click', '.btn', this._createRipple.bind(this));
        },

        /**
         * Create ripple effect on button click
         * @private
         * @param {Event} ev
         */
        _createRipple: function (ev) {
            var $button = $(ev.currentTarget);
            var rect = ev.currentTarget.getBoundingClientRect();
            var size = Math.max(rect.width, rect.height);
            var x = ev.clientX - rect.left - size / 2;
            var y = ev.clientY - rect.top - size / 2;
            
            var $ripple = $('<span class="ripple"></span>').css({
                width: size,
                height: size,
                left: x,
                top: y
            });
            
            $button.append($ripple);
            
            setTimeout(function () {
                $ripple.remove();
            }, 600);
        }
    });

    // =============================================================================
    // PERFORMANCE OPTIMIZATIONS
    // =============================================================================

    var PerformanceOptimizer = publicWidget.Widget.extend({
        selector: 'body',

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._optimizePerformance();
            return this._super.apply(this, arguments);
        },

        /**
         * Optimize performance for animations
         * @private
         */
        _optimizePerformance: function () {
            // Add will-change property to animated elements
            $('.hover-lift, .hover-scale, .hover-tilt, .cursor-magnetic').css('will-change', 'transform');
            
            // Remove will-change after animation
            $(document).on('transitionend', '.hover-lift, .hover-scale, .hover-tilt', function () {
                $(this).css('will-change', 'auto');
            });
            
            // Throttle scroll events
            var scrollTimeout;
            $(window).on('scroll', function () {
                if (scrollTimeout) {
                    clearTimeout(scrollTimeout);
                }
                
                scrollTimeout = setTimeout(function () {
                    // Trigger custom scroll event for other widgets
                    $(document).trigger('optimizedScroll');
                }, 16); // ~60fps
            });
            
            // Debounce resize events
            var resizeTimeout;
            $(window).on('resize', function () {
                if (resizeTimeout) {
                    clearTimeout(resizeTimeout);
                }
                
                resizeTimeout = setTimeout(function () {
                    $(document).trigger('optimizedResize');
                }, 250);
            });
        }
    });

// Register widgets
publicWidget.registry.WebsiteLoader = WebsiteLoader;
publicWidget.registry.CursorFollowThrough = CursorFollowThrough;
publicWidget.registry.MagneticElements = MagneticElements;
publicWidget.registry.SmoothScrollEnhancements = SmoothScrollEnhancements;
publicWidget.registry.ModernLinkEffects = ModernLinkEffects;
publicWidget.registry.PerformanceOptimizer = PerformanceOptimizer;
