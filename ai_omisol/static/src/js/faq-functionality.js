/** @odoo-module **/

/**
 * OMISOL FAQ FUNCTIONALITY
 * ========================
 * Interactive FAQ page functionality
 */

import publicWidget from "@web/legacy/js/public/public_widget";
import { _t } from "@web/core/l10n/translation";
import { rpc } from "@web/core/network/rpc";

var FAQWidget = publicWidget.Widget.extend({
    selector: '.faq-content',
    events: {
        'click .accordion-button': '_onAccordionClick',
        'input #faqSearch': '_onSearchInput',
        'click .faq-categories-sidebar a': '_onCategoryClick',
    },

    /**
     * @override
     */
    start: function () {
        this._super.apply(this, arguments);
        this._initFAQFunctionality();
        return this._super.apply(this, arguments);
    },

    /**
     * Initialize FAQ functionality
     * @private
     */
    _initFAQFunctionality: function () {
        // Initialize search
        this._initSearch();
        
        // Initialize smooth scrolling for categories
        this._initSmoothScrolling();
        
        // Initialize accordion tracking
        this._initAccordionTracking();
        
        // Initialize helpful/not helpful buttons
        this._initFeedbackButtons();
    },

    /**
     * Initialize search functionality
     * @private
     */
    _initSearch: function () {
        var self = this;
        var $searchInput = $('#faqSearch');
        var searchTimeout;
        
        $searchInput.on('input', function () {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function () {
                self._performSearch($searchInput.val());
            }, 300);
        });
        
        // Search on enter
        $searchInput.on('keypress', function (e) {
            if (e.which === 13) {
                self._performSearch($searchInput.val());
            }
        });
    },

    /**
     * Perform FAQ search
     * @private
     * @param {string} query
     */
    _performSearch: function (query) {
        var $faqItems = $('.accordion-item');
        var $categories = $('.faq-category-section');
        
        if (!query.trim()) {
            // Show all items
            $faqItems.show();
            $categories.show();
            return;
        }
        
        query = query.toLowerCase();
        var hasVisibleItems = false;
        
        $categories.each(function () {
            var $category = $(this);
            var $categoryItems = $category.find('.accordion-item');
            var categoryHasVisible = false;
            
            $categoryItems.each(function () {
                var $item = $(this);
                var question = $item.find('.accordion-button').text().toLowerCase();
                var answer = $item.find('.accordion-body').text().toLowerCase();
                
                if (question.includes(query) || answer.includes(query)) {
                    $item.show();
                    categoryHasVisible = true;
                    hasVisibleItems = true;
                } else {
                    $item.hide();
                }
            });
            
            if (categoryHasVisible) {
                $category.show();
            } else {
                $category.hide();
            }
        });
        
        // Show no results message if needed
        this._toggleNoResults(!hasVisibleItems);
    },

    /**
     * Toggle no results message
     * @private
     * @param {boolean} show
     */
    _toggleNoResults: function (show) {
        var $noResults = $('.faq-no-results');
        
        if (show && $noResults.length === 0) {
            var noResultsHtml = `
                <div class="faq-no-results text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No FAQs found</h4>
                    <p class="text-secondary">Try adjusting your search terms or browse by category.</p>
                </div>
            `;
            $('.faq-content').append(noResultsHtml);
        } else if (!show) {
            $noResults.remove();
        }
    },

    /**
     * Initialize smooth scrolling for category links
     * @private
     */
    _initSmoothScrolling: function () {
        $('.faq-categories-sidebar a[href^="#"]').on('click', function (e) {
            e.preventDefault();
            var target = $(this.getAttribute('href'));
            
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 800, 'easeInOutCubic');
                
                // Update active category
                $('.faq-categories-sidebar a').removeClass('active');
                $(this).addClass('active');
            }
        });
    },

    /**
     * Initialize accordion click tracking
     * @private
     */
    _initAccordionTracking: function () {
        // Track when FAQs are opened
        $(document).on('shown.bs.collapse', '.accordion-collapse', function () {
            var $accordion = $(this);
            var faqId = $accordion.attr('id').replace('collapse-', '');
            
            // Track view
            rpc('/faq/mark_helpful', {
                faq_id: parseInt(faqId)
            }).catch(function () {
                // Ignore errors for tracking
            });
        });
    },

    /**
     * Initialize feedback buttons
     * @private
     */
    _initFeedbackButtons: function () {
        // Add global functions for helpful/not helpful
        window.markHelpful = this._markHelpful.bind(this);
        window.markNotHelpful = this._markNotHelpful.bind(this);
    },

    /**
     * Mark FAQ as helpful
     * @private
     * @param {number} faqId
     */
    _markHelpful: function (faqId) {
        var self = this;
        
        rpc('/faq/mark_helpful', {
            faq_id: faqId
        }).then(function (result) {
            if (result.success) {
                // Update count
                var $button = $(`button[onclick="markHelpful(${faqId})"]`);
                $button.html(`<i class="fas fa-thumbs-up me-1"></i>Yes (${result.helpful_count})`);
                
                // Show thank you message
                self._showFeedbackMessage('Thank you for your feedback!', 'success');
                
                // Disable buttons
                $button.prop('disabled', true);
                $(`button[onclick="markNotHelpful(${faqId})"]`).prop('disabled', true);
            }
        }).catch(function () {
            self._showFeedbackMessage('Failed to submit feedback. Please try again.', 'error');
        });
    },

    /**
     * Mark FAQ as not helpful
     * @private
     * @param {number} faqId
     */
    _markNotHelpful: function (faqId) {
        var self = this;
        
        rpc('/faq/mark_not_helpful', {
            faq_id: faqId
        }).then(function (result) {
            if (result.success) {
                // Update count
                var $button = $(`button[onclick="markNotHelpful(${faqId})"]`);
                $button.html(`<i class="fas fa-thumbs-down me-1"></i>No (${result.not_helpful_count})`);
                
                // Show thank you message
                self._showFeedbackMessage('Thank you for your feedback! We\'ll work on improving this answer.', 'warning');
                
                // Disable buttons
                $button.prop('disabled', true);
                $(`button[onclick="markHelpful(${faqId})"]`).prop('disabled', true);
            }
        }).catch(function () {
            self._showFeedbackMessage('Failed to submit feedback. Please try again.', 'error');
        });
    },

    /**
     * Show feedback message
     * @private
     * @param {string} message
     * @param {string} type
     */
    _showFeedbackMessage: function (message, type) {
        var alertClass = type === 'success' ? 'alert-success' : 
                        type === 'warning' ? 'alert-warning' : 'alert-danger';
        
        var $alert = $(`
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'times-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        $('body').append($alert);
        
        // Auto remove after 5 seconds
        setTimeout(function () {
            $alert.alert('close');
        }, 5000);
    },

    /**
     * Handle accordion click
     * @private
     * @param {Event} ev
     */
    _onAccordionClick: function (ev) {
        // Add smooth animation
        var $button = $(ev.currentTarget);
        var $icon = $button.find('i');
        
        setTimeout(function () {
            if ($button.hasClass('collapsed')) {
                $icon.removeClass('fa-minus').addClass('fa-question-circle');
            } else {
                $icon.removeClass('fa-question-circle').addClass('fa-minus');
            }
        }, 150);
    },

    /**
     * Handle search input
     * @private
     * @param {Event} ev
     */
    _onSearchInput: function (ev) {
        // This is handled by the init function
    },

    /**
     * Handle category click
     * @private
     * @param {Event} ev
     */
    _onCategoryClick: function (ev) {
        // This is handled by the smooth scrolling init
    }
});

// Register widget
publicWidget.registry.FAQWidget = FAQWidget;
