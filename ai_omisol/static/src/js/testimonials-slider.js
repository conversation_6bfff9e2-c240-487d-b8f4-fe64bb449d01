/**
 * Testimonials Carousel with Auto-scroll
 * 3 cards in row, single card scrolling
 */

(function() {
    'use strict';

    let currentIndex = 0;
    let track;
    let items;
    let autoSlideInterval;
    const autoSlideDelay = 4000; // 4 seconds
    let itemsToShow = 3; // Default for desktop
    let totalItems = 0;

    function initTestimonialsCarousel() {
        track = document.querySelector('.testimonials-track');
        items = document.querySelectorAll('.testimonial-item');

        if (!track || items.length === 0) return;

        totalItems = items.length;

        // Determine items to show based on screen size
        updateItemsToShow();

        // Start auto-slide
        startAutoSlide();

        // Pause auto-slide on hover
        const carouselContainer = document.querySelector('.testimonials-carousel-container');
        if (carouselContainer) {
            carouselContainer.addEventListener('mouseenter', stopAutoSlide);
            carouselContainer.addEventListener('mouseleave', startAutoSlide);
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            updateItemsToShow();
            updateCarousel();
        });
    }

    function updateItemsToShow() {
        if (window.innerWidth <= 768) {
            itemsToShow = 1; // Mobile: 1 card
        } else {
            itemsToShow = 3; // Desktop: 3 cards
        }
    }

    function updateCarousel() {
        if (!track) return;

        const translateX = -(currentIndex * (100 / itemsToShow));
        track.style.transform = `translateX(${translateX}%)`;
    }

    function nextSlide() {
        const maxIndex = totalItems - itemsToShow;
        if (currentIndex >= maxIndex) {
            currentIndex = 0; // Loop back to start
        } else {
            currentIndex++;
        }
        updateCarousel();
    }

    function prevSlide() {
        if (currentIndex <= 0) {
            currentIndex = totalItems - itemsToShow; // Loop to end
        } else {
            currentIndex--;
        }
        updateCarousel();
    }

    function startAutoSlide() {
        stopAutoSlide(); // Clear any existing interval
        autoSlideInterval = setInterval(nextSlide, autoSlideDelay);
    }

    function stopAutoSlide() {
        if (autoSlideInterval) {
            clearInterval(autoSlideInterval);
            autoSlideInterval = null;
        }
    }

    // Global function for button clicks
    window.slideTestimonials = function(direction) {
        stopAutoSlide();
        if (direction === 1) {
            nextSlide();
        } else {
            prevSlide();
        }
        // Restart auto-slide after manual interaction
        setTimeout(startAutoSlide, 3000);
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTestimonialsCarousel);
    } else {
        initTestimonialsCarousel();
    }

    // Re-initialize on page navigation (for SPA-like behavior)
    document.addEventListener('page:loaded', initTestimonialsCarousel);

    // About Us page testimonials
    let aboutCurrentIndex = 0;
    let aboutTrack;
    let aboutItems;
    let aboutAutoSlideInterval;

    function initAboutTestimonialsCarousel() {
        aboutTrack = document.querySelector('#aboutTestimonialsTrack');
        aboutItems = document.querySelectorAll('#aboutTestimonialsTrack .testimonial-item');

        if (!aboutTrack || aboutItems.length === 0) return;

        // Start auto-slide for about page
        startAboutAutoSlide();

        // Pause auto-slide on hover
        const aboutCarouselContainer = aboutTrack.closest('.testimonials-carousel-container');
        if (aboutCarouselContainer) {
            aboutCarouselContainer.addEventListener('mouseenter', stopAboutAutoSlide);
            aboutCarouselContainer.addEventListener('mouseleave', startAboutAutoSlide);
        }
    }

    function updateAboutCarousel() {
        if (!aboutTrack) return;

        const itemsToShow = window.innerWidth <= 768 ? 1 : 3;
        const translateX = -(aboutCurrentIndex * (100 / itemsToShow));
        aboutTrack.style.transform = `translateX(${translateX}%)`;
    }

    function nextAboutSlide() {
        const itemsToShow = window.innerWidth <= 768 ? 1 : 3;
        const maxIndex = aboutItems.length - itemsToShow;
        if (aboutCurrentIndex >= maxIndex) {
            aboutCurrentIndex = 0;
        } else {
            aboutCurrentIndex++;
        }
        updateAboutCarousel();
    }

    function prevAboutSlide() {
        const itemsToShow = window.innerWidth <= 768 ? 1 : 3;
        if (aboutCurrentIndex <= 0) {
            aboutCurrentIndex = aboutItems.length - itemsToShow;
        } else {
            aboutCurrentIndex--;
        }
        updateAboutCarousel();
    }

    function startAboutAutoSlide() {
        stopAboutAutoSlide();
        aboutAutoSlideInterval = setInterval(nextAboutSlide, autoSlideDelay);
    }

    function stopAboutAutoSlide() {
        if (aboutAutoSlideInterval) {
            clearInterval(aboutAutoSlideInterval);
            aboutAutoSlideInterval = null;
        }
    }

    // Global function for about page testimonials
    window.slideAboutTestimonials = function(direction) {
        stopAboutAutoSlide();
        if (direction === 1) {
            nextAboutSlide();
        } else {
            prevAboutSlide();
        }
        setTimeout(startAboutAutoSlide, 3000);
    };

    // Initialize about testimonials
    initAboutTestimonialsCarousel();

    // Counter Animation for About Page
    function animateCounters() {
        const counters = document.querySelectorAll('.stat-counter');

        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            const numberElement = counter.querySelector('.about-stat-number');
            const isPercentage = numberElement.textContent.includes('%');
            const hasPlus = numberElement.textContent.includes('+');

            let current = 0;
            const increment = target / 50; // 50 steps

            const updateCounter = () => {
                if (current < target) {
                    current += increment;
                    if (current > target) current = target;

                    let displayValue = Math.floor(current);
                    if (isPercentage) {
                        numberElement.textContent = displayValue + '%';
                    } else if (hasPlus) {
                        numberElement.textContent = displayValue + '+';
                    } else {
                        numberElement.textContent = displayValue;
                    }

                    requestAnimationFrame(updateCounter);
                }
            };

            // Start animation when element is in view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateCounter();
                        observer.unobserve(entry.target);
                    }
                });
            });

            observer.observe(counter);
        });
    }

    // Initialize counter animation
    animateCounters();

})();
