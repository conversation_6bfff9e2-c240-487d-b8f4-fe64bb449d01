/* Product Gallery and Image Zoom Functionality */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize image zoom functionality - DISABLED
    function initImageZoom() {
        // Image zoom functionality disabled - images will auto-fit instead
        // Remove cursor pointer from images to indicate they're not clickable
        const productImages = document.querySelectorAll('.product-main-image, .omisol-product-image');
        productImages.forEach(img => {
            img.style.cursor = 'default';
        });
    }

    // Thumbnail gallery functionality
    function initThumbnailGallery() {
        const thumbnails = document.querySelectorAll('.product-thumbnail');
        const mainImage = document.querySelector('.product-main-image');

        if (thumbnails.length > 0 && mainImage) {
            thumbnails.forEach(thumb => {
                thumb.addEventListener('click', function() {
                    // Remove active class from all thumbnails
                    thumbnails.forEach(t => t.classList.remove('active'));
                    
                    // Add active class to clicked thumbnail
                    this.classList.add('active');
                    
                    // Update main image
                    mainImage.src = this.src.replace('/image_small/', '/image/');
                    mainImage.alt = this.alt;
                });
            });

            // Set first thumbnail as active
            if (thumbnails[0]) {
                thumbnails[0].classList.add('active');
            }
        }
    }

    // Product card hover effects
    function initProductCardEffects() {
        const productCards = document.querySelectorAll('.omisol-product-card');
        
        productCards.forEach(card => {
            const image = card.querySelector('.omisol-product-image');
            
            if (image) {
                card.addEventListener('mouseenter', function() {
                    image.style.transform = 'scale(1.05)';
                });
                
                card.addEventListener('mouseleave', function() {
                    image.style.transform = 'scale(1)';
                });
            }
        });
    }

    // Initialize all functionality
    initImageZoom();
    initThumbnailGallery();
    initProductCardEffects();

    // Lazy loading for images
    function initLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }

    // Initialize lazy loading if supported
    if ('IntersectionObserver' in window) {
        initLazyLoading();
    }
});

// Product category extraction utility
function extractProductNameAndCategory(fullName) {
    if (fullName && fullName.includes('|')) {
        const parts = fullName.split('|');
        return {
            name: parts[0].trim(),
            category: parts[1].trim()
        };
    }
    return {
        name: fullName || '',
        category: ''
    };
}

// Export for use in other scripts
window.ProductGallery = {
    extractProductNameAndCategory: extractProductNameAndCategory
};
