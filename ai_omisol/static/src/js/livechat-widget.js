/** @odoo-module **/

/**
 * OMISOL AUTHENTICATED LIVE CHAT
 * ==============================
 * Live chat widget that only works for authenticated users
 */

import publicWidget from "@web/legacy/js/public/public_widget";
import { _t } from "@web/core/l10n/translation";
import { rpc } from "@web/core/network/rpc";

var AuthenticatedLiveChat = publicWidget.Widget.extend({
    selector: 'body',
    events: {
        'click .livechat-toggle': '_onToggleChat',
        'click .livechat-close': '_onCloseChat',
        'click .livechat-minimize': '_onMinimizeChat',
        'keypress .livechat-input': '_onInputKeypress',
        'click .livechat-send': '_onSendMessage',
    },

    /**
     * @override
     */
    start: function () {
        this._super.apply(this, arguments);
        this._initLiveChat();
        return this._super.apply(this, arguments);
    },

    /**
     * Initialize live chat widget
     * @private
     */
    _initLiveChat: function () {
        // Check if user is logged in
        this._checkUserAuthentication().then((isAuthenticated) => {
            if (isAuthenticated) {
                this._createChatWidget();
                this._loadChatSession();
            }
        });
    },

    /**
     * Check if user is authenticated
     * @private
     * @returns {Promise<boolean>}
     */
    _checkUserAuthentication: function () {
        return rpc('/web/session/get_session_info').then((sessionInfo) => {
            return sessionInfo.uid && sessionInfo.uid !== false;
        }).catch(() => {
            return false;
        });
    },

    /**
     * Create chat widget HTML
     * @private
     */
    _createChatWidget: function () {
        var chatWidget = `
            <div class="omisol-livechat-widget" id="omisolLiveChat" style="display: none;">
                <!-- Chat Toggle Button -->
                <div class="livechat-toggle cursor-magnetic" data-magnetic-strength="0.4" title="Chat with us">
                    <i class="fas fa-comments"></i>
                    <span class="livechat-badge" style="display: none;">1</span>
                </div>
                
                <!-- Chat Window -->
                <div class="livechat-window" style="display: none;">
                    <div class="livechat-header">
                        <div class="livechat-header-info">
                            <div class="livechat-avatar">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <div class="livechat-title">
                                <h4>Omisol Support</h4>
                                <span class="livechat-status">Online</span>
                            </div>
                        </div>
                        <div class="livechat-controls">
                            <button class="livechat-minimize" title="Minimize">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button class="livechat-close" title="Close">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="livechat-messages" id="livechatMessages">
                        <div class="livechat-welcome">
                            <div class="welcome-message">
                                <i class="fas fa-robot"></i>
                                <p>Hello! How can we help you today?</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="livechat-input-area">
                        <div class="livechat-typing" style="display: none;">
                            <span>Support is typing...</span>
                        </div>
                        <div class="livechat-input-container">
                            <input type="text" class="livechat-input" placeholder="Type your message..." maxlength="500">
                            <button class="livechat-send" title="Send">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(chatWidget);
        this._addChatStyles();
        
        // Show the widget
        $('#omisolLiveChat').fadeIn(300);
    },

    /**
     * Add chat widget styles
     * @private
     */
    _addChatStyles: function () {
        if ($('#omisol-livechat-styles').length) return;
        
        var styles = `
            <style id="omisol-livechat-styles">
                .omisol-livechat-widget {
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    z-index: 9998;
                    font-family: 'Roboto', Arial, sans-serif;
                }
                
                .livechat-toggle {
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 24px;
                    cursor: pointer;
                    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
                    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                    position: relative;
                    animation: chatPulse 2s infinite;
                }
                
                .livechat-toggle:hover {
                    transform: scale(1.1);
                    box-shadow: 0 12px 35px rgba(0, 123, 255, 0.6);
                    animation-play-state: paused;
                }
                
                .livechat-badge {
                    position: absolute;
                    top: -5px;
                    right: -5px;
                    background: #dc3545;
                    color: white;
                    border-radius: 50%;
                    width: 20px;
                    height: 20px;
                    font-size: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: bold;
                }
                
                .livechat-window {
                    position: absolute;
                    bottom: 80px;
                    right: 0;
                    width: 350px;
                    height: 450px;
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    border: 1px solid #e2e8f0;
                }
                
                .livechat-header {
                    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                    color: white;
                    padding: 15px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .livechat-header-info {
                    display: flex;
                    align-items: center;
                }
                
                .livechat-avatar {
                    font-size: 24px;
                    margin-right: 10px;
                }
                
                .livechat-title h4 {
                    margin: 0;
                    font-size: 16px;
                    font-weight: 600;
                }
                
                .livechat-status {
                    font-size: 12px;
                    opacity: 0.9;
                }
                
                .livechat-controls {
                    display: flex;
                    gap: 5px;
                }
                
                .livechat-controls button {
                    background: none;
                    border: none;
                    color: white;
                    cursor: pointer;
                    padding: 5px;
                    border-radius: 3px;
                    transition: background 0.2s;
                }
                
                .livechat-controls button:hover {
                    background: rgba(255, 255, 255, 0.2);
                }
                
                .livechat-messages {
                    flex: 1;
                    padding: 15px;
                    overflow-y: auto;
                    background: #f8fafc;
                }
                
                .livechat-welcome {
                    text-align: center;
                    margin-bottom: 20px;
                }
                
                .welcome-message {
                    background: white;
                    padding: 15px;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }
                
                .welcome-message i {
                    font-size: 24px;
                    color: #007bff;
                    margin-bottom: 10px;
                }
                
                .welcome-message p {
                    margin: 0;
                    color: #64748b;
                }
                
                .chat-message {
                    margin-bottom: 15px;
                    display: flex;
                    align-items: flex-end;
                }
                
                .chat-message.sent {
                    justify-content: flex-end;
                }
                
                .message-bubble {
                    max-width: 80%;
                    padding: 10px 15px;
                    border-radius: 18px;
                    word-wrap: break-word;
                }
                
                .chat-message.received .message-bubble {
                    background: white;
                    color: #1e293b;
                    border-bottom-left-radius: 4px;
                    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                }
                
                .chat-message.sent .message-bubble {
                    background: #007bff;
                    color: white;
                    border-bottom-right-radius: 4px;
                }
                
                .message-time {
                    font-size: 11px;
                    opacity: 0.7;
                    margin-top: 5px;
                }
                
                .livechat-typing {
                    padding: 10px 15px;
                    font-style: italic;
                    color: #64748b;
                    font-size: 14px;
                }
                
                .livechat-input-area {
                    border-top: 1px solid #e2e8f0;
                    background: white;
                }
                
                .livechat-input-container {
                    display: flex;
                    padding: 15px;
                    gap: 10px;
                }
                
                .livechat-input {
                    flex: 1;
                    border: 1px solid #e2e8f0;
                    border-radius: 20px;
                    padding: 10px 15px;
                    font-size: 14px;
                    outline: none;
                    transition: border-color 0.2s;
                }
                
                .livechat-input:focus {
                    border-color: #007bff;
                }
                
                .livechat-send {
                    background: #007bff;
                    color: white;
                    border: none;
                    border-radius: 50%;
                    width: 40px;
                    height: 40px;
                    cursor: pointer;
                    transition: all 0.2s;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .livechat-send:hover {
                    background: #0056b3;
                    transform: scale(1.05);
                }
                
                .livechat-send:disabled {
                    background: #94a3b8;
                    cursor: not-allowed;
                    transform: none;
                }
                
                @keyframes chatPulse {
                    0%, 100% {
                        transform: scale(1);
                        box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
                    }
                    50% {
                        transform: scale(1.05);
                        box-shadow: 0 12px 35px rgba(0, 123, 255, 0.6);
                    }
                }
                
                @media (max-width: 768px) {
                    .omisol-livechat-widget {
                        bottom: 10px;
                        right: 10px;
                    }
                    
                    .livechat-window {
                        width: calc(100vw - 20px);
                        height: calc(100vh - 100px);
                        bottom: 70px;
                        right: -10px;
                    }
                    
                    .livechat-toggle {
                        width: 50px;
                        height: 50px;
                        font-size: 20px;
                    }
                }
            </style>
        `;
        
        $('head').append(styles);
    },

    /**
     * Load existing chat session
     * @private
     */
    _loadChatSession: function () {
        rpc('/omisol/livechat/get_session').then((session) => {
            if (session) {
                this.chatSession = session;
                this._loadChatHistory();
            }
        }).catch(() => {
            // No existing session
        });
    },

    /**
     * Toggle chat window
     * @private
     */
    _onToggleChat: function (ev) {
        ev.preventDefault();
        var $window = $('.livechat-window');
        
        if ($window.is(':visible')) {
            $window.slideUp(300);
        } else {
            $window.slideDown(300);
            $('.livechat-input').focus();
            
            // Create session if not exists
            if (!this.chatSession) {
                this._createChatSession();
            }
        }
    },

    /**
     * Close chat window
     * @private
     */
    _onCloseChat: function (ev) {
        ev.preventDefault();
        $('.livechat-window').slideUp(300);
    },

    /**
     * Minimize chat window
     * @private
     */
    _onMinimizeChat: function (ev) {
        ev.preventDefault();
        $('.livechat-window').slideUp(300);
    },

    /**
     * Handle input keypress
     * @private
     */
    _onInputKeypress: function (ev) {
        if (ev.which === 13) { // Enter key
            this._sendMessage();
        }
    },

    /**
     * Handle send button click
     * @private
     */
    _onSendMessage: function (ev) {
        ev.preventDefault();
        this._sendMessage();
    },

    /**
     * Send message
     * @private
     */
    _sendMessage: function () {
        var $input = $('.livechat-input');
        var message = $input.val().trim();
        
        if (!message) return;
        
        // Add message to chat
        this._addMessage(message, 'sent');
        $input.val('');
        
        // Send to server
        if (this.chatSession) {
            rpc('/omisol/livechat/send_message', {
                session_id: this.chatSession.id,
                message: message
            }).catch(() => {
                this._addMessage('Failed to send message. Please try again.', 'error');
            });
        }
    },

    /**
     * Create new chat session
     * @private
     */
    _createChatSession: function () {
        rpc('/omisol/livechat/create_session').then((session) => {
            this.chatSession = session;
            this._addMessage('Connected to support. Please wait for an operator to join.', 'system');
        }).catch(() => {
            this._addMessage('Failed to connect to support. Please try again later.', 'error');
        });
    },

    /**
     * Add message to chat
     * @private
     */
    _addMessage: function (message, type) {
        var $messages = $('#livechatMessages');
        var timestamp = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        
        var messageClass = type === 'sent' ? 'sent' : 'received';
        var messageHtml = `
            <div class="chat-message ${messageClass}">
                <div class="message-bubble">
                    ${message}
                    <div class="message-time">${timestamp}</div>
                </div>
            </div>
        `;
        
        $messages.append(messageHtml);
        $messages.scrollTop($messages[0].scrollHeight);
    },

    /**
     * Load chat history
     * @private
     */
    _loadChatHistory: function () {
        // Implementation for loading chat history
        // This would fetch messages from the server
    }
});

// Register widget
publicWidget.registry.AuthenticatedLiveChat = AuthenticatedLiveChat;
