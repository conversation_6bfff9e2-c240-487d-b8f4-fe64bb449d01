/* Website Loader Styles */

.omisol-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.omisol-loader.fade-out {
    opacity: 0;
    visibility: hidden;
}

.loader-content {
    text-align: center;
    color: #2c3e50;
}

.loader-logo {
    width: 120px;
    height: auto;
    margin-bottom: 30px;
    animation: logoFloat 2s ease-in-out infinite;
}

.loader-text {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #2f496e;
    opacity: 0.9;
}

.loader-subtitle {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 40px;
}

/* Loading Animation */
.loading-animation {
    position: relative;
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
}

.loading-bar {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.8) 50%, 
        transparent 100%);
    animation: loadingSlide 2s ease-in-out infinite;
}

/* Floating dots animation */
.loading-dots {
    display: flex;
    gap: 8px;
    margin-top: 20px;
    justify-content: center;
}

.loading-dot {
    width: 8px;
    height: 8px;
    background: #2f496e;
    border-radius: 50%;
    animation: dotBounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

/* Keyframe Animations */
@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes loadingSlide {
    0% {
        left: -100%;
    }
    50% {
        left: 0%;
    }
    100% {
        left: 100%;
    }
}

@keyframes dotBounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .loader-logo {
        width: 100px;
        margin-bottom: 20px;
    }
    
    .loader-text {
        font-size: 1.3rem;
    }
    
    .loader-subtitle {
        font-size: 0.9rem;
        margin-bottom: 30px;
    }
    
    .loading-animation {
        width: 150px;
    }
}

/* Additional industrial-themed elements */
.loader-background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2px, transparent 2px);
    background-size: 50px 50px;
    animation: patternMove 20s linear infinite;
}

@keyframes patternMove {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(50px, 50px);
    }
}

/* Progress percentage */
.loader-progress {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 15px;
    font-weight: 500;
}

/* Tank monitoring themed loader */
.tank-container {
    position: relative;
    margin: 30px auto;
    width: 80px;
    height: 120px;
}

.tank-body {
    width: 60px;
    height: 100px;
    border: 3px solid #2f496e;
    border-radius: 8px 8px 20px 20px;
    position: relative;
    margin: 0 auto;
    overflow: hidden;
    background: transparent;
    box-shadow: inset 0 0 10px rgba(47, 73, 110, 0.2);
}

.tank-liquid {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(0deg, #2f496e 0%, #1e40af 100%);
    height: 0%;
    animation: tankFill 3s ease-in-out infinite;
    border-radius: 0 0 17px 17px;
    opacity: 0.8;
}

.tank-gauge-line {
    position: absolute;
    right: -8px;
    width: 15px;
    height: 2px;
    background: #2f496e;
    opacity: 0.7;
}

.tank-gauge-line:nth-child(2) { top: 25%; }
.tank-gauge-line:nth-child(3) { top: 50%; }
.tank-gauge-line:nth-child(4) { top: 75%; }

.tank-pipes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.pipe-inlet {
    position: absolute;
    top: 20px;
    left: -5px;
    width: 10px;
    height: 3px;
    background: #2f496e;
    border-radius: 2px;
}

.pipe-outlet {
    position: absolute;
    bottom: 30px;
    right: -5px;
    width: 10px;
    height: 3px;
    background: #2f496e;
    border-radius: 2px;
}

@keyframes tankFill {
    0%, 100% {
        height: 15%;
    }
    50% {
        height: 85%;
    }
}
