/* Omisol Website Styles - Professional Industrial Theme */

/* ===== CUSTOM CURSOR EFFECTS - DISABLED ===== */
/* Custom cursor implementation disabled for better user experience */
body {
    cursor: auto; /* Keep default cursor */
    position: relative;
}

/* Custom cursor completely disabled */

/* Button hover effects with ripple */
.btn, button {
    position: relative;
    overflow: hidden;
}

/* Ripple effect for buttons with background */
.btn:hover::after,
button:hover::after {
    content: '';
    position: absolute;
    width: 100px;
    height: 100px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    pointer-events: none;
    animation: button-ripple 0.6s ease-out forwards;
    top: 50%;
    left: 50%;
}

/* Cursor hover effects only for buttons with background */
.btn:hover ~ body::before {
    border-color: var(--primary-blue, #007bff);
    transform: translate(-50%, -50%) scale(2);
    animation: cursor-ripple 0.4s ease-in-out;
}

.btn:hover ~ body::after {
    background-color: var(--primary-blue, #007bff);
    transform: translate(-50%, -50%) scale(1.5);
}

/* Button ripple animation */
@keyframes button-ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.05;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

/* Cursor ripple animation */
@keyframes cursor-ripple {
    0% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(2); }
    100% { transform: translate(-50%, -50%) scale(1.5); }
}

/* Ensure cursor visibility on light backgrounds */
body.cursor-hovering::before {
    border-color: var(--primary-blue, #007bff) !important;
    transform: translate(-50%, -50%) scale(1.5) !important;
}

body.cursor-hovering::after {
    background-color: var(--primary-blue, #007bff) !important;
    transform: translate(-50%, -50%) scale(1.2) !important;
}
/* ===== END CUSTOM CURSOR EFFECTS ===== */

/* Industrial Equipment Color Palette */
:root {
    /* Primary Blue Colors - Company Logo Blue */
    --primary-blue: #007bff;
    --primary-blue-light: #4dabf7;
    --primary-blue-dark: #0056b3;
    --primary-blue-darker: #003d82;

    /* Industrial Blue Colors */
    --industrial-blue: #1e3a8a;
    --industrial-blue-light: #3b82f6;
    --industrial-blue-dark: #1e40af;

    /* Secondary Blue Colors (replacing orange) */
    --secondary-orange: #2f496e;
    --secondary-orange-light: #3b82f6;
    --secondary-orange-dark: #1e3a8a;
    --industrial-orange: #2f496e;
    --industrial-orange-light: #3b82f6;
    --industrial-orange-dark: #1e3a8a;

    /* Industrial Accent Colors */
    --industrial-steel: #64748b;
    --industrial-steel-light: #94a3b8;
    --industrial-steel-dark: #475569;
    --industrial-yellow: #eab308;
    --industrial-red: #dc2626;
    --industrial-green: #16a34a;

    /* Background Colors */
    --bg-white: #ffffff;
    --bg-light: #f8fafc;
    --bg-industrial: #f1f5f9;
    --bg-steel: #e2e8f0;
    --bg-dark: #334155;

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-white: #ffffff;
    --text-steel: #475569;
}


/* Typography - Industrial Professional */
body {
    line-height: 1.6;
    color: var(--text-primary) !important;
    font-weight: 400;
    background: var(--bg-industrial) !important;
    background-image:
        linear-gradient(45deg, rgba(30, 58, 138, 0.03) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(30, 58, 138, 0.03) 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, rgba(30, 58, 138, 0.03) 75%),
        linear-gradient(-45deg, transparent 75%, rgba(30, 58, 138, 0.03) 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: #212529 !important;
    letter-spacing: -0.025em;
}

h1 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #212529 !important;
}

h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #212529 !important;
}

h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #212529 !important;
}

p {
    color: #212529 !important;
    margin-bottom: 1rem;
}

/* Header Styles - Enhanced Industrial Equipment */
.omisol-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 4px solid var(--primary-blue);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0, 123, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 0.5rem 0;
}

.omisol-header::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--secondary-orange);
}

.omisol-navbar {
    padding: 1.25rem 0;
}

.omisol-logo {
    height: 50px;
    width: auto;
    max-width: 200px;
    transition: all 0.3s ease;
}

.omisol-logo:hover {
    filter: brightness(1.1);
    transition: filter 0.3s ease;
}

.omisol-nav-link {
    color: var(--text-primary);
    font-weight: 600;
    text-decoration: none;
    padding: 0.75rem 1.25rem;
    transition: all 0.3s ease;
    position: relative;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
    border-radius: 6px;
}

.omisol-nav-link:hover {
    color: var(--primary-blue);
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(253, 126, 20, 0.1) 100%);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.omisol-nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-blue) 0%, var(--secondary-orange) 100%);
    transition: width 0.3s ease;
    border-radius: 2px;
}

.omisol-nav-link:hover::after {
    width: 100%;
}

/* Enhanced Navigation Styling */
.omisol-nav-link {
    color: var(--text-primary) !important;
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    padding: 0.75rem 1rem !important;
    margin: 0 0.25rem !important;
    border-radius: 6px !important;
    transition: all 0.3s ease !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    position: relative !important;
    background: transparent !important;
    display: flex !important;
    align-items: center !important;
    text-decoration: none !important;
}

.omisol-nav-link i {
    color: var(--primary-blue) !important;
    font-size: 0.9rem;
}

/* Enhanced Dropdown Styling */
.omisol-dropdown {
    border: 2px solid var(--primary-blue) !important;
    border-radius: 8px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    background: #ffffff !important;
    margin-top: 0.5rem !important;
}

.omisol-dropdown .dropdown-item {
    padding: 0.75rem 1.25rem !important;
    color: var(--text-primary) !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    text-decoration: none !important;
}

.omisol-dropdown .dropdown-item:hover {
    background: var(--primary-blue) !important;
    color: white !important;
    border-left: 3px solid var(--secondary-orange) !important;
    transition: all 0.3s ease !important;
}

.omisol-dropdown .dropdown-item i {
    color: var(--primary-blue) !important;
    transition: color 0.3s ease !important;
}

.omisol-dropdown .dropdown-item:hover i {
    color: white !important;
}

/* Contact Info in Header */
.nav-contact-info {
    border-right: 2px solid var(--border-color);
    padding-right: 1rem;
    margin-right: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
}

.contact-text {
    color: var(--text-secondary) !important;
    font-weight: 500;
}

/* Contact Us Button */
.btn-omisol-contact {
    background: var(--primary-blue) !important;
    color: white !important;
    border: 2px solid var(--primary-blue) !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    border-radius: 6px !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
}

.btn-omisol-contact:hover {
    background: var(--secondary-orange) !important;
    border-color: var(--secondary-orange) !important;
    color: white !important;
    box-shadow: 0 0 15px rgba(249, 115, 22, 0.4) !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
}

.btn-omisol-contact i {
    color: white !important;
}

/* Hero Section - Industrial Equipment */
.omisol-hero {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 70%, var(--industrial-steel-dark) 100%);
    color: var(--text-white);
    padding: 120px 0;
    position: relative;
    overflow: hidden;
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.omisol-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, rgba(253, 126, 20, 0.1) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(253, 126, 20, 0.1) 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, rgba(253, 126, 20, 0.1) 75%),
        linear-gradient(-45deg, transparent 75%, rgba(253, 126, 20, 0.1) 75%);
    background-size: 40px 40px;
    background-position: 0 0, 0 20px, 20px -20px, -20px 0px;
    opacity: 0.3;
}

.omisol-hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--secondary-orange);
}

.omisol-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.08)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.4;
}

.omisol-hero::after {
    content: '';
    position: absolute;
    top: 0;
    right: -10%;
    width: 40%;
    height: 100%;
    background: radial-gradient(ellipse at center, rgba(255,255,255,0.1) 0%, transparent 70%);
    opacity: 0.6;
}

.omisol-hero-content {
    position: relative;
    z-index: 2;
}

.omisol-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    font-weight: 800;
    color: white !important;
}

.omisol-hero p {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    color: #e9ecef !important;
    font-weight: 400;
    line-height: 1.7;
}

/* Buttons - Industrial Professional */
.btn-omisol-primary {
    background: var(--primary-blue) !important;
    border: 3px solid var(--primary-blue-dark) !important;
    color: #ffffff !important;
    padding: 16px 40px !important;
    font-weight: 700 !important;
    border-radius: 4px !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-transform: uppercase !important;
    font-size: 0.9rem !important;
    letter-spacing: 0.1em !important;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    position: relative !important;
    z-index: 10 !important;
    text-shadow: none !important;
}

.btn-omisol-primary:hover {
    background: var(--primary-blue-dark) !important;
    color: #ffffff !important;
    border-color: var(--primary-blue-darker) !important;
    box-shadow: 0 0 20px rgba(0, 123, 255, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    text-shadow: none !important;
}

.btn-omisol-primary:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3) !important;
}

.btn-omisol-secondary {
    background: white !important;
    border: 3px solid var(--secondary-orange) !important;
    color: var(--secondary-orange) !important;
    padding: 14px 38px !important;
    font-weight: 700 !important;
    border-radius: 4px !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: inline-block !important;
    text-transform: uppercase !important;
    font-size: 0.9rem !important;
    letter-spacing: 0.1em !important;
    box-shadow: 0 4px 8px rgba(253, 126, 20, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
    position: relative !important;
    z-index: 1 !important;
}

.btn-omisol-secondary:hover {
    background: var(--secondary-orange) !important;
    color: white !important;
    border-color: var(--secondary-orange-dark) !important;
    box-shadow: 0 0 20px rgba(253, 126, 20, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    transition: all 0.3s ease !important;
}

.btn-omisol-secondary:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(253, 126, 20, 0.2) !important;
}

/* Section Styles - Industrial Equipment */
.omisol-section {
    padding: 100px 0;
    position: relative;
}

.omisol-section:nth-child(even) {
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.03) 0%, rgba(234, 88, 12, 0.03) 100%);
    border-top: 2px solid rgba(30, 58, 138, 0.1);
    border-bottom: 2px solid rgba(234, 88, 12, 0.1);
}

.omisol-section:nth-child(odd) {
    background: var(--bg-white);
}

/* Industrial accent bars */
.omisol-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--secondary-orange);
    z-index: 1;
}

/* Industrial grid pattern overlay */
.omisol-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(30, 58, 138, 0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(30, 58, 138, 0.02) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 0;
}

.omisol-section-title {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    color: var(--primary-blue);
}

.omisol-section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    width: 80px;
    height: 4px;
    background: var(--secondary-orange);
    transform: translateX(-50%);
}

/* Product Cards - Industrial Equipment */
.omisol-product-card {
    background: var(--bg-white);
    border: 4px solid var(--primary-blue);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 2rem;
    box-shadow:
        0 8px 25px rgba(0, 123, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    position: relative;
}

.omisol-product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--secondary-orange);
    z-index: 1;
}

/* Clickable Product Card Link */
.product-card-link {
    text-decoration: none !important;
    color: inherit !important;
    display: block;
    transition: all 0.3s ease;
}

.product-card-link:hover {
    text-decoration: none !important;
    color: inherit !important;
}

.product-card-link:hover .omisol-product-card {
    border-color: #007bff;
    box-shadow: 0 0 25px rgba(0, 123, 255, 0.25);
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.02) 0%, rgba(253, 126, 20, 0.02) 100%);
    transition: all 0.3s ease;
}

.omisol-product-card:hover {
    border-color: #007bff;
    box-shadow: 0 0 25px rgba(0, 123, 255, 0.2);
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.02) 0%, rgba(253, 126, 20, 0.02) 100%);
    transition: all 0.3s ease;
}

.omisol-product-image {
    width: 100%;
    height: 280px;
    object-fit: contain;
    border-bottom: 2px solid #dee2e6;
    cursor: default;
}

/* Product Image Container for Zoom Effect */
.product-image-container {
    overflow: hidden;
    position: relative;
    border-radius: 8px 8px 0 0;
}

/* Additional Media Gallery Styles */
.product-media-gallery {
    position: relative;
    margin-bottom: 2rem;
}

.product-main-image {
    width: 100%;
    height: 400px;
    object-fit: contain;
    border-radius: 8px;
    cursor: default;
}

/* Thumbnail Gallery */
.product-thumbnails {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    overflow-x: auto;
    padding: 5px 0;
}

.product-thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.product-thumbnail:hover,
.product-thumbnail.active {
    border-color: var(--primary-blue);
    transform: scale(1.05);
}

.omisol-product-content {
    padding: 2rem;
    border-top: 1px solid #e5e7eb;
    background: #fafafa;
}

.omisol-product-title {
    font-size: 1.375rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: #212529;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.omisol-product-description {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
    font-weight: 700;
    font-size: 1rem;
}

.omisol-product-category {
    background: #007bff;
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    font-weight: 700;
    display: inline-block;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

/* Statistics/Counters - Professional Industrial */
.omisol-stats {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(253, 126, 20, 0.1) 100%);
    color: var(--text-primary);
    padding: 80px 0;
    border-top: 4px solid var(--primary-blue);
    border-bottom: 4px solid var(--secondary-orange);
    position: relative;
}

.omisol-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 10% 20%, rgba(40, 167, 69, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(111, 66, 193, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.omisol-stat-item {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem 1rem;
    border: 3px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, var(--primary-blue), var(--secondary-orange), var(--accent-green)) border-box;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.omisol-stat-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 123, 255, 0.2);
}

.omisol-stat-number {
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--primary-blue);
    display: block;
    margin-bottom: 0.75rem;
    line-height: 1;
}

.omisol-stat-label {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Gallery - Professional Industrial */
.omisol-gallery-item {
    position: relative;
    overflow: hidden;
    margin-bottom: 2rem;
    border: 2px solid #dee2e6;
    background: white;
    transition: all 0.2s ease;
}

.omisol-gallery-item:hover {
    border-color: #007bff;
}

.omisol-gallery-image {
    width: 100%;
    height: 280px;
    object-fit: cover;
    transition: transform 0.2s ease;
}

.omisol-gallery-item:hover .omisol-gallery-image {
    transform: scale(1.02);
}

.omisol-gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.9);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.omisol-gallery-item:hover .omisol-gallery-overlay {
    opacity: 1;
}



/* Responsive Design - Professional Industrial */
@media (max-width: 1200px) {
    .omisol-hero h1 {
        font-size: 3rem;
    }

    .omisol-section {
        padding: 80px 0;
    }
}

@media (max-width: 992px) {
    .omisol-hero {
        padding: 80px 0;
    }

    .omisol-hero h1 {
        font-size: 2.5rem;
    }

    .omisol-section {
        padding: 60px 0;
    }

    .omisol-nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }

    .omisol-stat-number {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .omisol-hero {
        padding: 100px 0 60px 0;
        text-align: center;
        margin-top: 20px;
    }

    .omisol-hero-content {
        padding-top: 40px;
    }

    .omisol-hero h1 {
        font-size: 2.25rem;
        margin-bottom: 1rem;
    }

    .omisol-hero p {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .omisol-stat-number {
        font-size: 2.25rem;
    }

    .omisol-section {
        padding: 50px 0;
    }

    .omisol-section-title {
        margin-bottom: 2.5rem;
    }

    .omisol-product-card {
        margin-bottom: 1.5rem;
    }

    .btn-omisol-primary,
    .btn-omisol-secondary {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
        text-align: center;
        padding: 16px 20px;
    }

    .omisol-navbar {
        padding: 1rem 0;
    }

    .omisol-logo {
        height: 40px;
    }

    /* Mobile menu styles */
    .navbar-collapse {
        background: white;
        border: 2px solid #dee2e6;
        margin-top: 1rem;
        padding: 1rem;
    }

    .omisol-nav-link {
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 0.5rem;
        padding: 1rem;
    }

    .omisol-nav-link:last-child {
        border-bottom: none;
    }
}

@media (max-width: 576px) {
    .omisol-hero {
        padding: 120px 0 60px 0;
        margin-top: 30px;
    }

    .omisol-hero-content {
        padding-top: 50px;
    }

    .omisol-hero h1 {
        font-size: 1.875rem;
    }

    .omisol-hero p {
        font-size: 0.95rem;
    }

    .omisol-stat-number {
        font-size: 2rem;
    }

    .omisol-stat-label {
        font-size: 0.9rem;
    }

    .omisol-section {
        padding: 40px 0;
    }

    .omisol-product-content {
        padding: 1.5rem;
    }

    .omisol-product-title {
        font-size: 1.125rem;
    }



    .omisol-stat-item {
        padding: 1.5rem 0.75rem;
    }
}

/* Product Detail Page Specific Styles */
.product-download-link {
    border: 1px solid #e5e7eb !important;
    background: #f8fafc !important;
    color: #1f2937 !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    padding: 12px !important;
    margin-bottom: 12px !important;
}

.product-download-link:hover {
    background: #e2e8f0 !important;
    border-color: #007bff !important;
    color: #1f2937 !important;
    text-decoration: none !important;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.product-download-icon {
    color: #2f496e !important;
    font-size: 1.5rem !important;
    margin-right: 12px !important;
}

.product-category-badge {
    background: #2f496e !important;
    color: white !important;
    font-size: 0.875rem !important;
    padding: 0.5rem 1rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
    display: inline-block !important;
    margin-bottom: 1rem !important;
    border-radius: 20px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Breadcrumb Navigation */
.breadcrumb-section {
    background: var(--bg-white);
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    font-size: 0.875rem;
}

.breadcrumb-item a {
    color: var(--primary-blue) !important;
    text-decoration: none !important;
}

.breadcrumb-item a:hover {
    color: var(--primary-blue-dark) !important;
    text-decoration: none !important;
}

.breadcrumb-item.active {
    color: var(--text-secondary) !important;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "\f054" !important; /* FontAwesome chevron-right */
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    color: var(--text-secondary) !important;
    font-size: 0.8rem !important;
}

/* Simplified Product Detail Layout */
.product-detail-container {
    background: #fafafa;
    min-height: 100vh;
    padding: 1rem 0;
}

.product-detail-card {
    background: var(--bg-white);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 1rem;
    border: 1px solid #e5e7eb;
}

.product-content-section {
    padding: 2rem;
    background: var(--bg-white);
}

/* Enhanced Link Visibility */
a {
    color: var(--primary-blue) !important;
    text-decoration: none !important;
}

a:hover {
    color: var(--primary-blue-dark) !important;
    text-decoration: none !important;
}

/* ===== PROFESSIONAL STATISTICS SECTION ===== */

.statistics-section-inline {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
    overflow: hidden;
}

.statistics-section-inline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23007bff" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.stats-professional-container {
    padding: 40px 0;
}

.stat-professional-item {
    padding: 20px 15px;
    transition: all 0.3s ease;
}

.stat-number-container-pro {
    margin-bottom: 15px;
    line-height: 1;
}

.stat-number-pro {
    display: inline-block;
    font-size: 4rem;
    font-weight: 700;
    font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    color: transparent;
    -webkit-text-stroke: 2px #2f496e;
    text-stroke: 2px #2f496e;
    -webkit-text-fill-color: transparent;
    line-height: 1;
    transition: all 0.3s ease;
    font-variant-numeric: tabular-nums;
    background: transparent;
}

.stat-suffix-pro {
    display: inline-block;
    font-size: 3rem;
    font-weight: 700;
    font-family: 'Segoe UI', 'Roboto', sans-serif;
    color: transparent;
    -webkit-text-stroke: 2px #2f496e;
    text-stroke: 2px #2f496e;
    -webkit-text-fill-color: transparent;
    line-height: 1;
    margin-left: 2px;
    background: transparent;
}

.stat-label-pro {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.3;
    margin-top: 10px;
}

.stat-professional-item.animate .stat-number-pro {
    -webkit-text-stroke: 3px #0056b3;
    text-stroke: 3px #0056b3;
    animation: professionalBorderPulse 0.6s ease-out;
}

@keyframes professionalBorderPulse {
    0% {
        -webkit-text-stroke: 3px #007bff;
        text-stroke: 3px #007bff;
        transform: scale(1);
    }
    50% {
        -webkit-text-stroke: 4px #0056b3;
        text-stroke: 4px #0056b3;
        transform: scale(1.05);
    }
    100% {
        -webkit-text-stroke: 3px #0056b3;
        text-stroke: 3px #0056b3;
        transform: scale(1);
    }
}

/* Responsive design for professional stats */
@media (max-width: 992px) {
    .stat-number-pro {
        font-size: 3.5rem;
        -webkit-text-stroke: 2.5px #007bff;
        text-stroke: 2.5px #007bff;
    }

    .stat-suffix-pro {
        font-size: 2.5rem;
        -webkit-text-stroke: 2.5px #2f496e;
        text-stroke: 2.5px #2f496e;
    }

    .stat-label-pro {
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .stats-professional-container {
        padding: 30px 0;
    }

    .stat-professional-item {
        padding: 15px 10px;
        margin-bottom: 20px;
    }

    .stat-number-pro {
        font-size: 3rem;
        -webkit-text-stroke: 2px #007bff;
        text-stroke: 2px #007bff;
    }

    .stat-suffix-pro {
        font-size: 2.2rem;
        -webkit-text-stroke: 2px #2f496e;
        text-stroke: 2px #2f496e;
    }

    .stat-label-pro {
        font-size: 0.85rem;
        margin-top: 8px;
    }
}

@media (max-width: 480px) {
    .stat-number-pro {
        font-size: 2.5rem;
        -webkit-text-stroke: 2px #007bff;
        text-stroke: 2px #007bff;
    }

    .stat-suffix-pro {
        font-size: 2rem;
        -webkit-text-stroke: 2px #2f496e;
        text-stroke: 2px #2f496e;
    }

    .stat-label-pro {
        font-size: 0.8rem;
        line-height: 1.2;
    }
}

.stats-inline-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 60px;
    padding: 40px 0;
}

.stat-inline-item {
    text-align: center;
    position: relative;
}

.stat-number-inline {
    display: inline-block;
    font-size: 4.5rem;
    font-weight: 700;
    font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    color: transparent;
    -webkit-text-stroke: 3px #2f496e;
    text-stroke: 3px #2f496e;
    line-height: 1;
    margin-right: 5px;
    transition: all 0.3s ease;
}

.stat-suffix-inline {
    display: inline-block;
    font-size: 3.5rem;
    font-weight: 900;
    font-family: 'Arial Black', 'Arial', sans-serif;
    color: transparent;
    -webkit-text-stroke: 3px #2f496e;
    text-stroke: 3px #2f496e;
    line-height: 1;
    margin-right: 15px;
}

.stat-label-inline {
    display: inline-block;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 1px;
    vertical-align: middle;
    line-height: 1.2;
    max-width: 120px;
    text-align: left;
}

.stat-inline-item.animate .stat-number-inline {
    -webkit-text-stroke: 3px #0056b3;
    text-stroke: 3px #0056b3;
    animation: inlineBorderPulse 0.6s ease-out;
}

@keyframes inlineBorderPulse {
    0% {
        -webkit-text-stroke: 3px #007bff;
        text-stroke: 3px #007bff;
        transform: scale(1);
    }
    50% {
        -webkit-text-stroke: 4px #0056b3;
        text-stroke: 4px #0056b3;
        transform: scale(1.05);
    }
    100% {
        -webkit-text-stroke: 3px #0056b3;
        text-stroke: 3px #0056b3;
        transform: scale(1);
    }
}

/* Responsive design for inline stats */
@media (max-width: 768px) {
    .stats-inline-container {
        flex-direction: column;
        gap: 30px;
    }

    .stat-inline-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .stat-number-inline {
        font-size: 3.5rem;
        -webkit-text-stroke: 2px #007bff;
        text-stroke: 2px #007bff;
    }

    .stat-suffix-inline {
        font-size: 2.5rem;
        -webkit-text-stroke: 2px #2f496e;
        text-stroke: 2px #2f496e;
    }

    .stat-label-inline {
        font-size: 1rem;
        max-width: none;
    }
}

.section-title-professional {
    font-size: 2.8rem;
    font-weight: 700;
    color: #1e40af;
    margin-bottom: 1.5rem;
    position: relative;
    text-align: center;
    line-height: 1.2;
}

.section-title-professional::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #1e40af 0%, #06b6d4 50%, #2f496e 100%);
    border-radius: 2px;
    opacity: 0.9;
    animation: continuousFluidFlow 6s ease-in-out infinite;
}

@keyframes continuousFluidFlow {
    0% {
        background: linear-gradient(90deg, #1e40af 0%, #2563eb 50%, #2f496e 100%);
        opacity: 0.85;
    }
    16.66% {
        background: linear-gradient(90deg, #1f42b0 0%, #2764ec 50%, #2f496e 100%);
        opacity: 0.86;
    }
    33.33% {
        background: linear-gradient(90deg, #2044b1 0%, #2865ed 50%, #2f496e 100%);
        opacity: 0.87;
    }
    50% {
        background: linear-gradient(90deg, #2146b2 0%, #2966ee 50%, #2f496e 100%);
        opacity: 0.88;
    }
    66.66% {
        background: linear-gradient(90deg, #2248b3 0%, #2a67ef 50%, #2f496e 100%);
        opacity: 0.87;
    }
    83.33% {
        background: linear-gradient(90deg, #234ab4 0%, #2b68f0 50%, #2f496e 100%);
        opacity: 0.86;
    }
    100% {
        background: linear-gradient(90deg, #1e40af 0%, #2563eb 50%, #2f496e 100%);
        opacity: 0.85;
    }
}

.section-subtitle-professional {
    font-size: 1.1rem;
    color: #495057;
    margin-bottom: 3rem;
    line-height: 1.7;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.stat-card-professional {
    background: white;
    border-radius: 15px;
    padding: 40px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.stat-card-professional::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
}

.stat-card-professional:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 123, 255, 0.15);
    border-color: #007bff;
}

.stat-number-container {
    font-size: 4rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 15px;
    font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    position: relative;
}

.stat-number-animated {
    display: inline-block;
    font-variant-numeric: tabular-nums;
    letter-spacing: -2px;
    color: transparent;
    -webkit-text-stroke: 2px #2f496e;
    text-stroke: 2px #2f496e;
    font-weight: 700;
    text-shadow: none;
    background: none;
    transition: all 0.3s ease;
}

.stat-number-animated.animate {
    -webkit-text-stroke: 2px #1e40af;
    text-stroke: 2px #1e40af;
    animation: borderPulse 0.6s ease-out;
}

.stat-suffix {
    font-size: 3rem;
    font-weight: 900;
    color: transparent;
    -webkit-text-stroke: 2px #2f496e;
    text-stroke: 2px #2f496e;
    margin-left: 2px;
    display: inline-block;
}

@keyframes borderPulse {
    0% {
        -webkit-text-stroke: 2px #2f496e;
        text-stroke: 2px #2f496e;
        transform: scale(1);
    }
    50% {
        -webkit-text-stroke: 3px #1e40af;
        text-stroke: 3px #1e40af;
        transform: scale(1.05);
    }
    100% {
        -webkit-text-stroke: 2px #1e40af;
        text-stroke: 2px #1e40af;
        transform: scale(1);
    }
}

.stat-label-professional {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-description-professional {
    font-size: 0.95rem;
    color: #6c757d;
    line-height: 1.4;
}

/* Animation for number counting */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-number-animated.animate {
    animation: countUp 0.6s ease-out;
}

/* ===== ENHANCED UI ELEMENTS ===== */

/* Enhanced bullet points for specifications, features, applications */
.specs-content-enhanced ul,
.features-content-enhanced ul,
.applications-content-enhanced ul {
    list-style: none;
    padding-left: 0;
}

.specs-content-enhanced ul li,
.features-content-enhanced ul li,
.applications-content-enhanced ul li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 10px;
    line-height: 1.6;
}

.specs-content-enhanced ul li::before,
.features-content-enhanced ul li::before,
.applications-content-enhanced ul li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-blue);
    font-size: 14px;
    line-height: 1.6;
    font-weight: bold;
}

/* Professional download cards */
.download-card-enhanced {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #e9ecef;
    border-radius: 16px;
    padding: 25px;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    color: inherit;
}

.download-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.download-card-enhanced:hover {
    border-color: #007bff;
    box-shadow: 0 15px 35px rgba(0, 123, 255, 0.1);
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
    transform: translateY(-2px);
    text-decoration: none;
    color: inherit;
}

.download-card-enhanced:hover::before {
    transform: scaleX(1);
}

.download-icon-large {
    text-align: center;
    margin-bottom: 20px;
    position: relative;
}

.download-icon-large i {
    font-size: 3rem;
    color: #2f496e;
    transition: all 0.3s ease;
}

.download-card-enhanced:hover .download-icon-large i {
    transform: scale(1.1);
    color: #1e40af;
}

.download-content-enhanced {
    flex-grow: 1;
    text-align: center;
}

.download-title {
    font-weight: 700;
    margin-bottom: 12px;
    color: #2c3e50;
    font-size: 1.1rem;
    line-height: 1.3;
}

.download-description {
    font-size: 0.9rem;
    margin-bottom: 20px;
    color: #6c757d;
    line-height: 1.5;
}

.download-meta-enhanced {
    margin-bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.download-meta-enhanced .badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
}

.file-size-enhanced {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.download-action {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.download-card-enhanced:hover .download-action {
    opacity: 1;
    transform: scale(1.1);
}

/* Downloads section styling */
.downloads-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.downloads-tabs-enhanced .nav-pills .nav-link {
    background: transparent;
    border: 2px solid #e9ecef;
    color: #6c757d;
    font-weight: 600;
    padding: 12px 24px;
    border-radius: 25px;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.downloads-tabs-enhanced .nav-pills .nav-link.active,
.downloads-tabs-enhanced .nav-pills .nav-link:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 123, 255, 0.3);
}

/* Enhanced Product Detail Sections */
.product-title-enhanced {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.product-short-description {
    margin-bottom: 2rem;
}

.product-description-enhanced {
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
    color: #495057;
}

.section-title-small {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    margin-bottom: 2rem;
}

.product-video-iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

.btn-primary-enhanced,
.btn-outline-primary-enhanced,
.btn-success-enhanced {
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary-enhanced {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    color: white;
}

.btn-primary-enhanced:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
    color: white;
}

.btn-outline-primary-enhanced {
    background: transparent;
    border: 2px solid #007bff;
    color: #007bff;
}

.btn-outline-primary-enhanced:hover {
    background: #007bff;
    color: white;
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.btn-success-enhanced {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
    color: white;
}

.btn-success-enhanced:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    color: white;
}

/* Product Details Cards */
.product-details-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.detail-card {
    background: white;
    border-radius: 16px;
    padding: 30px;
    height: 100%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.detail-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
}

.detail-card:hover {
    box-shadow: 0 20px 40px rgba(0, 123, 255, 0.12);
    border-color: #007bff;
}

.detail-header {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
}

.detail-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.detail-icon i {
    font-size: 1.5rem;
    color: white;
}

.detail-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    line-height: 1.2;
}

.detail-content {
    font-size: 1rem;
    line-height: 1.6;
    color: #495057;
}

/* Enhanced section titles */
.spec-title,
.feature-title,
.application-title {
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* Enhanced homepage sections */
.omisol-hero-advanced {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    color: #2c3e50;
    position: relative;
    overflow: hidden;
    min-height: 100vh;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(0deg, transparent 49px, rgba(120, 120, 120, 1) 50px, rgba(120, 120, 120, 1) 50.5px, transparent 51.5px),
        linear-gradient(90deg, transparent 49px, rgba(120, 120, 120, 1) 50px, rgba(120, 120, 120, 1) 50.5px, transparent 51.5px);
    background-size: 50px 50px;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.82);
}

/* Comment out old beam animations - not using for now */
/*
.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: -300px;
    width: 300px;
    height: 100%;
    background:
        linear-gradient(90deg,
            transparent 0%,
            rgba(0, 123, 255, 0.1) 20%,
            rgba(0, 123, 255, 0.6) 50%,
            rgba(0, 123, 255, 0.1) 80%,
            transparent 100%
        );
    background-size: 300px 2px;
    background-repeat: repeat-y;
    background-position: 0 50px;
    animation: horizontalBeamFlow 10s linear infinite;
    opacity: 0;
}

.hero-background::after {
    content: '';
    position: absolute;
    top: -300px;
    left: 0;
    width: 100%;
    height: 300px;
    background:
        linear-gradient(0deg,
            transparent 0%,
            rgba(0, 123, 255, 0.1) 20%,
            rgba(0, 123, 255, 0.5) 50%,
            rgba(0, 123, 255, 0.1) 80%,
            transparent 100%
        );
    background-size: 2px 300px;
    background-repeat: repeat-x;
    background-position: 50px 0;
    animation: verticalBeamFlow 15s linear infinite;
    animation-delay: 5s;
    opacity: 0;
}

@keyframes horizontalBeamFlow {
    0% {
        left: -300px;
        opacity: 0;
    }
    5% {
        opacity: 0.8;
    }
    95% {
        opacity: 0.8;
    }
    100% {
        left: 100%;
        opacity: 0;
    }
}

@keyframes verticalBeamFlow {
    0% {
        top: -300px;
        opacity: 0;
    }
    5% {
        opacity: 0.6;
    }
    95% {
        opacity: 0.6;
    }
    100% {
        top: 100%;
        opacity: 0;
    }
}
*/

.hero-content-advanced {
    position: relative;
    z-index: 2;
}

.hero-title-advanced {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    color: #1a1a1a;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);
}

.text-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    text-shadow: none;
}

/* Consistent Omnisol Brand Gradient */
.omnisol-gradient,
.text-omnisol-gradient {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    text-shadow: none;
    position: relative;
}

.omnisol-gradient::after,
.text-omnisol-gradient::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #2f496e 0%, #1e40af 100%);
    border-radius: 1px;
    opacity: 0.7;
}

.hero-subtitle-advanced {
    font-size: 1.25rem;
    line-height: 1.6;
    color: #2c3e50;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.btn-primary-gradient {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    color: #ffffff !important;
    font-weight: 600;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-primary-gradient:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    box-shadow: 0 0 20px rgba(0, 123, 255, 0.4);
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Professional Button Styles - Inspired by Reference Website */

/* Primary Button - Professional Blue */
.btn-primary, .btn-omisol-primary, .btn-primary-gradient {
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
    border: 2px solid #1e40af;
    color: #ffffff !important;
    font-weight: 600;
    font-size: 1rem;
    padding: 14px 32px;
    border-radius: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.2);
}

.btn-primary:hover, .btn-omisol-primary:hover, .btn-primary-gradient:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
    border-color: #1d4ed8;
    color: #ffffff !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Outline Button - Professional Style */
.btn-outline-primary, .btn-outline-omisol {
    background: transparent;
    border: 2px solid #1e40af;
    color: #1e40af !important;
    font-weight: 600;
    font-size: 1rem;
    padding: 14px 32px;
    border-radius: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.1);
}

.btn-outline-primary:hover, .btn-outline-omisol:hover {
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
    border-color: #1e40af;
    color: #ffffff !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Secondary Button - Professional Orange */
.btn-secondary-professional, .btn-quote {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border: 2px solid #2f496e;
    color: #ffffff !important;
    font-weight: 600;
    font-size: 1rem;
    padding: 14px 32px;
    border-radius: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 15px rgba(47, 73, 110, 0.2);
}

.btn-secondary-professional:hover, .btn-quote:hover {
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
    border-color: #1e40af;
    color: #ffffff !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(47, 73, 110, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Button Icon Styles */
.btn i {
    margin-right: 8px;
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.btn:hover i {
    transform: translateX(2px);
}

/* Button Ripple Effect - Reference Website Style */
.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 8px;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
    pointer-events: none;
    z-index: 1;
}

.btn:hover::before {
    transform: scaleX(1);
}

/* Button Text and Icon Layer */
.btn span, .btn i {
    position: relative;
    z-index: 2;
}

/* Enhanced Button Hover Animation */
.btn {
    background-size: 200% 100%;
    background-position: 100% 0;
    transition: all 0.4s ease, background-position 0.4s ease;
}

.btn:hover {
    background-position: 0% 0;
}

/* Mobile Responsive Buttons */
@media (max-width: 768px) {
    .btn-primary, .btn-omisol-primary, .btn-primary-gradient,
    .btn-outline-primary, .btn-outline-omisol,
    .btn-secondary-professional, .btn-quote {
        padding: 12px 24px;
        font-size: 0.9rem;
        letter-spacing: 0.3px;
    }

    .btn i {
        margin-right: 6px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .btn-primary, .btn-omisol-primary, .btn-primary-gradient,
    .btn-outline-primary, .btn-outline-omisol,
    .btn-secondary-professional, .btn-quote {
        padding: 10px 20px;
        font-size: 0.85rem;
        width: 100%;
        margin-bottom: 10px;
    }
}

/* Button Group Spacing */
.hero-actions .btn {
    margin-right: 15px;
    margin-bottom: 10px;
}

.hero-actions .btn:last-child {
    margin-right: 0;
}

.cta-buttons .btn {
    margin-right: 15px;
    margin-bottom: 10px;
}

.cta-buttons .btn:last-child {
    margin-right: 0;
}

@media (max-width: 768px) {
    .hero-actions .btn,
    .cta-buttons .btn {
        margin-right: 0;
        margin-bottom: 15px;
        width: 100%;
    }
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    color: white;
    font-size: 1.5rem;
    opacity: 0.7;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* ===== ABOUT US PAGE STYLES ===== */

.about-hero-section {
    background: #ffffff;
    position: relative;
    overflow: hidden;
    min-height: 80vh;
    padding: 80px 0;
}

.about-hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.about-hero-subtitle {
    font-size: 1.3rem;
    color: #495057;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.about-stat-item {
    text-align: center;
}

.about-stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.about-stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
}

.about-hero-image img {
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Company Story Section */
.company-story-section {
    background: #f8f9fa;
    padding: 80px 0;
}

.section-title-about {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.section-subtitle-about {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 0;
}

.story-text {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #495057;
    margin-bottom: 1.5rem;
}

.story-highlights {
    margin-top: 2rem;
}

.highlight-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-weight: 500;
    color: #495057;
}

.highlight-item i {
    margin-right: 12px;
    font-size: 1.2rem;
}

/* Mission Vision Values */
.mission-vision-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.foundation-card {
    background: white;
    padding: 40px 30px;
    border-radius: 16px;
    text-align: center;
    height: 100%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.foundation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 123, 255, 0.12);
    border-color: #007bff;
}

.foundation-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.foundation-icon i {
    font-size: 2rem;
    color: white;
}

.foundation-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
}

.foundation-text {
    color: #495057;
    line-height: 1.6;
    margin: 0;
}

/* Technical Expertise */
.technical-expertise-section {
    background: #ffffff;
}

.expertise-intro {
    font-size: 1.1rem;
    color: #495057;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.expertise-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.expertise-item:hover {
    background: #e3f2fd;
    transform: translateX(5px);
}

.expertise-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.expertise-icon i {
    font-size: 1.5rem;
    color: white;
}

.expertise-details h5 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.expertise-details p {
    color: #495057;
    margin: 0;
    line-height: 1.5;
}

/* Why Choose Us */
.why-choose-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.advantage-card {
    background: white;
    padding: 30px 20px;
    border-radius: 12px;
    height: 100%;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.advantage-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 123, 255, 0.1);
    border-color: #007bff;
}

.advantage-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.advantage-icon i {
    font-size: 1.8rem;
    color: white;
}

.advantage-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 12px;
}

.advantage-text {
    color: #495057;
    line-height: 1.5;
    margin: 0;
}

/* Call to Action */
.about-cta-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
    color: #2c3e50;
    padding: 80px 0;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    margin-top: 2rem;
}

/* ===== CERTIFICATIONS SECTION ===== */

.certifications-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.certification-item {
    padding: 20px 15px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid #e9ecef;
}

.certification-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 123, 255, 0.1);
    border-color: #007bff;
}

.cert-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
}

.cert-icon i {
    font-size: 1.5rem;
    color: white;
}

.cert-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.cert-desc {
    font-size: 0.8rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.4;
}

/* ===== ENHANCED TECHNICAL EXCELLENCE ===== */

.technical-excellence-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 60px 0;
    border-radius: 20px;
    margin: 40px 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
}

.excellence-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.excellence-description {
    font-size: 1.1rem;
    color: #495057;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding: 15px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateX(5px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.1);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: 1.3rem;
    color: white;
}

.feature-content h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.feature-content p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.4;
}

.excellence-stats {
    background: white;
    border-radius: 16px;
    padding: 40px 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    height: fit-content;
}

.stat-item-enhanced {
    text-align: center;
    margin-bottom: 2rem;
    padding: 20px 0;
    border-bottom: 1px solid #e9ecef;
}

.stat-item-enhanced:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.stat-number-large {
    margin-bottom: 10px;
}

.stat-label-enhanced {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Mobile Responsiveness for Technical Excellence */
@media (max-width: 768px) {
    .technical-excellence-section {
        padding: 40px 20px;
        margin: 20px 0;
    }

    .excellence-title {
        font-size: 1.5rem;
    }

    .excellence-description {
        font-size: 1rem;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
        padding: 20px;
    }

    .feature-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .excellence-stats {
        margin-top: 30px;
        padding: 30px 20px;
    }

    .stat-item-enhanced {
        margin-bottom: 1.5rem;
        padding: 15px 0;
    }
}

@media (max-width: 480px) {
    .technical-excellence-section {
        padding: 30px 15px;
    }

    .excellence-title {
        font-size: 1.3rem;
    }

    .feature-item {
        padding: 15px;
    }

    .excellence-stats {
        padding: 25px 15px;
    }
}

/* ===== ENHANCED SOLUTION CARDS ===== */

.solution-card-enhanced {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 16px;
    padding: 40px 30px;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.solution-card-enhanced:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
    border-color: #007bff;
    /* No position changes, no shadows */
}

.solution-icon-enhanced {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    color: white;
    transition: all 0.3s ease;
}

.solution-card-enhanced:hover .solution-icon-enhanced {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
}

.solution-title-enhanced {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 20px;
    line-height: 1.3;
}

.solution-description-enhanced {
    font-size: 1rem;
    color: #495057;
    line-height: 1.6;
    text-align: center;
    margin-bottom: 25px;
}

.solution-features-enhanced {
    list-style: none;
    padding: 0;
    margin-bottom: 30px;
    flex-grow: 1;
}

.solution-features-enhanced li {
    position: relative;
    padding: 8px 0 8px 25px;
    color: #495057;
    font-size: 0.95rem;
    line-height: 1.5;
}

.solution-features-enhanced li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 8px;
    color: #1e40af;
    font-weight: bold;
    font-size: 1rem;
}

.solution-card-enhanced .btn {
    display: block;
    text-align: center;
    margin: 0 auto;
    width: fit-content;
    margin-top: auto;
}

/* Mobile Responsiveness for Solution Cards */
@media (max-width: 768px) {
    .solution-card-enhanced {
        padding: 30px 20px;
        margin-bottom: 30px;
    }

    .solution-icon-enhanced {
        width: 70px;
        height: 70px;
        margin-bottom: 20px;
    }

    .solution-title-enhanced {
        font-size: 1.2rem;
        margin-bottom: 15px;
    }

    .solution-description-enhanced {
        font-size: 0.95rem;
        margin-bottom: 20px;
    }

    .solution-features-enhanced {
        margin-bottom: 25px;
    }

    .solution-features-enhanced li {
        padding: 6px 0 6px 20px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .solution-card-enhanced {
        padding: 25px 15px;
    }

    .solution-icon-enhanced {
        width: 60px;
        height: 60px;
    }

    .solution-title-enhanced {
        font-size: 1.1rem;
    }

    .solution-description-enhanced {
        font-size: 0.9rem;
    }
}

/* ===== ENHANCED FEATURED PRODUCTS ===== */

.featured-products-section {
    background: #f8f9fa;
    position: relative;
}

.product-card-enhanced {
    display: block;
    text-decoration: none;
    color: inherit;
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 16px;
    overflow: hidden;
    height: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    position: relative;
}

.product-card-enhanced:hover {
    background: #ffffff;
    border-color: #1e40af;
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(30, 64, 175, 0.12);
    text-decoration: none;
    color: inherit;
}

.product-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.02) 0%, rgba(59, 130, 246, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 16px;
    pointer-events: none;
}

.product-card-enhanced:hover::before {
    opacity: 1;
}

.product-image-container {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
    background: #f8f9fa;
}

.product-image-enhanced {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.product-card-enhanced:hover .product-image-enhanced {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 2;
}

.product-badge .badge {
    font-size: 0.75rem;
    padding: 6px 12px;
    border-radius: 20px;
}

.product-content-enhanced {
    padding: 25px 20px 20px;
    display: flex;
    flex-direction: column;
    height: calc(100% - 250px);
    position: relative;
    z-index: 2;
    min-height: 280px;
    justify-content: space-between;
}

.product-category-enhanced {
    font-size: 0.75rem;
    color: #1e40af;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    margin-bottom: 12px;
    background: rgba(30, 64, 175, 0.1);
    padding: 4px 12px;
    border-radius: 20px;
    display: inline-block;
    border: 1px solid rgba(30, 64, 175, 0.2);
    width: auto;
    max-width: fit-content;
}

.product-title-enhanced {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 15px;
    line-height: 1.3;
    min-height: 2.6rem;
}

.product-description-enhanced {
    font-size: 0.95rem;
    color: #495057;
    line-height: 1.5;
    margin-bottom: 20px;
    flex-grow: 1;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 6rem;
    transition: color 0.3s ease;
}

.product-card-enhanced:hover .product-description-enhanced {
    color: #374151;
}

.product-card-enhanced:hover .product-title-enhanced {
    color: #1e40af;
}

.product-card-enhanced:hover .product-category-enhanced {
    background: rgba(30, 64, 175, 0.15);
    border-color: rgba(30, 64, 175, 0.3);
}

.product-action-enhanced {
    margin-top: auto;
    padding: 15px 20px 20px;
    text-align: center;
}

.view-details-text {
    color: #2f496e;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: rgba(47, 73, 110, 0.1);
    border-radius: 20px;
    border: 1px solid rgba(47, 73, 110, 0.2);
}

.product-card-enhanced:hover .view-details-text {
    color: white;
    background: #2f496e;
    border-color: #2f496e;
}

.view-details-text i {
    transition: transform 0.3s ease;
}

.product-card-enhanced:hover .view-details-text i {
    transform: translateX(3px);
}

/* Mobile Responsiveness for Featured Products */
@media (max-width: 768px) {
    .product-image-container {
        height: 200px;
    }

    .product-content-enhanced {
        padding: 20px 15px 10px;
        height: calc(100% - 200px);
        min-height: 400px;
    }

    .product-title-enhanced {
        font-size: 1.1rem;
        min-height: 2.2rem;
    }

    .product-description-enhanced {
        font-size: 0.9rem;
        -webkit-line-clamp: 2;
    }
}

@media (max-width: 480px) {
    .product-image-container {
        height: 180px;
    }

    .product-content-enhanced {
        padding: 15px 12px 10px;
        height: calc(100% - 180px);
        min-height: 360px;
    }

    .product-title-enhanced {
        font-size: 1rem;
        min-height: 2rem;
    }
}

/* ===== INDUSTRIAL SHOWCASE SECTION ===== */

.industrial-showcase-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
    position: relative;
    min-height: 60vh;
    display: flex;
    align-items: center;
    color: #2c3e50;
}

.industrial-background-overlay {
    position: relative;
    z-index: 2;
    width: 100%;
}

.industrial-showcase-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
}

.industrial-showcase-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    opacity: 0.95;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.industrial-showcase-features {
    margin-bottom: 2.5rem;
}

.showcase-feature {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px 20px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.showcase-feature:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.feature-icon-small {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.feature-icon-small i {
    color: white;
    font-size: 1.1rem;
}

.showcase-feature span {
    font-weight: 600;
    font-size: 0.95rem;
}

/* Mobile Responsiveness for Industrial Showcase */
@media (max-width: 768px) {
    .industrial-showcase-section {
        min-height: 50vh;
        padding: 60px 0;
    }

    .industrial-showcase-title {
        font-size: 2.5rem;
    }

    .industrial-showcase-subtitle {
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .showcase-feature {
        padding: 12px 15px;
        margin-bottom: 10px;
    }

    .feature-icon-small {
        width: 35px;
        height: 35px;
        margin-right: 10px;
    }

    .showcase-feature span {
        font-size: 0.9rem;
    }
}

/* ===== ENHANCED CERTIFICATIONS ===== */

.certifications-section-enhanced {
    background: #f8f9fa;
    position: relative;
}

.certification-item-enhanced {
    background: white;
    border-radius: 16px;
    padding: 30px 20px;
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.certification-item-enhanced:hover {
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
    border-color: #1e40af;
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(30, 64, 175, 0.15);
}

.cert-logo-enhanced {
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(47, 73, 110, 0.15);
    border: 2px solid #e9ecef;
    padding: 10px;
}

.cert-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 8px;
}

.certification-item-enhanced:hover .cert-logo-enhanced {
    border-color: #2f496e;
    transform: scale(1.05);
    box-shadow: 0 12px 30px rgba(47, 73, 110, 0.25);
}

.cert-title-enhanced {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
    line-height: 1.3;
}

.cert-desc-enhanced {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.4;
}

/* Mobile Responsiveness for Certifications */
@media (max-width: 768px) {
    .certification-item-enhanced {
        padding: 25px 15px;
        margin-bottom: 20px;
    }

    .cert-logo-enhanced {
        width: 70px;
        height: 70px;
        margin-bottom: 15px;
        padding: 8px;
    }

    .cert-title-enhanced {
        font-size: 1rem;
    }

    .cert-desc-enhanced {
        font-size: 0.85rem;
    }
}

/* ===== ENHANCED PRODUCT SPECS ===== */

.product-specs-enhanced {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px 15px;
    margin-bottom: 15px;
    border-left: 4px solid #1e40af;
}

.spec-item {
    font-size: 0.85rem;
    color: #495057;
    margin-bottom: 5px;
}

.spec-item:last-child {
    margin-bottom: 0;
}

.spec-item strong {
    color: #2c3e50;
}

/* ===== CLIENTS SECTION WITH SMOOTH SCROLLING ===== */

.clients-section {
    background: #f8f9fa !important;
    overflow: hidden;
}

.clients-scroll-container {
    width: 100%;
    overflow: hidden;
    position: relative;
    padding: 20px 0;
}

.clients-scroll-track {
    display: flex;
    align-items: center;
    animation: smoothScroll 30s linear infinite;
    width: calc(200% + 40px);
}

.client-logo-item {
    flex: 0 0 auto;
    margin: 0 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 12px;
    padding: 20px 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    min-width: 180px;
    height: 100px;
}

.client-logo-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(30, 64, 175, 0.15);
    border-color: #1e40af;
}

.client-logo {
    max-width: 140px;
    max-height: 60px;
    width: auto;
    height: auto;
    object-fit: contain;
    filter: grayscale(100%) opacity(0.7);
    transition: all 0.3s ease;
}

.client-logo-item:hover .client-logo {
    filter: grayscale(0%) opacity(1);
    transform: scale(1.05);
}

@keyframes smoothScroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Pause animation on hover */
.clients-scroll-container:hover .clients-scroll-track {
    animation-play-state: paused;
}

/* Mobile Responsiveness for Clients */
@media (max-width: 768px) {
    .clients-scroll-track {
        animation-duration: 25s;
    }

    .client-logo-item {
        margin: 0 20px;
        padding: 15px 20px;
        min-width: 150px;
        height: 80px;
    }

    .client-logo {
        max-width: 120px;
        max-height: 50px;
    }
}

@media (max-width: 480px) {
    .clients-scroll-track {
        animation-duration: 20s;
    }

    .client-logo-item {
        margin: 0 15px;
        padding: 12px 15px;
        min-width: 130px;
        height: 70px;
    }

    .client-logo {
        max-width: 100px;
        max-height: 40px;
    }
}

/* ===== ENHANCED FEATURE CARDS ===== */

.feature-card-enhanced {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 16px;
    padding: 40px 30px;
    height: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.feature-card-enhanced:hover {
    background: white;
    border-color: #1e40af;
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(30, 64, 175, 0.15);
}

.feature-icon-enhanced {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    color: white;
    transition: all 0.3s ease;
}

.feature-icon-enhanced i {
    font-size: 2.5rem;
    color: white;
}

.feature-card-enhanced:hover .feature-icon-enhanced {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
    transform: scale(1.05);
}

.feature-title-enhanced {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
    line-height: 1.3;
}

.feature-text-enhanced {
    font-size: 1rem;
    color: #495057;
    line-height: 1.6;
    margin: 0;
}

/* ===== TESTIMONIALS CAROUSEL SECTION ===== */

.testimonials-section {
    background: #ffffff;
    position: relative;
}

.testimonials-carousel-container {
    position: relative;
    overflow: hidden;
    margin: 0 auto;
}

.testimonials-track {
    display: flex;
    transition: transform 0.5s ease;
    width: 200%; /* 6 items * 33.33% = 200% */
}

.testimonial-item {
    flex: 0 0 33.33%;
    padding: 0 15px;
}

.testimonials-section {
    padding: 80px 0;
}

.testimonials-carousel-container {
    padding: 0 20px;
}

.testimonial-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 16px;
    padding: 30px 25px;
    height: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    position: relative;
    border-top: 4px solid transparent;
}

.testimonial-card:hover {
    background: white;
    border-color: #2f496e;
    border-top-color: #2f496e;
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(47, 73, 110, 0.15);
}

.testimonial-content {
    margin-bottom: 25px;
}

.quote-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: white;
    font-size: 1.2rem;
}

.testimonial-text {
    font-size: 1.1rem;
    color: #495057;
    line-height: 1.6;
    font-style: italic;
    margin: 0;
}

.testimonial-author {
    display: flex;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.author-avatar {
    margin-right: 15px;
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    border: 3px solid #e9ecef;
    position: relative;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.avatar-img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
}

/* Default avatar when no image */
.author-avatar:not(:has(.avatar-img))::before {
    content: attr(data-initials);
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
}

.author-info {
    flex-grow: 1;
}

.author-name {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.author-position {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

.testimonials-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
    gap: 20px;
}

.testimonial-btn {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.testimonial-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
    transform: scale(1.1);
}

/* Mobile Responsiveness for Testimonials */
@media (max-width: 768px) {
    .testimonial-item {
        flex: 0 0 100%;
    }

    .testimonials-track {
        width: 600%; /* 6 items * 100% = 600% for mobile */
    }

    .testimonial-card {
        padding: 25px 20px;
        margin-bottom: 20px;
    }

    .quote-icon {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .testimonial-text {
        font-size: 0.95rem;
    }

    .avatar-img {
        width: 50px;
        height: 50px;
    }

    .author-name {
        font-size: 1rem;
    }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .feature-card-enhanced,
    .testimonial-card {
        padding: 25px 20px;
        margin-bottom: 20px;
    }

    .feature-icon-enhanced {
        width: 70px;
        height: 70px;
        margin-bottom: 20px;
    }

    .feature-title-enhanced {
        font-size: 1.2rem;
    }

    .feature-text-enhanced,
    .testimonial-text {
        font-size: 0.95rem;
    }

    .quote-icon {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .avatar-img {
        width: 50px;
        height: 50px;
    }

    .author-name {
        font-size: 1rem;
    }
}

/* ===== ENHANCED CTA SECTION ===== */

.omisol-cta-enhanced {
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
    color: #2c3e50;
    position: relative;
    overflow: hidden;
}

.omisol-cta-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="ctaGrid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23ctaGrid)"/></svg>');
    opacity: 0.3;
}

.cta-title-enhanced {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
    line-height: 1.2;
}

.cta-subtitle-enhanced {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    line-height: 1.6;
    opacity: 0.95;
    position: relative;
    z-index: 2;
}

.cta-features {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

.cta-feature-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.cta-feature-item i {
    color: #10b981;
    margin-right: 8px;
    font-size: 1rem;
}

.cta-feature-item span {
    font-weight: 600;
    font-size: 0.9rem;
}

.cta-buttons-enhanced {
    position: relative;
    z-index: 2;
}

.cta-buttons-enhanced .btn {
    display: block;
    width: 100%;
    max-width: 250px;
    margin: 0 auto;
}

/* Mobile Responsiveness for CTA */
@media (max-width: 768px) {
    .cta-title-enhanced {
        font-size: 2rem;
        text-align: center;
    }

    .cta-subtitle-enhanced {
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .cta-features {
        justify-content: center;
        margin-bottom: 1.5rem;
    }

    .cta-feature-item {
        padding: 8px 12px;
        font-size: 0.85rem;
    }

    .cta-buttons-enhanced {
        text-align: center;
        margin-top: 20px;
    }
}

@media (max-width: 480px) {
    .cta-title-enhanced {
        font-size: 1.8rem;
    }

    .cta-features {
        flex-direction: column;
        align-items: center;
    }

    .cta-feature-item {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

/* ===== OUR WORKS SECTION ===== */

.our-works-section {
    background: #ffffff;
    padding: 80px 0;
}

.nowbutton:hover {
    background-color: transparent !important;
    color: #2f496e !important;
}

.nowbutton {
    margin-top: 30px;
}

.card-container {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    height: 400px;
    cursor: pointer;
}

/* Added new sliding border styles */
.card-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background-color: #1e40af !important;
    transition: width 0.5s ease;
    z-index: 3;
}

.card-container:hover::after {
    width: 100%;
}

.card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.card-overlay {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    transition: transform 0.5s ease;
    z-index: 2;
    background: rgba(0, 0, 0, 0.6);
    padding: 15px;
    border-radius: 8px;
}

.company-badge {
    background-color: #1e40af;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    display: inline-block;
    margin-bottom: 10px;
    font-size: 14px;
}

.card-title {
    color: white;
    font-size: 24px;
    font-weight: bold;
    margin: 0;
}

.arrow-icon {
    display: none;
}

.card-container::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
    z-index: 1;
}

.card-container:hover .card-overlay {
    transform: translateY(-20px);
}

.card-container:hover .arrow-icon {
    transform: translateY(0);
    opacity: 1;
}

.card-container:hover .card-image {
    transform: scale(1.1);
}

.card-title > .text-white {
    font-family: 'Segoe UI', sans-serif;
    font-weight: 800;
}

.viewall:hover {
    color: #2f496e !important;
}

.link {
    position: relative;
    text-decoration: none;
    transition: all 0.9s cubic-bezier(0.075, 0.82, 0.165, 1);
}

.link::before {
    position: absolute;
    content: '';
    border-radius: 1em;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    transition: transform 0.9s cubic-bezier(0.075, 0.82, 0.165, 1);
}

.link::after {
    position: absolute;
    content: '';
    border-radius: 1em;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #2f496e;
    transform: translateX(-100%);
    transition: transform 0.9s cubic-bezier(0.075, 0.82, 0.165, 1);
}

.link:hover::before {
    transform: scaleX(0.3);
}

.link:hover::after {
    transform: translateX(0);
}

.link:focus {
    outline: none;
    box-shadow: 0 0 0 2px #2f496e;
}

/* Mobile responsiveness for Our Works */
@media (max-width: 768px) {
    .card-container {
        height: 300px;
        margin-bottom: 30px;
    }

    .card-title {
        font-size: 20px;
    }

    .company-badge {
        font-size: 12px;
        padding: 4px 12px;
    }

    .arrow-icon {
        width: 35px;
        height: 35px;
    }

    .link {
        padding: 0.15em 0;
    }
}

/* ===== BADGE FIXES ===== */

.bg-primary-gradient {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%) !important;
    color: white !important;
    border: none !important;
}

.bg-primary {
    background: #2f496e !important;
    color: white !important;
    border: none !important;
}

/* ===== END BADGE FIXES ===== */

/* ===== END OUR WORKS SECTION ===== */

/* ===== PRODUCTS PAGE STYLES ===== */

.products-hero-section {
    background: #ffffff;
    padding: 80px 0;
    position: relative;
}

.products-hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.products-hero-subtitle {
    font-size: 1.3rem;
    color: #495057;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.product-categories-section {
    background: #f8f9fa;
}

.category-card-enhanced {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 16px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.category-card-enhanced:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(47, 73, 110, 0.1);
    border-color: #2f496e;
}

.category-icon-enhanced {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    color: white;
    transition: all 0.3s ease;
}

.category-icon-enhanced i {
    font-size: 2rem;
    color: white;
}

.category-card-enhanced:hover .category-icon-enhanced {
    transform: scale(1.1);
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
}

.category-title-enhanced {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
}

.category-description-enhanced {
    color: #6c757d;
    margin-bottom: 20px;
    line-height: 1.6;
}

.category-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-features li {
    color: #495057;
    padding: 5px 0;
    position: relative;
    padding-left: 20px;
}

.category-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #2f496e;
    font-weight: bold;
}

.products-listing-section {
    background: #ffffff;
    position: relative;
}

.products-listing-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(47, 73, 110, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(47, 73, 110, 0.03) 1px, transparent 1px);
    background-size: 30px 30px;
    pointer-events: none;
    z-index: 1;
}

.products-listing-section .container {
    position: relative;
    z-index: 2;
}

.search-container {
    position: relative;
}

.search-input,
input[type="search"],
.form-control[type="search"],
.o_searchview_input {
    padding-left: 45px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white !important;
    color: #495057 !important;
}

.search-input:focus,
input[type="search"]:focus,
.form-control[type="search"]:focus,
.o_searchview_input:focus {
    border-color: #2f496e !important;
    box-shadow: 0 0 0 0.2rem rgba(47, 73, 110, 0.25) !important;
    background: white !important;
    outline: none !important;
}

/* Override any orange search backgrounds */
.search-container input,
.search-form input,
.navbar-search input,
.global-search input {
    background: white !important;
    border-color: #e9ecef !important;
}

.search-container input:focus,
.search-form input:focus,
.navbar-search input:focus,
.global-search input:focus {
    background: white !important;
    border-color: #2f496e !important;
    box-shadow: 0 0 0 0.2rem rgba(47, 73, 110, 0.25) !important;
}

.search-icon {
    color: #6c757d;
    font-size: 1rem;
}

.search-icon-btn {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    font-size: 1rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.search-icon-btn:hover {
    color: #2f496e;
}

.category-filter,
.sort-filter {
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.category-filter:focus,
.sort-filter:focus {
    border-color: #2f496e;
    box-shadow: 0 0 0 0.2rem rgba(47, 73, 110, 0.25);
}

.no-products-message {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 60px 40px;
}

.pagination .page-link {
    color: #2f496e;
    border: 1px solid #e9ecef;
    padding: 10px 15px;
    margin: 0 2px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.pagination .page-link:hover,
.pagination .page-item.active .page-link {
    background-color: #2f496e !important;
    border-color: #2f496e !important;
    color: white !important;
}

.pagination .page-item.active .page-link {
    background-color: #1e40af !important;
    border-color: #1e40af !important;
    color: white !important;
}

.product-item {
    transition: all 0.3s ease;
}

.product-item.hidden {
    display: none;
}

.products-cta-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
    color: #2c3e50;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.cta-subtitle {
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

.cta-buttons {
    margin-top: 30px;
}

/* Product filtering animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.product-item.show {
    animation: fadeInUp 0.5s ease;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .products-hero-title {
        font-size: 2.5rem;
    }

    .products-hero-subtitle {
        font-size: 1.1rem;
    }

    .category-card-enhanced {
        padding: 20px;
        margin-bottom: 30px;
    }

    .filter-btn {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .cta-title {
        font-size: 2rem;
    }
}

/* ===== NEW PRODUCT CARD DESIGN ===== */

.product-card-enhanced-new {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.product-card-enhanced-new:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(47, 73, 110, 0.15);
    border-color: #2f496e;
}

.product-image-enhanced-new {
    position: relative;
    height: 280px;
    overflow: hidden;
}

.product-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card-enhanced-new:hover .product-img {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, transparent 50%, rgba(0,0,0,0.3) 100%);
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    padding: 15px;
}

.product-badges {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.badge-category {
    background: rgba(47, 73, 110, 0.9);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-featured {
    background: rgba(255, 193, 7, 0.9);
    color: #000;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.product-content-enhanced-new {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-title-new {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 12px;
    line-height: 1.3;
}

.product-description-new {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 15px;
}

.product-features-preview {
    margin-bottom: 15px;
}

.features-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2f496e;
    margin-bottom: 8px;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    font-size: 0.85rem;
    color: #495057;
    padding: 3px 0;
    position: relative;
    padding-left: 15px;
}

/* Handle dynamic content that might already have bullets */
.features-content-enhanced ul {
    list-style: none;
    padding-left: 0;
}

.features-content-enhanced ul li {
    position: relative;
    padding-left: 20px;
    margin-bottom: 8px;
}

.features-content-enhanced ul li::before {
    content: '✓';
    color: #2f496e;
    position: absolute;
    left: 0;
    font-weight: bold;
    font-size: 0.9rem;
}

.features-list li::before {
    content: '✓';
    color: #2f496e;
    position: absolute;
    left: 0;
    font-weight: bold;
    font-size: 0.9rem;
}

.product-specs-preview {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.spec-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem;
    color: #495057;
}

.spec-item i {
    color: #2f496e;
    font-size: 0.9rem;
}

.product-actions-new {
    margin-top: auto;
    display: flex;
    gap: 10px;
}

.btn-view-details {
    flex: 1;
    background: #2f496e;
    border-color: #2f496e;
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-view-details:hover {
    background: #1e40af;
    border-color: #1e40af;
    color: white;
    transform: translateY(-1px);
}

.btn-quote {
    flex: 1;
    border-color: #2f496e;
    color: #2f496e;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-quote:hover {
    background: #2f496e;
    border-color: #2f496e;
    color: white;
    transform: translateY(-1px);
}

/* Mobile responsiveness for new cards */
@media (max-width: 768px) {
    .product-image-enhanced-new {
        height: 220px;
    }

    .product-content-enhanced-new {
        padding: 20px;
    }

    .product-title-new {
        font-size: 1.2rem;
    }

    .product-actions-new {
        flex-direction: column;
    }

    .product-specs-preview {
        justify-content: center;
    }
}

/* ===== PRODUCT DETAIL PAGE STYLES ===== */

.product-detail-hero {
    background: #ffffff;
    position: relative;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 2rem;
}

.breadcrumb-item a {
    color: #2f496e;
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #6c757d;
}

.main-product-image {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
}

.product-badges-detail {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.badge-category-detail {
    background: rgba(47, 73, 110, 0.9);
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 500;
}

.badge-featured-detail {
    background: rgba(255, 193, 7, 0.9);
    color: #000;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
}

.product-detail-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.product-detail-subtitle {
    font-size: 1.2rem;
    color: #6c757d;
    line-height: 1.5;
    margin-bottom: 2rem;
}

.product-highlights {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
    color: #495057;
}

.highlight-item i {
    color: #2f496e;
    font-size: 1.1rem;
}

.quick-features {
    background: #ffffff;
    border: 1px solid #e9ecef;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.features-title-detail {
    color: #2f496e;
    font-weight: 600;
    margin-bottom: 15px;
}

.features-list-detail {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list-detail li {
    padding: 8px 0;
    position: relative;
    padding-left: 20px;
    color: #495057;
}

.features-list-detail li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #2f496e;
    font-weight: bold;
}

.product-actions-detail {
    margin-top: 2rem;
}

.product-info-tabs {
    background: #f8f9fa;
}

.product-detail-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 0;
}

.product-detail-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 15px 25px;
    border-radius: 0;
    transition: all 0.3s ease;
}

.product-detail-tabs .nav-link:hover,
.product-detail-tabs .nav-link.active {
    color: #2f496e;
    background: #ffffff;
    border-bottom: 3px solid #2f496e;
}

.product-tab-content {
    background: #ffffff;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tab-content-inner {
    padding: 40px;
}

.product-quick-specs {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    border-left: 4px solid #2f496e;
}

.spec-item-detail {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.spec-item-detail:last-child {
    border-bottom: none;
}

.specifications-content,
.features-content,
.applications-content {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    border-left: 4px solid #2f496e;
}

.specifications-content pre,
.features-content pre,
.applications-content pre {
    background: none;
    border: none;
    color: #495057;
    font-family: 'Segoe UI', sans-serif;
    white-space: pre-wrap;
    margin: 0;
}

.downloads-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.download-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.download-item:hover {
    border-color: #2f496e;
    box-shadow: 0 4px 12px rgba(47, 73, 110, 0.1);
}

.download-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.download-info h6 {
    margin-bottom: 5px;
    color: #2c3e50;
    font-weight: 600;
}

.download-info p {
    margin-bottom: 10px;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Mobile responsiveness for product detail */
@media (max-width: 768px) {
    .product-detail-title {
        font-size: 2rem;
    }

    .product-detail-subtitle {
        font-size: 1.1rem;
    }

    .product-highlights {
        padding: 20px;
    }

    .tab-content-inner {
        padding: 25px;
    }

    .product-detail-tabs .nav-link {
        padding: 12px 15px;
        font-size: 0.9rem;
    }

    .downloads-grid {
        grid-template-columns: 1fr;
    }

    .download-item {
        flex-direction: column;
        text-align: center;
    }
}

/* ===== CONTACT US PAGE STYLES ===== */

.contact-hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.contact-hero-title {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.contact-hero-subtitle {
    font-size: 1.2rem;
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.contact-highlights {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.contact-highlight-item {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
    color: #495057;
}

.contact-highlight-item i {
    font-size: 1.2rem;
}

.contact-card {
    background: white;
    border-radius: 16px;
    padding: 30px;
    height: 100%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    text-align: center;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(47, 73, 110, 0.15);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2rem;
}

.contact-card h4 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 20px;
}

.contact-details p {
    margin-bottom: 10px;
    color: #495057;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.social-link {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(47, 73, 110, 0.3);
    color: white;
}

.contact-form-container {
    background: white;
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-form .form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.contact-form .form-control,
.contact-form .form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.contact-form .form-control:focus,
.contact-form .form-select:focus {
    border-color: #2f496e;
    box-shadow: 0 0 0 0.2rem rgba(47, 73, 110, 0.25);
}

.map-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-cta {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    color: white;
}

.contact-cta .cta-title {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.contact-cta .cta-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    margin-bottom: 0;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .contact-hero-title {
        font-size: 2.5rem;
    }

    .contact-hero-subtitle {
        font-size: 1.1rem;
    }

    .contact-highlights {
        margin-top: 2rem;
    }

    .contact-form-container {
        padding: 30px 20px;
    }

    .contact-cta .cta-title {
        font-size: 1.8rem;
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .contact-cta .col-lg-4 {
        text-align: center !important;
        margin-top: 1rem;
    }
}

/* ===== MARQUEE ANIMATION STYLES ===== */

.marquee-section {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    overflow: hidden;
    white-space: nowrap;
    position: relative;
}

.marquee-container {
    width: 100%;
    overflow: hidden;
}

.marquee-content {
    display: inline-flex;
    animation: marqueeScroll 30s linear infinite;
    gap: 60px;
    align-items: center;
}

.marquee-item {
    color: white;
    font-size: 1.1rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

.marquee-item:hover {
    opacity: 1;
}

.marquee-item i {
    color: #ffd700;
    font-size: 1.2rem;
}

@keyframes marqueeScroll {
    0% {
        transform: translateX(0%);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Pause animation on hover */
.marquee-section:hover .marquee-content {
    animation-play-state: paused;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .marquee-item {
        font-size: 1rem;
    }

    .marquee-content {
        gap: 40px;
        animation-duration: 25s;
    }
}

@media (max-width: 480px) {
    .marquee-item {
        font-size: 0.9rem;
    }

    .marquee-content {
        gap: 30px;
        animation-duration: 20s;
    }
}

/* ===== END MARQUEE ANIMATION STYLES ===== */

/* ===== FOOTER STYLES ===== */

.omisol-footer {
    background: #1a1a1a;
    color: #ffffff;
    padding: 60px 0 20px;
}

.footer-section {
    margin-bottom: 30px;
}

.footer-title {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    border-bottom: 2px solid #2f496e;
    padding-bottom: 10px;
}

.footer-subtitle {
    color: #ffffff !important;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #2f496e;
    padding-bottom: 8px;
    display: inline-block;
}

.footer-text {
    color: #cccccc;
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 12px;
    line-height: 1.6;
}

.footer-links a {
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #ffd700;
}

.footer-contact-list li {
    color: #cccccc;
    margin-bottom: 15px;
}

.footer-contact-list i {
    color: #2f496e;
    width: 20px;
}

.footer-social {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #2f496e;
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #ffd700;
    color: #1a1a1a;
    transform: translateY(-2px);
}

.footer-divider {
    border-color: #333333;
    margin: 40px 0 20px;
}

.footer-copyright {
    color: #999999;
    margin: 0;
}

.footer-legal a {
    color: #999999;
    text-decoration: none;
    margin: 0 10px;
}

.footer-legal a:hover {
    color: #ffd700;
}

.footer-legal .separator {
    color: #666666;
}

/* ===== COURSES PAGE STYLES ===== */

.video-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(47, 73, 110, 0.15);
}

.video-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 3rem;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.video-card:hover .video-overlay {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
    position: relative;
}

.video-thumbnail {
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.video-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.section-title {
    color: #2f496e;
    font-weight: 600;
    margin-bottom: 20px;
}

/* ===== BREADCRUMB STYLES ===== */

.custom-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: "\f054"; /* FontAwesome chevron-right */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    color: #6c757d;
    font-size: 0.8rem;
}

.custom-breadcrumb .breadcrumb-item a {
    color: #2f496e;
    text-decoration: none;
}

.custom-breadcrumb .breadcrumb-item a:hover {
    color: #1e40af;
    text-decoration: underline;
}

.custom-breadcrumb .breadcrumb-item.active {
    color: #6c757d;
}

/* ===== FAQ PAGE STYLES ===== */

.faq-hero {
    padding: 80px 0;
}

.faq-content {
    padding: 80px 0;
}

.faq-image-container {
    position: relative;
}

.faq-image-container img {
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.faq-title {
    color: #2f496e;
    font-weight: 600;
    font-size: 2rem;
}

.faq-accordion .accordion-item {
    border: none;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.faq-question {
    background: white;
    border: none;
    padding: 20px 25px;
    font-weight: 600;
    color: #2f496e;
    border-radius: 8px;
    box-shadow: none;
    display: flex;
    align-items: center;
    text-align: left;
}

.faq-question:not(.collapsed) {
    background: #2f496e;
    color: white;
}

.faq-question:focus {
    box-shadow: none;
    border: none;
}

.faq-question::after {
    content: '+';
    font-size: 1.5rem;
    font-weight: bold;
    margin-left: auto;
    transition: transform 0.3s ease;
}

.faq-question:not(.collapsed)::after {
    content: '−';
    transform: rotate(0deg);
}

/* Hide default Bootstrap accordion button icon */
.faq-question .accordion-button::after,
.accordion-button::after,
.faq-question::after {
    display: none !important;
}

/* Ensure only our custom icon shows */
.faq-question {
    position: relative;
}

.faq-question::before {
    display: none !important;
}

.faq-number {
    color: #1e40af;
    font-weight: bold;
    margin-right: 15px;
    font-size: 1.1rem;
}

.faq-question:not(.collapsed) .faq-number {
    color: #ffd700;
}

.faq-answer {
    padding: 25px;
    background: #f8f9fa;
    color: #666;
    line-height: 1.6;
    border-top: 1px solid #e9ecef;
}

.questions-cta {
    padding: 80px 0;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.cta-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .faq-hero {
        padding: 60px 0;
    }

    .faq-content {
        padding: 60px 0;
    }

    .faq-title {
        font-size: 1.75rem;
    }

    .cta-title {
        font-size: 2rem;
    }

    .faq-question {
        padding: 15px 20px;
        font-size: 0.95rem;
    }

    .faq-answer {
        padding: 20px;
    }
}

/* ===== DOWNLOADS PAGE STYLES ===== */

.downloads-hero {
    padding: 80px 0;
}

.downloads-content {
    padding: 80px 0;
}

.downloads-tabs-container {
    margin-bottom: 50px;
}

.downloads-nav .nav-link {
    background: white;
    border: 2px solid #e9ecef;
    color: #2f496e;
    padding: 15px 25px;
    margin: 0 10px 10px 0;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.downloads-nav .nav-link:hover {
    background: #f8f9fa;
    border-color: #2f496e;
    color: #2f496e;
    transform: translateY(-2px);
}

.downloads-nav .nav-link.active {
    background: #2f496e;
    border-color: #2f496e;
    color: white;
}

.downloads-nav .nav-link .badge {
    background: rgba(255, 255, 255, 0.2) !important;
}

.downloads-nav .nav-link.active .badge {
    background: rgba(255, 255, 255, 0.3) !important;
}

.download-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.download-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(47, 73, 110, 0.15);
}

.download-card-header {
    padding: 25px 25px 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.download-icon {
    font-size: 2.5rem;
}

.download-meta {
    text-align: right;
}

.download-meta .file-type {
    display: block;
    background: #e9ecef;
    color: #495057;
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 4px;
    margin-bottom: 5px;
}

.download-meta .file-size {
    font-size: 0.85rem;
}

.download-card-body {
    padding: 0 25px 15px;
    flex-grow: 1;
}

.download-title {
    color: #2f496e;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 15px;
    line-height: 1.4;
}

.download-description {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 15px;
}

.download-stats {
    margin-top: auto;
}

.download-card-footer {
    padding: 20px 25px;
    background: #f8f9fa;
    margin-top: auto;
}

.download-btn {
    background: #2f496e;
    border-color: #2f496e;
    font-weight: 600;
    padding: 12px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.download-btn:hover {
    background: #1e40af;
    border-color: #1e40af;
    transform: translateY(-1px);
}

.empty-state {
    padding: 80px 20px;
}

.downloads-support {
    padding: 80px 0;
}

.support-actions .btn {
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 8px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .downloads-hero {
        padding: 60px 0;
    }

    .downloads-content {
        padding: 60px 0;
    }

    .downloads-nav .nav-link {
        padding: 12px 20px;
        margin: 0 5px 10px 0;
        font-size: 0.9rem;
    }

    .download-card-header {
        padding: 20px 20px 10px;
    }

    .download-card-body {
        padding: 0 20px 10px;
    }

    .download-card-footer {
        padding: 15px 20px;
    }

    .download-icon {
        font-size: 2rem;
    }

    .support-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
}

/* ===== SCROLL ANIMATIONS ===== */

/* Fade in from bottom */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Fade in from left */
@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Fade in from right */
@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scale in */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Scroll animation classes */
.scroll-animate {
    opacity: 0;
    transition: all 0.6s ease;
}

.scroll-animate.animate {
    opacity: 1;
}

.fade-up {
    animation: fadeInUp 0.8s ease forwards;
}

.fade-left {
    animation: fadeInLeft 0.8s ease forwards;
}

.fade-right {
    animation: fadeInRight 0.8s ease forwards;
}

.scale-in {
    animation: scaleIn 0.8s ease forwards;
}

/* Staggered animations */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }

/* Parallax effect */
.parallax-element {
    transition: transform 0.1s ease-out;
}

/* ===== MODERN PRODUCT DETAIL STYLES ===== */

.product-info-modern {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
}

.section-title-modern {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2f496e;
    margin-bottom: 1rem;
}

.title-accent {
    color: #1e40af;
    position: relative;
}

.title-accent::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #1e40af 0%, #2f496e 100%);
    border-radius: 2px;
}

.section-subtitle-modern {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 3rem;
}

.info-card-modern {
    background: white;
    border-radius: 20px;
    padding: 0;
    height: 100%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    overflow: hidden;
}

.info-card-modern:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(47, 73, 110, 0.15);
    border-color: #2f496e;
}

.card-header-modern {
    background: linear-gradient(135deg, #1e40af 0%, #2f496e 100%);
    color: white;
    padding: 25px 30px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 10px rgba(30, 64, 175, 0.15);
}

.card-icon-modern {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.card-title-modern {
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0;
}

.card-content-modern {
    padding: 30px;
    flex-grow: 1;
}

.quick-specs-modern {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin-top: 20px;
}

.spec-row-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.spec-row-modern:last-child {
    border-bottom: none;
}

.spec-label-modern {
    font-weight: 600;
    color: #2f496e;
}

.spec-value-modern {
    color: #495057;
    text-align: right;
}

.specs-content-modern,
.features-content-modern,
.applications-content-modern {
    line-height: 1.7;
}

.specs-content-modern ul,
.features-content-modern ul,
.applications-content-modern ul {
    list-style: none;
    padding-left: 0;
}

.specs-content-modern ul li,
.features-content-modern ul li,
.applications-content-modern ul li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 10px;
}

.specs-content-modern ul li::before,
.features-content-modern ul li::before,
.applications-content-modern ul li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 0;
    color: #1e40af;
    font-weight: bold;
    font-size: 14px;
}

.downloads-card-modern {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.downloads-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.download-item-modern-link {
    text-decoration: none;
    color: inherit;
    display: block;
    transition: all 0.3s ease;
}

.download-item-modern-link:hover {
    text-decoration: none;
    color: inherit;
}

.download-item-modern {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.download-item-modern-link:hover .download-item-modern {
    border-color: #2f496e;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(47, 73, 110, 0.1);
}

.download-icon-modern {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.download-info-modern {
    flex-grow: 1;
}

.download-title-modern {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2f496e;
    margin-bottom: 5px;
}

.download-desc-modern {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.btn-download-modern {
    background: linear-gradient(135deg, #1e40af 0%, #2f496e 100%);
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.btn-download-modern:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(30, 64, 175, 0.3);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .section-title-modern {
        font-size: 2rem;
    }

    .card-header-modern {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .card-content-modern {
        padding: 20px;
    }

    .downloads-grid-modern {
        grid-template-columns: 1fr;
    }

    .download-item-modern {
        flex-direction: column;
        text-align: center;
    }
}

/* ===== HEADER CUSTOMIZATIONS ===== */

/* Hide default phone number in Odoo header */
.navbar .o_header_contact_info,
.navbar .contact-info,
.navbar .phone-number,
.navbar [href^="tel:"],
.navbar .fa-phone,
.navbar .fa-mobile,
.navbar .fa-mobile-alt {
    display: none !important;
}

/* Hide any default contact info in header */
.navbar .nav-contact-info,
.navbar .contact-item {
    display: none !important;
}

/* ===== CREATIVE BACKGROUND PATTERNS ===== */

/* Geometric Pattern Background */
.bg-pattern-geometric {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.bg-pattern-geometric::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(47, 73, 110, 0.1) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(30, 64, 175, 0.1) 2px, transparent 2px);
    background-size: 50px 50px;
    background-position: 0 0, 25px 25px;
    pointer-events: none;
}

/* Hexagon Pattern Background */
.bg-pattern-hexagon {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
}

.bg-pattern-hexagon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232f496e' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
}

/* Circuit Pattern Background */
.bg-pattern-circuit {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    position: relative;
}

.bg-pattern-circuit::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%231e40af' fill-opacity='0.03'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z'/%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
}

/* Gradient Mesh Background */
.bg-gradient-mesh {
    background:
        radial-gradient(circle at 20% 50%, rgba(47, 73, 110, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(30, 64, 175, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(47, 73, 110, 0.05) 0%, transparent 50%),
        linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

/* Subtle Wave Pattern */
.bg-pattern-waves {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.bg-pattern-waves::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 0h2v20H9V0zm25.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm-2.15 21.906a5 5 0 01-8.718 4.804l-1.34.772a5 5 0 11-9.8-1.98l1.34-.772a5 5 0 018.718-4.804l1.34-.772a5 5 0 119.8 1.98l-1.34.772z' fill='%232f496e' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
    pointer-events: none;
}

/* Apply patterns to sections */
.section-pattern-1 {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    position: relative;
}

.section-pattern-1::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(45deg, rgba(47, 73, 110, 0.02) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(47, 73, 110, 0.02) 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, rgba(30, 64, 175, 0.02) 75%),
        linear-gradient(-45deg, transparent 75%, rgba(30, 64, 175, 0.02) 75%);
    background-size: 30px 30px;
    background-position: 0 0, 0 15px, 15px -15px, -15px 0px;
    pointer-events: none;
}

.section-pattern-2 {
    background:
        radial-gradient(circle at 25% 25%, rgba(47, 73, 110, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(30, 64, 175, 0.05) 0%, transparent 50%),
        linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

/* ===== HTML FIELD CONTENT STYLING ===== */

/* Enhanced HTML Field Content Styling for ul/li elements */
.specs-content-modern ul,
.features-content-modern ul,
.applications-content-modern ul,
.card-content-modern ul,
.product-detail-content ul,
.content-body-premium ul,
.tab-pane ul {
    list-style: none;
    padding-left: 0;
    margin: 15px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 15px;
    padding: 25px;
    border-left: 4px solid #2f496e;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: relative;
}

.specs-content-modern ul::before,
.features-content-modern ul::before,
.applications-content-modern ul::before,
.card-content-modern ul::before,
.product-detail-content ul::before,
.content-body-premium ul::before,
.tab-pane ul::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #2f496e 0%, #1e40af 100%);
    border-radius: 15px 15px 0 0;
}

.specs-content-modern ul li,
.features-content-modern ul li,
.applications-content-modern ul li,
.card-content-modern ul li,
.product-detail-content ul li,
.content-body-premium ul li,
.tab-pane ul li {
    position: relative;
    padding: 15px 0 15px 40px;
    margin-bottom: 12px;
    line-height: 1.8;
    color: #495057;
    border-bottom: 1px solid rgba(47, 73, 110, 0.08);
    transition: all 0.3s ease;
    font-size: 0.95rem;
    word-wrap: break-word;
    white-space: pre-wrap;
    overflow-wrap: break-word;
}

.specs-content-modern ul li:last-child,
.features-content-modern ul li:last-child,
.applications-content-modern ul li:last-child,
.card-content-modern ul li:last-child,
.product-detail-content ul li:last-child,
.content-body-premium ul li:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.specs-content-modern ul li::before,
.features-content-modern ul li::before,
.applications-content-modern ul li::before,
.card-content-modern ul li::before,
.product-detail-content ul li::before,
.content-body-premium ul li::before,
.tab-pane ul li::before {
    content: '▶';
    position: absolute;
    left: 0;
    top: 15px;
    color: white;
    font-size: 10px;
    font-weight: bold;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(47, 73, 110, 0.2);
}

.specs-content-modern ul li:hover,
.features-content-modern ul li:hover,
.applications-content-modern ul li:hover,
.card-content-modern ul li:hover,
.product-detail-content ul li:hover,
.content-body-premium ul li:hover,
.tab-pane ul li:hover {
    background: rgba(47, 73, 110, 0.05);
    padding-left: 45px;
    color: #2f496e;
    border-radius: 8px;
    margin-left: -5px;
    margin-right: -5px;
    padding-right: 10px;
}

.specs-content-modern ul li:hover::before,
.features-content-modern ul li:hover::before,
.applications-content-modern ul li:hover::before,
.card-content-modern ul li:hover::before,
.product-detail-content ul li:hover::before,
.content-body-premium ul li:hover::before,
.tab-pane ul li:hover::before {
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

/* Style for paragraphs in HTML content */
.specs-content-modern p,
.features-content-modern p,
.applications-content-modern p,
.card-content-modern p,
.product-detail-content p {
    line-height: 1.7;
    margin-bottom: 15px;
    color: #495057;
}

/* Style for headings in HTML content */
.specs-content-modern h1, .specs-content-modern h2, .specs-content-modern h3,
.specs-content-modern h4, .specs-content-modern h5, .specs-content-modern h6,
.features-content-modern h1, .features-content-modern h2, .features-content-modern h3,
.features-content-modern h4, .features-content-modern h5, .features-content-modern h6,
.applications-content-modern h1, .applications-content-modern h2, .applications-content-modern h3,
.applications-content-modern h4, .applications-content-modern h5, .applications-content-modern h6,
.card-content-modern h1, .card-content-modern h2, .card-content-modern h3,
.card-content-modern h4, .card-content-modern h5, .card-content-modern h6,
.product-detail-content h1, .product-detail-content h2, .product-detail-content h3,
.product-detail-content h4, .product-detail-content h5, .product-detail-content h6 {
    color: #2f496e;
    font-weight: 600;
    margin-top: 20px;
    margin-bottom: 15px;
}

/* Style for strong/bold text */
.specs-content-modern strong,
.features-content-modern strong,
.applications-content-modern strong,
.card-content-modern strong,
.product-detail-content strong {
    color: #2f496e;
    font-weight: 600;
}

/* Style for tables in HTML content */
.specs-content-modern table,
.features-content-modern table,
.applications-content-modern table,
.card-content-modern table,
.product-detail-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.specs-content-modern table th,
.features-content-modern table th,
.applications-content-modern table th,
.card-content-modern table th,
.product-detail-content table th {
    background: #2f496e;
    color: white;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
}

.specs-content-modern table td,
.features-content-modern table td,
.applications-content-modern table td,
.card-content-modern table td,
.product-detail-content table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
}

.specs-content-modern table tr:last-child td,
.features-content-modern table tr:last-child td,
.applications-content-modern table tr:last-child td,
.card-content-modern table tr:last-child td,
.product-detail-content table tr:last-child td {
    border-bottom: none;
}

/* Enhanced styling for ordered lists */
.specs-content-modern ol,
.features-content-modern ol,
.applications-content-modern ol,
.card-content-modern ol,
.product-detail-content ol,
.content-body-premium ol {
    padding-left: 0;
    margin: 15px 0;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    border-left: 4px solid #1e40af;
    counter-reset: custom-counter;
}

.specs-content-modern ol li,
.features-content-modern ol li,
.applications-content-modern ol li,
.card-content-modern ol li,
.product-detail-content ol li,
.content-body-premium ol li {
    position: relative;
    padding: 12px 0 12px 40px;
    margin-bottom: 10px;
    line-height: 1.7;
    color: #495057;
    border-bottom: 1px solid rgba(30, 64, 175, 0.1);
    counter-increment: custom-counter;
    transition: all 0.3s ease;
}

.specs-content-modern ol li:last-child,
.features-content-modern ol li:last-child,
.applications-content-modern ol li:last-child,
.card-content-modern ol li:last-child,
.product-detail-content ol li:last-child,
.content-body-premium ol li:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.specs-content-modern ol li::before,
.features-content-modern ol li::before,
.applications-content-modern ol li::before,
.card-content-modern ol li::before,
.product-detail-content ol li::before,
.content-body-premium ol li::before {
    content: counter(custom-counter);
    position: absolute;
    left: 0;
    top: 12px;
    color: white;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    font-size: 12px;
    font-weight: bold;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.specs-content-modern ol li:hover,
.features-content-modern ol li:hover,
.applications-content-modern ol li:hover,
.card-content-modern ol li:hover,
.product-detail-content ol li:hover,
.content-body-premium ol li:hover {
    background: rgba(30, 64, 175, 0.05);
    padding-left: 45px;
    color: #1e40af;
}

.specs-content-modern ol li:hover::before,
.features-content-modern ol li:hover::before,
.applications-content-modern ol li:hover::before,
.card-content-modern ol li:hover::before,
.product-detail-content ol li:hover::before,
.content-body-premium ol li:hover::before {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

/* Nested lists styling */
.specs-content-modern ul ul,
.features-content-modern ul ul,
.applications-content-modern ul ul,
.card-content-modern ul ul,
.product-detail-content ul ul,
.content-body-premium ul ul {
    background: white;
    border-left: 2px solid #6c757d;
    margin: 10px 0 10px 20px;
    padding: 15px;
}

.specs-content-modern ul ul li::before,
.features-content-modern ul ul li::before,
.applications-content-modern ul ul li::before,
.card-content-modern ul ul li::before,
.product-detail-content ul ul li::before,
.content-body-premium ul ul li::before {
    content: '→';
    color: #6c757d;
    background: rgba(108, 117, 125, 0.1);
}

/* ===== END HTML FIELD CONTENT STYLING ===== */

/* ===== END CREATIVE BACKGROUND PATTERNS ===== */

/* ===== END HEADER CUSTOMIZATIONS ===== */

/* ===== PREMIUM PRODUCT DETAIL STYLES ===== */

.product-info-premium {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
}

.section-title-premium {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2f496e;
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle-premium {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 3rem;
    text-align: center;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Premium Navigation Pills */
.nav-premium {
    background: white;
    border-radius: 50px;
    padding: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.nav-link-premium {
    border-radius: 40px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    color: #6c757d !important;
    border: none !important;
    background: transparent !important;
    transition: all 0.3s ease !important;
    margin: 0 4px !important;
}

.nav-link-premium:hover {
    color: #2f496e !important;
    background: rgba(47, 73, 110, 0.1) !important;
    transform: translateY(-1px);
}

.nav-link-premium.active {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(47, 73, 110, 0.3);
}

.nav-link-premium.active:hover {
    background: linear-gradient(135deg, #1e40af 0%, #2f496e 100%) !important;
    color: white !important;
}

/* Tab Content */
.tab-content-premium {
    margin-top: 40px;
}

.tab-pane {
    padding: 0;
    margin: 0;
}

.content-card-premium {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    margin: 0;
}

.content-title-premium {
    color: #2f496e;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 15px;
}

.content-title-premium::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #2f496e 0%, #1e40af 100%);
    border-radius: 2px;
}

.content-body-premium {
    line-height: 1.8;
    color: #495057;
    font-size: 1rem;
}

/* Quick Specs Card */
.specs-card-premium {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 20px;
    padding: 30px;
    color: white;
    height: 100%;
    box-shadow: 0 5px 15px rgba(47, 73, 110, 0.1);
}

.specs-title-premium {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
}

.specs-list-premium {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.spec-item-premium {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.spec-item-premium:last-child {
    border-bottom: none;
}

.spec-label-premium {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
}

.spec-value-premium {
    font-weight: 700;
    color: white;
    text-align: right;
    font-size: 0.95rem;
}

/* Downloads Section */
.downloads-section-premium {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.download-categories-premium {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.download-category-premium {
    border-radius: 16px;
    overflow: hidden;
}

.category-header-premium {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 30px;
    display: flex;
    align-items: center;
    gap: 15px;
    border-bottom: 1px solid #e9ecef;
}

.category-icon-premium {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.category-icon-premium.brochure {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
}

.category-icon-premium.datasheet {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
}

.category-icon-premium.manual {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
}

.category-icon-premium.certificate {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
}

.category-title-premium {
    color: #2f496e;
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0;
    flex-grow: 1;
}

.category-count-premium {
    background: #2f496e;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.download-grid-premium {
    padding: 30px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.download-card-premium {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 16px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.download-card-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(47, 73, 110, 0.05), transparent);
    transition: left 0.5s ease;
}

.download-card-premium:hover {
    border-color: #2f496e;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(47, 73, 110, 0.15);
    text-decoration: none;
    color: inherit;
}

.download-card-premium:hover::before {
    left: 100%;
}

.download-card-premium.brochure:hover {
    border-color: #2f496e;
    box-shadow: 0 10px 25px rgba(47, 73, 110, 0.15);
}

.download-card-premium.datasheet:hover {
    border-color: #2f496e;
    box-shadow: 0 10px 25px rgba(47, 73, 110, 0.15);
}

.download-card-premium.manual:hover {
    border-color: #2f496e;
    box-shadow: 0 10px 25px rgba(47, 73, 110, 0.15);
}

.download-card-premium.certificate:hover {
    border-color: #2f496e;
    box-shadow: 0 10px 25px rgba(47, 73, 110, 0.15);
}

.download-icon-premium {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    flex-shrink: 0;
}

.download-card-premium.brochure .download-icon-premium {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
}

.download-card-premium.datasheet .download-icon-premium {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
}

.download-card-premium.manual .download-icon-premium {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
}

.download-card-premium.specification .download-icon-premium {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
}

.download-card-premium.certificate .download-icon-premium {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
}

.download-content-premium {
    flex-grow: 1;
}

.download-name-premium {
    color: #2f496e;
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.download-desc-premium {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 12px;
    line-height: 1.5;
}

.download-meta-premium {
    display: flex;
    gap: 10px;
    align-items: center;
}

.file-type-premium {
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.file-size-premium {
    color: #6c757d;
    font-size: 0.8rem;
}

.download-action-premium {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.download-card-premium:hover .download-action-premium {
    transform: scale(1.1);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .section-title-premium {
        font-size: 2rem;
    }

    .product-info-premium {
        padding: 30px 0;
    }

    .nav-premium {
        flex-wrap: wrap;
        padding: 10px;
        border-radius: 15px;
    }

    .nav-link-premium {
        margin: 3px !important;
        padding: 10px 16px !important;
        font-size: 0.9rem !important;
        text-align: center;
        flex: 1;
        min-width: calc(50% - 6px);
    }

    .tab-content-premium {
        margin-top: 25px;
    }

    .content-card-premium,
    .downloads-section-premium {
        padding: 25px 20px;
        border-radius: 15px;
    }

    .content-title-premium {
        font-size: 1.3rem;
        margin-bottom: 20px;
    }

    .specs-card-premium {
        margin-top: 20px;
        padding: 25px 20px;
    }

    .download-grid-premium {
        grid-template-columns: 1fr;
        padding: 20px 15px;
        gap: 15px;
    }

    .download-card-premium {
        flex-direction: column;
        text-align: center;
        padding: 20px 15px;
    }

    .download-icon-premium {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
        margin-bottom: 15px;
    }

    .category-header-premium {
        flex-direction: column;
        text-align: center;
        gap: 10px;
        padding: 15px 20px;
    }

    .category-icon-premium {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .category-title-premium {
        font-size: 1.1rem;
    }
}

/* Extra small devices */
@media (max-width: 576px) {
    .nav-link-premium {
        min-width: 100%;
        margin: 2px 0 !important;
    }

    .content-card-premium,
    .downloads-section-premium {
        padding: 20px 15px;
    }

    .section-title-premium {
        font-size: 1.8rem;
    }

    .section-subtitle-premium {
        font-size: 1rem;
        padding: 0 15px;
    }
}

/* ===== ENHANCED RELATED PRODUCTS STYLES ===== */

.related-products-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    position: relative;
}

.product-card-enhanced {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card-enhanced:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(47, 73, 110, 0.15);
    border-color: #2f496e;
}

.product-image-enhanced {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.product-image-enhanced img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card-enhanced:hover .product-image-enhanced img {
    transform: scale(1.05);
}

.product-overlay-enhanced {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(47, 73, 110, 0.8) 0%, rgba(30, 64, 175, 0.8) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.product-card-enhanced:hover .product-overlay-enhanced {
    opacity: 1;
}

.product-content-enhanced {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.product-category-badge {
    display: inline-block;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 15px;
    align-self: flex-start;
}

.product-title-enhanced {
    color: #2f496e;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 15px;
    line-height: 1.3;
}

.product-description-enhanced {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 20px;
    flex-grow: 1;
}

.product-features-enhanced {
    margin-bottom: 20px;
}

.features-list-enhanced {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list-enhanced li {
    position: relative;
    padding: 5px 0 5px 20px;
    color: #495057;
    font-size: 0.9rem;
    line-height: 1.5;
}

.features-list-enhanced li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 5px;
    color: #28a745;
    font-weight: bold;
    font-size: 0.9rem;
}

.product-action-enhanced {
    padding: 0 25px 25px;
    margin-top: auto;
}

.product-action-enhanced .btn {
    border-radius: 12px;
    padding: 12px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.product-action-enhanced .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(47, 73, 110, 0.3);
}

/* ===== END ENHANCED RELATED PRODUCTS STYLES ===== */

/* ===== END PREMIUM PRODUCT DETAIL STYLES ===== */

/* ===== END MODERN PRODUCT DETAIL STYLES ===== */

/* ===== END SCROLL ANIMATIONS ===== */

/* ===== END DOWNLOADS PAGE STYLES ===== */

/* ===== END FAQ PAGE STYLES ===== */

/* ===== END BREADCRUMB STYLES ===== */

/* ===== END COURSES PAGE STYLES ===== */

/* ===== END FOOTER STYLES ===== */

/* Override any orange/warning backgrounds on contact page */
.contact-info,
.contact-form-section,
.map-section,
.contact-hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
}

.contact-info.bg-light,
.map-section.bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

/* Ensure no orange/warning colors in contact page */
.contact-info .alert-warning,
.contact-form-section .alert-warning,
.map-section .alert-warning,
.contact-hero .alert-warning {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
    border-color: #2f496e !important;
    color: #2f496e !important;
}

/* Override any Bootstrap warning classes */
.bg-warning {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%) !important;
    color: white !important;
}

.alert-warning {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
    border-color: #2f496e !important;
    color: #2f496e !important;
}

/* Professional contact page sections */
.contact-info-professional {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
}

.contact-form-professional {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.section-title-professional {
    color: #2f496e;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle-professional {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 3rem;
    text-align: center;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Enhanced contact cards */
.contact-card-enhanced {
    background: white;
    border-radius: 20px;
    padding: 35px;
    height: 100%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    text-align: center;
    border: 1px solid #e9ecef;
}

.contact-card-enhanced:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(47, 73, 110, 0.15);
    border-color: #2f496e;
}

.contact-icon-enhanced {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    color: white;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.contact-card-enhanced:hover .contact-icon-enhanced {
    transform: scale(1.1);
}

/* ===== CREATIVE CONTACT PAGE STYLES ===== */

/* Creative Hero Section */
.contact-hero-creative {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
    overflow: hidden;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(47, 73, 110, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(30, 64, 175, 0.05) 0%, transparent 50%),
        linear-gradient(45deg, transparent 49%, rgba(47, 73, 110, 0.02) 50%, transparent 51%);
    animation: patternFloat 20s ease-in-out infinite;
}

@keyframes patternFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 20px;
    animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.contact-hero-title-creative {
    font-size: 3.5rem;
    font-weight: 800;
    color: #2f496e;
    line-height: 1.2;
    margin-bottom: 25px;
}

.title-highlight {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.title-highlight::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #2f496e 0%, #1e40af 100%);
    border-radius: 2px;
}

.contact-hero-subtitle-creative {
    font-size: 1.2rem;
    color: #6c757d;
    line-height: 1.7;
    margin-bottom: 40px;
    max-width: 500px;
}

/* Stats Grid */
.contact-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
    margin-top: 40px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(47, 73, 110, 0.15);
    border-color: #2f496e;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2f496e;
    line-height: 1;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Hero Visual Elements */
.contact-hero-visual {
    position: relative;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-cards {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-card {
    position: absolute;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.floating-card i {
    font-size: 1.5rem;
    color: #2f496e;
}

.floating-card span {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
}

.floating-card.card-1 {
    top: 20%;
    left: 10%;
    animation: float1 6s ease-in-out infinite;
}

.floating-card.card-2 {
    top: 60%;
    right: 15%;
    animation: float2 8s ease-in-out infinite;
}

.floating-card.card-3 {
    bottom: 20%;
    left: 20%;
    animation: float3 7s ease-in-out infinite;
}

@keyframes float1 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(2deg); }
}

@keyframes float2 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(-2deg); }
}

@keyframes float3 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-25px) rotate(1deg); }
}

.hero-main-visual {
    position: relative;
    z-index: 2;
}

.visual-circle {
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    border: 2px solid rgba(47, 73, 110, 0.2);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

.visual-dots {
    position: absolute;
    bottom: -30px;
    left: -30px;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, #2f496e 2px, transparent 2px);
    background-size: 20px 20px;
    opacity: 0.3;
    animation: pulse 4s ease-in-out infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

.hero-image {
    width: 400px;
    height: 300px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.hero-image:hover {
    transform: scale(1.05);
    box-shadow: 0 25px 50px rgba(47, 73, 110, 0.2);
}

/* Creative Contact Information Section */
.contact-info-creative {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.section-header-creative {
    max-width: 600px;
    margin: 0 auto;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(47, 73, 110, 0.1);
    color: #2f496e;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.section-title-creative {
    font-size: 2.8rem;
    font-weight: 700;
    color: #2f496e;
    line-height: 1.3;
    margin-bottom: 20px;
}

.title-accent {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle-creative {
    font-size: 1.1rem;
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Timeline Layout */
.contact-cards-timeline {
    position: relative;
    padding: 40px 0;
}

.timeline-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #2f496e 20%, #1e40af 50%, #2f496e 80%, transparent 100%);
    transform: translateY(-50%);
    z-index: 1;
}

.contact-card-timeline {
    position: relative;
    z-index: 2;
}

.timeline-marker {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
}

.marker-dot {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border-radius: 50%;
    position: relative;
    box-shadow: 0 0 0 4px white, 0 0 0 6px #e9ecef;
}

.marker-pulse {
    position: absolute;
    top: -10px;
    left: -10px;
    width: 40px;
    height: 40px;
    border: 2px solid #2f496e;
    border-radius: 50%;
    opacity: 0;
    animation: markerPulse 2s ease-out infinite;
}

@keyframes markerPulse {
    0% {
        transform: scale(0.5);
        opacity: 1;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

/* Creative Contact Cards */
.contact-card-creative {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    margin-top: 40px;
}

.contact-card-creative:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(47, 73, 110, 0.15);
    border-color: #2f496e;
}

.card-header-creative {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    padding: 25px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.contact-icon-creative {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    backdrop-filter: blur(10px);
}

.location-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
}

.location-badge.usa {
    background: rgba(255, 255, 255, 0.25);
}

.location-badge.support {
    background: rgba(255, 255, 255, 0.3);
    animation: supportBlink 2s ease-in-out infinite;
}

@keyframes supportBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.card-content-creative {
    padding: 30px;
}

.office-title {
    color: #2f496e;
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.company-name {
    color: #1e40af;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.address-block,
.office-hours,
.partnership-info,
.social-connect {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #2f496e;
}

.address-block i,
.office-hours i,
.partnership-info i,
.social-connect i {
    color: #2f496e;
    font-size: 1.2rem;
    margin-top: 2px;
    flex-shrink: 0;
}

.address-text {
    line-height: 1.6;
    color: #495057;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.contact-method:hover {
    background: #e9ecef;
    border-left-color: #2f496e;
    transform: translateX(5px);
}

.contact-method i {
    color: #2f496e;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.contact-method span {
    color: #495057;
    font-weight: 500;
}

.support-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 20px;
    font-style: italic;
}

.social-title {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #2f496e;
    font-weight: 600;
    margin-bottom: 15px;
}

.social-links-creative {
    display: flex;
    gap: 12px;
}

.social-link-creative {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-link-creative.facebook {
    background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
}

.social-link-creative.instagram {
    background: linear-gradient(135deg, #e4405f 0%, #f77737 50%, #fccc63 100%);
}

.social-link-creative:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* Creative Contact Form Section */
.contact-form-creative {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
    overflow: hidden;
}

.form-background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 70% 30%, rgba(30, 64, 175, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 30% 70%, rgba(47, 73, 110, 0.03) 0%, transparent 50%);
    animation: formPatternMove 15s ease-in-out infinite;
}

@keyframes formPatternMove {
    0%, 100% { transform: translateX(0px) translateY(0px); }
    50% { transform: translateX(20px) translateY(-20px); }
}

.form-section-header {
    position: relative;
    z-index: 2;
}

.form-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 25px;
    box-shadow: 0 5px 15px rgba(47, 73, 110, 0.3);
}

.form-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2f496e;
    line-height: 1.3;
    margin-bottom: 20px;
}

.form-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    line-height: 1.6;
    max-width: 500px;
    margin: 0 auto;
}

.contact-form-container-creative {
    background: white;
    border-radius: 25px;
    padding: 50px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    position: relative;
    overflow: hidden;
}

.contact-form-container-creative::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #2f496e 0%, #1e40af 50%, #2f496e 100%);
}

/* Enhanced Form Styling */
.contact-form .form-label {
    font-weight: 700;
    color: #2f496e;
    margin-bottom: 10px;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.contact-form .form-control,
.contact-form .form-select {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px 20px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
    position: relative;
}

.contact-form .form-control:focus,
.contact-form .form-select:focus {
    border-color: #2f496e;
    box-shadow: 0 0 0 0.2rem rgba(47, 73, 110, 0.15);
    background: white;
    transform: translateY(-2px);
}

.contact-form .form-control::placeholder {
    color: #adb5bd;
    font-style: italic;
}

/* Form Submit Button Enhancement */
.contact-form .btn-primary {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border: none;
    padding: 15px 40px;
    border-radius: 12px;
    font-weight: 700;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-form .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.contact-form .btn-primary:hover::before {
    left: 100%;
}

.contact-form .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(47, 73, 110, 0.3);
}

/* Alert Enhancements */
.alert {
    border-radius: 15px;
    border: none;
    padding: 20px 25px;
    font-weight: 600;
    margin-bottom: 30px;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 5px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 5px solid #dc3545;
}

/* Mobile Responsiveness for Creative Design */
@media (max-width: 768px) {
    .contact-hero-title-creative {
        font-size: 2.5rem;
    }

    .contact-stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .floating-cards {
        display: none;
    }

    .hero-image {
        width: 100%;
        height: 250px;
    }

    .section-title-creative {
        font-size: 2.2rem;
    }

    .timeline-line {
        display: none;
    }

    .timeline-marker {
        position: relative;
        top: 0;
        left: 0;
        transform: none;
        margin-bottom: 20px;
        text-align: center;
    }

    .contact-card-creative {
        margin-top: 20px;
    }

    .contact-form-container-creative {
        padding: 30px 20px;
        border-radius: 20px;
    }

    .form-title {
        font-size: 2rem;
    }

    .card-content-creative {
        padding: 25px 20px;
    }
}

@media (max-width: 576px) {
    .contact-hero-title-creative {
        font-size: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .section-title-creative {
        font-size: 1.8rem;
    }

    .form-title {
        font-size: 1.8rem;
    }

    .contact-form-container-creative {
        padding: 25px 15px;
    }
}

/* ===== SEARCH RESULTS PAGE STYLES ===== */

.search-results-hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.search-results-title {
    color: #2f496e;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.search-results-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
}

.results-section {
    margin-bottom: 40px;
}

.results-section-title {
    color: #2f496e;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.results-count {
    color: #6c757d;
    font-size: 1rem;
    font-weight: 400;
}

.search-result-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    height: 100%;
}

.search-result-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(47, 73, 110, 0.15);
    border-color: #2f496e;
}

.result-image {
    height: 200px;
    overflow: hidden;
}

.result-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.search-result-card:hover .result-image img {
    transform: scale(1.05);
}

.result-content {
    padding: 20px;
}

.result-title {
    color: #2f496e;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.result-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 15px;
}

.result-meta {
    margin-bottom: 15px;
}

.result-category {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1565c0;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.no-results {
    padding: 60px 0;
}

.no-results-icon {
    animation: float 3s ease-in-out infinite;
}

.search-suggestions {
    margin-top: 30px;
}

/* Download Results Styling */
.download-result {
    border-left: 4px solid #2f496e;
}

.download-type-badge {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    margin-bottom: 10px;
}

.file-size {
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.75rem;
    margin-right: 8px;
}

.product-name {
    color: #6c757d;
    font-size: 0.8rem;
    font-style: italic;
}

/* Page Results Styling */
.page-results {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.page-result-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    border-left: 4px solid #28a745;
}

.page-result-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(40, 167, 69, 0.15);
    border-color: #28a745;
}

.page-title {
    margin-bottom: 10px;
}

.page-title a {
    color: #2f496e;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
}

.page-title a:hover {
    color: #1e40af;
    text-decoration: underline;
}

.page-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 15px;
}

/* Enhanced search result cards */
.search-result-card.download-result:hover {
    border-left-color: #1e40af;
}

/* Mobile responsiveness for search results */
@media (max-width: 768px) {
    .page-results {
        gap: 15px;
    }

    .page-result-item {
        padding: 15px;
    }

    .download-type-badge {
        font-size: 0.7rem;
        padding: 3px 6px;
    }
}

/* ===== END SEARCH RESULTS PAGE STYLES ===== */

/* ===== END CREATIVE CONTACT PAGE STYLES ===== */

/* ===== END CONTACT US PAGE STYLES ===== */

/* No Downloads Message Styling */
.no-downloads-message {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    padding: 40px;
    margin: 20px 0;
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.no-downloads-message:hover {
    border-color: #2f496e;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.no-downloads-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.no-downloads-message h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
}

.no-downloads-message p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 25px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.no-downloads-message .btn {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.no-downloads-message .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(47, 73, 110, 0.3);
}

/* ===== END PRODUCT DETAIL PAGE STYLES ===== */

/* ===== MODERN HOMEPAGE PRODUCT CARDS ===== */

.product-card-modern {
    position: relative;
    height: 100%;
    transition: all 0.3s ease;
}

.product-card-inner {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.product-card-modern:hover .product-card-inner {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(47, 73, 110, 0.15);
    border-color: #2f496e;
}

/* Product Image Section */
.product-image-modern {
    position: relative;
    overflow: hidden;
    height: 280px;
}

.image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.product-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
}

.product-card-modern:hover .product-img {
    transform: scale(1.1);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(47, 73, 110, 0.9) 0%, rgba(30, 64, 175, 0.9) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.product-card-modern:hover .image-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: white;
}

.quick-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 20px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    cursor: pointer;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.action-btn i {
    font-size: 1.1rem;
}

/* Product Badge */
.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 2;
    box-shadow: 0 4px 12px rgba(47, 73, 110, 0.3);
}

/* Product Content Section */
.product-content-modern {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.product-header {
    margin-bottom: 15px;
}

.product-title-modern {
    color: #2f496e;
    font-size: 1.3rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 8px;
    transition: color 0.3s ease;
}

.product-card-modern:hover .product-title-modern {
    color: #1e40af;
}

/* Rating styles removed as requested */

.product-description-modern {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 20px;
    flex-grow: 1;
}

/* Product Features */
.product-features-modern {
    margin-bottom: 20px;
}

.features-title {
    color: #2f496e;
    font-size: 0.9rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.features-list-modern {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.features-list-modern li {
    position: relative;
    padding-left: 20px;
    color: #495057;
    font-size: 0.85rem;
    line-height: 1.4;
}

.features-list-modern li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 0;
    color: #28a745;
    font-weight: bold;
    font-size: 0.9rem;
}

/* Specifications preview styles removed as requested */

/* Product Footer */
.product-footer-modern {
    padding: 20px 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #e9ecef;
    margin-top: auto;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.availability,
.support {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem;
    color: #495057;
}

.availability i {
    color: #28a745;
}

.support i {
    color: #2f496e;
}

.btn-view-details {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-view-details::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-view-details:hover::before {
    left: 100%;
}

.btn-view-details:hover {
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
    background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
}

.btn-view-details i {
    transition: transform 0.3s ease;
}

.btn-view-details:hover i {
    transform: translateX(3px);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .product-image-modern {
        height: 220px;
    }

    .product-content-modern {
        padding: 20px;
    }

    .product-title-modern {
        font-size: 1.2rem;
    }

    .product-description-modern {
        font-size: 0.9rem;
    }

    .product-footer-modern {
        padding: 15px 20px;
    }

    .product-meta {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .btn-view-details {
        width: 100%;
        padding: 14px 20px;
    }

    .quick-actions {
        flex-direction: column;
        gap: 10px;
    }

    .action-btn {
        padding: 10px 16px;
        font-size: 0.85rem;
    }
}

@media (max-width: 576px) {
    .product-image-modern {
        height: 200px;
    }

    .product-content-modern {
        padding: 15px;
    }

    .product-footer-modern {
        padding: 15px;
    }

    .specs-tags {
        justify-content: center;
    }

    .features-list-modern li {
        font-size: 0.8rem;
    }
}

/* Animation Enhancements */
.product-card-modern {
    animation: fadeInUp 0.6s ease-out;
}

.product-card-modern:nth-child(1) { animation-delay: 0.1s; }
.product-card-modern:nth-child(2) { animation-delay: 0.2s; }
.product-card-modern:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover Animation for Card Elements */
.product-card-modern:hover .product-badge {
    transform: scale(1.05);
}

.product-card-modern:hover .spec-tag {
    background: linear-gradient(135deg, #2f496e 0%, #1e40af 100%);
    color: white;
    border-color: #2f496e;
}

.product-card-modern:hover .stars i {
    animation: starTwinkle 0.5s ease-in-out;
}

@keyframes starTwinkle {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* ===== END MODERN HOMEPAGE PRODUCT CARDS ===== */

/* ===== END NEW PRODUCT CARD DESIGN ===== */

/* ===== END PRODUCTS PAGE STYLES ===== */

/* ===== END ENHANCED CTA SECTION ===== */

/* ===== END TESTIMONIALS SECTION ===== */

/* ===== END CLIENTS SECTION ===== */

/* ===== END ENHANCED FEATURED PRODUCTS ===== */

/* ===== END ENHANCED SOLUTION CARDS ===== */

/* ===== END ENHANCED TECHNICAL EXCELLENCE ===== */

/* ===== END CERTIFICATIONS SECTION ===== */

/* ===== END ABOUT US PAGE STYLES ===== */

/* ===== END ENHANCED UI ELEMENTS ===== */

/* Footer Styles - Industrial Equipment - Force All Selectors */
footer, .footer, #footer,
.o_footer, .s_footer,
body footer, html footer,
[data-name="Footer"],
.oe_structure footer,
.website_footer,
footer.bg-light,
footer.o_footer {
    background: var(--primary-blue-dark) !important;
    color: #ffffff !important;
    padding: 40px 0 20px !important;
    border-top: 5px solid var(--secondary-orange) !important;
    margin-top: 0 !important;
    min-height: 200px !important;
}

/* Force all footer content to be white */
footer *, .footer *, #footer *,
.o_footer *, .s_footer *,
footer.bg-light *,
footer.o_footer *,
.website_footer * {
    color: #ffffff !important;
    background: transparent !important;
}

/* Footer headings */
footer h1, footer h2, footer h3, footer h4, footer h5, footer h6,
.footer h1, .footer h2, .footer h3, .footer h4, .footer h5, .footer h6,
#footer h1, #footer h2, #footer h3, #footer h4, #footer h5, #footer h6,
.o_footer h1, .o_footer h2, .o_footer h3, .o_footer h4, .o_footer h5, .o_footer h6 {
    color: #ffffff !important;
    font-weight: 700 !important;
    margin-bottom: 1rem !important;
}

/* Footer text */
footer p, footer li, footer span, footer div,
.footer p, .footer li, .footer span, .footer div,
#footer p, #footer li, #footer span, #footer div,
.o_footer p, .o_footer li, .o_footer span, .o_footer div {
    color: #ffffff !important;
    line-height: 1.6 !important;
    opacity: 1 !important;
}

/* Footer links */
footer a, .footer a, #footer a, .o_footer a {
    color: #ffffff !important;
    text-decoration: none !important;
    transition: color 0.3s ease !important;
    opacity: 1 !important;
}

footer a:hover, .footer a:hover, #footer a:hover, .o_footer a:hover {
    color: #2f496e !important;
    text-decoration: none !important;
}

/* Override any Odoo default footer styles - Only for footer */
footer.bg-light {
    background: var(--primary-blue-dark) !important;
}

/* Force footer visibility */
footer, .footer, #footer, .o_footer {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* Replace bullet points with checkmarks and arrows */
ul li, ol li {
    list-style: none !important;
    position: relative !important;
    padding-left: 25px !important;
    margin-bottom: 8px !important;
}

/* Removed conflicting checkmark rules */

/* Right arrows for navigation and links */
.footer ul li:before,
.navigation ul li:before,
ul.nav-list li:before {
    content: "→" !important;
    color: var(--secondary-orange) !important;
    font-weight: bold !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    font-size: 1em !important;
}

/* Applications and general lists use checkmarks */
.applications ul li:before,
.specifications ul li:before,
ul.spec-list li:before,
ul.app-list li:before {
    content: "✓" !important;
    color: var(--primary-blue) !important;
    font-weight: bold !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    font-size: 1.1em !important;
}

/* Footer improvements */
footer .container {
    max-width: 1200px !important;
}

footer .row {
    align-items: flex-start !important;
}

footer .col-md-3, footer .col-md-4 {
    margin-bottom: 30px !important;
}

/* Footer list styling */
footer ul {
    padding-left: 0 !important;
    margin-bottom: 20px !important;
}

footer ul li {
    padding: 8px 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    transition: all 0.3s ease !important;
}

footer ul li:hover {
    padding-left: 10px !important;
    border-bottom-color: var(--secondary-orange) !important;
}

footer ul li:last-child {
    border-bottom: none !important;
}

/* Image Zoom Modal - REMOVED - Images now auto-fit */

/* Improved Parallel Sections Layout */
.parallel-sections-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1rem 0;
    margin-top: 0.5rem;
}

.parallel-sections-container .container {
    max-width: 1400px;
}

.parallel-section {
    background: #f8fafc;
    padding: 1rem;
    height: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    min-height: 250px;
}

.parallel-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.parallel-section-title {
    color: var(--primary-blue);
    font-size: 1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-align: left;
    padding-bottom: 0.3rem;
    border-bottom: 2px solid rgba(59, 130, 246, 0.2);
    position: relative;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
}

.parallel-section-title i {
    color: var(--primary-blue);
    margin-right: 0.5rem;
    font-size: 1.1rem;
    background: rgba(59, 130, 246, 0.1);
    padding: 0.3rem;
    border-radius: 4px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.parallel-section-content {
    color: var(--text-primary);
    line-height: 1.6;
}

/* Technical Specifications - Consistent List Style */
.spec-item {
    margin-bottom: 0.3rem;
    padding: 0.3rem 0 0.3rem 1.5rem;
    background: transparent;
    border-radius: 6px;
    font-size: 0.9rem;
    line-height: 1.4;
    color: var(--text-primary);
    font-weight: 500;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.spec-item:last-child {
    border-bottom: none;
}

.spec-item:hover {
    transform: translateY(-2px);
    color: var(--primary-blue);
}

.spec-item:before {
    content: "•";
    position: absolute;
    left: 0;
    top: 0.3rem;
    color: var(--primary-blue);
    font-weight: bold;
    font-size: 1rem;
}

.spec-item strong {
    color: var(--primary-blue);
    font-weight: 600;
    margin-right: 0.5rem;
}

.spec-item span {
    color: var(--text-primary);
}

/* Feature and Application Lists - Clean Style */
.feature-list,
.application-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-list li,
.application-list li {
    position: relative;
    padding: 0.3rem 0 0.3rem 1.5rem;
    margin-bottom: 0.3rem;
    color: var(--text-primary);
    background: transparent;
    border-radius: 6px;
    font-size: 0.9rem;
    line-height: 1.4;
    font-weight: 500;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
}

.feature-list li:last-child,
.application-list li:last-child {
    border-bottom: none;
}

.feature-list li:hover,
.application-list li:hover {
    transform: translateY(-2px);
    color: var(--primary-blue);
}

.feature-list li:before,
.application-list li:before {
    content: "•";
    position: absolute;
    left: 0;
    top: 0.3rem;
    color: var(--primary-blue);
    font-weight: bold;
    font-size: 1rem;
}

/* Remove underlines from all links */
.parallel-section a,
.download-item,
.feature-list a,
.application-list a,
.spec-item a {
    text-decoration: none !important;
}

.parallel-section a:hover,
.download-item:hover,
.feature-list a:hover,
.application-list a:hover,
.spec-item a:hover {
    text-decoration: none !important;
}

/* Enhanced Equal Height Columns */
.parallel-sections-container .row {
    display: flex;
    align-items: stretch;
    gap: 1rem;
    margin: 0 -0.5rem;
}

.parallel-sections-container .col-lg-4 {
    display: flex;
    flex: 1;
    padding: 0 0.5rem;
}

/* Improved Responsive Design */
@media (max-width: 991px) {
    .parallel-sections-container {
        padding: 3rem 0;
    }

    .parallel-sections-container .row {
        display: block;
        gap: 0;
        margin: 0;
    }

    .parallel-sections-container .col-lg-4 {
        display: block;
        margin-bottom: 2rem;
        padding: 0;
    }

    .parallel-section {
        margin-bottom: 0;
        padding: 2rem 1.5rem;
    }

    .parallel-section-title {
        font-size: 1.2rem;
    }
}

@media (max-width: 768px) {
    .parallel-sections-container {
        padding: 2rem 0;
    }

    .parallel-section {
        padding: 1.5rem 1rem;
        margin-bottom: 1.5rem;
    }

    .parallel-section-title {
        font-size: 1.1rem;
    }
}

/* Downloads Section - Parallel Layout */
.downloads-final-section {
    background: #f8f9fa;
    padding: 4rem 0;
    margin-top: 2rem;
}

.downloads-main-title {
    color: var(--primary-blue);
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 1rem;
    border-bottom: 3px solid var(--primary-blue);
}

.downloads-main-title i {
    color: var(--primary-blue);
    font-size: 1.8rem;
}

.download-column {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    height: 100%;
}

.download-category {
    margin-bottom: 2.5rem;
}

.download-category:last-child {
    margin-bottom: 0;
}

.download-category-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.download-category-header i {
    color: var(--primary-blue);
    font-size: 1.3rem;
    margin-right: 0.75rem;
    background: rgba(var(--primary-blue-rgb), 0.1);
    padding: 0.5rem;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.download-category-header h5 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.download-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    margin-bottom: 0.75rem;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.download-item:hover {
    background: white;
    border-color: var(--primary-blue);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: var(--text-primary);
}

.download-item:hover .download-action {
    background: var(--primary-blue);
    color: white;
}

.download-icon-wrapper {
    background: rgba(var(--primary-blue-rgb), 0.1);
    border-radius: 8px;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.download-icon-wrapper i {
    color: var(--primary-blue);
    font-size: 1.2rem;
}

.download-details {
    flex: 1;
}

.download-details h6 {
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.95rem;
    line-height: 1.3;
}

.download-details small {
    color: #6c757d;
    font-size: 0.8rem;
    line-height: 1.2;
}

.download-action {
    background: #e9ecef;
    border-radius: 6px;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.download-action i {
    color: #6c757d;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

/* No Downloads Message */
.no-downloads-message {
    text-align: center;
    padding: 3rem 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.no-downloads-message i {
    color: #9ca3af;
    font-size: 3rem;
    margin-bottom: 1rem;
}

.no-downloads-message h5 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.no-downloads-message p {
    color: #6c757d;
    margin: 0;
}

/* Product Short Description Styling */
.product-short-description {
    color: var(--text-primary) !important;
    font-size: 1.25rem !important;
    line-height: 1.6 !important;
    font-weight: 700 !important;
    margin-bottom: 1.5rem !important;
}

/* Go to Website Button - Odoo Ecommerce Style */
.btn-goto-website {
    background: var(--bg-white) !important;
    border: 2px solid var(--primary-blue) !important;
    color: var(--primary-blue) !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    border-radius: 6px !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 0.95rem !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1) !important;
}

.btn-goto-website:hover {
    background: var(--primary-blue) !important;
    color: white !important;
    border-color: var(--primary-blue-dark) !important;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2) !important;
    transform: translateY(-1px) !important;
    text-decoration: none !important;
}

.btn-goto-website:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2) !important;
}

.btn-goto-website i {
    font-size: 1rem !important;
    transition: transform 0.3s ease !important;
}

.btn-goto-website:hover i {
    transform: translateX(2px) !important;
}

/* Product Action Buttons Container */
.product-action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 2rem;
    align-items: center;
}

.product-action-buttons .btn {
    min-width: 160px;
    justify-content: center;
}

/* Product Description Lists - Force Checkmarks */
.product-description ul li,
.omisol-product-description ul li,
.product-features li,
.product-applications li,
.product-specifications li {
    list-style: none !important;
    position: relative !important;
    padding-left: 25px !important;
    margin-bottom: 8px !important;
}

.product-description ul li:before,
.omisol-product-description ul li:before,
.product-features li:before,
.product-applications li:before,
.product-specifications li:before {
    content: "✓" !important;
    color: var(--accent-green) !important;
    font-weight: bold !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    font-size: 1.2em !important;
    line-height: 1 !important;
}

/* Override any default list styles */
ul, ol {
    padding-left: 0 !important;
}

/* Ensure text content is properly spaced */
.product-description ul,
.omisol-product-description ul,
.product-features,
.product-applications,
.product-specifications {
    margin-bottom: 1.5rem !important;
    padding-left: 0 !important;
}

/* Additional list improvements */
.feature-list ul,
.product-features ul,
.product-applications ul {
    list-style: none !important;
    padding-left: 0 !important;
    margin: 0 !important;
}

.feature-list ul li,
.product-features ul li,
.product-applications ul li {
    padding: 0.3rem 0 0.3rem 1.5rem !important;
    margin-bottom: 0.3rem !important;
    position: relative !important;
    line-height: 1.4 !important;
}

/* Removed conflicting checkmark rules */

/* Footer specific improvements */
footer {
    background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue-darker) 100%) !important;
    box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1) !important;
}

footer .container {
    padding: 40px 15px 20px !important;
}

footer h3, footer h4, footer h5 {
    color: #ffffff !important;
    font-weight: 600 !important;
    margin-bottom: 20px !important;
    font-size: 1.1rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    border-bottom: 2px solid var(--secondary-orange) !important;
    padding-bottom: 8px !important;
    display: inline-block !important;
}

/* Replace Bullet Points with Checkmarks and Arrows */
/* Product Features, Applications, Specifications Lists */
.omisol-product-content ul,
.product-content-section ul,
.content-section ul,
.feature-list ul,
.applications-list ul,
.specifications-list ul {
    list-style: none !important;
    padding-left: 0 !important;
    margin-left: 0 !important;
}

.omisol-product-content ul li,
.product-content-section ul li,
.content-section ul li,
.feature-list ul li,
.applications-list ul li,
.specifications-list ul li {
    position: relative;
    padding-left: 1.5rem !important;
    margin-bottom: 0.3rem !important;
    line-height: 1.4 !important;
}

/* Removed conflicting checkmark rules */

/* Right Arrows for Applications and Specifications */
.applications-list ul li::before,
.specifications-list ul li::before {
    content: "▶" !important;
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-blue) !important;
    font-weight: 700 !important;
    font-size: 0.9rem !important;
    line-height: 1.8 !important;
}

/* Alternative: Font Awesome Icons */
.fa-check-list ul {
    list-style: none !important;
    padding-left: 0 !important;
}

.fa-check-list ul li {
    position: relative;
    padding-left: 35px !important;
    margin-bottom: 10px !important;
}

.fa-check-list ul li::before {
    content: "\f00c" !important; /* Font Awesome check icon */
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    position: absolute;
    left: 0;
    top: 0;
    color: var(--industrial-green) !important;
    font-size: 1rem !important;
}

/* Arrow list style */
.fa-arrow-list ul {
    list-style: none !important;
    padding-left: 0 !important;
}

.fa-arrow-list ul li {
    position: relative;
    padding-left: 35px !important;
    margin-bottom: 10px !important;
}

.fa-arrow-list ul li::before {
    content: "\f061" !important; /* Font Awesome arrow-right icon */
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-blue) !important;
    font-size: 0.9rem !important;
}

/* Similar Products Section - Hide or Style Better */
.similar-products-section,
.recommended-products-section {
    display: none !important; /* Hide the horrible similar products section */
}

/* Related Products Section - Keep visible with enhanced styling */
.related-products-section {
    display: block !important;
}

/* If you want to show similar products, use this styling instead */
.similar-products-container {
    background: var(--bg-white);
    border: 3px solid var(--industrial-orange);
    border-radius: 8px;
    padding: 2rem;
    margin: 3rem 0;
    box-shadow: 0 8px 25px rgba(234, 88, 12, 0.15);
}

.similar-products-title {
    color: var(--industrial-blue);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 3px solid var(--industrial-orange);
    padding-bottom: 0.5rem;
}

.similar-product-card {
    background: var(--bg-white);
    border: 2px solid var(--industrial-steel);
    border-radius: 6px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.similar-product-card:hover {
    border-color: var(--industrial-orange);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(234, 88, 12, 0.2);
}



/* Override for buttons to remove underlines */
.btn-omisol-primary,
.btn-omisol-secondary,
.product-download-link {
    text-decoration: none !important;
}

.btn-omisol-primary:hover,
.btn-omisol-secondary:hover {
    text-decoration: none !important;
}

/* Print Styles - Professional Industrial */
@media print {
    .omisol-header,
    .btn-omisol-primary,
    .btn-omisol-secondary {
        display: none !important;
    }

    .omisol-section {
        padding: 20px 0;
    }

    .omisol-hero {
        background: var(--bg-white) !important;
        color: var(--text-primary) !important;
    }

    .omisol-product-card,
    .omisol-gallery-item,
    .omisol-stat-item {
        border: 1px solid var(--text-primary) !important;
        box-shadow: none !important;
    }
}

/* Animation Classes - Professional Industrial */
.fade-in,
.slide-in-left,
.slide-in-right {
    opacity: 1;
    transform: none;
    /* Animations disabled for now to ensure content visibility */
}

/* Additional Professional Industrial Styles */
.omisol-divider {
    height: 4px;
    background: var(--primary-blue);
    border: none;
    margin: 3rem 0;
}

.omisol-text-blue {
    color: var(--primary-blue) !important;
}

.omisol-bg-blue {
    background-color: var(--primary-blue) !important;
}

.omisol-border-blue {
    border-color: var(--primary-blue) !important;
}

/* Enhanced Product Detail Styles */
.product-video-container {
    background: var(--bg-white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
}

.video-container {
    background: var(--bg-white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
}

.product-image-container {
    background: var(--bg-white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
}

.product-image-placeholder {
    background: #f9f9f9;
    border: 2px dashed var(--secondary-orange);
    border-radius: 8px;
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.product-image-placeholder:hover {
    border-color: var(--secondary-orange-dark);
    background: #f0f0f0;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.product-image-placeholder i {
    font-size: 3rem;
    color: var(--secondary-orange);
    margin-bottom: 1rem;
}

.content-section {
    background: var(--bg-light);
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.content-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.feature-card {
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.feature-card:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-blue);
    transform: translateY(-2px);
}

.feature-card h5 {
    color: var(--primary-blue);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.feature-card h5 i {
    margin-right: 0.5rem;
}

/* Removed duplicate feature-list definition - using enhanced version below */

.product-certification-badge {
    display: inline-block;
    background: var(--success-green);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 600;
    margin: 0.5rem 0.5rem 0.5rem 0;
    box-shadow: var(--shadow-light);
}

/* Technical Section Styling */
.section-title {
    color: var(--industrial-blue);
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    border-bottom: 4px solid var(--industrial-blue-dark);
    padding-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.section-title i {
    margin-right: 0.75rem;
    color: var(--industrial-orange) !important;
    font-size: 1.4rem;
    background: var(--industrial-orange);
    -webkit-background-clip: initial;
    -webkit-text-fill-color: initial;
    background-clip: initial;
    padding: 8px;
    border-radius: 50%;
    background: var(--industrial-orange);
    color: white !important;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Technical Specifications Container */
.technical-specifications {
    background: var(--bg-light);
    border: 3px solid var(--industrial-blue-dark);
    border-radius: 8px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.15);
    position: relative;
}



.downloads-section {
    background: var(--bg-white);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 6px 20px rgba(234, 88, 12, 0.15);
    margin-bottom: 2rem;
    position: relative;
}

.download-category {
    background: var(--bg-light);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 12px rgba(30, 58, 138, 0.1);
    border: 2px solid var(--industrial-steel);
    border-left: 5px solid var(--industrial-blue-dark);
    transition: all 0.3s ease;
}

.download-category:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.download-category:last-child {
    margin-bottom: 0;
}

.download-category-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.download-category-title i {
    margin-right: 0.75rem;
    color: var(--secondary-orange);
    font-size: 1.2rem;
}

.download-category-title {
    color: var(--industrial-blue);
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1.2rem;
    padding-bottom: 0.75rem;
    border-bottom: 3px solid var(--industrial-orange);
    display: flex;
    align-items: center;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.download-category-title i {
    margin-right: 0.75rem;
    color: var(--industrial-orange) !important;
    background: rgba(234, 88, 12, 0.1);
    padding: 8px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Enhanced Download Links */
.download-link {
    display: flex;
    align-items: center;
    padding: 1rem;
    margin-bottom: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
    background: var(--bg-white);
}

.download-link:hover {
    background: var(--bg-light);
    border-color: var(--primary-blue);
    color: var(--text-primary);
    text-decoration: none;
    box-shadow: var(--shadow-blue);
}

.download-icon {
    color: #2f496e;
    margin-right: 1rem;
    font-size: 1.5rem;
    width: 24px;
    text-align: center;
}

.download-info h6 {
    margin: 0;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
}

.download-info small {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Enhanced Technical Specifications Table */
.spec-table {
    background: var(--bg-light);
    border-radius: 8px;
    overflow: hidden;
    margin-top: 1.5rem;
    border: 3px solid var(--industrial-blue-dark);
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.15);
}

.spec-row {
    display: flex;
    border-bottom: 2px solid var(--industrial-blue-dark);
    transition: all 0.3s ease;
    position: relative;
}

.spec-row:hover {
    background: rgba(234, 88, 12, 0.05);
}

.spec-row:last-child {
    border-bottom: none;
}

.spec-label {
    background: #f8f9fa;
    padding: 1.25rem;
    font-weight: 700;
    color: #000000;
    min-width: 240px;
    border-right: 3px solid var(--industrial-blue-dark);
    position: relative;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.05em;
}

.spec-label::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -10px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid #f8f9fa;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
}

.spec-value {
    padding: 1.25rem;
    color: #000000;
    flex: 1;
    background: var(--bg-light);
    font-weight: 600;
    font-size: 1rem;
    border-left: 2px solid var(--industrial-blue-dark);
}

/* Enhanced Button Styles */
.btn-primary-custom {
    background: var(--primary-blue);
    border: none;
    padding: 1rem 2.5rem;
    font-weight: 600;
    border-radius: 8px;
    color: white;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-light);
}

.btn-primary-custom:hover {
    background: var(--primary-blue-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-blue);
    color: white;
    text-decoration: none;
}

.btn-secondary-custom {
    background: transparent;
    border: 2px solid var(--secondary-orange);
    color: var(--secondary-orange);
    padding: 1rem 2.5rem;
    font-weight: 600;
    border-radius: 8px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-secondary-custom:hover {
    background: var(--secondary-orange);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(249, 115, 22, 0.2);
    text-decoration: none;
}

/* Orange Accent Classes */
.text-orange {
    color: var(--secondary-orange) !important;
}

.text-orange-light {
    color: var(--secondary-orange-light) !important;
}

.text-orange-dark {
    color: var(--secondary-orange-dark) !important;
}

.bg-orange {
    background-color: var(--secondary-orange) !important;
}

.bg-orange-light {
    background-color: var(--secondary-orange-light) !important;
}

.border-orange {
    border-color: var(--secondary-orange) !important;
}

.shadow-orange {
    box-shadow: var(--shadow-orange) !important;
}

/* Industrial Icon Styling */
.icon-orange, .fa-orange, .icon-industrial {
    color: var(--industrial-orange) !important;
    background: rgba(234, 88, 12, 0.1);
    padding: 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    transition: all 0.3s ease;
}

.icon-orange:hover, .fa-orange:hover, .icon-industrial:hover {
    color: white !important;
    background: var(--industrial-orange);
    transform: scale(1.1);
}

/* Technical Icons - Only apply to non-feature icons */
.fa-cog:not(.feature-icon-enhanced i),
.fa-gear:not(.feature-icon-enhanced i),
.fa-wrench:not(.feature-icon-enhanced i),
.fa-tools:not(.feature-icon-enhanced i),
.fa-microchip:not(.feature-icon-enhanced i) {
    color: var(--industrial-blue) !important;
}

/* Ensure feature icons use white color on gradient background */
.feature-icon-enhanced .fa-cog,
.feature-icon-enhanced .fa-gear,
.feature-icon-enhanced .fa-wrench,
.feature-icon-enhanced .fa-tools,
.feature-icon-enhanced .fa-microchip,
.feature-icon-enhanced .fa-shield-alt,
.feature-icon-enhanced .fa-shield {
    color: white !important;
}

/* Parallel section icons should use consistent styling */
.parallel-section-title .fa-industry {
    color: var(--primary-blue) !important;
}

.fa-download, .fa-file-pdf, .fa-file-doc {
    color: var(--industrial-red) !important;
}

.fa-check, .fa-check-circle {
    color: var(--industrial-green) !important;
}

.fa-exclamation-triangle, .fa-warning {
    color: var(--industrial-yellow) !important;
}

/* General Icon Enhancement */
i, .fa, .fas, .far, .fab {
    transition: all 0.3s ease;
}

i:hover, .fa:hover, .fas:hover, .far:hover, .fab:hover {
    transform: scale(1.1);
}

/* Removed duplicate feature-list styling - using parallel section styles instead */



/* Enhanced Feature Boxes - Matching Design */
.omisol-feature-box {
    background: var(--bg-light);
    border: none;
    border-radius: 8px;
    margin-bottom: 2rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.omisol-feature-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.omisol-feature-header {
    background: var(--bg-light);
    color: var(--industrial-blue-dark);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border-bottom: 2px solid var(--industrial-blue-dark);
}

.omisol-feature-header i {
    font-size: 1.25rem;
    color: white !important;
}

.omisol-feature-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 700;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.omisol-feature-content {
    padding: 1.5rem;
    background: var(--bg-light);
    min-height: 200px;
}

.omisol-feature-list,
.omisol-applications-list {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.omisol-feature-list li,
.omisol-applications-list li {
    padding: 0.5rem 0 0.5rem 1.5rem !important;
    margin-bottom: 0.5rem !important;
    position: relative !important;
    line-height: 1.5 !important;
    color: var(--text-primary) !important;
    font-weight: 500;
    border-bottom: 1px solid rgba(249, 115, 22, 0.1);
}

.omisol-feature-list li:last-child,
.omisol-applications-list li:last-child {
    border-bottom: none;
}

.omisol-feature-list li:before {
    content: "✓" !important;
    position: absolute !important;
    left: 0 !important;
    top: 0.5rem !important;
    color: var(--industrial-green) !important;
    font-weight: bold !important;
    font-size: 1rem !important;
    line-height: 1 !important;
}

.omisol-applications-list li:before {
    content: "✓" !important;
    position: absolute !important;
    left: 0 !important;
    top: 0.5rem !important;
    color: var(--industrial-green) !important;
    font-weight: bold !important;
    font-size: 1rem !important;
    line-height: 1 !important;
}

.applications-category-title {
    color: var(--primary-blue) !important;
    margin-bottom: 1rem !important;
    font-weight: 600;
    font-size: 1rem;
    display: flex;
    align-items: center;
}

.applications-category-title i {
    color: var(--primary-blue) !important;
}

/* Ensure consistent box heights */
.content-section .omisol-feature-box {
    height: auto;
    min-height: 280px;
    display: flex;
    flex-direction: column;
}

.content-section .omisol-feature-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

/* Responsive Design for Product Pages */
@media (max-width: 768px) {
    .feature-grid {
        grid-template-columns: 1fr;
    }

    .spec-row {
        flex-direction: column;
    }

    .spec-label {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        min-width: auto;
    }

    .product-image-placeholder {
        height: 150px;
    }

    .content-section {
        padding: 1.5rem;
    }

    .downloads-section {
        padding: 1.5rem;
    }

    .omisol-feature-box {
        min-height: auto;
    }

    .omisol-feature-content {
        min-height: 150px;
    }
}

/* Related Products Styling - Consistent with Feature Boxes */
.omisol-related-product-card {
    background: #ffffff;
    border: 2px solid var(--primary-blue);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.omisol-related-product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.related-product-header {
    background: var(--primary-blue);
    color: white;
    padding: 0.75rem 1rem;
    text-align: center;
}

.related-product-category {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.related-product-content {
    padding: 1.5rem;
    background: #fafbfc;
    flex: 1;
    display: flex;
    flex-direction: column;
    text-align: center;
}

.related-product-image {
    margin-bottom: 1rem;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 4px;
    background: #f1f5f9;
}

.related-product-image img {
    max-height: 100%;
    max-width: 100%;
    object-fit: contain;
}

.related-product-title {
    color: var(--text-primary) !important;
    font-size: 1rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    line-height: 1.3;
}

.related-product-description {
    color: var(--text-secondary) !important;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1.5rem;
    flex: 1;
}

.btn-related-product {
    background: var(--primary-blue);
    color: white !important;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    margin-top: auto;
}

.btn-related-product:hover {
    background: var(--primary-blue-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    color: white !important;
    text-decoration: none;
}

/* Enhanced Product Detail Sections */
.feature-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    font-size: 1.1rem;
    color: var(--text-dark);
}

.download-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.download-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.download-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.download-content h5 {
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.download-content p {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}
