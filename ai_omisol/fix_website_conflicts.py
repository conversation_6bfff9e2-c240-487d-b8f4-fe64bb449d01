#!/usr/bin/env python3
"""
Fix website page conflicts by removing duplicate/conflicting records
"""

import xmlrpc.client

# Odoo connection details
url = "https://omnisol.arihantai.com"
db = "omnisol.arihantai.com"
username = "demo"
password = "demo"

def connect_to_odoo():
    """Connect to Odoo and return models proxy"""
    try:
        print("🔗 Connecting to Odoo server...")
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            raise Exception("Authentication failed. Please check credentials.")
        
        print(f"✅ Successfully authenticated as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        return models, uid
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return None, None

def remove_conflicting_pages(models, uid):
    """Remove conflicting website page records"""
    try:
        print("\n🔧 Removing conflicting website page records...")
        
        # Remove duplicate homepage records (keep only one)
        homepage_pages = models.execute_kw(db, uid, password, 'website.page', 'search', 
                                         [[('url', '=', '/')]])
        
        if len(homepage_pages) > 1:
            print(f"📋 Found {len(homepage_pages)} homepage records, removing duplicates...")
            # Keep the first one, remove the rest
            for page_id in homepage_pages[1:]:
                try:
                    models.execute_kw(db, uid, password, 'website.page', 'unlink', [[page_id]])
                    print(f"   ✅ Removed duplicate homepage record ID: {page_id}")
                except Exception as e:
                    print(f"   ❌ Error removing homepage record {page_id}: {e}")
        
        # Remove loader demo page record (we want controller to handle it)
        loader_pages = models.execute_kw(db, uid, password, 'website.page', 'search', 
                                       [[('url', '=', '/loader-demo')]])
        
        for page_id in loader_pages:
            try:
                models.execute_kw(db, uid, password, 'website.page', 'unlink', [[page_id]])
                print(f"   ✅ Removed loader demo page record ID: {page_id}")
            except Exception as e:
                print(f"   ❌ Error removing loader demo record {page_id}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error removing conflicting pages: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Fixing Website Page Conflicts")
    print("="*50)
    
    # Connect to Odoo
    models, uid = connect_to_odoo()
    if not models:
        print("❌ Failed to connect to Odoo")
        return False
    
    # Remove conflicting pages
    success = remove_conflicting_pages(models, uid)
    
    if success:
        print("\n✅ Website page conflicts resolved!")
        print("🔄 Please upgrade the module to apply template fixes.")
    else:
        print("\n❌ Failed to resolve website page conflicts!")
    
    return success

if __name__ == "__main__":
    main()
