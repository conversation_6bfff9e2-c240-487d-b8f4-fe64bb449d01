#!/usr/bin/env python3
"""
<PERSON><PERSON>t to create sample download data for testing the downloads section
"""

import xmlrpc.client
import base64

# Odoo connection details
url = 'https://omnisol.arihantai.com'
db = 'odoo'
username = '<EMAIL>'
password = 'Arihant@123'

# Connect to Odoo
common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
uid = common.authenticate(db, username, password, {})

if uid:
    print(f"✅ Successfully authenticated as user ID: {uid}")
    
    models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
    
    # Get the product with ID 4
    product_id = 4
    product = models.execute_kw(db, uid, password, 'omisol.product.showcase', 'read', [product_id], {'fields': ['name', 'download_ids']})
    
    if product:
        print(f"📋 Product: {product[0]['name']}")
        print(f"📋 Current downloads: {product[0]['download_ids']}")
        
        # Create sample download files
        sample_downloads = [
            {
                'name': 'Technical Support Services Brochure',
                'file_type': 'brochure',
                'description': 'Comprehensive overview of our technical support services and capabilities',
                'is_active': True,
                'product_id': product_id,
                'file_size': '2.5 MB'
            },
            {
                'name': 'Service Level Agreement (SLA)',
                'file_type': 'datasheet',
                'description': 'Detailed service level agreements and response times',
                'is_active': True,
                'product_id': product_id,
                'file_size': '1.2 MB'
            },
            {
                'name': 'Support Request Form',
                'file_type': 'manual',
                'description': 'Form for submitting technical support requests',
                'is_active': True,
                'product_id': product_id,
                'file_size': '0.8 MB'
            },
            {
                'name': 'ISO 9001 Certificate',
                'file_type': 'certificate',
                'description': 'Quality management system certification',
                'is_active': True,
                'product_id': product_id,
                'file_size': '1.5 MB'
            }
        ]
        
        # Create download records
        for download_data in sample_downloads:
            try:
                download_id = models.execute_kw(db, uid, password, 'omisol.product.download', 'create', [download_data])
                print(f"✅ Created download: {download_data['name']} (ID: {download_id})")
            except Exception as e:
                print(f"❌ Error creating download {download_data['name']}: {e}")
        
        print("\n🎉 Sample download data created successfully!")
        
    else:
        print("❌ Product not found")
        
else:
    print("❌ Authentication failed")
