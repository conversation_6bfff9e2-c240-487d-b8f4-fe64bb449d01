#!/usr/bin/env python3
"""
Remove duplicate homepage records to prevent conflicts
"""

import xmlrpc.client

# Odoo connection details
url = "https://omnisol.arihantai.com"
db = "omnisol.arihantai.com"
username = "demo"
password = "demo"

def connect_to_odoo():
    """Connect to Odoo and return models proxy"""
    try:
        print("🔗 Connecting to Odoo server...")
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            raise Exception("Authentication failed. Please check credentials.")
        
        print(f"✅ Successfully authenticated as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        return models, uid
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return None, None

def remove_duplicate_homepages(models, uid):
    """Remove all duplicate homepage records"""
    try:
        print("\n🔧 Checking for duplicate homepage records...")
        
        # Find all homepage records
        homepage_pages = models.execute_kw(db, uid, password, 'website.page', 'search_read', 
                                         [[('url', '=', '/')]], 
                                         {'fields': ['id', 'name', 'view_id', 'website_published']})
        
        print(f"📋 Found {len(homepage_pages)} homepage records:")
        for page in homepage_pages:
            print(f"   ID: {page['id']}, Name: {page['name']}, View: {page['view_id']}, Published: {page['website_published']}")
        
        if len(homepage_pages) > 1:
            print(f"\n🗑️ Removing {len(homepage_pages) - 1} duplicate homepage records...")
            
            # Keep the first one, remove the rest
            for page in homepage_pages[1:]:
                try:
                    models.execute_kw(db, uid, password, 'website.page', 'unlink', [[page['id']]])
                    print(f"   ✅ Removed homepage record ID: {page['id']} (Name: {page['name']})")
                except Exception as e:
                    print(f"   ❌ Error removing homepage record {page['id']}: {e}")
        
        elif len(homepage_pages) == 1:
            print("✅ Only one homepage record found - no duplicates to remove")
        else:
            print("⚠️ No homepage records found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error removing duplicate homepages: {e}")
        return False

def check_conflicting_routes(models, uid):
    """Check for other conflicting website page records"""
    try:
        print("\n🔍 Checking for other conflicting routes...")
        
        conflict_urls = ['/faq', '/loader-demo', '/products']
        
        for url_path in conflict_urls:
            pages = models.execute_kw(db, uid, password, 'website.page', 'search_read', 
                                    [[('url', '=', url_path)]], 
                                    {'fields': ['id', 'name', 'view_id', 'website_published']})
            
            if pages:
                print(f"\n⚠️ Found conflicting page records for {url_path}:")
                for page in pages:
                    print(f"   ID: {page['id']}, Name: {page['name']}, View: {page['view_id']}")
                
                # Remove these conflicting records since we want controllers to handle them
                for page in pages:
                    try:
                        models.execute_kw(db, uid, password, 'website.page', 'unlink', [[page['id']]])
                        print(f"   ✅ Removed conflicting page record ID: {page['id']} for {url_path}")
                    except Exception as e:
                        print(f"   ❌ Error removing page record {page['id']}: {e}")
            else:
                print(f"✅ No conflicting records found for {url_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking conflicting routes: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Removing Duplicate Homepage Records")
    print("="*50)
    
    # Connect to Odoo
    models, uid = connect_to_odoo()
    if not models:
        print("❌ Failed to connect to Odoo")
        return False
    
    # Remove duplicate homepages
    success1 = remove_duplicate_homepages(models, uid)
    
    # Check for other conflicting routes
    success2 = check_conflicting_routes(models, uid)
    
    if success1 and success2:
        print("\n✅ All duplicate and conflicting page records cleaned up!")
        print("🔄 Website routes should now work properly with controllers.")
    else:
        print("\n❌ Some issues occurred during cleanup!")
    
    return success1 and success2

if __name__ == "__main__":
    main()
