#!/usr/bin/env python3
"""
Check website routes and page records for conflicts
"""

import xmlrpc.client

# Odoo connection details
url = "https://omnisol.arihantai.com"
db = "omnisol.arihantai.com"
username = "demo"
password = "demo"

def connect_to_odoo():
    """Connect to Odoo and return models proxy"""
    try:
        print("🔗 Connecting to Odoo server...")
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            raise Exception("Authentication failed. Please check credentials.")
        
        print(f"✅ Successfully authenticated as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        return models, uid
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return None, None

def check_website_pages(models, uid):
    """Check for website page records that might conflict"""
    try:
        print("\n🔍 Checking website page records...")
        
        # Search for pages with URLs that might conflict
        conflict_urls = ['/', '/faq', '/loader-demo', '/products', '/contactus']
        
        for url_path in conflict_urls:
            pages = models.execute_kw(db, uid, password, 'website.page', 'search_read', 
                                    [[('url', '=', url_path)]], 
                                    {'fields': ['name', 'url', 'view_id', 'website_published']})
            
            print(f"\n📋 URL: {url_path}")
            if pages:
                for page in pages:
                    print(f"   Page: {page['name']}")
                    print(f"   View ID: {page['view_id']}")
                    print(f"   Published: {page['website_published']}")
            else:
                print("   No website page record found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking website pages: {e}")
        return False

def check_ir_http_routes(models, uid):
    """Check controller routes"""
    try:
        print("\n🔍 Checking controller routes...")
        
        # This is harder to check via XML-RPC, but we can check if our module is loaded
        modules = models.execute_kw(db, uid, password, 'ir.module.module', 'search_read',
                                  [[('name', '=', 'ai_omisol')]], 
                                  {'fields': ['name', 'state', 'latest_version']})
        
        if modules:
            module = modules[0]
            print(f"📋 Module ai_omisol:")
            print(f"   State: {module['state']}")
            print(f"   Version: {module['latest_version']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking routes: {e}")
        return False

def check_templates(models, uid):
    """Check if templates exist"""
    try:
        print("\n🔍 Checking templates...")
        
        template_ids = [
            'ai_omisol.Omisol_homepage',
            'ai_omisol.faq_page', 
            'ai_omisol.loader_demo_page'
        ]
        
        for template_id in template_ids:
            try:
                # Try to find the template
                views = models.execute_kw(db, uid, password, 'ir.ui.view', 'search_read',
                                        [[('key', '=', template_id)]], 
                                        {'fields': ['name', 'key', 'active']})
                
                print(f"📋 Template: {template_id}")
                if views:
                    view = views[0]
                    print(f"   Name: {view['name']}")
                    print(f"   Active: {view['active']}")
                else:
                    print("   ❌ Template not found")
                    
            except Exception as e:
                print(f"   ❌ Error checking template: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking templates: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Checking Website Routes and Templates")
    print("="*60)
    
    # Connect to Odoo
    models, uid = connect_to_odoo()
    if not models:
        print("❌ Failed to connect to Odoo")
        return False
    
    # Check website pages
    check_website_pages(models, uid)
    
    # Check routes
    check_ir_http_routes(models, uid)
    
    # Check templates
    check_templates(models, uid)
    
    print("\n" + "="*60)
    print("🎯 ANALYSIS COMPLETE")

if __name__ == "__main__":
    main()
