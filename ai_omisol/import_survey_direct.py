#!/usr/bin/env python3
"""
Direct Survey Import Script
Imports survey data directly using Odoo's ORM instead of XML-RPC
"""

import os
import sys
import csv
from collections import defaultdict

# Add Odoo to Python path
sys.path.append('/usr/lib/python3/dist-packages')

# Set up Odoo environment
os.environ['ODOO_RC'] = '/etc/odoo/odoo.conf'

import odoo
from odoo import api, SUPERUSER_ID
from odoo.modules.registry import Registry

def read_csv_data():
    """Read questions and answers from CSV files"""
    questions = []
    answers_by_question = defaultdict(list)
    
    try:
        # Read questions
        print("📖 Reading questions from CSV...")
        with open('survey_questions.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                questions.append({
                    'id': int(row['sequence']),
                    'title': row['title'].strip(),
                    'question_type': row['question_type'].strip(),
                    'sequence': int(row['sequence']),
                    'chapter': row.get('chapter', '').strip()
                })
        
        print(f"✅ Read {len(questions)} questions from CSV")
        
        # Read answers
        print("📖 Reading answers from CSV...")
        with open('survey_answers.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                question_id = int(row['question_sequence'])
                answers_by_question[question_id].append({
                    'value': row['answer_value'].strip(),
                    'sequence': int(row['answer_sequence']),
                    'is_correct': row['is_correct'].strip().lower() == 'true'
                })
        
        total_answers = sum(len(answers) for answers in answers_by_question.values())
        print(f"✅ Read {total_answers} answers for {len(answers_by_question)} questions from CSV")
        
        return questions, answers_by_question
        
    except FileNotFoundError as e:
        print(f"❌ CSV file not found: {e}")
        return [], {}
    except Exception as e:
        print(f"❌ Error reading CSV files: {e}")
        return [], {}

def create_survey_with_data(env, questions, answers_by_question):
    """Create survey with questions and answers using Odoo ORM"""
    try:
        print("\n🚀 Creating survey with questions using Odoo ORM...")
        
        # Create survey
        survey_data = {
            'title': 'OEL8000III Series Controller Safety, Installation, Features, and Operation Survey',
            'description': 'Comprehensive survey covering safety, installation, features, and operation of OEL8000III Series Controllers',
            'access_mode': 'public',
            'users_login_required': False,
        }
        
        survey = env['survey.survey'].create(survey_data)
        print(f"✅ Survey created with ID: {survey.id}")
        
        # Create questions and answers
        created_questions = 0
        created_answers = 0
        
        for q in questions:
            try:
                # Create question
                question_data = {
                    'title': q['title'],
                    'question_type': q['question_type'],
                    'sequence': q['sequence'],
                    'is_page': False,
                    'survey_id': survey.id
                }
                
                question = env['survey.question'].create(question_data)
                created_questions += 1
                
                # Create answers for this question
                question_answers = answers_by_question.get(q['id'], [])
                for answer in question_answers:
                    try:
                        answer_data = {
                            'value': answer['value'],
                            'sequence': answer['sequence'],
                            'is_correct': answer['is_correct'],
                            'question_id': question.id
                        }
                        
                        env['survey.question.answer'].create(answer_data)
                        created_answers += 1
                        
                    except Exception as e:
                        print(f"    ❌ Error creating answer for question {q['sequence']}: {e}")
                
                if created_questions % 10 == 0:
                    print(f"    ✅ Created {created_questions} questions so far...")
                
            except Exception as e:
                print(f"    ❌ Error creating question {q['sequence']}: {e}")
        
        # Commit the transaction
        env.cr.commit()
        
        print(f"✅ Successfully created {created_questions} questions and {created_answers} answers")
        return survey.id, created_questions, created_answers
        
    except Exception as e:
        print(f"❌ Error creating survey: {e}")
        env.cr.rollback()
        return None, 0, 0

def verify_survey_import(env, survey_id):
    """Verify that the survey was imported correctly"""
    try:
        print(f"\n🔍 Verifying survey import for Survey ID {survey_id}...")
        
        # Get survey details
        survey = env['survey.survey'].browse(survey_id)
        if not survey.exists():
            print("❌ Survey not found!")
            return False
        
        print(f"📊 Survey Title: {survey.title}")
        print(f"🔓 Access Mode: {survey.access_mode}")
        print(f"👤 Login Required: {survey.users_login_required}")
        
        # Get questions
        questions = env['survey.question'].search([('survey_id', '=', survey_id)])
        print(f"📝 Questions: {len(questions)}")
        
        # Get answers
        total_answers = 0
        for question in questions[:5]:  # Show first 5 questions
            answers = env['survey.question.answer'].search([('question_id', '=', question.id)])
            total_answers += len(answers)
            print(f"  - {question.title[:50]}... ({question.question_type}): {len(answers)} answers")
        
        # Count all answers
        all_answers = env['survey.question.answer'].search([('question_id', 'in', questions.ids)])
        total_answers = len(all_answers)
        
        print(f"📋 Total Answers: {total_answers}")
        
        if len(questions) > 0 and total_answers > 0:
            print("✅ Survey import verification PASSED!")
            return True
        else:
            print("❌ Survey import verification FAILED - No questions or answers found!")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying survey: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Starting Direct Survey Import using Odoo ORM")
    print("="*60)
    
    try:
        # Initialize Odoo
        odoo.tools.config.parse_config([])
        
        # Get database name
        db_name = 'omnisol.arihantai.com'
        
        # Create registry and environment
        registry = Registry.new(db_name)
        with registry.cursor() as cr:
            env = api.Environment(cr, SUPERUSER_ID, {})
            
            print(f"✅ Connected to Odoo database: {db_name}")
            
            # Read CSV data
            questions, answers_by_question = read_csv_data()
            if not questions:
                print("❌ No questions found in CSV")
                return False
            
            # Create survey with questions and answers
            survey_id, created_questions, created_answers = create_survey_with_data(env, questions, answers_by_question)
            if not survey_id:
                print("❌ Failed to create survey")
                return False
            
            # Verify import
            success = verify_survey_import(env, survey_id)
            
            if success:
                print(f"\n🎉 SUCCESS! Survey imported successfully with ID: {survey_id}")
                print(f"✅ Created {created_questions} questions and {created_answers} answers!")
                print(f"🌐 Access the survey at: https://omnisol.arihantai.com/survey/start/{survey_id}")
            else:
                print(f"\n❌ Import verification failed for Survey ID: {survey_id}")
            
            return success
            
    except Exception as e:
        print(f"❌ Error during survey import: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
