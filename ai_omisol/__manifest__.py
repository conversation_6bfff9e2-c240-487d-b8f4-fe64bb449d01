{
    'name': 'Omisol Website',
    'version': '********.0',
    'category': 'Website',
    'summary': 'Custom website module for Omisol with product showcase functionality',
    'description': """
        Custom website module for Omisol Private Limited
        ================================================
        
        Features:
        - Custom homepage with hero section
        - Product showcase (non-ecommerce)
        - Certificates section
        - Support section
        - Photo gallery
        - Company statistics counters
        - About section
        - Custom product management
        
        Design inspired by Pidilite and Omntec themes.
    """,
    'author': 'Omisol Private Limited',
    'website': 'https://www.omisol.com',
    'images': ['static/description/icon.png'],
    'depends': [
        'base',
        'website',
        'portal',
        'mail',
        'im_livechat',
    ],
    'data': [
        'security/ir.model.access.csv',
        'data/website_data.xml',
        'data/product_demo_data.xml',
        'data/product_downloads_real.xml',
        'data/load_download_files.xml',
        'data/email_templates.xml',
        'views/website_templates.xml',
        'views/product_showcase_views.xml',
        'views/customer_management_views.xml',
        'views/faq_views.xml',
        'views/faq_website_templates.xml',
        'views/course_page_templates.xml',
        'views/website_menus.xml',
        'views/loader_demo_templates.xml',
        'views/about_us_templates.xml',
        'views/contact_us_templates.xml',
        'wizard/customer_import_wizard_views.xml',
    ],
    'assets': {
        # Remove from global variable scope to avoid conflicts
        # 'web._assets_primary_variables': [
        #     'ai_omisol/static/src/scss/primary_variables.scss',
        # ],

        # 'web._assets_secondary_variables': [
        #     'ai_omisol/static/src/scss/secondary_variables.scss',
        # ],

        # Remove Bootstrap overrides to avoid conflicts
        # 'web._assets_frontend_helpers': [
        #     ('prepend', 'ai_omisol/static/src/scss/bootstrap_overridden.scss'),
        # ],

        # Main frontend assets - minimal and safe (no SCSS conflicts)
        'web.assets_frontend': [
            # Only essential CSS that doesn't conflict with Odoo
            'ai_omisol/static/src/css/website.css',
            'ai_omisol/static/src/css/loader.css',
            'ai_omisol/static/src/js/testimonials-slider.js',
            'ai_omisol/static/src/js/products-filter.js',
            'ai_omisol/static/src/js/loader.js',
        ],



        # Removed lazy-loaded assets to avoid conflicts
    },
    'installable': True,
    'application': True,
    'auto_install': False,

    # Test configuration
    'test': [
        'tests/test_module_loading.py',
    ],
    'license': 'LGPL-3',
}
