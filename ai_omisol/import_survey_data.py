#!/usr/bin/env python3
"""
Import Survey Questions and Answers from CSV
Creates a complete survey with questions and answer choices
"""

import csv
import xmlrpc.client
import os

# Odoo connection settings
ODOO_URL = 'https://omnisol.arihantai.com'
ODOO_DB = 'odoo'
ODOO_USERNAME = 'admin'
ODOO_PASSWORD = 'admin'

def connect_to_odoo():
    """Connect to Odoo and return common and models objects"""
    common = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/common')
    uid = common.authenticate(ODOO_DB, ODOO_USERNAME, ODOO_PASSWORD, {})
    
    if not uid:
        raise Exception("Authentication failed")
    
    models = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/object')
    return common, models, uid

def create_survey(models, uid):
    """Create the main survey"""
    survey_data = {
        'title': 'OEL8000III Series Controller Training & Certification',
        'description': '''
        <p>Comprehensive training survey covering OEL8000III series controllers, MTG probes, 
        and automatic tank gauging systems. This survey tests knowledge of:</p>
        <ul>
            <li>Safety & Installation procedures</li>
            <li>Features & Programming</li>
            <li>Operation, Diagnostics & Troubleshooting</li>
        </ul>
        <p>Designed for technicians working with petroleum and chemical facility monitoring equipment.</p>
        ''',
        'access_mode': 'public',
        'users_login_required': False,
        'is_attempts_limited': False,
        'scoring_type': 'scoring_with_answers',
        'certification': True,
        'certification_mail_template_id': False,
        'certification_give_badge': False,
    }
    
    survey_id = models.execute_kw(
        ODOO_DB, uid, ODOO_PASSWORD,
        'survey.survey', 'create',
        [survey_data]
    )
    
    print(f"✅ Created survey with ID: {survey_id}")
    return survey_id

def import_questions_from_csv(models, uid, survey_id, csv_file_path):
    """Import questions from CSV file"""
    if not os.path.exists(csv_file_path):
        print(f"❌ CSV file not found: {csv_file_path}")
        return
    
    questions_created = 0
    current_page_id = None
    
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        
        for row in reader:
            try:
                sequence = int(row['sequence'])
                title = row['title'].strip()
                question_type = row['question_type'].strip()
                is_page = row['is_page'].strip().upper() == 'TRUE'
                chapter = row['chapter'].strip() if row['chapter'] else 'General'
                
                # Create page if this is a page question
                if is_page:
                    page_data = {
                        'survey_id': survey_id,
                        'title': chapter,
                        'question_type': 'simple_choice',
                        'sequence': sequence,
                        'is_page': True,
                        'description': f'<h3>{title}</h3>',
                    }
                    
                    current_page_id = models.execute_kw(
                        ODOO_DB, uid, ODOO_PASSWORD,
                        'survey.question', 'create',
                        [page_data]
                    )
                    print(f"📄 Created page: {chapter}")
                    continue
                
                # Create regular question
                question_data = {
                    'survey_id': survey_id,
                    'title': title,
                    'question_type': question_type,
                    'sequence': sequence,
                    'is_page': False,
                    'page_id': current_page_id,
                    'constr_mandatory': True,
                }
                
                # Map question types
                if question_type == 'simple_choice':
                    question_data['question_type'] = 'simple_choice'
                elif question_type == 'single_choice':
                    question_data['question_type'] = 'simple_choice'
                else:
                    question_data['question_type'] = 'simple_choice'
                
                question_id = models.execute_kw(
                    ODOO_DB, uid, ODOO_PASSWORD,
                    'survey.question', 'create',
                    [question_data]
                )
                
                # Create answer choices for choice questions
                if question_type in ['simple_choice', 'single_choice']:
                    create_default_answers(models, uid, question_id, question_type)
                
                questions_created += 1
                print(f"✅ Created question {sequence}: {title[:50]}...")
                
            except Exception as e:
                print(f"❌ Error creating question {row.get('sequence', 'unknown')}: {e}")
                continue
    
    print(f"📊 Total questions created: {questions_created}")

def create_default_answers(models, uid, question_id, question_type):
    """Create default answer choices for questions"""
    if question_type == 'simple_choice':
        # True/False questions
        answers = [
            {'value': 'True', 'is_correct': True, 'sequence': 1},
            {'value': 'False', 'is_correct': False, 'sequence': 2},
        ]
    else:
        # Multiple choice questions - create generic options
        answers = [
            {'value': 'Option A', 'is_correct': True, 'sequence': 1},
            {'value': 'Option B', 'is_correct': False, 'sequence': 2},
            {'value': 'Option C', 'is_correct': False, 'sequence': 3},
            {'value': 'Option D', 'is_correct': False, 'sequence': 4},
        ]
    
    for answer_data in answers:
        answer_data['question_id'] = question_id
        
        models.execute_kw(
            ODOO_DB, uid, ODOO_PASSWORD,
            'survey.question.answer', 'create',
            [answer_data]
        )

def main():
    """Main function to import survey data"""
    print("🚀 Starting Survey Data Import")
    print("=" * 50)
    
    try:
        # Connect to Odoo
        print("🔗 Connecting to Odoo...")
        common, models, uid = connect_to_odoo()
        print(f"✅ Connected successfully as user ID: {uid}")
        
        # Create survey
        print("\n📋 Creating survey...")
        survey_id = create_survey(models, uid)
        
        # Import questions
        print("\n📝 Importing questions from CSV...")
        csv_path = '/mnt/extra-addons/ai_omisol/survey_questions.csv'
        import_questions_from_csv(models, uid, survey_id, csv_path)
        
        print(f"\n🎉 Survey import completed successfully!")
        print(f"Survey ID: {survey_id}")
        print(f"Access the survey at: {ODOO_URL}/survey/start/{survey_id}")
        
    except Exception as e:
        print(f"❌ Error during import: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
