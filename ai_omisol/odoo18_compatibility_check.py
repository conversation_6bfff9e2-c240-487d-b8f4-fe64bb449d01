#!/usr/bin/env python3
"""
Odoo 18 Compatibility Checker
=============================
Comprehensive validation script to check for all Odoo 18 compatibility issues
"""

import os
import xml.etree.ElementTree as ET
import re
import sys

def check_xml_compatibility():
    """Check XML files for Odoo 18 compatibility issues"""
    print("🔍 Checking XML files for Odoo 18 compatibility...")
    
    issues = []
    xml_files = []
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.xml'):
                xml_files.append(os.path.join(root, file))
    
    for xml_file in xml_files:
        try:
            with open(xml_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for deprecated attributes
            if 'attrs=' in content:
                issues.append(f"❌ {xml_file}: Found deprecated 'attrs=' attribute")
            
            if 'states=' in content:
                issues.append(f"❌ {xml_file}: Found deprecated 'states=' attribute")
            
            # Check for tree vs list
            if '<tree' in content:
                issues.append(f"❌ {xml_file}: Found <tree> tag, should be <list>")
            
            if 'view_mode="tree' in content or "view_mode='tree" in content:
                issues.append(f"❌ {xml_file}: Found view_mode='tree', should be 'list'")
            
            # Check for active_id usage
            if 'active_id' in content:
                issues.append(f"⚠️ {xml_file}: Found 'active_id', consider using 'id'")
            
            # Check for XML syntax
            try:
                ET.parse(xml_file)
            except ET.ParseError as e:
                issues.append(f"❌ {xml_file}: XML syntax error - {e}")
            
            if not issues:
                print(f"✅ {xml_file}")
                
        except Exception as e:
            issues.append(f"❌ {xml_file}: Error reading file - {e}")
    
    return issues

def check_python_compatibility():
    """Check Python files for Odoo 18 compatibility issues"""
    print("\n🐍 Checking Python files for Odoo 18 compatibility...")
    
    issues = []
    python_files = []
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    for py_file in python_files:
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for deprecated imports (exclude self)
            if 'from odoo.addons.web.controllers.main import' in content and py_file != './odoo18_compatibility_check.py':
                issues.append(f"⚠️ {py_file}: Check web controller imports for Odoo 18")
            
            # Check for deprecated API decorators (exclude self)
            if '@api.one' in content and py_file != './odoo18_compatibility_check.py':
                issues.append(f"❌ {py_file}: Found deprecated @api.one decorator")

            if '@api.multi' in content and py_file != './odoo18_compatibility_check.py':
                issues.append(f"❌ {py_file}: Found deprecated @api.multi decorator")
            
            # Check for old-style string formatting in _() calls (more specific)
            old_format_pattern = r'_\([^)]*%[sd][^)]*\)'
            matches = re.findall(old_format_pattern, content)
            if matches and py_file != './odoo18_compatibility_check.py':  # Exclude self
                issues.append(f"⚠️ {py_file}: Found old-style string formatting in translations")
            
            # Check syntax
            try:
                compile(content, py_file, 'exec')
            except SyntaxError as e:
                issues.append(f"❌ {py_file}: Python syntax error - {e}")
            
            if not issues:
                print(f"✅ {py_file}")
                
        except Exception as e:
            issues.append(f"❌ {py_file}: Error reading file - {e}")
    
    return issues

def check_manifest_compatibility():
    """Check manifest for Odoo 18 compatibility"""
    print("\n📋 Checking manifest for Odoo 18 compatibility...")
    
    issues = []
    
    try:
        with open('__manifest__.py', 'r', encoding='utf-8') as f:
            content = f.read()
            manifest = eval(content)
        
        # Check version compatibility
        version = manifest.get('version', '')
        if not version.startswith('18.'):
            issues.append(f"⚠️ Version should start with '18.' for Odoo 18")
        
        # Check for required dependencies
        depends = manifest.get('depends', [])
        if 'base' not in depends:
            issues.append(f"❌ 'base' should be in dependencies")
        
        # Check for deprecated keys
        deprecated_keys = ['active', 'installable_on_saas']
        for key in deprecated_keys:
            if key in manifest:
                issues.append(f"⚠️ Deprecated manifest key: {key}")
        
        # Check data files exist
        data_files = manifest.get('data', [])
        for data_file in data_files:
            if not os.path.exists(data_file):
                issues.append(f"❌ Data file not found: {data_file}")
        
        if not issues:
            print("✅ Manifest is Odoo 18 compatible")
            
    except Exception as e:
        issues.append(f"❌ Error reading manifest: {e}")
    
    return issues

def check_security_files():
    """Check security files for common issues"""
    print("\n🔒 Checking security files...")
    
    issues = []
    security_dir = 'security'
    
    if os.path.exists(security_dir):
        for file in os.listdir(security_dir):
            if file.endswith('.csv'):
                file_path = os.path.join(security_dir, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for proper CSV format
                    lines = content.strip().split('\n')
                    if len(lines) < 2:
                        issues.append(f"⚠️ {file_path}: Security file seems empty or malformed")
                    
                    # Check header
                    if lines and not lines[0].startswith('id,name,model_id'):
                        issues.append(f"⚠️ {file_path}: Check CSV header format")
                    
                    if not issues:
                        print(f"✅ {file_path}")
                        
                except Exception as e:
                    issues.append(f"❌ {file_path}: Error reading file - {e}")
    else:
        issues.append(f"⚠️ No security directory found")
    
    return issues

def check_asset_bundles():
    """Check for asset bundle compatibility"""
    print("\n📦 Checking asset bundles...")
    
    issues = []
    
    try:
        with open('__manifest__.py', 'r', encoding='utf-8') as f:
            content = f.read()
            manifest = eval(content)
        
        assets = manifest.get('assets', {})
        
        # Check for deprecated asset bundles
        deprecated_bundles = [
            'web.assets_common',
            'web.assets_backend',
            'web.assets_frontend_minimal'
        ]
        
        for bundle in deprecated_bundles:
            if bundle in assets:
                issues.append(f"⚠️ Deprecated asset bundle: {bundle}")
        
        # Check for new recommended bundles
        recommended_bundles = [
            'web.assets_backend',
            'web.assets_frontend',
            'web.assets_frontend_lazy'
        ]
        
        has_assets = any(bundle in assets for bundle in recommended_bundles)
        if assets and not has_assets:
            issues.append(f"⚠️ Consider using recommended asset bundles")
        
        if not issues:
            print("✅ Asset bundles look good")
            
    except Exception as e:
        issues.append(f"❌ Error checking asset bundles: {e}")
    
    return issues

def main():
    """Main validation function"""
    print("🚀 Odoo 18 Compatibility Check")
    print("=" * 50)
    
    all_issues = []
    
    # Run all checks
    all_issues.extend(check_xml_compatibility())
    all_issues.extend(check_python_compatibility())
    all_issues.extend(check_manifest_compatibility())
    all_issues.extend(check_security_files())
    all_issues.extend(check_asset_bundles())
    
    print("\n" + "=" * 50)
    
    if all_issues:
        print(f"⚠️ Found {len(all_issues)} compatibility issues:")
        for issue in all_issues:
            print(f"  • {issue}")
        
        # Separate critical from warnings
        critical = [i for i in all_issues if i.startswith('❌')]
        warnings = [i for i in all_issues if i.startswith('⚠️')]
        
        print(f"\n📊 Summary:")
        print(f"  Critical issues: {len(critical)}")
        print(f"  Warnings: {len(warnings)}")
        
        if critical:
            print("\n❌ Module has critical issues that must be fixed!")
            sys.exit(1)
        else:
            print("\n✅ No critical issues found. Warnings should be reviewed.")
            sys.exit(0)
    else:
        print("✅ All compatibility checks passed! Module is fully Odoo 18 compatible.")
        sys.exit(0)

if __name__ == "__main__":
    main()
