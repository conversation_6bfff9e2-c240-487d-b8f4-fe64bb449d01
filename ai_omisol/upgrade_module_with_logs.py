#!/usr/bin/env python3
"""
Module Upgrade Script with Log Analysis
Upgrades the ai_omisol module and analyzes logs for errors
"""

import xmlrpc.client
import time
import subprocess
import re
import os

# Odoo connection details
url = "https://omnisol.arihantai.com"
db = "omnisol.arihantai.com"
username = "demo"
password = "demo"

def connect_to_odoo():
    """Connect to Odoo and return models proxy"""
    try:
        print("🔗 Connecting to Odoo server...")
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            raise Exception("Authentication failed. Please check credentials.")
        
        print(f"✅ Successfully authenticated as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        return models, uid
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return None, None

def get_module_info(models, uid, module_name):
    """Get current module information"""
    try:
        module_ids = models.execute_kw(db, uid, password, 'ir.module.module', 'search', 
                                     [[('name', '=', module_name)]])
        if not module_ids:
            print(f"❌ Module '{module_name}' not found")
            return None
        
        module_info = models.execute_kw(db, uid, password, 'ir.module.module', 'read', 
                                      [module_ids, ['name', 'state', 'latest_version', 'installed_version']])
        return module_info[0] if module_info else None
    except Exception as e:
        print(f"❌ Error getting module info: {e}")
        return None

def read_odoo_logs(lines=50):
    """Read recent Odoo server logs"""
    log_file = "/var/log/odoo/odoo-server.log"
    try:
        if os.path.exists(log_file):
            result = subprocess.run(['tail', '-n', str(lines), log_file], 
                                  capture_output=True, text=True)
            return result.stdout
        else:
            print(f"⚠️ Log file {log_file} not found")
            return ""
    except Exception as e:
        print(f"❌ Error reading logs: {e}")
        return ""

def analyze_logs_for_errors(logs):
    """Analyze logs for errors and return structured information"""
    errors = []
    warnings = []
    
    # Split logs into lines
    lines = logs.split('\n')
    
    for i, line in enumerate(lines):
        # Look for ERROR level logs
        if 'ERROR' in line and 'ai_omisol' in line:
            # Get context (current line + next few lines)
            context = '\n'.join(lines[i:i+3])
            errors.append({
                'line': line.strip(),
                'context': context,
                'type': 'ERROR'
            })
        
        # Look for WARNING level logs
        elif 'WARNING' in line and 'ai_omisol' in line:
            warnings.append({
                'line': line.strip(),
                'type': 'WARNING'
            })
        
        # Look for specific error patterns
        elif any(pattern in line for pattern in ['ParseError', 'ValueError', 'AttributeError', 'ImportError']):
            if 'ai_omisol' in line or i > 0 and 'ai_omisol' in lines[i-1]:
                context = '\n'.join(lines[max(0, i-2):i+3])
                errors.append({
                    'line': line.strip(),
                    'context': context,
                    'type': 'EXCEPTION'
                })
    
    return errors, warnings

def upgrade_module(models, uid, module_name):
    """Upgrade the specified module"""
    try:
        print(f"\n🔄 Starting upgrade of module '{module_name}'...")
        
        # Get module ID
        module_ids = models.execute_kw(db, uid, password, 'ir.module.module', 'search', 
                                     [[('name', '=', module_name)]])
        if not module_ids:
            print(f"❌ Module '{module_name}' not found")
            return False
        
        module_id = module_ids[0]
        
        # Get current logs timestamp for comparison
        print("📋 Capturing pre-upgrade logs...")
        pre_logs = read_odoo_logs(20)
        
        # Trigger module upgrade
        print("🚀 Triggering module upgrade...")
        result = models.execute_kw(db, uid, password, 'ir.module.module', 'button_immediate_upgrade', 
                                 [[module_id]])
        
        # Wait a moment for the upgrade to process
        print("⏳ Waiting for upgrade to complete...")
        time.sleep(5)
        
        # Get post-upgrade logs
        print("📋 Capturing post-upgrade logs...")
        post_logs = read_odoo_logs(50)
        
        # Analyze logs for errors
        errors, warnings = analyze_logs_for_errors(post_logs)
        
        # Get updated module info
        updated_info = get_module_info(models, uid, module_name)
        
        print(f"\n📊 Upgrade Results:")
        print(f"   Module State: {updated_info.get('state', 'unknown') if updated_info else 'unknown'}")
        print(f"   Errors Found: {len(errors)}")
        print(f"   Warnings Found: {len(warnings)}")
        
        if errors:
            print(f"\n❌ ERRORS DETECTED:")
            for i, error in enumerate(errors, 1):
                print(f"\n   Error {i}:")
                print(f"   Type: {error['type']}")
                print(f"   Line: {error['line']}")
                if 'context' in error:
                    print(f"   Context:\n{error['context']}")
        
        if warnings:
            print(f"\n⚠️ WARNINGS DETECTED:")
            for i, warning in enumerate(warnings, 1):
                print(f"   Warning {i}: {warning['line']}")
        
        if not errors:
            print("✅ Module upgrade completed successfully!")
            return True
        else:
            print("❌ Module upgrade completed with errors!")
            return False
            
    except Exception as e:
        print(f"❌ Error during module upgrade: {e}")
        
        # Still try to get logs for analysis
        logs = read_odoo_logs(30)
        errors, warnings = analyze_logs_for_errors(logs)
        
        if errors:
            print(f"\n❌ ERRORS FROM LOGS:")
            for error in errors:
                print(f"   {error['line']}")
        
        return False

def main():
    """Main function"""
    print("🚀 Starting Module Upgrade with Log Analysis")
    print("="*60)
    
    # Connect to Odoo
    models, uid = connect_to_odoo()
    if not models:
        print("❌ Failed to connect to Odoo")
        return False
    
    module_name = "ai_omisol"
    
    # Get initial module info
    print(f"\n📋 Getting current module information...")
    initial_info = get_module_info(models, uid, module_name)
    if initial_info:
        print(f"   Module: {initial_info['name']}")
        print(f"   Current State: {initial_info['state']}")
        print(f"   Installed Version: {initial_info.get('installed_version', 'N/A')}")
    
    # Perform upgrade
    success = upgrade_module(models, uid, module_name)
    
    if success:
        print(f"\n🎉 Module '{module_name}' upgraded successfully!")
    else:
        print(f"\n❌ Module '{module_name}' upgrade failed or completed with errors!")
        print("   Check the error details above for specific issues to fix.")
    
    return success

if __name__ == "__main__":
    main()
