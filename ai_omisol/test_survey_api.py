#!/usr/bin/env python3
"""
Test Survey API to understand the proper structure
"""

import xmlrpc.client

# Odoo connection details
url = "https://omnisol.arihantai.com"
db = "omnisol.arihantai.com"
username = "demo"
password = "demo"

def connect_to_odoo():
    """Connect to Odoo and return models proxy"""
    try:
        print("Connecting to Odoo server...")
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            raise Exception("Authentication failed. Please check credentials.")
        
        print(f"Successfully authenticated as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        return models, uid
    except Exception as e:
        print(f"Connection error: {e}")
        return None, None

def explore_survey_models(models, uid):
    """Explore survey models to understand structure"""
    try:
        print("\n🔍 Exploring Survey Models...")
        
        # Get survey.survey fields
        print("\n📋 Survey.survey fields:")
        survey_fields = models.execute_kw(db, uid, password, 'survey.survey', 'fields_get', [])
        important_fields = ['title', 'description', 'question_ids', 'access_mode', 'users_login_required']
        for field in important_fields:
            if field in survey_fields:
                field_info = survey_fields[field]
                print(f"  - {field}: {field_info.get('type', 'unknown')} - {field_info.get('string', 'No description')}")
        
        # Get survey.question fields
        print("\n📝 Survey.question fields:")
        question_fields = models.execute_kw(db, uid, password, 'survey.question', 'fields_get', [])
        important_q_fields = ['title', 'question_type', 'survey_id', 'suggested_answer_ids', 'sequence', 'is_page']
        for field in important_q_fields:
            if field in question_fields:
                field_info = question_fields[field]
                print(f"  - {field}: {field_info.get('type', 'unknown')} - {field_info.get('string', 'No description')}")
        
        # Get survey.question.answer fields
        print("\n💡 Survey.question.answer fields:")
        answer_fields = models.execute_kw(db, uid, password, 'survey.question.answer', 'fields_get', [])
        important_a_fields = ['value', 'question_id', 'is_correct', 'sequence']
        for field in important_a_fields:
            if field in answer_fields:
                field_info = answer_fields[field]
                print(f"  - {field}: {field_info.get('type', 'unknown')} - {field_info.get('string', 'No description')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error exploring models: {e}")
        return False

def test_create_with_one2many(models, uid):
    """Test creating survey with questions using one2many"""
    try:
        print("\n🧪 Testing Survey Creation with One2Many...")
        
        # Create survey with questions in one call
        survey_data = {
            'title': 'Test Survey with One2Many',
            'description': 'Testing one2many relationship for questions',
            'access_mode': 'public',
            'users_login_required': False,
            'question_ids': [
                (0, 0, {  # Create new question
                    'title': 'Test Question 1',
                    'question_type': 'simple_choice',
                    'sequence': 1,
                    'is_page': False,
                    'suggested_answer_ids': [
                        (0, 0, {'value': 'Answer A', 'sequence': 1, 'is_correct': True}),
                        (0, 0, {'value': 'Answer B', 'sequence': 2, 'is_correct': False}),
                        (0, 0, {'value': 'Answer C', 'sequence': 3, 'is_correct': False}),
                    ]
                }),
                (0, 0, {  # Create another question
                    'title': 'Test Question 2',
                    'question_type': 'simple_choice',
                    'sequence': 2,
                    'is_page': False,
                    'suggested_answer_ids': [
                        (0, 0, {'value': 'Option 1', 'sequence': 1, 'is_correct': False}),
                        (0, 0, {'value': 'Option 2', 'sequence': 2, 'is_correct': True}),
                    ]
                })
            ]
        }
        
        # Try to create survey with questions
        survey_id = models.execute_kw(db, uid, password, 'survey.survey', 'create', [survey_data])
        print(f"✅ Survey created with ID: {survey_id}")
        
        # Verify creation
        survey_info = models.execute_kw(db, uid, password, 'survey.survey', 'read', [[survey_id], ['title', 'question_ids']])
        if survey_info and len(survey_info) > 0:
            survey_data = survey_info[0]
            print(f"📊 Survey: {survey_data['title']}")
            print(f"📝 Questions: {len(survey_data['question_ids'])} questions created")
        
            # Get question details
            if survey_data['question_ids']:
                questions = models.execute_kw(db, uid, password, 'survey.question', 'read', [survey_data['question_ids'], ['title', 'suggested_answer_ids']])
                for q in questions:
                    print(f"  - {q['title']}: {len(q['suggested_answer_ids'])} answers")
        
        return survey_id
        
    except Exception as e:
        print(f"❌ Error testing one2many creation: {e}")
        return None

def main():
    """Main function"""
    print("🚀 Testing Survey API Structure")
    print("="*60)
    
    # Connect to Odoo
    models, uid = connect_to_odoo()
    if not models:
        print("❌ Failed to connect to Odoo")
        return False
    
    # Explore models
    explore_success = explore_survey_models(models, uid)
    if not explore_success:
        print("❌ Failed to explore models")
        return False
    
    # Test one2many creation
    survey_id = test_create_with_one2many(models, uid)
    if survey_id:
        print(f"\n🎉 SUCCESS! Survey created with ID: {survey_id}")
        print("✅ One2Many relationship works for creating surveys with questions and answers!")
    else:
        print("\n❌ One2Many creation failed")
    
    return survey_id is not None

if __name__ == "__main__":
    main()
