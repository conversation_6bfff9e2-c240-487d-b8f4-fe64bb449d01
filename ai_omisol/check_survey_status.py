#!/usr/bin/env python3
"""
Check Survey Import Status
Verifies if survey questions and answers were properly imported
"""

import xmlrpc.client

# Odoo connection details
url = "https://omnisol.arihantai.com"
db = "omnisol.arihantai.com"
username = "demo"
password = "demo"

def check_survey_status():
    try:
        print("Connecting to Odoo server...")
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            raise Exception("Authentication failed. Please check credentials.")
        
        print(f"Successfully authenticated as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        # Check surveys
        print("\n" + "="*60)
        print("📋 CHECKING SURVEYS")
        print("="*60)
        
        surveys = models.execute_kw(db, uid, password, 'survey.survey', 'search_read', 
                                   [[]], {'fields': ['id', 'title', 'access_mode']})
        
        print(f"Found {len(surveys)} surveys:")
        for survey in surveys:
            print(f"  - ID: {survey['id']}, Title: {survey['title']}")
        
        # Check questions for each survey
        for survey in surveys:
            survey_id = survey['id']
            print(f"\n📝 Checking questions for Survey ID {survey_id}: {survey['title']}")
            
            questions = models.execute_kw(db, uid, password, 'survey.question', 'search_read',
                                        [[['survey_id', '=', survey_id]]], 
                                        {'fields': ['id', 'title', 'question_type', 'sequence', 'is_page']})
            
            print(f"  Found {len(questions)} questions")
            
            if questions:
                # Show first few questions
                for i, question in enumerate(questions[:5]):
                    print(f"    Q{i+1}: {question['title'][:60]}...")
                    print(f"         Type: {question['question_type']}, Sequence: {question['sequence']}, Is Page: {question['is_page']}")
                
                if len(questions) > 5:
                    print(f"    ... and {len(questions) - 5} more questions")
                
                # Check answers for first question
                first_question_id = questions[0]['id']
                answers = models.execute_kw(db, uid, password, 'survey.question.answer', 'search_read',
                                          [[['question_id', '=', first_question_id]]], 
                                          {'fields': ['id', 'value', 'is_correct', 'sequence']})
                
                print(f"  Sample answers for first question (ID {first_question_id}):")
                for answer in answers:
                    correct_mark = "✓" if answer['is_correct'] else "✗"
                    print(f"    {correct_mark} {answer['value']}")
            else:
                print("  ❌ No questions found!")
        
        # Summary
        print("\n" + "="*60)
        print("📊 SUMMARY")
        print("="*60)
        
        total_questions = 0
        total_answers = 0
        
        for survey in surveys:
            survey_id = survey['id']
            questions = models.execute_kw(db, uid, password, 'survey.question', 'search',
                                        [[['survey_id', '=', survey_id]]])
            question_count = len(questions)
            total_questions += question_count
            
            if questions:
                answers = models.execute_kw(db, uid, password, 'survey.question.answer', 'search',
                                          [[['question_id', 'in', questions]]])
                answer_count = len(answers)
                total_answers += answer_count
                
                print(f"Survey '{survey['title']}': {question_count} questions, {answer_count} answers")
            else:
                print(f"Survey '{survey['title']}': {question_count} questions, 0 answers")
        
        print(f"\nTotal: {total_questions} questions, {total_answers} answers across all surveys")
        
        if total_questions == 0:
            print("\n❌ NO QUESTIONS FOUND - Import failed!")
            print("Recommendation: Use manual import or CSV import method")
        elif total_questions < 100:
            print(f"\n⚠️  PARTIAL IMPORT - Expected 100 questions, found {total_questions}")
            print("Recommendation: Complete the import manually")
        else:
            print(f"\n✅ IMPORT SUCCESSFUL - Found {total_questions} questions!")
        
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    check_survey_status()
