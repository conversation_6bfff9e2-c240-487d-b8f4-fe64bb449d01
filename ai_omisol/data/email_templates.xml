<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Email Template for Customer Credentials -->
        <record id="email_template_customer_credentials" model="mail.template">
            <field name="name">Customer Login Credentials</field>
            <field name="model_id" ref="model_omisol_customer_user"/>
            <field name="subject">Your Omisol Account Credentials - Welcome!</field>
            <field name="email_from">${(object.customer_id.company_id.email or '<EMAIL>')|safe}</field>
            <field name="email_to">${object.email}</field>
            <field name="lang">${object.customer_id.lang or 'en_US'}</field>
            <field name="auto_delete" eval="True"/>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px; font-family: 'Roboto', Arial, Helvetica, sans-serif; font-size: 14px; color: #1e293b;">
    <table border="0" cellpadding="0" cellspacing="0" style="padding-top: 16px; background-color: #f8fafc; width: 100%;">
        <tr>
            <td align="center">
                <table border="0" cellpadding="0" cellspacing="0" width="590" style="padding: 16px; background-color: white; color: #1e293b; border-top: 4px solid #007bff; border-radius: 8px; box-shadow: 0 4px 8px rgba(0, 123, 255, 0.15);">
                    <!-- Header -->
                    <tr>
                        <td align="center" style="padding: 20px 0;">
                            <img src="/ai_omisol/static/src/img/omisol-logo.png" alt="Omisol" style="height: 60px; max-width: 200px;" onerror="this.style.display='none'"/>
                            <h1 style="margin: 20px 0 10px 0; font-family: 'Roboto', Arial, sans-serif; font-size: 28px; font-weight: 700; color: #007bff;">
                                Welcome to Omisol!
                            </h1>
                            <p style="margin: 0; font-size: 16px; color: #64748b;">
                                Your account has been created successfully
                            </p>
                        </td>
                    </tr>
                    
                    <!-- Content -->
                    <tr>
                        <td style="padding: 20px;">
                            <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.6;">
                                Dear <strong>${object.name}</strong>,
                            </p>
                            
                            <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.6;">
                                We're excited to welcome you to Omisol! Your account has been created and you can now access our platform with the credentials below.
                            </p>
                            
                            <!-- Credentials Box -->
                            <div style="background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(249, 115, 22, 0.05) 100%); border: 2px solid #007bff; border-radius: 8px; padding: 20px; margin: 20px 0;">
                                <h3 style="margin: 0 0 15px 0; font-family: 'Roboto', Arial, sans-serif; font-size: 18px; font-weight: 600; color: #007bff;">
                                    <i style="margin-right: 8px;">🔐</i> Your Login Credentials
                                </h3>
                                
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: 600; color: #1e293b; width: 120px;">
                                            Username:
                                        </td>
                                        <td style="padding: 8px 0; font-family: 'Courier New', monospace; background: #f1f5f9; padding: 4px 8px; border-radius: 4px; color: #007bff; font-weight: 600;">
                                            ${object.username}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: 600; color: #1e293b;">
                                            Password:
                                        </td>
                                        <td style="padding: 8px 0; font-family: 'Courier New', monospace; background: #f1f5f9; padding: 4px 8px; border-radius: 4px; color: #007bff; font-weight: 600;">
                                            ${object.password}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: 600; color: #1e293b;">
                                            Login URL:
                                        </td>
                                        <td style="padding: 8px 0;">
                                            <a href="${ctx.get('base_url', 'http://localhost:8069')}/web/login" 
                                               style="color: #007bff; text-decoration: none; font-weight: 600;">
                                                ${ctx.get('base_url', 'http://localhost:8069')}/web/login
                                            </a>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            
                            <!-- Security Notice -->
                            <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; padding: 15px; margin: 20px 0;">
                                <p style="margin: 0; font-size: 14px; color: #92400e;">
                                    <strong>🔒 Security Notice:</strong> Please keep these credentials safe and consider changing your password after your first login for enhanced security.
                                </p>
                            </div>
                            
                            <!-- Call to Action -->
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="${ctx.get('base_url', 'http://localhost:8069')}/web/login" 
                                   style="display: inline-block; background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; text-decoration: none; padding: 12px 30px; border-radius: 6px; font-weight: 600; font-size: 16px; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);">
                                    Login to Your Account
                                </a>
                            </div>
                            
                            <p style="margin: 20px 0 0 0; font-size: 16px; line-height: 1.6;">
                                If you have any questions or need assistance, please don't hesitate to contact our support team.
                            </p>
                            
                            <p style="margin: 20px 0 0 0; font-size: 16px; line-height: 1.6;">
                                Best regards,<br/>
                                <strong>The Omisol Team</strong>
                            </p>
                        </td>
                    </tr>
                    
                    <!-- Footer -->
                    <tr>
                        <td style="padding: 20px; background: linear-gradient(135deg, #0056b3 0%, #003d82 100%); border-radius: 0 0 8px 8px;">
                            <table style="width: 100%;">
                                <tr>
                                    <td style="text-align: center;">
                                        <p style="margin: 0; font-size: 14px; color: #e2e8f0;">
                                            © ${datetime.datetime.now().year} Omisol Private Limited. All rights reserved.
                                        </p>
                                        <p style="margin: 10px 0 0 0; font-size: 12px; color: #94a3b8;">
                                            Leading provider of industrial solutions, chemical products, and specialized equipment.
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
            </field>
        </record>
        
        <!-- Email Template for Password Reset -->
        <record id="email_template_password_reset" model="mail.template">
            <field name="name">Customer Password Reset</field>
            <field name="model_id" ref="model_omisol_customer_user"/>
            <field name="subject">Your Omisol Account Password Has Been Reset</field>
            <field name="email_from">${(object.customer_id.company_id.email or '<EMAIL>')|safe}</field>
            <field name="email_to">${object.email}</field>
            <field name="lang">${object.customer_id.lang or 'en_US'}</field>
            <field name="auto_delete" eval="True"/>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px; font-family: 'Roboto', Arial, Helvetica, sans-serif; font-size: 14px; color: #1e293b;">
    <table border="0" cellpadding="0" cellspacing="0" style="padding-top: 16px; background-color: #f8fafc; width: 100%;">
        <tr>
            <td align="center">
                <table border="0" cellpadding="0" cellspacing="0" width="590" style="padding: 16px; background-color: white; color: #1e293b; border-top: 4px solid #f97316; border-radius: 8px; box-shadow: 0 4px 8px rgba(249, 115, 22, 0.15);">
                    <!-- Header -->
                    <tr>
                        <td align="center" style="padding: 20px 0;">
                            <img src="/ai_omisol/static/src/img/omisol-logo.png" alt="Omisol" style="height: 60px; max-width: 200px;" onerror="this.style.display='none'"/>
                            <h1 style="margin: 20px 0 10px 0; font-family: 'Roboto', Arial, sans-serif; font-size: 28px; font-weight: 700; color: #f97316;">
                                Password Reset
                            </h1>
                            <p style="margin: 0; font-size: 16px; color: #64748b;">
                                Your password has been reset successfully
                            </p>
                        </td>
                    </tr>
                    
                    <!-- Content -->
                    <tr>
                        <td style="padding: 20px;">
                            <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.6;">
                                Dear <strong>${object.name}</strong>,
                            </p>
                            
                            <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.6;">
                                Your Omisol account password has been reset. Please use the new credentials below to access your account.
                            </p>
                            
                            <!-- New Credentials Box -->
                            <div style="background: linear-gradient(135deg, rgba(249, 115, 22, 0.05) 0%, rgba(0, 123, 255, 0.05) 100%); border: 2px solid #f97316; border-radius: 8px; padding: 20px; margin: 20px 0;">
                                <h3 style="margin: 0 0 15px 0; font-family: 'Roboto', Arial, sans-serif; font-size: 18px; font-weight: 600; color: #f97316;">
                                    <i style="margin-right: 8px;">🔑</i> Your New Login Credentials
                                </h3>
                                
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: 600; color: #1e293b; width: 120px;">
                                            Username:
                                        </td>
                                        <td style="padding: 8px 0; font-family: 'Courier New', monospace; background: #f1f5f9; padding: 4px 8px; border-radius: 4px; color: #f97316; font-weight: 600;">
                                            ${object.username}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: 600; color: #1e293b;">
                                            New Password:
                                        </td>
                                        <td style="padding: 8px 0; font-family: 'Courier New', monospace; background: #f1f5f9; padding: 4px 8px; border-radius: 4px; color: #f97316; font-weight: 600;">
                                            ${object.password}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            
                            <!-- Call to Action -->
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="${ctx.get('base_url', 'http://localhost:8069')}/web/login" 
                                   style="display: inline-block; background: linear-gradient(135deg, #f97316 0%, #ea580c 100%); color: white; text-decoration: none; padding: 12px 30px; border-radius: 6px; font-weight: 600; font-size: 16px; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 4px 8px rgba(249, 115, 22, 0.3);">
                                    Login with New Password
                                </a>
                            </div>
                            
                            <p style="margin: 20px 0 0 0; font-size: 16px; line-height: 1.6;">
                                Best regards,<br/>
                                <strong>The Omisol Team</strong>
                            </p>
                        </td>
                    </tr>
                    
                    <!-- Footer -->
                    <tr>
                        <td style="padding: 20px; background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%); border-radius: 0 0 8px 8px;">
                            <table style="width: 100%;">
                                <tr>
                                    <td style="text-align: center;">
                                        <p style="margin: 0; font-size: 14px; color: #fed7aa;">
                                            © ${datetime.datetime.now().year} Omisol Private Limited. All rights reserved.
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
            </field>
        </record>
    </data>
</odoo>
