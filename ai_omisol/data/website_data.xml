<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Sample Company Statistics -->
        <record id="stat_years_experience" model="omisol.company.statistics">
            <field name="name">Years of Experience</field>
            <field name="value">25</field>
            <field name="suffix">+</field>
            <field name="icon">fa-calendar</field>
            <field name="description">Years serving the industry</field>
            <field name="sequence">10</field>
        </record>

        <record id="stat_happy_clients" model="omisol.company.statistics">
            <field name="name">Happy Clients</field>
            <field name="value">500</field>
            <field name="suffix">+</field>
            <field name="icon">fa-users</field>
            <field name="description">Satisfied customers worldwide</field>
            <field name="sequence">20</field>
        </record>

        <record id="stat_projects_completed" model="omisol.company.statistics">
            <field name="name">Projects Completed</field>
            <field name="value">1000</field>
            <field name="suffix">+</field>
            <field name="icon">fa-check-circle</field>
            <field name="description">Successfully completed projects</field>
            <field name="sequence">30</field>
        </record>

        <record id="stat_countries_served" model="omisol.company.statistics">
            <field name="name">Countries Served</field>
            <field name="value">15</field>
            <field name="suffix">+</field>
            <field name="icon">fa-globe</field>
            <field name="description">International presence</field>
            <field name="sequence">40</field>
        </record>

        <!-- Sample Product Showcase - Removed generic products, keeping only OMNTEC products from product_demo_data.xml -->

        <!-- Sample Certificates -->
        <record id="cert_iso_9001" model="omisol.company.certificate">
            <field name="name">ISO 9001:2015 Quality Management</field>
            <field name="description">Quality Management System certification ensuring consistent quality in our products and services.</field>
            <field name="issuing_authority">Bureau Veritas</field>
            <field name="certificate_number">IN-QMS-001234</field>
            <field name="issue_date">2023-01-15</field>
            <field name="expiry_date">2026-01-14</field>
            <field name="sequence">10</field>
        </record>

        <record id="cert_iso_14001" model="omisol.company.certificate">
            <field name="name">ISO 14001:2015 Environmental Management</field>
            <field name="description">Environmental Management System certification demonstrating our commitment to environmental protection.</field>
            <field name="issuing_authority">SGS India</field>
            <field name="certificate_number">IN-EMS-005678</field>
            <field name="issue_date">2023-03-20</field>
            <field name="expiry_date">2026-03-19</field>
            <field name="sequence">20</field>
        </record>

        <record id="cert_ohsas_18001" model="omisol.company.certificate">
            <field name="name">OHSAS 18001:2007 Occupational Health &amp; Safety</field>
            <field name="description">Occupational Health and Safety Management System certification ensuring workplace safety.</field>
            <field name="issuing_authority">TUV India</field>
            <field name="certificate_number">IN-OHS-009012</field>
            <field name="issue_date">2023-05-10</field>
            <field name="expiry_date">2026-05-09</field>
            <field name="sequence">30</field>
        </record>
        <!-- Website Menu Items -->
        <record id="menu_about" model="website.menu">
            <field name="name">About Us</field>
            <field name="url">/about</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence">10</field>
        </record>

        <record id="menu_products" model="website.menu">
            <field name="name">Products</field>
            <field name="url">/products</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence">20</field>
        </record>

        <record id="menu_courses" model="website.menu">
            <field name="name">Training</field>
            <field name="url">/courses</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence">30</field>
        </record>

        <record id="menu_support" model="website.menu">
            <field name="name">Support</field>
            <field name="url">/support</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence">35</field>
        </record>

        <record id="menu_contact" model="website.menu">
            <field name="name">Contact Us</field>
            <field name="url">/contactus</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence">50</field>
        </record>

    </data>
</odoo>
