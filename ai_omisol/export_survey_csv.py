#!/usr/bin/env python3
"""
Export Survey Questions to CSV
Creates CSV files for manual import into Odoo
"""

import csv
import uuid
from import_survey import survey_data

def export_to_csv():
    # Export Questions
    questions_file = 'survey_questions.csv'
    answers_file = 'survey_answers.csv'
    
    print("Exporting survey questions and answers to CSV...")
    
    # Questions CSV
    with open(questions_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['sequence', 'title', 'question_type', 'is_page', 'chapter']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for chapter in survey_data["chapters"]:
            for question in chapter["questions"]:
                is_page = question["number"] in [1, 29, 79]
                writer.writerow({
                    'sequence': question["number"],
                    'title': question["text"],
                    'question_type': question["type"],
                    'is_page': 'TRUE' if is_page else 'FALSE',
                    'chapter': chapter["chapter_name"]
                })
    
    # Answers CSV
    with open(answers_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['question_sequence', 'question_title', 'answer_value', 'is_correct', 'answer_sequence']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for chapter in survey_data["chapters"]:
            for question in chapter["questions"]:
                for idx, option in enumerate(question["options"]):
                    is_correct = option == question["correct_answer"]
                    writer.writerow({
                        'question_sequence': question["number"],
                        'question_title': question["text"][:50] + "...",
                        'answer_value': option,
                        'is_correct': 'TRUE' if is_correct else 'FALSE',
                        'answer_sequence': idx + 1
                    })
    
    print(f"✅ Questions exported to: {questions_file}")
    print(f"✅ Answers exported to: {answers_file}")
    print(f"📊 Total: {sum(len(chapter['questions']) for chapter in survey_data['chapters'])} questions")
    print(f"📊 Total: {sum(len(question['options']) for chapter in survey_data['chapters'] for question in chapter['questions'])} answers")
    
    # Create import instructions
    instructions_file = 'SURVEY_IMPORT_INSTRUCTIONS.md'
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write("""# Survey Import Instructions

## Manual Import Method (Recommended)

### Step 1: Access Survey
1. Go to Odoo backend: Apps → Survey → Surveys
2. Select Survey ID 4: "OEL8000III Series Controller Safety, Installation, Features, and Operation Survey"

### Step 2: Create Questions
Use the questions from `survey_questions.csv`:

#### Chapter 1: Safety & Installation (Questions 1-28)
- Question 1 should be marked as "Is Page" = True (starts new page)
- Questions 2-28 should be "Is Page" = False

#### Chapter 2: Features & Programming (Questions 29-78)  
- Question 29 should be marked as "Is Page" = True (starts new page)
- Questions 30-78 should be "Is Page" = False

#### Chapter 3: Operation & Diagnostics (Questions 79-100)
- Question 79 should be marked as "Is Page" = True (starts new page)
- Questions 80-100 should be "Is Page" = False

### Step 3: Add Answer Options
For each question, add the answer options from `survey_answers.csv`:
- Mark the correct answer with "Is Correct" = True
- All other answers should be "Is Correct" = False

## CSV Import Method (Alternative)

### Step 1: Prepare CSV Files
- Use `survey_questions.csv` for questions
- Use `survey_answers.csv` for answers

### Step 2: Import via Odoo
1. Go to Survey Questions list view
2. Use Import function
3. Map CSV columns to Odoo fields
4. Import questions first, then answers

## Question Types
- `simple_choice`: True/False questions
- `single_choice`: Multiple choice questions

## Total Content
- 100 Questions across 3 chapters
- Professional OEL8000III controller certification content
- All correct answers provided

## Verification
After import, verify:
- All 100 questions are present
- All answer options are correct
- Correct answers are marked properly
- Page breaks at questions 1, 29, and 79
""")
    
    print(f"📋 Import instructions created: {instructions_file}")
    print("\n" + "="*60)
    print("🎯 SURVEY EXPORT COMPLETED!")
    print("="*60)
    print("Files created:")
    print(f"  - {questions_file} (100 questions)")
    print(f"  - {answers_file} (answer options)")
    print(f"  - {instructions_file} (import guide)")
    print("\nNext steps:")
    print("1. Use manual import method (recommended)")
    print("2. Or try CSV import via Odoo interface")
    print("3. Follow instructions in SURVEY_IMPORT_INSTRUCTIONS.md")

if __name__ == "__main__":
    export_to_csv()
