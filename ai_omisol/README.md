# Omisol Website Module

A professional Odoo 18 website module for Omisol Private Limited, featuring comprehensive product showcase functionality with industrial-grade design and enhanced user experience.

## Overview

This module provides a complete website solution for showcasing industrial products, with special focus on the PROTEUS-K Automatic Tank Gauge and related equipment. Built with modern web standards and responsive design principles.

## Key Features

### 🏭 **Product Showcase**
- Detailed product pages with comprehensive information
- Professional image galleries and video integration
- Organized download centers with categorized files
- Technical specifications and feature highlights
- SEO-optimized product URLs and meta tags

### 🎨 **Professional Design**
- Industrial-themed styling with modern blue color palette
- Responsive layout optimized for all devices
- Professional typography and spacing
- Interactive elements with smooth animations
- Print-friendly styling for documentation

### � **Download Management**
- Categorized downloads (Brochures, Manuals, Specifications, Engineering)
- File type detection and appropriate icons
- Download tracking and analytics
- Secure file serving with access controls

### 🔧 **Technical Features**
- Built for Odoo 18 with modern web standards
- Clean, maintainable code structure
- Comprehensive security model
- Performance optimized assets
- Mobile-first responsive design

## Installation

### Prerequisites
- Odoo 18.0 or later
- Website module enabled

### Steps
1. **Copy Module**: Place in your Odoo addons directory as `ai_omisol`
2. **Update Apps**: Refresh the apps list in Odoo
3. **Install**: Install the "Omisol Website" module
4. **Configure**: Set up products and content through the backend

## Module Structure

```
ai_omisol/
├── __manifest__.py              # Module configuration
├── __init__.py                  # Module initialization
├── controllers/
│   └── main.py                  # Web controllers and routes
├── data/
│   ├── product_demo_data.xml    # Sample product data
│   └── website_data.xml         # Website configuration
├── models/
│   └── product_showcase.py      # Data models
├── security/
│   └── ir.model.access.csv      # Access rights
├── static/src/
│   ├── css/website.css          # Styling and themes
│   └── js/website.js            # Frontend interactions
└── views/
    ├── product_showcase_views.xml   # Backend views
    ├── website_menus.xml            # Website navigation
    └── website_templates.xml       # Frontend templates
```

## Usage

### Product Management
1. Navigate to **Website → Products** in Odoo backend
2. Create/edit products with detailed information
3. Upload product images and videos
4. Add downloadable files organized by category
5. Configure SEO settings and meta tags

### Content Organization
- **Categories**: Organize products by type (Equipment, Solutions, etc.)
- **Downloads**: Group files by type (Brochures, Manuals, Specifications)
- **Features**: Highlight key product capabilities
- **Applications**: Describe use cases and industries

## Customization

### Color Scheme
Modify CSS custom properties in `static/src/css/website.css`:
```css
:root {
    --primary-blue: #2563eb;        /* Main brand color */
    --primary-blue-light: #60a5fa;  /* Accent color */
    --primary-blue-dark: #1d4ed8;   /* Dark variant */
}
```

### Layout and Typography
- Responsive breakpoints for mobile optimization
- Professional typography with Roboto font family
- Flexible grid system for content organization
- Consistent spacing and visual hierarchy

## Featured Products

The module includes comprehensive data for:
- **PROTEUS-K Automatic Tank Gauge**: Advanced tank monitoring system
- Complete technical specifications and documentation
- Professional download center with categorized files
- Video integration and image galleries

## Technical Specifications

- **Odoo Version**: 18.0+
- **Dependencies**: website, base
- **Database**: PostgreSQL recommended
- **Browser Support**: Modern browsers (Chrome, Firefox, Safari, Edge)
- **Mobile**: Fully responsive design

## Support & Maintenance

**Omisol Private Limited**
- Website: https://www.omisol.com
- Email: <EMAIL>

## License

Proprietary software developed exclusively for Omisol Private Limited. All rights reserved.


