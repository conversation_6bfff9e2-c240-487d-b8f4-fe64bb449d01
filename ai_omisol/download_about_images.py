#!/usr/bin/env python3
"""
Script to download images from dev.omisol.co.in/home/<USER>
"""

import requests
from bs4 import BeautifulSoup
import os
import urllib.parse
from PIL import Image
import io

def create_directories():
    """Create necessary directories for storing images"""
    os.makedirs('static/src/img/about', exist_ok=True)
    os.makedirs('static/src/img/works', exist_ok=True)
    print("Created directories: static/src/img/about, static/src/img/works")

def download_image(url, filename, directory='about'):
    """Download and save an image"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # Open image and convert to RGB if necessary
        img = Image.open(io.BytesIO(response.content))
        if img.mode in ('RGBA', 'LA', 'P'):
            img = img.convert('RGB')
        
        # Save as high-quality JPEG
        filepath = os.path.join(f'static/src/img/{directory}', filename)
        img.save(filepath, 'JPEG', quality=90, optimize=True)
        
        print(f"Downloaded: {filename}")
        return True
        
    except Exception as e:
        print(f"Error downloading {url}: {e}")
        return False

def scrape_about_images():
    """Scrape images from the about page"""
    url = "http://dev.omisol.co.in/home/<USER>"
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find all images
        images = soup.find_all('img')
        
        print(f"Found {len(images)} images on about page")
        
        # Download images
        downloaded_count = 0
        for i, img in enumerate(images):
            if downloaded_count >= 10:  # Limit to 10 images
                break
                
            src = img.get('src', '')
            if not src:
                continue
                
            # Make URL absolute
            if src.startswith('//'):
                src = 'http:' + src
            elif src.startswith('/'):
                src = 'http://dev.omisol.co.in' + src
            elif not src.startswith('http'):
                src = 'http://dev.omisol.co.in/' + src
            
            # Get alt text for filename
            alt_text = img.get('alt', f'about_image_{i+1}')
            # Clean filename
            filename = ''.join(c for c in alt_text if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = filename.replace(' ', '_').lower()
            if not filename:
                filename = f'about_image_{i+1}'
            filename += '.jpg'
            
            if download_image(src, filename, 'about'):
                downloaded_count += 1
        
        print(f"Successfully downloaded {downloaded_count} about images")
        
    except Exception as e:
        print(f"Error scraping about page: {e}")

def create_work_images():
    """Create placeholder work images"""
    print("Creating work project images...")
    
    # Work project types
    projects = [
        'petroleum_facility',
        'chemical_plant', 
        'oil_terminal',
        'refinery',
        'storage_tank',
        'pipeline_monitoring'
    ]
    
    for i, project in enumerate(projects):
        try:
            # Create a placeholder image
            img = Image.new('RGB', (400, 400), color='#f8f9fa')
            
            filename = f'{project}.jpg'
            filepath = os.path.join('static/src/img/works', filename)
            img.save(filepath, 'JPEG', quality=90)
            
            print(f"Created work image: {filename}")
            
        except Exception as e:
            print(f"Error creating work image for {project}: {e}")

def main():
    """Main function"""
    print("Starting image download...")
    
    # Create directories
    create_directories()
    
    # Download about images
    scrape_about_images()
    
    # Create work images
    create_work_images()
    
    print("Image download completed!")
    print("Images saved in:")
    print("- static/src/img/about/")
    print("- static/src/img/works/")

if __name__ == "__main__":
    main()
