#!/usr/bin/env python3
"""
Capture detailed 500 error information from website
"""

import requests
import re
import time

# URLs that are returning 500 errors
error_urls = [
    "/",
    "/faq", 
    "/loader-demo"
]

base_url = "https://omnisol.arihantai.com"

def capture_500_error_details(url):
    """Capture and parse 500 error details"""
    try:
        full_url = base_url + url
        print(f"\n🔍 Analyzing 500 Error for: {full_url}")
        print("="*70)
        
        response = requests.get(full_url, timeout=10)
        
        if response.status_code == 500:
            content = response.text
            
            # Extract the main error message
            error_title_match = re.search(r'<h1[^>]*>(.*?)</h1>', content, re.DOTALL)
            if error_title_match:
                print(f"📋 Error Title: {error_title_match.group(1).strip()}")
            
            # Extract traceback if present
            traceback_match = re.search(r'<pre[^>]*>(.*?)</pre>', content, re.DOTALL)
            if traceback_match:
                traceback = traceback_match.group(1).strip()
                # Clean up HTML entities
                traceback = traceback.replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')
                print(f"\n📋 Full Traceback:")
                print(traceback)
            
            # Look for specific error patterns in the content
            error_patterns = [
                (r"ValueError: (.+)", "ValueError"),
                (r"AttributeError: (.+)", "AttributeError"), 
                (r"NameError: (.+)", "NameError"),
                (r"KeyError: (.+)", "KeyError"),
                (r"TemplateNotFound: (.+)", "Template Not Found"),
                (r"ParseError: (.+)", "Parse Error"),
                (r"View '([^']+)' in website \d+ not found", "View Not Found"),
                (r"can only parse strings", "Parse Error - String Expected"),
            ]
            
            found_errors = []
            for pattern, error_type in error_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    found_errors.append((error_type, match))
            
            if found_errors:
                print(f"\n📋 Specific Errors Found:")
                for error_type, error_msg in found_errors:
                    print(f"   {error_type}: {error_msg}")
            
            # Look for file references
            file_refs = re.findall(r'/mnt/extra-addons/ai_omisol/[^"\'<>\s]+', content)
            if file_refs:
                print(f"\n📋 File References:")
                for ref in set(file_refs):
                    print(f"   {ref}")
            
            return True
        else:
            print(f"❌ Expected 500 error but got {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error capturing details: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Capturing 500 Error Details")
    print("="*50)
    
    for url in error_urls:
        capture_500_error_details(url)
        time.sleep(1)
    
    print("\n" + "="*70)
    print("🎯 NEXT STEPS:")
    print("1. Review the error details above")
    print("2. Fix the identified issues in controllers/templates")
    print("3. Test the pages again")

if __name__ == "__main__":
    main()
