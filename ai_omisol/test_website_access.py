#!/usr/bin/env python3
"""
Test Website Access Script
Tests various website pages to identify template issues
"""

import requests
import time

# Website URLs to test
base_url = "https://omnisol.arihantai.com"
test_urls = [
    "/",
    "/products",
    "/faq",
    "/loader-demo",
    "/contactus",
]

def test_url(url):
    """Test a specific URL and return status"""
    try:
        full_url = base_url + url
        print(f"🔗 Testing: {full_url}")
        
        response = requests.get(full_url, timeout=10, allow_redirects=True)
        
        if response.status_code == 200:
            print(f"✅ SUCCESS: {url} - Status: {response.status_code}")
            
            # Check for common error indicators in content
            content = response.text.lower()
            if "traceback" in content or "error" in content or "exception" in content:
                print(f"⚠️  WARNING: {url} - Page loaded but contains error content")
                return False
            return True
        else:
            print(f"❌ ERROR: {url} - Status: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"⏰ TIMEOUT: {url} - Request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print(f"🔌 CONNECTION ERROR: {url} - Could not connect")
        return False
    except Exception as e:
        print(f"❌ EXCEPTION: {url} - {e}")
        return False

def main():
    """Main function"""
    print("🚀 Testing Website Access")
    print("="*50)
    
    success_count = 0
    total_count = len(test_urls)
    
    for url in test_urls:
        if test_url(url):
            success_count += 1
        print()  # Empty line for readability
        time.sleep(1)  # Small delay between requests
    
    print("📊 SUMMARY:")
    print(f"   Successful: {success_count}/{total_count}")
    print(f"   Failed: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 All website pages are working correctly!")
    else:
        print("⚠️  Some website pages have issues that need to be fixed.")
    
    return success_count == total_count

if __name__ == "__main__":
    main()
