#!/usr/bin/env python3
"""
Manual script to load files from static/downloads into database
Run this from Odoo shell:
python3 odoo-bin shell -d your_database_name
Then: exec(open('scripts/manual_load_files.py').read())
"""

import os
import base64
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def manual_load_files():
    """Manually load files from static/downloads directory"""
    
    # Get the download model
    download_model = env['omisol.product.download']
    
    # Call the load method
    result = download_model.load_files_from_directory()
    
    # Also try to load files directly if the method fails
    if not result:
        logger.info("Direct method failed, trying manual approach...")
        
        # Get module path
        from odoo.modules import get_module_path
        module_path = get_module_path('ai_omisol')
        downloads_dir = os.path.join(module_path, 'static', 'downloads')
        
        logger.info(f"Module path: {module_path}")
        logger.info(f"Downloads dir: {downloads_dir}")
        
        if os.path.exists(downloads_dir):
            files = os.listdir(downloads_dir)
            logger.info(f"Files in directory: {files}")
            
            # Try to load one file as test
            test_file = 'OMNTEC ATG Controller Comparison Chart.pdf'
            if test_file in files:
                file_path = os.path.join(downloads_dir, test_file)
                try:
                    with open(file_path, 'rb') as f:
                        content = f.read()
                    
                    encoded = base64.b64encode(content)
                    logger.info(f"Successfully read {len(content)} bytes from {test_file}")
                    
                    # Find the record
                    record = env.ref('ai_omisol.download_proteus_k_comparison', raise_if_not_found=False)
                    if record:
                        record.write({'file_data': encoded})
                        env.cr.commit()
                        logger.info(f"Successfully updated record with file data")
                    else:
                        logger.error("Record not found")
                        
                except Exception as e:
                    logger.error(f"Error: {e}")
        else:
            logger.error(f"Downloads directory not found: {downloads_dir}")
    
    return result

# Run the function
if __name__ == "__main__":
    logger.info("Starting manual file loading...")
    result = manual_load_files()
    logger.info(f"File loading completed. Result: {result}")

# Instructions
print("""
To run this script:

1. From Odoo shell:
   python3 odoo-bin shell -d your_database_name
   Then run: exec(open('scripts/manual_load_files.py').read())

2. Or call the method directly:
   env['omisol.product.download'].manual_load_files()

3. Check the logs for detailed information about what files were loaded.
""")
