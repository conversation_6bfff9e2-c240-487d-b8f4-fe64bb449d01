#!/usr/bin/env python3
"""
OMISOL Module Validation Script
===============================
Quick validation script to check if the module is properly structured
"""

import os
import xml.etree.ElementTree as ET
import sys

def validate_xml_files():
    """Validate all XML files for syntax errors"""
    print("🔍 Validating XML files...")
    
    xml_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.xml'):
                xml_files.append(os.path.join(root, file))
    
    errors = []
    for xml_file in xml_files:
        try:
            ET.parse(xml_file)
            print(f"✅ {xml_file}")
        except ET.ParseError as e:
            errors.append(f"❌ {xml_file}: {e}")
            print(f"❌ {xml_file}: {e}")
    
    return errors

def validate_python_files():
    """Basic validation of Python files"""
    print("\n🐍 Validating Python files...")
    
    python_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    errors = []
    for py_file in python_files:
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # Basic syntax check
                compile(content, py_file, 'exec')
            print(f"✅ {py_file}")
        except SyntaxError as e:
            errors.append(f"❌ {py_file}: {e}")
            print(f"❌ {py_file}: {e}")
        except Exception as e:
            errors.append(f"⚠️ {py_file}: {e}")
            print(f"⚠️ {py_file}: {e}")
    
    return errors

def validate_manifest():
    """Validate manifest file"""
    print("\n📋 Validating manifest...")
    
    try:
        with open('__manifest__.py', 'r', encoding='utf-8') as f:
            content = f.read()
            manifest = eval(content)
            
        required_keys = ['name', 'version', 'depends', 'data']
        missing_keys = [key for key in required_keys if key not in manifest]
        
        if missing_keys:
            print(f"❌ Missing required keys: {missing_keys}")
            return [f"Missing required keys: {missing_keys}"]
        
        print("✅ Manifest structure is valid")
        
        # Check if all data files exist
        missing_files = []
        for data_file in manifest.get('data', []):
            if not os.path.exists(data_file):
                missing_files.append(data_file)
        
        if missing_files:
            print(f"❌ Missing data files: {missing_files}")
            return [f"Missing data files: {missing_files}"]
        
        print("✅ All data files exist")
        return []
        
    except Exception as e:
        error = f"❌ Manifest validation failed: {e}"
        print(error)
        return [error]

def validate_view_consistency():
    """Check for common view issues"""
    print("\n👁️ Validating view consistency...")
    
    errors = []
    
    # Check for tree vs list consistency
    view_files = ['views/customer_management_views.xml', 'views/faq_views.xml']
    
    for view_file in view_files:
        if os.path.exists(view_file):
            try:
                with open(view_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for tree tags (should be list in Odoo 18)
                if '<tree' in content:
                    errors.append(f"❌ {view_file}: Found <tree> tags, should be <list> in Odoo 18")
                    print(f"❌ {view_file}: Found <tree> tags, should be <list>")
                
                # Check for view_mode="tree" (should be "list")
                if 'view_mode="tree' in content or "view_mode='tree" in content:
                    errors.append(f"❌ {view_file}: Found view_mode='tree', should be 'list'")
                    print(f"❌ {view_file}: Found view_mode='tree', should be 'list'")
                
                # Check for active_id usage (should be id in Odoo 18)
                if 'active_id' in content:
                    errors.append(f"⚠️ {view_file}: Found 'active_id', consider using 'id' instead")
                    print(f"⚠️ {view_file}: Found 'active_id', consider using 'id' instead")

                # Check for deprecated attrs attribute (removed in Odoo 18)
                if 'attrs=' in content:
                    errors.append(f"❌ {view_file}: Found 'attrs=' attribute, should use 'invisible=' in Odoo 18")
                    print(f"❌ {view_file}: Found 'attrs=' attribute, should use 'invisible='")

                # Check for deprecated states attribute (removed in Odoo 18)
                if 'states=' in content:
                    errors.append(f"❌ {view_file}: Found 'states=' attribute, should use 'readonly=' in Odoo 18")
                    print(f"❌ {view_file}: Found 'states=' attribute, should use 'readonly='")
                
                if not errors:
                    print(f"✅ {view_file}")
                    
            except Exception as e:
                error = f"❌ {view_file}: {e}"
                errors.append(error)
                print(error)
    
    return errors

def main():
    """Main validation function"""
    print("🚀 OMISOL Module Validation")
    print("=" * 50)
    
    all_errors = []
    
    # Run all validations
    all_errors.extend(validate_xml_files())
    all_errors.extend(validate_python_files())
    all_errors.extend(validate_manifest())
    all_errors.extend(validate_view_consistency())
    
    print("\n" + "=" * 50)
    
    if all_errors:
        print(f"❌ Validation completed with {len(all_errors)} errors:")
        for error in all_errors:
            print(f"  • {error}")
        sys.exit(1)
    else:
        print("✅ All validations passed! Module is ready for installation.")
        sys.exit(0)

if __name__ == "__main__":
    main()
