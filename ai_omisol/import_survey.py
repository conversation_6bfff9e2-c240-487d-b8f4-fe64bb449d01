import xmlrpc.client
import uuid
import json

# Odoo connection details
url = "https://omnisol.arihantai.com"  # Odoo server URL
db = "omnisol.arihantai.com"  # Odoo database name
username = "demo"  # Update with actual admin username
password = "demo"  # Update with actual admin password

# Connect to Odoo
try:
    print("Connecting to Odoo server...")
    common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
    uid = common.authenticate(db, username, password, {})

    if not uid:
        raise Exception("Authentication failed. Please check credentials.")

    print(f"Successfully authenticated as user ID: {uid}")
    models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')

except Exception as e:
    print(f"Connection error: {e}")
    exit(1)

# Survey data (based on the provided JSON structure)
survey_data = {
    "survey_title": "OEL8000III Series Controller Safety, Installation, Features, and Operation Survey",
    "chapters": [
        {
            "chapter_name": "Safety & Installation",
            "questions": [
                {"number": 1, "text": "It is important to block off the work area before beginning any work on tanks and other hazardous locations.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 2, "text": "What must you always do before servicing any sensors, probes, or replacing any components in the ATG controller?", "type": "single_choice", "options": ["Turn off power", "Take photos of wiring", "Press the test button", "Check programming"], "correct_answer": "Turn off power"},
                {"number": 3, "text": "Holes can be drilled in the chassis of the OEL8000III series controllers for conduit runs only if pre‐formed knockouts do not line up with the conduit.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 4, "text": "The OEL8000III series controllers and remote annunciators may be installed in hazardous locations.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 5, "text": "What is the input power requirements for the OEL8000III series controller?", "type": "single_choice", "options": ["90‐350 VAC", "100‐240 VAC", "85‐125 VAC", "260‐480 VAC"], "correct_answer": "90‐350 VAC"},
                {"number": 6, "text": "The purpose of the knock-out's on the OEL8000III series controllers are for?", "type": "single_choice", "options": ["To provide for easy mounting of required conduits", "To separate intrinsically and non-intrinsically safe compartments inside of the controller", "To provide for ventilation of the controller", "None of the above"], "correct_answer": "To provide for easy mounting of required conduits"},
                {"number": 7, "text": "Which sets of cabling can be combined in a single conduit?", "type": "single_choice", "options": ["AC Power and Remote Annunciator", "AC Power and Probe", "Probe and Sensor", "Sensor and Remote Annunciator"], "correct_answer": "AC Power and Remote Annunciator"},
                {"number": 8, "text": "To protect your OEL8000III series controller from power surges (i.e.: lighting), you should?", "type": "single_choice", "options": ["Connect the ground wire to earth ground", "Connect to a UPS", "Install OMNTEC’s SP‐2 surge protector", "All the above"], "correct_answer": "Connect the ground wire to earth ground"},
                {"number": 9, "text": "An Earth Ground wire must be connected when installing an OEL8000III series controller.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 10, "text": "What AMP rating circuit breaker must be used with an OEL8000III series controller for 120 VAC service?", "type": "single_choice", "options": ["15 amp", "60 amp", "5 amp", "35 amp"], "correct_answer": "15 amp"},
                {"number": 11, "text": "OEL8000III series controllers can be mounted outdoors if which of these components are added?", "type": "single_choice", "options": ["ENC‐4X", "TEM‐CS", "HTR-1", "All the above"], "correct_answer": "ENC‐4X"},
                {"number": 12, "text": "The MTG probes require which cable for installation?", "type": "single_choice", "options": ["EC‐4 (or Belden 9940)", "EC‐2 (or Belden 8761)", "EC‐6", "Any 22 AWG shielded cable"], "correct_answer": "EC‐4 (or Belden 9940)"},
                {"number": 13, "text": "MTG probe cable cannot exceed how many feet away from the OEL8000III series controller?", "type": "single_choice", "options": ["500’", "1000’", "2000’", "4000’"], "correct_answer": "500’"},
                {"number": 14, "text": "A water-proof junction box must be used to house any splices from the MTG probe to the OEL8000III series controller.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 15, "text": "What determines the minimum clearance above the tank for proper probe installation?", "type": "single_choice", "options": ["The probe type", "The probe lenght", "The space above the tank", "All the above"], "correct_answer": "The probe type"},
                {"number": 16, "text": "The ballast on an MTG probe is located where?", "type": "single_choice", "options": ["On the head of the probe", "On the water float", "Part of the retaining clip", "On the product float"], "correct_answer": "On the head of the probe"},
                {"number": 17, "text": "For maximum accuracy, the bottom of an MTG probe should always be resting on the floor of the tank.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 18, "text": "The floats on an Oil Water Separator are installed the same way as any other probe.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 19, "text": "Oil Water Separators can only be installed on an OEL8000IIIX series controller.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 20, "text": "When wiring the 'Bright Eye' BX-Series sensors, can you run one 4‐conductor cable out to the tank field and loop all the sensors to one cable?", "type": "single_choice", "options": ["Yes", "No", "Sometimes"], "correct_answer": "Yes"},
                {"number": 21, "text": "The BX-Series sensors require what type of cable?", "type": "single_choice", "options": ["2-conductor 22 AWG (or better) shielded with drain", "6-conductor 22 AWG (or better) shielded with drain", "4-conductor 22 AWG (or better) shielded with drain", "12-conductor 22 AWG (or better) shielded with drain"], "correct_answer": "2-conductor 22 AWG (or better) shielded with drain"},
                {"number": 22, "text": "BX-Series sensor cable cannot exceed how many feet from the OEL8000III series controller?", "type": "single_choice", "options": ["500’", "1000’", "2000’", "3000’"], "correct_answer": "500’"},
                {"number": 23, "text": "A dry contact input or float-type sensor can be used with an OEL8000III series controller if connected to what?", "type": "single_choice", "options": ["BX‐RES", "BX‐UT", "Float sensors cannot be used with OMNTEC ATG controllers"], "correct_answer": "BX‐RES"},
                {"number": 24, "text": "Which sensor is used to monitor temperature in refrigerators, freezers and coolers with any OEL8000III series controller?", "type": "single_choice", "options": ["BX‐VC‐1", "BX‐TC‐1", "BX‐UT", "None"], "correct_answer": "BX‐VC‐1"},
                {"number": 25, "text": "The RAS remote annunciators used with OEL8000III series controllers are powered by what voltage?", "type": "single_choice", "options": ["120 VAC", "12 VDC", "24 VDC", "30 VAC"], "correct_answer": "120 VAC"},
                {"number": 26, "text": "RAS remote annunciators require shielded cable.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 27, "text": "When using an OEL8000IIIK series controller, an external relay box is required when wiring a RAS remote annunciator with more than two lights.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 28, "text": "What means does the Mini Me use to communicate with any industry-standard ATG?", "type": "single_choice", "options": ["RS‐232 port", "RS‐485 port", "Both A and B", "None of the above"], "correct_answer": "RS‐232 port"}
            ]
        },
        {
            "chapter_name": "Features & Programming",
            "questions": [
                {"number": 29, "text": "The OEL8000III series controllers have the ability to measure in metric.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 30, "text": "The OEL8000III series controllers are UL, C.U.L, CE and ATEX approved.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 31, "text": "Which of the following features come standard on a OEL8000III series K, X and W controller?", "type": "single_choice", "options": ["Ethernet", "Webserver", "Email", "All the above"], "correct_answer": "Ethernet"},
                {"number": 32, "text": "OEL8000III series controllers utilize what type of printer?", "type": "single_choice", "options": ["Thermal", "Ink jet", "Laser", "Solid ink"], "correct_answer": "Thermal"},
                {"number": 33, "text": "The OEL8000III series controller provides for Parameter Backup and SC Card logging.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 34, "text": "How many on-board relays are there in an OEL8000III series K, X and W controllers?", "type": "single_choice", "options": ["1", "2", "3", "4"], "correct_answer": "1"},
                {"number": 35, "text": "What are the internal relays rated at on OEL8000III series K, X and W controllers?", "type": "single_choice", "options": ["5A @ 120VAC", "3A @ 120VAC", ".45A @ 125VAC", "2A @ 24VDC"], "correct_answer": "5A @ 120VAC"},
                {"number": 36, "text": "The on-board relays in an OEL8000III series K, X and W controller can be tied directly to a pump for pump shutdown.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 37, "text": "Which accessory can be added to an OEL8000IIIK4 or K8 series controller for additional relay outputs?", "type": "single_choice", "options": ["XB‐RB8", "XC‐R8", "Both A and B", "Neither A nor B"], "correct_answer": "XB‐RB8"},
                {"number": 38, "text": "Modbus is available via which of these methods when using a OEL8000III series K, X or W controller?", "type": "single_choice", "options": ["RS‐485 port", "RS‐232 port", "Ethernet", "All the above"], "correct_answer": "RS‐485 port"},
                {"number": 39, "text": "How many RS-232 ports come standard in the OEL8000III series controllers?", "type": "single_choice", "options": ["2", "4", "1", "8"], "correct_answer": "2"},
                {"number": 40, "text": "The OEL8000IIIK4 controller can accept up to how many MTG Probes?", "type": "single_choice", "options": ["40", "8", "16", "4"], "correct_answer": "40"},
                {"number": 41, "text": "The OEL8000IIIX controller can accept up to how many MTG Probes?", "type": "single_choice", "options": ["40", "8", "16", "4"], "correct_answer": "40"},
                {"number": 42, "text": "The OMNTEC MTG inventory probes provide which measurements:", "type": "single_choice", "options": ["Product levels", "Water levels", "Temperature", "All the above"], "correct_answer": "Product levels"},
                {"number": 43, "text": "What is the principle of operation for OMNTEC’s optical BX sensors?", "type": "single_choice", "options": ["Refraction of light", "Conductivity", "Reflection of sound", "Inductance"], "correct_answer": "Refraction of light"},
                {"number": 44, "text": "How many total BX sensors can the OEL8000IIIK4 handle?", "type": "single_choice", "options": ["22", "8", "16", "11"], "correct_answer": "22"},
                {"number": 45, "text": "How many sensor network bus connections are available in the OEL8000IIIK4?", "type": "single_choice", "options": ["1", "2", "3", "4"], "correct_answer": "1"},
                {"number": 46, "text": "Only BX sensors can be used on OEL8000III series controllers.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 47, "text": "The primary application of a remote annunciators is as a cautionary audio-visual alarm.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 48, "text": "The RAS remote annunciator can only be tested from the ATG controller.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 49, "text": "The Mini-Me can be used to acknowledge alarms on the main OEL8000III series controller it's connected to.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 50, "text": "The Mini-Me can only be programmed for transmitter mode.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 51, "text": "The level display in the RD625 remote display connects with an OEL8000III series K, X or W controller via", "type": "single_choice", "options": ["3-conductor cable from the RS‐232 port", "3-conductor cable from the RS‐485 port", "RJ-45 connection from Ethernet port", "Both A and B"], "correct_answer": "3-conductor cable from the RS‐232 port"},
                {"number": 52, "text": "When programming an MTG probe into the OEL8000III series controller, where would you find the wire speed number?", "type": "single_choice", "options": ["Installation manual", "On the Internet", "Tank chart", "Label on the head of the MTG probe"], "correct_answer": "Installation manual"},
                {"number": 53, "text": "Tank charts must be programmed into the OEL8000III series controller for conversion from level to volume.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 54, "text": "The final calibration procedure for MTG probes requires the following procedures:", "type": "single_choice", "options": ["Stick the tank using water paste", "Enter the product level into Proteus setup", "Enter the water level into Proteus setup", "All the above"], "correct_answer": "Stick the tank using water paste"},
                {"number": 55, "text": "In the OEL8000III series controller 'Setup Menu', where would you delete a sensor?", "type": "single_choice", "options": ["BX Sensor Parameters", "Tank Parameters", "Sensor Control", "None of the above"], "correct_answer": "BX Sensor Parameters"},
                {"number": 56, "text": "In an OEL8000III series controller, BX sensors can be programmed with its exact description and location.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 57, "text": "In the OEL8000III series controller 'Setup Menu', which sub menu do you enter to program sensor locations?", "type": "single_choice", "options": ["BX Sensor Parameters", "Tank Parameters", "Sensor Control", "None of the above"], "correct_answer": "BX Sensor Parameters"},
                {"number": 58, "text": "How many programmed events can be assigned to the MCU board relays?", "type": "single_choice", "options": ["10", "40", "30", "50"], "correct_answer": "10"},
                {"number": 59, "text": "Where can you program relay events and modes in the Setup Menu of the OEL8000III series K, X or W controller?", "type": "single_choice", "options": ["Email Account", "Interface Boards/Relays", "Communications", "Network Properties"], "correct_answer": "Interface Boards/Relays"},
                {"number": 60, "text": "What are the three relay modes?", "type": "single_choice", "options": ["Light, Sensor, Probe", "Light, Sensor, Horn", "Light, Sensor, Relay", "Light, Horn, Relay"], "correct_answer": "Light, Sensor, Probe"},
                {"number": 61, "text": "The relays on the MCU board can be used for RAS-3 series remote annunciators.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 62, "text": "Where would you program Comm Port settings?", "type": "single_choice", "options": ["Utilities/Setup/Comm Ports", "Utilities/Setup/Modbus", "Utilities/Setup/Network Properties", "Utilities/Help Menu/Version Numbers"], "correct_answer": "Utilities/Setup/Comm Ports"},
                {"number": 63, "text": "What are the default RS‐232 communication settings for the OEL8000III series controllers?", "type": "single_choice", "options": ["9600 baud, 8 bits, odd parity, 1 stop bit", "57600 baud, 8 bits, no parity, 1 stop bit", "57600 baud, 8 bits, no parity, 0 stop bits", "9600 baud, 8 bits, no parity, 1 stop bit"], "correct_answer": "9600 baud, 8 bits, odd parity, 1 stop bit"},
                {"number": 64, "text": "Modbus capabilities with OEL8000III series K, X and W controllers are:", "type": "single_choice", "options": ["Security code enabled", "RS‐485 or RS‐232 compatible", "Ethernet compatible", "All the above"], "correct_answer": "Security code enabled"},
                {"number": 65, "text": "Where would you program an IP address?", "type": "single_choice", "options": ["Utilities/Setup/Comm Ports", "Utilities/Setup/Modbus", "Utilities/Setup/Network Properties", "Utilities/System Units"], "correct_answer": "Utilities/Setup/Network Properties"},
                {"number": 66, "text": "In the sub menu, NETWORK PROPERTIES, the IP address can be set for static or dynamic.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 67, "text": "In the OEL8000III series K, X and W controllers, how many email addresses or mobile numbers can you send alarms to?", "type": "single_choice", "options": ["2", "5", "6", "8"], "correct_answer": "2"},
                {"number": 68, "text": "When using an OEL8000III series controller, on which screen can a VLD test be programmed?", "type": "single_choice", "options": ["Utilities/Set Up/VLD Leak System Settings", "Reports/VLD Log", "Sensor Status/View Sensors", "Utilities/Diagnostics"], "correct_answer": "Utilities/Set Up/VLD Leak System Settings"},
                {"number": 69, "text": "In OEL8000III series controllers, while a VLD test is running, how many hours must a tank remain inactive?", "type": "single_choice", "options": ["2", "4.5", "6", "8.5"], "correct_answer": "2"},
                {"number": 70, "text": "In OEL8000III series controllers, after a VLD test has been completed, which GPH result can you print out?", "type": "single_choice", "options": ["0.1 GPH", "0.2 GPH", "Both A and B", "Based on parameters of test"], "correct_answer": "0.1 GPH"},
                {"number": 71, "text": "Where would you go to enable CITLD?", "type": "single_choice", "options": ["Tank settings in Setup", "Miscellaneous settings in Setup", "CITLD/Leak Detection settings in Setup", "VLD/Leak Detection settings in Setup"], "correct_answer": "Tank settings in Setup"},
                {"number": 72, "text": "CITLD can be added to a system already installed in the field.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 73, "text": "Where would you set up the Oil Water Separator tank profile?", "type": "single_choice", "options": ["Utilities/Set up/Tank Parameters", "Utilities/Set up/Tank Alarm Settings", "Utilities/Set up/Tank Colors & Orientation", "Utilities/Set up/System Units"], "correct_answer": "Utilities/Set up/Tank Parameters"},
                {"number": 74, "text": "How many Alarm Set Points are required for an Oil Water Separator?", "type": "single_choice", "options": ["1", "3", "5", "7"], "correct_answer": "1"},
                {"number": 75, "text": "Where do you set the wireless System ID number?", "type": "single_choice", "options": ["Utilities/Set up/Network Properties", "Utilities/Set up/Misc Settings", "Utilities/Set up/Datacheck Settings", "Utilities/Set up/System Units"], "correct_answer": "Utilities/Set up/Network Properties"},
                {"number": 76, "text": "Datacheck Transmitter wiring determines what tank/sensor it will be assigned to.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 77, "text": "Datacheck Transmitter programming can be done in the field.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 78, "text": "In the OEL8000III series controller, where in SETUP would you change the security code?", "type": "single_choice", "options": ["System Units", "Network Properties", "Modbus", "Miscellaneous Settings"], "correct_answer": "System Units"}
            ]
        },
        {
            "chapter_name": "Operation, Diagnostics & Troubleshooting",
            "questions": [
                {"number": 79, "text": "In the OEL8000III series controllers, which menu would you enter to view any current alarms?", "type": "single_choice", "options": ["Reports", "Alarms", "Sensor Status", "Utilities"], "correct_answer": "Reports"},
                {"number": 80, "text": "When using OEL8000III series controllers, how many shift reports can automatically be printed out?", "type": "single_choice", "options": ["1", "5", "4", "0"], "correct_answer": "1"},
                {"number": 81, "text": "On any of the OEL8000III series controllers, what other term is used when describing 'Delivery Reports'.", "type": "single_choice", "options": ["Move", "Fill", "Pour", "Drop"], "correct_answer": "Move"},
                {"number": 82, "text": "What is the term OMNTEC uses for a leak test that can run on a tank, located at a site, that operates 24 hours a day and cannot shut down?", "type": "single_choice", "options": ["VLD (Volumetric Leak Detection)", "Sensor Test", "SLD (Statistical Leak Detection)", "CITLD (Continuous In-Tank Leak Detection)"], "correct_answer": "VLD (Volumetric Leak Detection)"},
                {"number": 83, "text": "The time it takes for the content of a tank to settle is the definition of what term?", "type": "single_choice", "options": ["Delta", "Ullage", "VLD test time", "Dwell time"], "correct_answer": "Delta"},
                {"number": 84, "text": "In OEL8000III series controllers, which key will shut off the horn after an alarm has occurred?", "type": "single_choice", "options": ["Test", "ACK", "Menu", "Print"], "correct_answer": "Test"},
                {"number": 85, "text": "When using the OEL8000III series controllers, what key is pressed to test the following: RAM, MTG probes, EPROM, BX‐Series sensors, LCD, LED’s, and horn?", "type": "single_choice", "options": ["Print", "Test", "ACK", "Menu"], "correct_answer": "Print"},
                {"number": 86, "text": "When using an OEL8000III series K, X or W controllers, what could you do if the printer is not printing?", "type": "single_choice", "options": ["Check that the thermal paper is installed properly with the thermal side facing up", "Reset Unit", "Power the system down, then power up while holding down the feed button", "All the above"], "correct_answer": "Check that the thermal paper is installed properly with the thermal side facing up"},
                {"number": 87, "text": "Printouts can be directed to the installed or network printer at the same time.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 88, "text": "Where can you check what firmware version is installed in the OEL8000III series controllers?", "type": "single_choice", "options": ["Utilities/Help Menu/Version Numbers", "Utilities/Setup/Network Properties", "Utilities/Diagnostics", "Utilities/System Boards"], "correct_answer": "Utilities/Help Menu/Version Numbers"},
                {"number": 89, "text": "What does a SYSTEM BUS alarm mean?", "type": "single_choice", "options": ["The probes are not wired properly", "There is a MCU Can Bus communication error between system boards", "The sensors are not wired properly", "The display is not working"], "correct_answer": "The probes are not wired properly"},
                {"number": 90, "text": "What should you do if there is a probe timeout error display on the OEL8000III series controller?", "type": "single_choice", "options": ["Swap probe inputs", "Check for pulsating DC voltage across white and black wires", "Connect probe directly to controller", "All the above"], "correct_answer": "Swap probe inputs"},
                {"number": 91, "text": "You should check probe installation, assembly and wiring when troubleshooting probe errors.", "type": "single_choice", "options": ["Always", "Never", "Sometimes"], "correct_answer": "Always"},
                {"number": 92, "text": "What should you do if you receive a SENSOR NO-REPLY on your OEL8000III series controller.", "type": "single_choice", "options": ["Remove the sensor from the installation and connect directly at the controller", "Check the sensor with a volt meter to verify 12VDC is present", "Replace the sensor", "Both A & B"], "correct_answer": "Remove the sensor from the installation and connect directly at the controller"},
                {"number": 93, "text": "If one sensor on a BX sensor bus fails, all sensors on that bus after that faulty sensor will fail.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 94, "text": "What is the best method for testing if a sensor is functioning properly?", "type": "single_choice", "options": ["Press the test button on the controller", "Short the white and green wires together", "Place the sensor in liquid and see if it alarms", "Wire the sensor directly at the controller"], "correct_answer": "Press the test button on the controller"},
                {"number": 95, "text": "Wiring issues are the only failure causes for any connected accessory.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 96, "text": "How can you test an RAS-Series remote?", "type": "single_choice", "options": ["By pressing the test button on the main ATG", "By holding down the acknowledgement switch for 5 seconds", "Both A and B", "Neither A nor B"], "correct_answer": "By pressing the test button on the main ATG"},
                {"number": 97, "text": "A common problem with RAS series remote annunciators is the grounding jumper installation.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 98, "text": "If the Mini Me remote display is not responding after hooking it up, you should:", "type": "single_choice", "options": ["Switch RS‐232 cables", "Swap Tx with Rx on the Mini Me", "Reprogram the OEL8000III series controller", "Reboot the Mini Me"], "correct_answer": "Switch RS‐232 cables"},
                {"number": 99, "text": "Line-of-sight has no effect on wireless Datacheck transmitters signal.", "type": "simple_choice", "options": ["True", "False"], "correct_answer": "True"},
                {"number": 100, "text": "Datacheck Transmitter communications can be checked using the following", "type": "single_choice", "options": ["Utilities/Diagnostics/View Probe 2222's", "Sensor Status/Fillcheck Diagnostic Transmissions", "Hyperterminal session to the XB-416DC/XB-800DC RS232 test port", "All the above"], "correct_answer": "Utilities/Diagnostics/View Probe 2222's"}
            ]
        }
    ]
}

# Create a new survey
print("Creating survey...")
try:
    survey_vals = {
        "title": survey_data["survey_title"],
        "access_mode": "public",
        "is_attempts_limited": False,
        "description": "Comprehensive certification survey for OEL8000III Series Controller covering Safety, Installation, Features, and Operation.",
    }
    survey_ids = models.execute_kw(db, uid, password, 'survey.survey', 'create', [[survey_vals]])
    survey_id = survey_ids[0] if isinstance(survey_ids, list) else survey_ids
    survey_external_id = f"__export__.survey_OEL8000III_Certification_{uuid.uuid4().hex[:8]}"
    print(f"Survey created successfully with ID: {survey_id}")

except Exception as e:
    print(f"Error creating survey: {e}")
    exit(1)

# Write external ID for the survey
models.execute_kw(db, uid, password, 'ir.model.data', 'create', [[{
    'name': survey_external_id.split('__export__.')[-1],
    'model': 'survey.survey',
    'module': '__export__',
    'res_id': survey_id,
}]])

# Process questions and answers
print("Processing questions and answers...")
question_records = []
answer_records = []
sequence = 10  # Starting sequence for answers
total_questions = sum(len(chapter["questions"]) for chapter in survey_data["chapters"])
current_question = 0

for chapter_idx, chapter in enumerate(survey_data["chapters"]):
    print(f"Processing Chapter {chapter_idx + 1}: {chapter['chapter_name']}")

    for question in chapter["questions"]:
        current_question += 1
        print(f"  Creating question {current_question}/{total_questions}: Q{question['number']}")

        try:
            # Generate unique external ID for the question
            question_external_id = f"__export__.survey_question_{question['number']}_{uuid.uuid4().hex[:8]}"

            # Determine if this question starts a new page
            is_page = question["number"] in [1, 29, 79]

            # Create question record
            question_vals = {
                "title": question["text"],
                "question_type": question["type"],
                "survey_id": survey_id,
                "is_page": is_page,
                "sequence": question["number"],
                "page_id": False,  # Will be set if this is not a page
            }
            question_ids = models.execute_kw(db, uid, password, 'survey.question', 'create', [[question_vals]])
            question_id = question_ids[0] if isinstance(question_ids, list) else question_ids

            # Write external ID for the question
            models.execute_kw(db, uid, password, 'ir.model.data', 'create', [[{
                'name': question_external_id.split('__export__.')[-1],
                'model': 'survey.question',
                'module': '__export__',
                'res_id': question_id,
            }]])

            # Create answer records
            for idx, option in enumerate(question["options"]):
                answer_external_id = f"__export__.survey_question_answer_{question['number']}_{idx}_{uuid.uuid4().hex[:8]}"
                is_correct = option == question["correct_answer"]
                answer_vals = {
                    "value": option,
                    "question_id": question_id,
                    "sequence": sequence + idx,
                    "is_correct": is_correct,
                }
                answer_ids = models.execute_kw(db, uid, password, 'survey.question.answer', 'create', [[answer_vals]])
                answer_id = answer_ids[0] if isinstance(answer_ids, list) else answer_ids

                # Write external ID for the answer
                models.execute_kw(db, uid, password, 'ir.model.data', 'create', [[{
                    'name': answer_external_id.split('__export__.')[-1],
                    'model': 'survey.question.answer',
                    'module': '__export__',
                    'res_id': answer_id,
                }]])

                answer_records.append({
                    "id": answer_external_id,
                    "sequence": sequence + idx,
                    "question_id": question["text"],
                    "value": option,
                    "id_duplicate": answer_external_id,
                    "question_id/id": question_external_id,
                })
        
            question_records.append({
                "id": question_external_id,
                "question_type": question["type"],
                "survey_id": "OEL8000III_Certification",
                "title": question["text"],
                "id_duplicate": question_external_id,
                "is_page": str(is_page).lower(),
            })

            sequence += len(question["options"]) + 10  # Increment sequence for next question

        except Exception as e:
            print(f"    Error creating question {question['number']}: {e}")
            print(f"    Question data: {question}")
            print(f"    Question vals: {question_vals}")
            continue

# # Optionally, write CSV files for reference (not necessary for XML-RPC import)
# with open('survey_question.csv', 'w', newline='', encoding='utf-8') as f:
#     writer = csv.DictWriter(f, fieldnames=["id", "question_type", "survey_id", "title", "id", "is_page"])
#     writer.writeheader()
#     for record in question_records:
#         writer.writerow({
#             "id": record["id"],
#             "question_type": record["question_type"],
#             "survey_id": record["survey_id"],
#             "title": record["title"],
#             "id": record["id_duplicate"],
#             "is_page": record["is_page"],
#         })

# with open('survey_label.csv', 'w', newline='', encoding='utf-8') as f:
#     writer = csv.DictWriter(f, fieldnames=["id", "sequence", "question_id", "value", "id", "question_id/id"])
#     writer.writeheader()
#     for record in answer_records:
#         writer.writerow({
#             "id": record["id"],
#             "sequence": record["sequence"],
#             "question_id": record["question_id"],
#             "value": record["value"],
#             "id": record["id_duplicate"],
#             "question_id/id": record["question_id/id"],
#         })

print("\n" + "="*60)
print("🎉 SURVEY IMPORT COMPLETED SUCCESSFULLY!")
print("="*60)
print(f"Survey Title: {survey_data['survey_title']}")
print(f"Total Questions: {total_questions}")
print(f"Chapters: {len(survey_data['chapters'])}")
print(f"Survey ID: {survey_id}")
print("="*60)
print("✅ All 100 questions with answers have been imported!")
print("✅ Survey is now available in Odoo backend")
print("✅ Access the survey from: Apps → Survey → Surveys")
print("="*60)