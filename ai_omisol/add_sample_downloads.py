#!/usr/bin/env python3
"""
Script to add sample download data for product ID 4
"""

import xmlrpc.client

# Odoo connection details
url = 'https://omnisol.arihantai.com'
db = 'odoo'
username = '<EMAIL>'
password = 'Arihant@123'

try:
    # Connect to Odoo
    common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
    uid = common.authenticate(db, username, password, {})

    if uid:
        print(f"✅ Successfully authenticated as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        # Sample download data for product ID 4 (Technical Support Services)
        sample_downloads = [
            {
                'name': 'Technical Support Services Overview',
                'file_type': 'brochure',
                'description': 'Comprehensive overview of our technical support services and capabilities',
                'is_active': True,
                'product_id': 4,
                'file_size': '2.5 MB',
                'download_url': '/ai_omisol/static/downloads/technical-support-brochure.pdf'
            },
            {
                'name': 'Service Level Agreement (SLA)',
                'file_type': 'datasheet',
                'description': 'Detailed service level agreements and response times for technical support',
                'is_active': True,
                'product_id': 4,
                'file_size': '1.2 MB',
                'download_url': '/ai_omisol/static/downloads/sla-datasheet.pdf'
            },
            {
                'name': 'Support Request User Manual',
                'file_type': 'manual',
                'description': 'Step-by-step guide for submitting and tracking support requests',
                'is_active': True,
                'product_id': 4,
                'file_size': '3.1 MB',
                'download_url': '/ai_omisol/static/downloads/support-manual.pdf'
            },
            {
                'name': 'ISO 9001:2015 Quality Certificate',
                'file_type': 'certificate',
                'description': 'Quality management system certification for our support services',
                'is_active': True,
                'product_id': 4,
                'file_size': '1.8 MB',
                'download_url': '/ai_omisol/static/downloads/iso-certificate.pdf'
            },
            {
                'name': 'Remote Support Setup Guide',
                'file_type': 'manual',
                'description': 'Instructions for setting up remote support access',
                'is_active': True,
                'product_id': 4,
                'file_size': '2.3 MB',
                'download_url': '/ai_omisol/static/downloads/remote-support-guide.pdf'
            }
        ]
        
        # Create download records
        created_count = 0
        for download_data in sample_downloads:
            try:
                download_id = models.execute_kw(db, uid, password, 'omisol.product.download', 'create', [download_data])
                print(f"✅ Created download: {download_data['name']} (ID: {download_id})")
                created_count += 1
            except Exception as e:
                print(f"❌ Error creating download {download_data['name']}: {e}")
        
        print(f"\n🎉 Successfully created {created_count} download records!")
        
        # Verify the downloads were created
        downloads = models.execute_kw(db, uid, password, 'omisol.product.download', 'search_read', 
                                    [[['product_id', '=', 4]]], 
                                    {'fields': ['name', 'file_type', 'is_active']})
        
        print(f"\n📋 Current downloads for product ID 4:")
        for download in downloads:
            print(f"   - {download['name']} ({download['file_type']}) - Active: {download['is_active']}")
        
    else:
        print("❌ Authentication failed")
        
except Exception as e:
    print(f"❌ Connection error: {e}")
