#!/usr/bin/env python3
"""
Comprehensive Website Error Diagnosis Script
Captures detailed error information from website pages
"""

import requests
import re
import time
import subprocess
import os

# Website URLs to test
base_url = "https://omnisol.arihantai.com"
test_urls = [
    "/",
    "/products", 
    "/faq",
    "/loader-demo",
    "/contactus",
]

def read_odoo_logs(lines=100):
    """Read recent Odoo server logs"""
    log_file = "/var/log/odoo/odoo-server.log"
    try:
        if os.path.exists(log_file):
            result = subprocess.run(['tail', '-n', str(lines), log_file], 
                                  capture_output=True, text=True)
            return result.stdout
        else:
            print(f"⚠️ Log file {log_file} not found")
            return ""
    except Exception as e:
        print(f"❌ Error reading logs: {e}")
        return ""

def extract_error_details(content):
    """Extract error details from HTML content"""
    errors = []
    
    # Look for Python tracebacks
    traceback_pattern = r'Traceback \(most recent call last\):(.*?)(?=\n\n|\n<|\Z)'
    tracebacks = re.findall(traceback_pattern, content, re.DOTALL)
    
    for tb in tracebacks:
        errors.append({
            'type': 'Python Traceback',
            'content': tb.strip()
        })
    
    # Look for specific error patterns
    error_patterns = [
        (r'ValueError: (.+)', 'ValueError'),
        (r'AttributeError: (.+)', 'AttributeError'),
        (r'NameError: (.+)', 'NameError'),
        (r'KeyError: (.+)', 'KeyError'),
        (r'ParseError: (.+)', 'ParseError'),
        (r'TemplateNotFound: (.+)', 'TemplateNotFound'),
    ]
    
    for pattern, error_type in error_patterns:
        matches = re.findall(pattern, content)
        for match in matches:
            errors.append({
                'type': error_type,
                'content': match.strip()
            })
    
    return errors

def test_url_detailed(url):
    """Test a URL and capture detailed error information"""
    try:
        full_url = base_url + url
        print(f"\n🔗 Testing: {full_url}")
        print("="*60)
        
        # Clear logs before request
        print("📋 Capturing pre-request logs...")
        pre_logs = read_odoo_logs(20)
        
        # Make request
        response = requests.get(full_url, timeout=15, allow_redirects=True)
        
        # Capture post-request logs
        print("📋 Capturing post-request logs...")
        time.sleep(2)  # Wait for logs to be written
        post_logs = read_odoo_logs(50)
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📏 Content Length: {len(response.text)} characters")
        
        if response.status_code == 200:
            print("✅ HTTP Status: SUCCESS")
            
            # Check for error content in successful response
            errors = extract_error_details(response.text)
            if errors:
                print("⚠️ ERRORS FOUND IN CONTENT:")
                for i, error in enumerate(errors, 1):
                    print(f"   Error {i} ({error['type']}):")
                    print(f"   {error['content'][:200]}...")
                return False
            else:
                print("✅ Content: Clean (no errors detected)")
                return True
        else:
            print(f"❌ HTTP Status: ERROR ({response.status_code})")
            
            # Extract errors from error page
            errors = extract_error_details(response.text)
            if errors:
                print("❌ ERROR DETAILS FROM RESPONSE:")
                for i, error in enumerate(errors, 1):
                    print(f"   Error {i} ({error['type']}):")
                    print(f"   {error['content']}")
            
            return False
            
    except requests.exceptions.Timeout:
        print(f"⏰ TIMEOUT: Request timed out after 15 seconds")
        return False
    except requests.exceptions.ConnectionError:
        print(f"🔌 CONNECTION ERROR: Could not connect to server")
        return False
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False

def analyze_logs_for_website_errors(logs):
    """Analyze logs specifically for website-related errors"""
    website_errors = []
    
    lines = logs.split('\n')
    for i, line in enumerate(lines):
        # Look for website-related errors
        if any(keyword in line.lower() for keyword in ['website', 'template', 'view', 'controller', 'ai_omisol']):
            if any(error_type in line for error_type in ['ERROR', 'CRITICAL', 'Exception', 'Traceback']):
                # Get context around the error
                context_start = max(0, i-2)
                context_end = min(len(lines), i+3)
                context = '\n'.join(lines[context_start:context_end])
                
                website_errors.append({
                    'line': line.strip(),
                    'context': context
                })
    
    return website_errors

def main():
    """Main diagnostic function"""
    print("🚀 Comprehensive Website Error Diagnosis")
    print("="*60)
    
    success_count = 0
    total_count = len(test_urls)
    all_errors = []
    
    for url in test_urls:
        success = test_url_detailed(url)
        if success:
            success_count += 1
        
        # Small delay between requests
        time.sleep(2)
    
    print("\n" + "="*60)
    print("📊 DIAGNOSIS SUMMARY:")
    print(f"   Successful Pages: {success_count}/{total_count}")
    print(f"   Failed Pages: {total_count - success_count}/{total_count}")
    
    # Analyze recent logs for website errors
    print("\n🔍 ANALYZING RECENT LOGS FOR WEBSITE ERRORS...")
    recent_logs = read_odoo_logs(200)
    website_errors = analyze_logs_for_website_errors(recent_logs)
    
    if website_errors:
        print(f"❌ FOUND {len(website_errors)} WEBSITE-RELATED ERRORS IN LOGS:")
        for i, error in enumerate(website_errors, 1):
            print(f"\n   Error {i}:")
            print(f"   Line: {error['line']}")
            print(f"   Context:\n{error['context']}")
    else:
        print("✅ No website-related errors found in recent logs")
    
    if success_count == total_count:
        print("\n🎉 All website pages are working correctly!")
    else:
        print(f"\n⚠️ {total_count - success_count} website pages need to be fixed.")
        print("\nRECOMMENDATIONS:")
        print("1. Check the error details above")
        print("2. Review controller code for missing models/methods")
        print("3. Verify template syntax and variable references")
        print("4. Ensure all required data exists in the database")
    
    return success_count == total_count

if __name__ == "__main__":
    main()
