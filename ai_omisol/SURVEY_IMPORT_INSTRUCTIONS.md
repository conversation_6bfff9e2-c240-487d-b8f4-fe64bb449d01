# Survey Import Instructions

## Manual Import Method (Recommended)

### Step 1: Access Survey
1. Go to Odoo backend: Apps → Survey → Surveys
2. Select Survey ID 4: "OEL8000III Series Controller Safety, Installation, Features, and Operation Survey"

### Step 2: Create Questions
Use the questions from `survey_questions.csv`:

#### Chapter 1: Safety & Installation (Questions 1-28)
- Question 1 should be marked as "Is Page" = True (starts new page)
- Questions 2-28 should be "Is Page" = False

#### Chapter 2: Features & Programming (Questions 29-78)  
- Question 29 should be marked as "Is Page" = True (starts new page)
- Questions 30-78 should be "Is Page" = False

#### Chapter 3: Operation & Diagnostics (Questions 79-100)
- Question 79 should be marked as "Is Page" = True (starts new page)
- Questions 80-100 should be "Is Page" = False

### Step 3: Add Answer Options
For each question, add the answer options from `survey_answers.csv`:
- Mark the correct answer with "Is Correct" = True
- All other answers should be "Is Correct" = False

## CSV Import Method (Alternative)

### Step 1: Prepare CSV Files
- Use `survey_questions.csv` for questions
- Use `survey_answers.csv` for answers

### Step 2: Import via Odoo
1. Go to Survey Questions list view
2. Use Import function
3. Map CSV columns to Odoo fields
4. Import questions first, then answers

## Question Types
- `simple_choice`: True/False questions
- `single_choice`: Multiple choice questions

## Total Content
- 100 Questions across 3 chapters
- Professional OEL8000III controller certification content
- All correct answers provided

## Verification
After import, verify:
- All 100 questions are present
- All answer options are correct
- Correct answers are marked properly
- Page breaks at questions 1, 29, and 79
