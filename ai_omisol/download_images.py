#!/usr/bin/env python3
import requests
import os
from urllib.parse import urljoin, urlparse
import re

# Create directory for images
os.makedirs('static/src/img/our_works', exist_ok=True)

# Get the homepage content
try:
    response = requests.get('http://dev.omisol.co.in/home/<USER>')
    content = response.text
    print("Successfully fetched homepage content")
except Exception as e:
    print(f"Error fetching homepage: {e}")
    exit(1)

# Look for image patterns in the content
img_patterns = [
    r'src=["\']([^"\']*uploads[^"\']*)["\']',
    r'src=["\']([^"\']*\.jpg[^"\']*)["\']',
    r'src=["\']([^"\']*\.png[^"\']*)["\']',
    r'src=["\']([^"\']*\.jpeg[^"\']*)["\']'
]

base_url = 'http://dev.omisol.co.in'
images_found = set()

for pattern in img_patterns:
    matches = re.findall(pattern, content, re.IGNORECASE)
    for match in matches:
        if 'uploads' in match or any(ext in match.lower() for ext in ['.jpg', '.png', '.jpeg']):
            if not match.startswith('http'):
                full_url = urljoin(base_url, match)
            else:
                full_url = match
            images_found.add(full_url)

print(f'Found {len(images_found)} potential images')

# Download first 6 images for Our Works section
downloaded = 0
for i, img_url in enumerate(list(images_found)[:10]):
    try:
        print(f'Downloading: {img_url}')
        img_response = requests.get(img_url, timeout=10)
        if img_response.status_code == 200:
            # Get file extension
            parsed_url = urlparse(img_url)
            filename = os.path.basename(parsed_url.path)
            if not filename or '.' not in filename:
                filename = f'work_{i+1}.jpg'
            
            filepath = f'static/src/img/our_works/{filename}'
            with open(filepath, 'wb') as f:
                f.write(img_response.content)
            print(f'Downloaded: {filepath}')
            downloaded += 1
            
            if downloaded >= 6:
                break
    except Exception as e:
        print(f'Error downloading {img_url}: {e}')

print(f'Successfully downloaded {downloaded} images')

# Also try to get specific Our Works images
our_works_urls = [
    'http://dev.omisol.co.in/uploads/gallery/gallery_1.jpg',
    'http://dev.omisol.co.in/uploads/gallery/gallery_2.jpg',
    'http://dev.omisol.co.in/uploads/gallery/gallery_3.jpg',
    'http://dev.omisol.co.in/uploads/gallery/gallery_4.jpg',
    'http://dev.omisol.co.in/uploads/gallery/gallery_5.jpg',
    'http://dev.omisol.co.in/uploads/gallery/gallery_6.jpg',
]

print("\nTrying specific Our Works images...")
for i, url in enumerate(our_works_urls):
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            filename = f'our_work_{i+1}.jpg'
            filepath = f'static/src/img/our_works/{filename}'
            with open(filepath, 'wb') as f:
                f.write(response.content)
            print(f'Downloaded: {filepath}')
        else:
            print(f'Failed to download {url}: {response.status_code}')
    except Exception as e:
        print(f'Error downloading {url}: {e}')
