# -*- coding: utf-8 -*-

from odoo import models, fields, api
import csv
import base64
import io
from collections import defaultdict

class SurveyImportWizard(models.TransientModel):
    _name = 'survey.import.wizard'
    _description = 'Survey Import Wizard'

    name = fields.Char('Survey Title', required=True, 
                      default='OEL8000III Series Controller Safety, Installation, Features, and Operation Survey')
    description = fields.Text('Survey Description', 
                             default='Comprehensive survey covering safety, installation, features, and operation of OEL8000III Series Controllers')
    access_mode = fields.Selection([
        ('public', 'Public'),
        ('token', 'Invited people only'),
    ], string='Access Mode', default='public')
    users_login_required = fields.Boolean('Login Required', default=False)
    
    questions_file = fields.Binary('Questions CSV File', required=True)
    questions_filename = fields.Char('Questions Filename')
    answers_file = fields.Binary('Answers CSV File', required=True)
    answers_filename = fields.Char('Answers Filename')
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('importing', 'Importing'),
        ('done', 'Done'),
        ('error', 'Error'),
    ], default='draft')
    
    result_message = fields.Text('Result Message', readonly=True)
    survey_id = fields.Many2one('survey.survey', 'Created Survey', readonly=True)

    def action_import_survey(self):
        """Import survey from CSV files"""
        try:
            self.state = 'importing'
            
            # Read CSV files
            questions, answers_by_question = self._read_csv_files()
            
            if not questions:
                self.state = 'error'
                self.result_message = 'No questions found in CSV file'
                return self._return_wizard()
            
            # Create survey
            survey = self._create_survey()
            
            # Create questions and answers
            created_questions, created_answers = self._create_questions_and_answers(survey, questions, answers_by_question)
            
            self.state = 'done'
            self.survey_id = survey.id
            self.result_message = f'Successfully imported survey with {created_questions} questions and {created_answers} answers!'
            
            return self._return_wizard()
            
        except Exception as e:
            self.state = 'error'
            self.result_message = f'Error importing survey: {str(e)}'
            return self._return_wizard()

    def _read_csv_files(self):
        """Read questions and answers from uploaded CSV files"""
        questions = []
        answers_by_question = defaultdict(list)
        
        # Read questions file
        questions_data = base64.b64decode(self.questions_file)
        questions_content = io.StringIO(questions_data.decode('utf-8'))
        questions_reader = csv.DictReader(questions_content)
        
        for row in questions_reader:
            questions.append({
                'id': int(row['sequence']),
                'title': row['title'].strip(),
                'question_type': row['question_type'].strip(),
                'sequence': int(row['sequence']),
                'chapter': row.get('chapter', '').strip()
            })
        
        # Read answers file
        answers_data = base64.b64decode(self.answers_file)
        answers_content = io.StringIO(answers_data.decode('utf-8'))
        answers_reader = csv.DictReader(answers_content)
        
        for row in answers_reader:
            question_id = int(row['question_sequence'])
            answers_by_question[question_id].append({
                'value': row['answer_value'].strip(),
                'sequence': int(row['answer_sequence']),
                'is_correct': row['is_correct'].strip().lower() == 'true'
            })
        
        return questions, answers_by_question

    def _create_survey(self):
        """Create the survey record"""
        survey_data = {
            'title': self.name,
            'description': self.description,
            'access_mode': self.access_mode,
            'users_login_required': self.users_login_required,
        }
        return self.env['survey.survey'].create(survey_data)

    def _create_questions_and_answers(self, survey, questions, answers_by_question):
        """Create questions and answers for the survey"""
        created_questions = 0
        created_answers = 0
        
        for q in questions:
            # Create question
            question_data = {
                'title': q['title'],
                'question_type': q['question_type'],
                'sequence': q['sequence'],
                'is_page': False,
                'survey_id': survey.id
            }
            
            question = self.env['survey.question'].create(question_data)
            created_questions += 1
            
            # Create answers for this question
            question_answers = answers_by_question.get(q['id'], [])
            for answer in question_answers:
                answer_data = {
                    'value': answer['value'],
                    'sequence': answer['sequence'],
                    'is_correct': answer['is_correct'],
                    'question_id': question.id
                }
                
                self.env['survey.question.answer'].create(answer_data)
                created_answers += 1
        
        return created_questions, created_answers

    def _return_wizard(self):
        """Return wizard action"""
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'survey.import.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }

    def action_view_survey(self):
        """View the created survey"""
        if self.survey_id:
            return {
                'type': 'ir.actions.act_window',
                'res_model': 'survey.survey',
                'res_id': self.survey_id.id,
                'view_mode': 'form',
                'target': 'current',
            }

    def action_reset(self):
        """Reset wizard to draft state"""
        self.state = 'draft'
        self.result_message = ''
        self.survey_id = False
        return self._return_wizard()
