from odoo import models, fields, api


class OmisolFAQCategory(models.Model):
    _name = 'omisol.faq.category'
    _description = 'FAQ Category'
    _order = 'sequence, name'

    name = fields.Char('Category Name', required=True)
    description = fields.Text('Description')
    sequence = fields.Integer('Sequence', default=10)
    is_active = fields.Boolean('Active', default=True)
    color = fields.Integer('Color Index', default=0)
    icon = fields.Char('Icon Class', default='fa-question-circle', help='Font Awesome icon class')
    
    # Computed fields
    faq_count = fields.Integer('FAQ Count', compute='_compute_faq_count')
    
    @api.depends('faq_ids')
    def _compute_faq_count(self):
        for category in self:
            category.faq_count = len(category.faq_ids.filtered('is_published'))
    
    # Relationship
    faq_ids = fields.One2many('omisol.faq', 'category_id', string='FAQs')


class OmisolFAQ(models.Model):
    _name = 'omisol.faq'
    _description = 'Frequently Asked Questions'
    _order = 'sequence, id desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char('Question', required=True, tracking=True)
    answer = fields.Html('Answer', required=True, tracking=True)
    category_id = fields.Many2one('omisol.faq.category', string='Category', required=True, tracking=True)
    sequence = fields.Integer('Sequence', default=10)
    is_published = fields.Boolean('Published', default=True, tracking=True)
    
    # Analytics fields
    view_count = fields.Integer('View Count', default=0)
    helpful_count = fields.Integer('Helpful Count', default=0)
    not_helpful_count = fields.Integer('Not Helpful Count', default=0)
    
    # Note: create_date, write_date, create_uid, write_uid are automatically provided by Odoo
    # Note: message_follower_ids, activity_ids, message_ids are provided by mail.thread and mail.activity.mixin
    
    # Computed fields
    helpfulness_ratio = fields.Float('Helpfulness Ratio', compute='_compute_helpfulness_ratio', store=True)
    
    @api.depends('helpful_count', 'not_helpful_count')
    def _compute_helpfulness_ratio(self):
        for faq in self:
            total_votes = faq.helpful_count + faq.not_helpful_count
            if total_votes > 0:
                faq.helpfulness_ratio = (faq.helpful_count / total_votes) * 100
            else:
                faq.helpfulness_ratio = 0.0
    
    def action_increment_view(self):
        """Increment view count"""
        self.view_count += 1
    
    def action_mark_helpful(self):
        """Mark FAQ as helpful"""
        self.helpful_count += 1
    
    def action_mark_not_helpful(self):
        """Mark FAQ as not helpful"""
        self.not_helpful_count += 1
    
    def action_toggle_published(self):
        """Toggle published status"""
        self.is_published = not self.is_published
    
    @api.model
    def get_popular_faqs(self, limit=5):
        """Get most popular FAQs based on view count"""
        return self.search([
            ('is_published', '=', True)
        ], order='view_count desc', limit=limit)
    
    @api.model
    def get_helpful_faqs(self, limit=5):
        """Get most helpful FAQs based on helpfulness ratio"""
        return self.search([
            ('is_published', '=', True),
            ('helpful_count', '>', 0)
        ], order='helpfulness_ratio desc', limit=limit)
    
    @api.model
    def search_faqs(self, query):
        """Search FAQs by question or answer content"""
        domain = [
            ('is_published', '=', True),
            '|',
            ('name', 'ilike', query),
            ('answer', 'ilike', query)
        ]
        return self.search(domain)
