# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ProductShowcase(models.Model):
    _name = 'omisol.product.showcase'
    _description = 'Product Showcase'
    _order = 'sequence, name'

    name = fields.Char('Product Name', required=True)
    description = fields.Text('Description')
    short_description = fields.Char('Short Description', size=200)

    # Computed fields for name and category extraction
    product_name_only = fields.Char('Product Name Only', compute='_compute_name_category', store=True)
    category_from_name = fields.Char('Category from Name', compute='_compute_name_category', store=True)
    image = fields.Image('Product Image', max_width=1920, max_height=1920)
    image_medium = fields.Image('Medium Image', related='image', max_width=512, max_height=512, store=True)
    image_small = fields.Image('Small Image', related='image', max_width=256, max_height=256, store=True)
    
    # Product Details
    category = fields.Selection([
        ('automatic_tank_gauging', 'Automatic Tank Gauging'),
        ('sensors', 'Sensors'),
        ('controllers', 'Controllers'),
        ('remote_displays', 'Remote Displays'),
        ('remote_alarms', 'Remote Alarms'),
        ('probes', 'Probes'),
        ('vaporless', 'Vaporless Product Line'),
        ('testing_equipment', 'Testing Equipment'),
    ], string='Category', default='automatic_tank_gauging')
    
    specifications = fields.Text('Technical Specifications')
    features = fields.Text('Key Features')
    applications = fields.Text('Applications')
    
    # Display Settings
    is_featured = fields.Boolean('Featured Product', default=False)
    is_published = fields.Boolean('Published', default=True)
    sequence = fields.Integer('Sequence', default=10)
    
    # SEO
    website_meta_title = fields.Char('Website Meta Title')
    website_meta_description = fields.Text('Website Meta Description')
    website_meta_keywords = fields.Char('Website Meta Keywords')
    
    # Additional Info
    brochure = fields.Binary('Product Brochure')
    brochure_filename = fields.Char('Brochure Filename')
    video_url = fields.Char('Video URL')

    # Product Downloads
    download_ids = fields.One2many('omisol.product.download', 'product_id', string='Downloads')

    # URL slug for SEO-friendly URLs
    website_slug = fields.Char('Website Slug', compute='_compute_website_slug', store=True)

    @api.depends('name')
    def _compute_name_category(self):
        """Extract product name and category from full name using | separator"""
        for product in self:
            if product.name and '|' in product.name:
                parts = product.name.split('|', 1)
                product.product_name_only = parts[0].strip()
                product.category_from_name = parts[1].strip()
            else:
                product.product_name_only = product.name or ''
                product.category_from_name = ''

    @api.depends('name')
    def _compute_website_slug(self):
        for product in self:
            if product.name:
                # Create URL-friendly slug
                slug = product.name.lower()
                slug = ''.join(c if c.isalnum() or c in '-_' else '-' for c in slug)
                slug = '-'.join(word for word in slug.split('-') if word)
                product.website_slug = slug
            else:
                product.website_slug = ''
    
    @api.model
    def get_featured_products(self, limit=6):
        """Get featured products for homepage"""
        return self.search([
            ('is_featured', '=', True),
            ('is_published', '=', True)
        ], limit=limit)
    
    @api.model
    def get_products_by_category(self, category, limit=None):
        """Get products by category"""
        domain = [
            ('category', '=', category),
            ('is_published', '=', True)
        ]
        return self.search(domain, limit=limit)

    def toggle_active(self):
        """Toggle the is_published field (used for archive functionality)"""
        for record in self:
            record.is_published = not record.is_published

    def action_go_to_website(self):
        """Redirect to the frontend product page"""
        self.ensure_one()
        if not self.is_published:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Product Not Published',
                    'message': 'This product is not published and cannot be viewed on the website.',
                    'type': 'warning',
                }
            }

        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        product_url = f"{base_url}/product/{self.id}"
        if self.website_slug:
            product_url += f"/{self.website_slug}"

        return {
            'type': 'ir.actions.act_url',
            'url': product_url,
            'target': 'new',
        }


class ProductDownload(models.Model):
    _name = 'omisol.product.download'
    _description = 'Product Downloads'
    _order = 'sequence, name'

    name = fields.Char('File Name', required=True)
    description = fields.Char('Description')
    file_data = fields.Binary('File', required=True)
    filename = fields.Char('Filename', required=True)
    file_type = fields.Selection([
        ('brochure', 'Brochure'),
        ('datasheet', 'Datasheet'),
        ('manual', 'Manual'),
        ('specification', 'Technical Specification'),
        ('certificate', 'Certificate'),
        ('drawing', 'Technical Drawing'),
        ('video', 'Video'),
        ('other', 'Other'),
    ], string='File Type', default='brochure', required=True)

    product_id = fields.Many2one('omisol.product.showcase', string='Product', required=True, ondelete='cascade')
    sequence = fields.Integer('Sequence', default=10)
    is_active = fields.Boolean('Active', default=True)
    download_count = fields.Integer('Download Count', default=0)

    # File info
    file_size = fields.Char('File Size', compute='_compute_file_info', store=True)
    file_extension = fields.Char('File Extension', compute='_compute_file_info', store=True)

    @api.depends('filename', 'file_data')
    def _compute_file_info(self):
        for download in self:
            if download.filename:
                # Get file extension
                download.file_extension = download.filename.split('.')[-1].upper() if '.' in download.filename else ''

                # Calculate file size (approximate)
                if download.file_data:
                    import base64
                    try:
                        file_size_bytes = len(base64.b64decode(download.file_data))
                        if file_size_bytes < 1024:
                            download.file_size = f"{file_size_bytes} B"
                        elif file_size_bytes < 1024 * 1024:
                            download.file_size = f"{file_size_bytes / 1024:.1f} KB"
                        else:
                            download.file_size = f"{file_size_bytes / (1024 * 1024):.1f} MB"
                    except:
                        download.file_size = "Unknown"
                else:
                    download.file_size = "0 B"
            else:
                download.file_extension = ''
                download.file_size = "0 B"

    @api.model
    def load_files_from_directory(self):
        """Load PDF files from static/downloads directory into database"""
        import os
        import base64
        from odoo.modules import get_module_path
        import logging

        _logger = logging.getLogger(__name__)

        module_path = get_module_path('ai_omisol')
        downloads_dir = os.path.join(module_path, 'static', 'downloads')

        _logger.info(f"Looking for files in: {downloads_dir}")

        if not os.path.exists(downloads_dir):
            _logger.error(f"Downloads directory does not exist: {downloads_dir}")
            return

        # File mapping: filename -> download record external_id
        # Based on actual downloaded files from omntec.com
        file_mappings = {
            # General Brochures
            'OMNTEC ATG Controller Comparison Chart.pdf': 'download_proteus_k_comparison',

            # Basics
            'PROTEUS-K4 Basics for USTs.pdf': 'download_proteus_k4_basics_ust',
            'PROTEUS-K4 Basics for ASTs.pdf': 'download_proteus_k4_basics_ast',
            'PROTEUS-K8 Basics.pdf': 'download_proteus_k8_basics',

            # O & M Manuals
            'Gen IV System Operating Manual  DOC00009.pdf': 'download_gen_iv_operating_manual',
            'Gen IV System Programming Manual DOC00008.pdf': 'download_gen_iv_programming_manual',
            'PROTEUS-K Installation Manual.pdf': 'download_installation_manual',
            'OEL8000III System Operating Manual.pdf': 'download_oel8000iii_operating_manual',
            'OEL8000III System Programming Manual.pdf': 'download_oel8000iii_programming_manual',
            'OEL8000III Quick Reference Guide.pdf': 'download_oel8000iii_quick_reference',
            'Web Page Programming Manual.pdf': 'download_web_programming_manual',
            'Modbus Communication Manual  DOC00005 1.3.pdf': 'download_modbus_manual',

            # Installation & Upgrade
            'Gen 3.0 To Gen IV Upgrade Kit KIT-G3-UPGRADE-1.pdf': 'download_gen3_to_gen4_upgrade',
            'Gen IV MCU Upgrade for Gen III OEL8000III-KX.pdf': 'download_gen4_mcu_upgrade',
            'OPTION BOARD INSTALLATION INSTRUCTIONS DI00012.pdf': 'download_option_board_installation',
            'Proteus Installation Checklist.pdf': 'download_proteus_installation_checklist',
            'OEL8000III-K  OEL8000III-X ADDING A NEW PRINTER.pdf': 'download_adding_new_printer',
            'Thermal Printer Paper Installation DI00002.pdf': 'download_thermal_printer_installation',
            'Printing Via CUPS Interface 500185.pdf': 'download_printing_cups',
            'Proteus Firmware Upgrade Gen 3.5 500183.pdf': 'download_firmware_upgrade',

            # Engineering Documentation
            'PROTEUS-K Written Spec.pdf': 'download_proteus_k_spec',
        }

        loaded_count = 0

        # List all files in downloads directory
        try:
            files_in_dir = os.listdir(downloads_dir)
            _logger.info(f"Files found in directory: {files_in_dir}")
        except Exception as e:
            _logger.error(f"Error listing directory: {e}")
            return False

        for filename, external_id in file_mappings.items():
            file_path = os.path.join(downloads_dir, filename)
            _logger.info(f"Checking file: {filename} -> {file_path}")

            if os.path.exists(file_path):
                try:
                    # Read file content
                    with open(file_path, 'rb') as f:
                        file_content = f.read()

                    _logger.info(f"Read {len(file_content)} bytes from {filename}")

                    # Encode to base64
                    encoded_content = base64.b64encode(file_content)

                    # Find the download record
                    download_record = self.env.ref(f'ai_omisol.{external_id}', raise_if_not_found=False)

                    if download_record:
                        # Update with file content
                        download_record.write({
                            'file_data': encoded_content
                        })
                        loaded_count += 1
                        _logger.info(f"Successfully loaded: {filename} ({len(file_content)} bytes)")
                    else:
                        _logger.error(f"Download record not found for: {external_id}")

                except Exception as e:
                    _logger.error(f"Error loading file {filename}: {e}")
            else:
                _logger.warning(f"File not found: {file_path}")

        _logger.info(f"Loaded {loaded_count} files successfully")
        return True

    @api.model
    def manual_load_files(self):
        """Manual method to load files - can be called from Odoo shell"""
        return self.load_files_from_directory()

    def toggle_active(self):
        """Toggle the is_active field"""
        for record in self:
            record.is_active = not record.is_active


class CompanyStatistics(models.Model):
    _name = 'omisol.company.statistics'
    _description = 'Company Statistics for Counters'
    _order = 'sequence'

    name = fields.Char('Statistic Name', required=True)
    value = fields.Char('Value', required=True)
    suffix = fields.Char('Suffix (e.g., +, %, etc.)')
    icon = fields.Char('Icon Class (FontAwesome)')
    description = fields.Char('Description')
    sequence = fields.Integer('Sequence', default=10)
    is_active = fields.Boolean('Active', default=True)

    def toggle_active(self):
        """Toggle the is_active field"""
        for record in self:
            record.is_active = not record.is_active


class CompanyGallery(models.Model):
    _name = 'omisol.company.gallery'
    _description = 'Company Photo Gallery'
    _order = 'sequence, name'

    name = fields.Char('Image Title', required=True)
    description = fields.Text('Description')
    image = fields.Image('Image', required=True, max_width=1920, max_height=1920)
    image_medium = fields.Image('Medium Image', related='image', max_width=512, max_height=512, store=True)
    image_small = fields.Image('Small Image', related='image', max_width=256, max_height=256, store=True)
    
    category = fields.Selection([
        ('facility', 'Facility'),
        ('team', 'Team'),
        ('products', 'Products'),
        ('events', 'Events'),
        ('certificates', 'Certificates'),
    ], string='Category', default='facility')
    
    sequence = fields.Integer('Sequence', default=10)
    is_published = fields.Boolean('Published', default=True)


class CompanyCertificate(models.Model):
    _name = 'omisol.company.certificate'
    _description = 'Company Certificates'
    _order = 'sequence, name'

    name = fields.Char('Certificate Name', required=True)
    description = fields.Text('Description')
    certificate_image = fields.Image('Certificate Image', required=True)
    issuing_authority = fields.Char('Issuing Authority')
    issue_date = fields.Date('Issue Date')
    expiry_date = fields.Date('Expiry Date')
    certificate_number = fields.Char('Certificate Number')
    
    sequence = fields.Integer('Sequence', default=10)
    is_active = fields.Boolean('Active', default=True)

    @api.depends('expiry_date')
    def _compute_is_valid(self):
        today = fields.Date.today()
        for cert in self:
            cert.is_valid = not cert.expiry_date or cert.expiry_date >= today

    is_valid = fields.Boolean('Valid', compute='_compute_is_valid', store=True)

    def toggle_active(self):
        """Toggle the is_active field"""
        for record in self:
            record.is_active = not record.is_active
