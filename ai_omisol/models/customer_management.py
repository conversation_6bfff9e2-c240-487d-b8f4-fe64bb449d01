# -*- coding: utf-8 -*-

import logging
import secrets
import string
import re
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from odoo.tools import email_normalize

_logger = logging.getLogger(__name__)


class OmisolCustomer(models.Model):
    _name = 'omisol.customer'
    _description = 'Omisol Customer Management'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='Customer Name',
        required=True,
        tracking=True,
        help="Full name of the customer"
    )
    
    email = fields.Char(
        string='Email',
        required=True,
        tracking=True,
        help="Customer's email address"
    )
    
    phone = fields.Char(
        string='Phone Number',
        tracking=True,
        help="Customer's phone number (for WhatsApp integration)"
    )
    
    whatsapp_number = fields.Char(
        string='WhatsApp Number',
        compute='_compute_whatsapp_number',
        store=True,
        help="Formatted WhatsApp number"
    )
    
    company_name = fields.Char(
        string='Company Name',
        tracking=True,
        help="Customer's company name"
    )
    
    address = fields.Text(
        string='Address',
        help="Customer's address"
    )
    
    city = fields.Char(string='City')
    state = fields.Char(string='State')
    country_id = fields.Many2one('res.country', string='Country')
    zip_code = fields.Char(string='ZIP Code')
    
    customer_type = fields.Selection([
        ('individual', 'Individual'),
        ('company', 'Company'),
        ('distributor', 'Distributor'),
        ('retailer', 'Retailer')
    ], string='Customer Type', default='individual', required=True)
    
    industry = fields.Selection([
        ('manufacturing', 'Manufacturing'),
        ('chemical', 'Chemical'),
        ('pharmaceutical', 'Pharmaceutical'),
        ('automotive', 'Automotive'),
        ('construction', 'Construction'),
        ('textile', 'Textile'),
        ('food', 'Food & Beverage'),
        ('other', 'Other')
    ], string='Industry')
    
    status = fields.Selection([
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('prospect', 'Prospect'),
        ('blocked', 'Blocked')
    ], string='Status', default='active', required=True, tracking=True)
    
    notes = fields.Text(string='Notes')
    
    # User Management
    user_ids = fields.One2many(
        'omisol.customer.user',
        'customer_id',
        string='Associated Users'
    )
    
    user_count = fields.Integer(
        string='Number of Users',
        compute='_compute_user_count',
        store=True
    )
    
    # Dates
    registration_date = fields.Datetime(
        string='Registration Date',
        default=fields.Datetime.now,
        required=True
    )
    
    last_activity_date = fields.Datetime(
        string='Last Activity',
        help="Last time customer was active"
    )
    
    @api.depends('phone')
    def _compute_whatsapp_number(self):
        """Format phone number for WhatsApp"""
        for record in self:
            if record.phone:
                # Remove all non-digit characters
                clean_number = re.sub(r'\D', '', record.phone)
                
                # Add country code if not present (assuming India +91 as default)
                if clean_number and not clean_number.startswith('91') and len(clean_number) == 10:
                    clean_number = '91' + clean_number
                
                record.whatsapp_number = clean_number
            else:
                record.whatsapp_number = False
    
    @api.depends('user_ids')
    def _compute_user_count(self):
        """Compute number of associated users"""
        for record in self:
            record.user_count = len(record.user_ids)
    
    @api.constrains('email')
    def _check_email(self):
        """Validate email format"""
        for record in self:
            if record.email:
                normalized_email = email_normalize(record.email)
                if not normalized_email:
                    raise ValidationError(_("Please enter a valid email address."))
                record.email = normalized_email
    
    @api.constrains('phone')
    def _check_phone(self):
        """Validate phone number format"""
        for record in self:
            if record.phone:
                # Basic phone validation
                clean_phone = re.sub(r'\D', '', record.phone)
                if len(clean_phone) < 10:
                    raise ValidationError(_("Phone number must be at least 10 digits."))
    
    def action_create_user(self):
        """Create a new user for this customer"""
        self.ensure_one()
        
        # Generate username from email
        username = self.email.split('@')[0]
        
        # Check if username already exists
        existing_user = self.env['omisol.customer.user'].search([
            ('username', '=', username)
        ])
        
        if existing_user:
            # Add number suffix if username exists
            counter = 1
            while existing_user:
                new_username = f"{username}{counter}"
                existing_user = self.env['omisol.customer.user'].search([
                    ('username', '=', new_username)
                ])
                counter += 1
            username = new_username
        
        # Generate random password
        password = self._generate_password()
        
        # Create user
        user = self.env['omisol.customer.user'].create({
            'customer_id': self.id,
            'name': self.name,
            'username': username,
            'email': self.email,
            'password': password,
            'phone': self.phone,
            'status': 'active'
        })
        
        # Send credentials via email
        user.send_credentials_email()
        
        # Log activity
        self.message_post(
            body=_("New user created: {}").format(username),
            message_type='notification'
        )
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('User Created'),
            'res_model': 'omisol.customer.user',
            'res_id': user.id,
            'view_mode': 'form',
            'target': 'current',
        }
    
    def action_view_users(self):
        """View associated users"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Customer Users'),
            'res_model': 'omisol.customer.user',
            'view_mode': 'list,form',
            'domain': [('customer_id', '=', self.id)],
            'context': {'default_customer_id': self.id},
            'target': 'current',
        }
    
    def action_send_whatsapp(self):
        """Open WhatsApp with predefined message"""
        self.ensure_one()
        
        if not self.whatsapp_number:
            raise UserError(_("WhatsApp number is not available for this customer."))
        
        # Get the latest user credentials
        latest_user = self.user_ids.filtered(lambda u: u.status == 'active')
        if not latest_user:
            raise UserError(_("No active users found for this customer."))
        
        latest_user = latest_user[0]  # Get the first active user
        
        # Prepare WhatsApp message
        message = _(
            "Hello {},\n\n"
            "Your Omisol account credentials:\n"
            "Username: {}\n"
            "Password: {}\n\n"
            "Please login at: {}\n\n"
            "Best regards,\n"
            "Omisol Team"
        ).format(
            self.name,
            latest_user.username,
            latest_user.password,
            self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        )
        
        # URL encode the message
        import urllib.parse
        encoded_message = urllib.parse.quote(message)
        
        # Create WhatsApp URL
        whatsapp_url = f"https://wa.me/{self.whatsapp_number}?text={encoded_message}"
        
        return {
            'type': 'ir.actions.act_url',
            'url': whatsapp_url,
            'target': 'new',
        }
    
    def _generate_password(self, length=8):
        """Generate a random password"""
        characters = string.ascii_letters + string.digits
        return ''.join(secrets.choice(characters) for _ in range(length))


class OmisolCustomerUser(models.Model):
    _name = 'omisol.customer.user'
    _description = 'Omisol Customer User'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    customer_id = fields.Many2one(
        'omisol.customer',
        string='Customer',
        required=True,
        ondelete='cascade'
    )
    
    name = fields.Char(
        string='Full Name',
        required=True,
        tracking=True
    )
    
    username = fields.Char(
        string='Username',
        required=True,
        tracking=True
    )
    
    email = fields.Char(
        string='Email',
        required=True,
        tracking=True
    )
    
    password = fields.Char(
        string='Password',
        required=True,
        help="Plain text password for customer reference"
    )
    
    phone = fields.Char(string='Phone Number')
    
    status = fields.Selection([
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('suspended', 'Suspended')
    ], string='Status', default='active', required=True, tracking=True)
    
    last_login = fields.Datetime(string='Last Login')
    login_count = fields.Integer(string='Login Count', default=0)
    
    # Odoo user link (if created)
    odoo_user_id = fields.Many2one(
        'res.users',
        string='Odoo User',
        help="Linked Odoo user account"
    )
    
    @api.constrains('username')
    def _check_username_unique(self):
        """Ensure username is unique"""
        for record in self:
            existing = self.search([
                ('username', '=', record.username),
                ('id', '!=', record.id)
            ])
            if existing:
                raise ValidationError(_("Username '{}' already exists.").format(record.username))
    
    def send_credentials_email(self):
        """Send login credentials via email"""
        self.ensure_one()
        
        template = self.env.ref('ai_omisol.email_template_customer_credentials', raise_if_not_found=False)
        if template:
            template.send_mail(self.id, force_send=True)
        else:
            # Fallback email sending
            self._send_credentials_email_fallback()
    
    def _send_credentials_email_fallback(self):
        """Fallback method to send credentials email"""
        mail_values = {
            'subject': _('Your Omisol Account Credentials'),
            'body_html': _("""
                <p>Dear {},</p>
                <p>Your Omisol account has been created successfully.</p>
                <p><strong>Login Details:</strong></p>
                <ul>
                    <li>Username: {}</li>
                    <li>Password: {}</li>
                    <li>Login URL: <a href="{}">{}</a></li>
                </ul>
                <p>Please keep these credentials safe and change your password after first login.</p>
                <p>Best regards,<br/>Omisol Team</p>
            """).format(
                self.name,
                self.username,
                self.password,
                self.env['ir.config_parameter'].sudo().get_param('web.base.url'),
                self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            ),
            'email_to': self.email,
            'email_from': self.env.company.email or '<EMAIL>',
        }
        
        mail = self.env['mail.mail'].create(mail_values)
        mail.send()
    
    def action_reset_password(self):
        """Reset user password"""
        self.ensure_one()
        
        new_password = self.customer_id._generate_password()
        self.password = new_password
        
        # Send new credentials
        self.send_credentials_email()
        
        self.message_post(
            body=_("Password reset for user: {}").format(self.username),
            message_type='notification'
        )
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Password Reset'),
                'message': _('New password has been generated and sent to the user.'),
                'type': 'success',
            }
        }
    
    def action_send_whatsapp_credentials(self):
        """Send credentials via WhatsApp"""
        return self.customer_id.action_send_whatsapp()


class OmisolLiveChatSession(models.Model):
    _name = 'omisol.livechat.session'
    _description = 'Omisol Live Chat Session'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char(
        string='Session Name',
        compute='_compute_name',
        store=True
    )

    customer_user_id = fields.Many2one(
        'omisol.customer.user',
        string='Customer User',
        required=True,
        help="The customer user who initiated the chat"
    )

    customer_id = fields.Many2one(
        'omisol.customer',
        string='Customer',
        related='customer_user_id.customer_id',
        store=True
    )

    operator_id = fields.Many2one(
        'res.users',
        string='Chat Operator',
        help="The internal user handling the chat"
    )

    session_uuid = fields.Char(
        string='Session UUID',
        required=True,
        default=lambda self: self._generate_session_uuid(),
        help="Unique identifier for the chat session"
    )

    status = fields.Selection([
        ('waiting', 'Waiting for Operator'),
        ('active', 'Active Chat'),
        ('closed', 'Closed'),
        ('abandoned', 'Abandoned')
    ], string='Status', default='waiting', required=True, tracking=True)

    start_time = fields.Datetime(
        string='Start Time',
        default=fields.Datetime.now,
        required=True
    )

    end_time = fields.Datetime(string='End Time')

    duration = fields.Integer(
        string='Duration (minutes)',
        compute='_compute_duration',
        store=True
    )

    message_count = fields.Integer(
        string='Message Count',
        compute='_compute_message_count'
    )

    rating = fields.Selection([
        ('1', '1 - Very Poor'),
        ('2', '2 - Poor'),
        ('3', '3 - Average'),
        ('4', '4 - Good'),
        ('5', '5 - Excellent')
    ], string='Customer Rating')

    feedback = fields.Text(string='Customer Feedback')

    @api.depends('customer_user_id', 'create_date')
    def _compute_name(self):
        """Compute session name"""
        for record in self:
            if record.customer_user_id:
                record.name = f"Chat with {record.customer_user_id.name} - {record.create_date.strftime('%Y-%m-%d %H:%M')}"
            else:
                record.name = f"Chat Session - {record.create_date.strftime('%Y-%m-%d %H:%M')}"

    @api.depends('start_time', 'end_time')
    def _compute_duration(self):
        """Compute chat duration"""
        for record in self:
            if record.start_time and record.end_time:
                delta = record.end_time - record.start_time
                record.duration = int(delta.total_seconds() / 60)
            else:
                record.duration = 0

    def _compute_message_count(self):
        """Compute number of messages in this session"""
        for record in self:
            record.message_count = len(record.message_ids)

    def _generate_session_uuid(self):
        """Generate unique session UUID"""
        import uuid
        return str(uuid.uuid4())

    def action_assign_to_me(self):
        """Assign chat session to current user"""
        self.ensure_one()
        self.operator_id = self.env.user
        self.status = 'active'

        # Notify customer that operator joined
        self.message_post(
            body=_("Chat operator {} has joined the conversation.").format(self.env.user.name),
            message_type='notification'
        )

    def action_close_session(self):
        """Close chat session"""
        self.ensure_one()
        self.status = 'closed'
        self.end_time = fields.Datetime.now()

        # Send closing message
        self.message_post(
            body=_("Chat session has been closed. Thank you for contacting Omisol!"),
            message_type='notification'
        )

    def action_view_customer(self):
        """View customer details"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Customer Details'),
            'res_model': 'omisol.customer',
            'res_id': self.customer_id.id,
            'view_mode': 'form',
            'target': 'current',
        }


class OmisolFAQ(models.Model):
    _name = 'omisol.faq'
    _description = 'Omisol FAQ Management'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'sequence, category_id, id'

    name = fields.Char(
        string='Question',
        required=True,
        tracking=True,
        help="The FAQ question"
    )

    answer = fields.Html(
        string='Answer',
        required=True,
        tracking=True,
        help="The detailed answer to the question"
    )

    category_id = fields.Many2one(
        'omisol.faq.category',
        string='Category',
        required=True,
        help="FAQ category for organization"
    )

    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help="Order of display within category"
    )

    is_published = fields.Boolean(
        string='Published',
        default=True,
        tracking=True,
        help="Whether this FAQ is visible on the website"
    )

    tags = fields.Char(
        string='Tags',
        help="Comma-separated tags for search functionality"
    )

    view_count = fields.Integer(
        string='View Count',
        default=0,
        help="Number of times this FAQ has been viewed"
    )

    helpful_count = fields.Integer(
        string='Helpful Count',
        default=0,
        help="Number of users who found this helpful"
    )

    not_helpful_count = fields.Integer(
        string='Not Helpful Count',
        default=0,
        help="Number of users who found this not helpful"
    )

    last_updated = fields.Datetime(
        string='Last Updated',
        default=fields.Datetime.now,
        tracking=True
    )

    created_by = fields.Many2one(
        'res.users',
        string='Created By',
        default=lambda self: self.env.user,
        readonly=True
    )

    def action_increment_view(self):
        """Increment view count"""
        self.view_count += 1

    def action_mark_helpful(self):
        """Mark as helpful"""
        self.helpful_count += 1
        return {'type': 'ir.actions.act_window_close'}

    def action_mark_not_helpful(self):
        """Mark as not helpful"""
        self.not_helpful_count += 1
        return {'type': 'ir.actions.act_window_close'}


class OmisolFAQCategory(models.Model):
    _name = 'omisol.faq.category'
    _description = 'FAQ Category'
    _order = 'sequence, name'

    name = fields.Char(
        string='Category Name',
        required=True,
        help="Name of the FAQ category"
    )

    description = fields.Text(
        string='Description',
        help="Description of this category"
    )

    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help="Order of display"
    )

    color = fields.Integer(
        string='Color',
        default=0,
        help="Color for category display"
    )

    icon = fields.Char(
        string='Icon',
        default='fa-question-circle',
        help="FontAwesome icon class"
    )

    is_active = fields.Boolean(
        string='Active',
        default=True,
        help="Whether this category is active"
    )

    faq_ids = fields.One2many(
        'omisol.faq',
        'category_id',
        string='FAQs'
    )

    faq_count = fields.Integer(
        string='FAQ Count',
        compute='_compute_faq_count'
    )

    @api.depends('faq_ids')
    def _compute_faq_count(self):
        """Compute number of FAQs in this category"""
        for record in self:
            record.faq_count = len(record.faq_ids.filtered('is_published'))
