#!/usr/bin/env python3
"""
Script to download client logos from dev.omisol.co.in
"""

import requests
from bs4 import BeautifulSoup
import os
import urllib.parse
from PIL import Image
import io

def create_directories():
    """Create necessary directories for storing logos"""
    os.makedirs('static/src/img/clients', exist_ok=True)
    print("Created directories: static/src/img/clients")

def download_image(url, filename):
    """Download and save an image"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # Open image and convert to RGB if necessary
        img = Image.open(io.BytesIO(response.content))
        if img.mode in ('RGBA', 'LA', 'P'):
            img = img.convert('RGB')
        
        # Resize to standard size (150x80)
        img = img.resize((150, 80), Image.LANCZOS)
        
        # Save as high-quality JPEG
        filepath = os.path.join('static/src/img/clients', filename)
        img.save(filepath, 'JPEG', quality=90, optimize=True)
        
        print(f"Downloaded and processed: {filename}")
        return True
        
    except Exception as e:
        print(f"Error downloading {url}: {e}")
        return False

def scrape_client_logos():
    """Scrape client logos from the website"""
    url = "http://dev.omisol.co.in/home/<USER>"
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find client logos section (usually before testimonials)
        # Look for common patterns in client logo sections
        logo_containers = []
        
        # Try different selectors for client logos
        selectors = [
            'img[alt*="client"]',
            'img[alt*="logo"]',
            'img[src*="client"]',
            'img[src*="logo"]',
            '.client img',
            '.clients img',
            '.logo img',
            '.logos img',
            '.partner img',
            '.partners img'
        ]
        
        for selector in selectors:
            images = soup.select(selector)
            if images:
                logo_containers.extend(images)
        
        # Also look for images in sections that might contain client logos
        sections = soup.find_all(['section', 'div'], class_=lambda x: x and any(
            keyword in x.lower() for keyword in ['client', 'partner', 'logo', 'brand']
        ))
        
        for section in sections:
            images = section.find_all('img')
            logo_containers.extend(images)
        
        # Remove duplicates
        unique_logos = {}
        for img in logo_containers:
            src = img.get('src', '')
            if src and src not in unique_logos:
                unique_logos[src] = img
        
        print(f"Found {len(unique_logos)} potential client logos")
        
        # Download logos
        downloaded_count = 0
        for i, (src, img) in enumerate(unique_logos.items()):
            if downloaded_count >= 12:  # Limit to 12 logos
                break
                
            # Make URL absolute
            if src.startswith('//'):
                src = 'http:' + src
            elif src.startswith('/'):
                src = 'http://dev.omisol.co.in' + src
            elif not src.startswith('http'):
                src = 'http://dev.omisol.co.in/' + src
            
            # Get alt text for filename
            alt_text = img.get('alt', f'client_logo_{i+1}')
            # Clean filename
            filename = ''.join(c for c in alt_text if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = filename.replace(' ', '_').lower()
            if not filename:
                filename = f'client_logo_{i+1}'
            filename += '.jpg'
            
            if download_image(src, filename):
                downloaded_count += 1
        
        print(f"Successfully downloaded {downloaded_count} client logos")
        
        # Create fallback logos if not enough found
        if downloaded_count < 8:
            create_fallback_logos()
        
    except Exception as e:
        print(f"Error scraping website: {e}")
        create_fallback_logos()

def create_fallback_logos():
    """Create fallback placeholder logos"""
    print("Creating fallback client logos...")
    
    # Common petroleum company names
    companies = [
        'IOCL', 'BPCL', 'HPCL', 'Reliance', 'Shell', 'BP', 
        'Total', 'Chevron', 'ExxonMobil', 'Cairn', 'ONGC', 'Essar'
    ]
    
    for i, company in enumerate(companies):
        try:
            # Create a simple logo placeholder
            img = Image.new('RGB', (150, 80), color='#f8f9fa')
            
            # You could add text here using PIL's ImageDraw if needed
            # For now, just save as placeholder
            
            filename = f'{company.lower()}_logo.jpg'
            filepath = os.path.join('static/src/img/clients', filename)
            img.save(filepath, 'JPEG', quality=90)
            
            print(f"Created fallback logo: {filename}")
            
        except Exception as e:
            print(f"Error creating fallback logo for {company}: {e}")

def main():
    """Main function"""
    print("Starting client logo download...")
    
    # Create directories
    create_directories()
    
    # Download logos
    scrape_client_logos()
    
    print("Client logo download completed!")
    print("Logos saved in: static/src/img/clients/")
    print("\nNext steps:")
    print("1. Update the client slider template to use these logos")
    print("2. Run the module upgrade to apply changes")

if __name__ == "__main__":
    main()
