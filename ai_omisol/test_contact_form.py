#!/usr/bin/env python3
"""
Test Contact Form Functionality using <PERSON><PERSON>
"""

import asyncio
from playwright.async_api import async_playwright
import sys

async def test_contact_form():
    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            print("🌐 Opening contact page...")
            await page.goto("https://omnisol.arihantai.com/contactus")
            await page.wait_for_load_state('networkidle')
            
            print("📋 Analyzing page sections...")
            
            # Check for orange backgrounds
            sections = await page.query_selector_all('section')
            for i, section in enumerate(sections):
                bg_color = await section.evaluate('el => getComputedStyle(el).backgroundColor')
                class_name = await section.get_attribute('class')
                print(f"Section {i+1}: {class_name} - Background: {bg_color}")
            
            print("\n🔍 Looking for contact form...")
            
            # Find contact form
            form = await page.query_selector('form')
            if not form:
                print("❌ No contact form found!")
                return False
            
            print("✅ Contact form found!")
            
            # Fill out the form
            print("📝 Filling out contact form...")
            
            # Fill name field
            name_field = await page.query_selector('input[name="name"], input[id*="name"], input[placeholder*="name" i]')
            if name_field:
                await name_field.fill("Test User")
                print("✅ Name field filled")
            else:
                print("❌ Name field not found")
            
            # Fill email field
            email_field = await page.query_selector('input[type="email"], input[name="email"], input[id*="email"]')
            if email_field:
                await email_field.fill("<EMAIL>")
                print("✅ Email field filled")
            else:
                print("❌ Email field not found")
            
            # Fill phone field
            phone_field = await page.query_selector('input[type="tel"], input[name="phone"], input[id*="phone"]')
            if phone_field:
                await phone_field.fill("+1234567890")
                print("✅ Phone field filled")
            else:
                print("❌ Phone field not found")
            
            # Fill subject field
            subject_field = await page.query_selector('input[name="subject"], input[id*="subject"], select[name="subject"]')
            if subject_field:
                tag_name = await subject_field.evaluate('el => el.tagName.toLowerCase()')
                if tag_name == 'select':
                    await subject_field.select_option(index=1)
                else:
                    await subject_field.fill("Test Subject")
                print("✅ Subject field filled")
            else:
                print("❌ Subject field not found")
            
            # Fill message field
            message_field = await page.query_selector('textarea[name="message"], textarea[id*="message"], textarea[placeholder*="message" i]')
            if message_field:
                await message_field.fill("This is a test message to check if the contact form is working properly.")
                print("✅ Message field filled")
            else:
                print("❌ Message field not found")
            
            # Take screenshot before submission
            await page.screenshot(path="contact_form_filled.png")
            print("📸 Screenshot taken: contact_form_filled.png")
            
            # Find submit button
            submit_button = await page.query_selector('button[type="submit"], input[type="submit"], button:has-text("Send"), button:has-text("Submit")')
            if submit_button:
                print("🔍 Submit button found, checking if form is ready...")
                
                # Check if button is enabled
                is_disabled = await submit_button.is_disabled()
                if is_disabled:
                    print("⚠️ Submit button is disabled")
                else:
                    print("✅ Submit button is enabled")
                    
                    # Note: We won't actually submit to avoid spam
                    print("ℹ️ Form appears to be working (not submitting to avoid spam)")
                    
            else:
                print("❌ Submit button not found")
            
            # Check for any JavaScript errors
            page.on('console', lambda msg: print(f"Console: {msg.text}"))
            page.on('pageerror', lambda error: print(f"Page Error: {error}"))
            
            await page.wait_for_timeout(2000)
            
            print("\n📊 Contact Form Test Summary:")
            print("✅ Page loads successfully")
            print("✅ Form elements are accessible")
            print("✅ Form can be filled out")
            print("ℹ️ Form submission not tested (to avoid spam)")
            
            return True
            
        except Exception as e:
            print(f"❌ Error during testing: {e}")
            return False
            
        finally:
            await browser.close()

if __name__ == "__main__":
    result = asyncio.run(test_contact_form())
    sys.exit(0 if result else 1)
