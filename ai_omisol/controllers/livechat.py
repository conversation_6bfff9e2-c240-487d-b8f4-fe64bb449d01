# -*- coding: utf-8 -*-

import json
import logging
from odoo import http, _
from odoo.http import request
from odoo.exceptions import AccessError, UserError

_logger = logging.getLogger(__name__)


class OmisolLiveChatController(http.Controller):

    @http.route('/omisol/livechat/get_session', type='json', auth='user', methods=['POST'])
    def get_chat_session(self):
        """Get existing chat session for current user"""
        try:
            # Find customer user for current user
            customer_user = self._get_customer_user()
            if not customer_user:
                return {'error': 'Customer user not found'}
            
            # Find active session
            session = request.env['omisol.livechat.session'].search([
                ('customer_user_id', '=', customer_user.id),
                ('status', 'in', ['waiting', 'active'])
            ], limit=1)
            
            if session:
                return {
                    'id': session.id,
                    'session_uuid': session.session_uuid,
                    'status': session.status,
                    'operator_name': session.operator_id.name if session.operator_id else None,
                    'start_time': session.start_time.isoformat() if session.start_time else None,
                }
            
            return None
            
        except Exception as e:
            _logger.error("Error getting chat session: %s", str(e))
            return {'error': str(e)}

    @http.route('/omisol/livechat/create_session', type='json', auth='user', methods=['POST'])
    def create_chat_session(self):
        """Create new chat session"""
        try:
            # Find customer user for current user
            customer_user = self._get_customer_user()
            if not customer_user:
                return {'error': 'Customer user not found. Please contact support.'}
            
            # Check if there's already an active session
            existing_session = request.env['omisol.livechat.session'].search([
                ('customer_user_id', '=', customer_user.id),
                ('status', 'in', ['waiting', 'active'])
            ], limit=1)
            
            if existing_session:
                return {
                    'id': existing_session.id,
                    'session_uuid': existing_session.session_uuid,
                    'status': existing_session.status,
                    'message': 'Reconnected to existing session'
                }
            
            # Create new session
            session = request.env['omisol.livechat.session'].create({
                'customer_user_id': customer_user.id,
                'status': 'waiting'
            })
            
            # Send welcome message
            session.message_post(
                body=_("Customer {} has started a new chat session.").format(customer_user.name),
                message_type='notification'
            )
            
            return {
                'id': session.id,
                'session_uuid': session.session_uuid,
                'status': session.status,
                'message': 'Chat session created successfully'
            }
            
        except Exception as e:
            _logger.error("Error creating chat session: %s", str(e))
            return {'error': str(e)}

    @http.route('/omisol/livechat/send_message', type='json', auth='user', methods=['POST'])
    def send_message(self, session_id, message):
        """Send message in chat session"""
        try:
            # Find session
            session = request.env['omisol.livechat.session'].browse(session_id)
            if not session.exists():
                return {'error': 'Session not found'}
            
            # Verify user access
            customer_user = self._get_customer_user()
            if not customer_user or session.customer_user_id.id != customer_user.id:
                return {'error': 'Access denied'}
            
            # Send message
            session.message_post(
                body=message,
                message_type='comment',
                author_id=request.env.user.partner_id.id
            )
            
            return {'success': True, 'message': 'Message sent'}
            
        except Exception as e:
            _logger.error("Error sending message: %s", str(e))
            return {'error': str(e)}

    @http.route('/omisol/livechat/get_messages', type='json', auth='user', methods=['POST'])
    def get_messages(self, session_id, last_message_id=None):
        """Get messages from chat session"""
        try:
            # Find session
            session = request.env['omisol.livechat.session'].browse(session_id)
            if not session.exists():
                return {'error': 'Session not found'}
            
            # Verify user access
            customer_user = self._get_customer_user()
            if not customer_user or session.customer_user_id.id != customer_user.id:
                return {'error': 'Access denied'}
            
            # Get messages
            domain = [('res_id', '=', session_id), ('model', '=', 'omisol.livechat.session')]
            if last_message_id:
                domain.append(('id', '>', last_message_id))
            
            messages = request.env['mail.message'].search(domain, order='create_date asc')
            
            message_data = []
            for msg in messages:
                message_data.append({
                    'id': msg.id,
                    'body': msg.body,
                    'author_name': msg.author_id.name,
                    'create_date': msg.create_date.isoformat(),
                    'message_type': msg.message_type,
                    'is_customer': msg.author_id.id == request.env.user.partner_id.id
                })
            
            return {
                'messages': message_data,
                'session_status': session.status
            }
            
        except Exception as e:
            _logger.error("Error getting messages: %s", str(e))
            return {'error': str(e)}

    @http.route('/omisol/livechat/close_session', type='json', auth='user', methods=['POST'])
    def close_session(self, session_id, rating=None, feedback=None):
        """Close chat session with optional feedback"""
        try:
            # Find session
            session = request.env['omisol.livechat.session'].browse(session_id)
            if not session.exists():
                return {'error': 'Session not found'}
            
            # Verify user access
            customer_user = self._get_customer_user()
            if not customer_user or session.customer_user_id.id != customer_user.id:
                return {'error': 'Access denied'}
            
            # Update session
            update_vals = {'status': 'closed', 'end_time': request.env.cr.now()}
            if rating:
                update_vals['rating'] = rating
            if feedback:
                update_vals['feedback'] = feedback
            
            session.write(update_vals)
            
            # Send closing message
            session.message_post(
                body=_("Chat session closed by customer."),
                message_type='notification'
            )
            
            return {'success': True, 'message': 'Session closed'}
            
        except Exception as e:
            _logger.error("Error closing session: %s", str(e))
            return {'error': str(e)}

    @http.route('/omisol/livechat/check_auth', type='json', auth='public', methods=['POST'])
    def check_authentication(self):
        """Check if user is authenticated and can use live chat"""
        try:
            if request.env.user._is_public():
                return {
                    'authenticated': False,
                    'message': 'Please log in to use live chat'
                }
            
            customer_user = self._get_customer_user()
            if not customer_user:
                return {
                    'authenticated': False,
                    'message': 'Customer account not found. Please contact support.'
                }
            
            return {
                'authenticated': True,
                'customer_name': customer_user.name,
                'customer_email': customer_user.email
            }
            
        except Exception as e:
            _logger.error("Error checking authentication: %s", str(e))
            return {
                'authenticated': False,
                'message': 'Authentication check failed'
            }

    def _get_customer_user(self):
        """Get customer user for current logged-in user"""
        try:
            if request.env.user._is_public():
                return None
            
            # Try to find customer user by email
            customer_user = request.env['omisol.customer.user'].search([
                ('email', '=', request.env.user.email)
            ], limit=1)
            
            if not customer_user:
                # Try to find by partner email
                partner = request.env.user.partner_id
                if partner and partner.email:
                    customer_user = request.env['omisol.customer.user'].search([
                        ('email', '=', partner.email)
                    ], limit=1)
            
            return customer_user
            
        except Exception as e:
            _logger.error("Error getting customer user: %s", str(e))
            return None

    @http.route('/omisol/livechat/operator/get_sessions', type='json', auth='user', methods=['POST'])
    def get_operator_sessions(self):
        """Get chat sessions for operators (backend users)"""
        try:
            # Check if user has access to chat sessions
            if not request.env.user.has_group('base.group_user'):
                return {'error': 'Access denied'}
            
            # Get waiting and active sessions
            sessions = request.env['omisol.livechat.session'].search([
                ('status', 'in', ['waiting', 'active'])
            ], order='create_date desc')
            
            session_data = []
            for session in sessions:
                session_data.append({
                    'id': session.id,
                    'customer_name': session.customer_user_id.name,
                    'customer_email': session.customer_user_id.email,
                    'status': session.status,
                    'start_time': session.start_time.isoformat(),
                    'operator_name': session.operator_id.name if session.operator_id else None,
                    'message_count': session.message_count
                })
            
            return {'sessions': session_data}
            
        except Exception as e:
            _logger.error("Error getting operator sessions: %s", str(e))
            return {'error': str(e)}

    @http.route('/omisol/livechat/operator/assign_session', type='json', auth='user', methods=['POST'])
    def assign_session(self, session_id):
        """Assign chat session to current operator"""
        try:
            # Check if user has access
            if not request.env.user.has_group('base.group_user'):
                return {'error': 'Access denied'}
            
            session = request.env['omisol.livechat.session'].browse(session_id)
            if not session.exists():
                return {'error': 'Session not found'}
            
            # Assign to current user
            session.action_assign_to_me()
            
            return {'success': True, 'message': 'Session assigned'}
            
        except Exception as e:
            _logger.error("Error assigning session: %s", str(e))
            return {'error': str(e)}
