# -*- coding: utf-8 -*-

import base64
import logging
from odoo import http, fields
from odoo.http import request

_logger = logging.getLogger(__name__)


class OmisolWebsite(http.Controller):

    @http.route('/', type='http', auth="public", website=True)
    def homepage(self, **kwargs):
        """Homepage with all sections"""
        try:
            # Get featured products
            ProductShowcase = request.env['omisol.product.showcase']
            featured_products = ProductShowcase.get_featured_products(limit=6)

            # Get company statistics
            statistics = request.env['omisol.company.statistics'].search([
                ('is_active', '=', True)
            ])

            # Get gallery images for homepage
            gallery_images = request.env['omisol.company.gallery'].search([
                ('is_published', '=', True),
                ('category', 'in', ['facility', 'products'])
            ], limit=8)

            values = {
                'featured_products': featured_products,
                'statistics': statistics,
                'gallery_images': gallery_images,
            }
            return request.render('ai_omisol.Omisol_homepage', values)
        except Exception as e:
            # Log the error and return a simple page
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Homepage error: {e}")

            # Return a simple homepage without dynamic content
            values = {
                'featured_products': request.env['omisol.product.showcase'].browse([]),
                'statistics': request.env['omisol.company.statistics'].browse([]),
                'gallery_images': request.env['omisol.company.gallery'].browse([]),
            }
            return request.render('ai_omisol.Omisol_homepage', values)

    @http.route('/products', type='http', auth="public", website=True)
    def products_page(self, category=None, **kwargs):
        """Products listing page"""
        try:
            ProductShowcase = request.env['omisol.product.showcase']

            domain = [('is_published', '=', True)]
            if category:
                domain.append(('category', '=', category))

            products = ProductShowcase.search(domain)

            # Get categories for filter - handle safely
            try:
                categories = ProductShowcase._fields['category'].selection or []
            except:
                categories = []

            values = {
                'products': products,
                'categories': categories,
                'current_category': category,
            }
            return request.render('ai_omisol.products_page', values)
        except Exception as e:
            # Fallback to basic template if there's an error
            values = {
                'products': [],
                'categories': [],
                'current_category': None,
                'error_message': str(e),
            }
            return request.render('ai_omisol.products_page', values)

    @http.route(['/product/<int:product_id>', '/product/<int:product_id>/<string:slug>'], type='http', auth="public", website=True)
    def product_detail(self, product_id, slug=None, **kwargs):
        """Product detail page"""
        try:
            product = request.env['omisol.product.showcase'].browse(product_id)

            if not product.exists() or not product.is_published:
                raise request.not_found()

            # Get related products from same category
            related_products = request.env['omisol.product.showcase'].search([
                ('category', '=', product.category),
                ('is_published', '=', True),
                ('id', '!=', product.id)
            ], limit=3)

            # Get categories for display
            try:
                categories = request.env['omisol.product.showcase']._fields['category'].selection or []
            except:
                categories = []

            values = {
                'product': product,
                'related_products': related_products,
                'categories': categories,
            }
            return request.render('ai_omisol.product_detail', values)
        except Exception as e:
            # Log the error and show a user-friendly message
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error in product detail page: {str(e)}")
            raise request.not_found()

    @http.route('/product/download/<int:download_id>', type='http', auth="public")
    def download_file(self, download_id, **kwargs):
        """Download product file"""
        download = request.env['omisol.product.download'].browse(download_id)

        if not download.exists() or not download.is_active:
            raise request.not_found()

        # Increment download count
        download.sudo().write({'download_count': download.download_count + 1})

        # Return file
        return request.make_response(
            base64.b64decode(download.file_data),
            headers=[
                ('Content-Type', 'application/octet-stream'),
                ('Content-Disposition', f'attachment; filename="{download.filename}"'),
            ]
        )



    @http.route('/certificates', type='http', auth="public", website=True)
    def certificates_page(self, **kwargs):
        """Certificates page"""
        certificates = request.env['omisol.company.certificate'].search([
            ('is_active', '=', True)
        ])

        values = {
            'certificates': certificates,
        }
        return request.render('ai_omisol.certificates_page', values)

    @http.route('/support', type='http', auth="public", website=True)
    def support_page(self, **kwargs):
        """Support page"""
        values = {}
        return request.render('ai_omisol.support_page', values)

    @http.route('/gallery', type='http', auth="public", website=True)
    def gallery_page(self, category=None, **kwargs):
        """Photo gallery page"""
        domain = [('is_published', '=', True)]
        if category:
            domain.append(('category', '=', category))
        
        gallery_images = request.env['omisol.company.gallery'].search(domain)
        
        # Get categories for filter
        categories = request.env['omisol.company.gallery']._fields['category'].selection
        
        values = {
            'gallery_images': gallery_images,
            'categories': categories,
            'current_category': category,
        }
        return request.render('ai_omisol.gallery_page', values)

    @http.route('/about', type='http', auth="public", website=True)
    def about_page(self, **kwargs):
        """About page"""
        # Get some statistics for about page
        statistics = request.env['omisol.company.statistics'].search([
            ('is_active', '=', True)
        ])

        values = {
            'statistics': statistics,
        }
        return request.render('ai_omisol.about_us_page', values)

    @http.route(['/faq', '/support'], type='http', auth='public', website=True)
    def support_page(self, **kwargs):
        """Support page with dynamic content"""
        try:
            # Get published FAQ categories with their FAQs
            faq_categories = request.env['omisol.faq.category'].sudo().search([
                ('is_active', '=', True)
            ], order='sequence, name')
        except:
            # Fallback if models don't exist yet
            faq_categories = []

        return request.render('ai_omisol.support_page', {
            'faq_categories': faq_categories,
        })

    @http.route('/contactus/submit', type='http', auth='public', website=True, methods=['POST'], csrf=True)
    def contact_form_submit(self, **kwargs):
        """Handle contact form submission"""
        try:
            # Get form data
            name = kwargs.get('name', '').strip()
            email = kwargs.get('email', '').strip()
            phone = kwargs.get('phone', '').strip()
            subject = kwargs.get('subject', '').strip()
            message = kwargs.get('message', '').strip()

            # Basic validation
            if not name or not email or not message:
                return request.render('ai_omisol.contact_us_page', {
                    'error': 'Please fill in all required fields.',
                    'form_data': kwargs
                })

            # Create contact record (you can customize this based on your needs)
            contact_data = {
                'name': name,
                'email': email,
                'phone': phone,
                'subject': subject,
                'message': message,
                'date_submitted': fields.Datetime.now(),
            }

            # Log the contact form submission
            _logger.info(f"Contact form submission: {name} ({email}) - {subject}")

            # You can save to database, send email, etc. here
            # For now, we'll just show a success message

            return request.render('ai_omisol.contact_us_page', {
                'success': 'Thank you for your message! We will get back to you soon.',
                'form_submitted': True
            })

        except Exception as e:
            _logger.error(f"Error processing contact form: {e}")
            return request.render('ai_omisol.contact_us_page', {
                'error': 'An error occurred while processing your message. Please try again.',
                'form_data': kwargs
            })

    @http.route('/search', type='http', auth='public', website=True, methods=['GET', 'POST'])
    def global_search(self, **kwargs):
        """Enhanced global search functionality"""
        search_term = kwargs.get('search', '').strip()
        search_type = kwargs.get('type', 'all')

        results = {
            'products': [],
            'faqs': [],
            'downloads': [],
            'pages': []
        }

        if search_term:
            try:
                # Search products with comprehensive fields
                if search_type in ['all', 'products']:
                    try:
                        # First check if the model exists
                        product_model = request.env['omisol.product.showcase'].sudo()

                        # Build search domain dynamically
                        domain = [('is_published', '=', True)]
                        search_conditions = []

                        # Add search conditions for each field
                        fields_to_search = ['name', 'short_description', 'description', 'features', 'specifications', 'applications', 'category']
                        for field in fields_to_search:
                            search_conditions.append((field, 'ilike', search_term))

                        # Combine with OR conditions
                        if search_conditions:
                            domain.append(('|',) * (len(search_conditions) - 1))
                            domain.extend(search_conditions)

                        products = product_model.search(domain, limit=15)
                        results['products'] = products

                        _logger.info(f"Product search for '{search_term}': found {len(products)} results")

                    except Exception as e:
                        _logger.error(f"Product search error: {e}")
                        results['products'] = []

                # Search FAQs
                if search_type in ['all', 'faqs']:
                    faqs = request.env['omisol.faq'].sudo().search([
                        ('is_active', '=', True),
                        '|',
                        ('question', 'ilike', search_term),
                        ('answer', 'ilike', search_term)
                    ], limit=15)
                    results['faqs'] = faqs

                # Search Downloads
                if search_type in ['all', 'downloads']:
                    downloads = request.env['omisol.product.download'].sudo().search([
                        ('is_active', '=', True),
                        '|', '|',
                        ('name', 'ilike', search_term),
                        ('description', 'ilike', search_term),
                        ('file_type', 'ilike', search_term)
                    ], limit=10)
                    results['downloads'] = downloads

                # Search website pages (comprehensive content indexing)
                if search_type in ['all', 'pages']:
                    page_results = []

                    # Enhanced searchable pages with comprehensive content from survey_questions.csv
                    searchable_pages = [
                        {
                            'title': 'About Us - Omisol Tank Monitoring Solutions',
                            'url': '/about',
                            'description': 'Leading provider of automatic tank gauging systems with 25+ years expertise in petroleum and chemical facilities',
                            'content': 'omisol tank monitoring leak detection industrial solutions petroleum chemical facilities atex certified safety automatic tank gauge magnetostrictive technology OEL8000III series controllers precision measurement fuel management inventory control hazardous location rated equipment'
                        },
                        {
                            'title': 'Contact Us - Technical Support & Consultation',
                            'url': '/contactus',
                            'description': 'Get in touch with our expert team for technical support, installation, and consultation services',
                            'content': 'contact support technical assistance consultation expert team global service installation maintenance troubleshooting customer service engineering support field service technicians'
                        },
                        {
                            'title': 'Support & FAQ - Technical Documentation',
                            'url': '/support',
                            'description': 'Comprehensive technical support, frequently asked questions, manuals, and documentation',
                            'content': 'support faq technical documentation help troubleshooting maintenance user manual installation guide calibration procedures sensor configuration temperature monitoring level measurement safety protocols ATEX certification hazardous area classification'
                        },
                        {
                            'title': 'Products - Tank Monitoring & Gauging Solutions',
                            'url': '/products',
                            'description': 'Complete range of automatic tank gauging systems, sensors, and monitoring equipment',
                            'content': 'products tank gauging monitoring solutions automatic industrial equipment PROTEUS magnetostrictive sensors level measurement temperature monitoring inventory management fuel management petroleum storage chemical storage leak detection overfill protection'
                        },
                        {
                            'title': 'PROTEUS-K Automatic Tank Gauge',
                            'url': '/products',
                            'description': 'Advanced automatic tank gauging system with magnetostrictive technology',
                            'content': 'PROTEUS-K automatic tank gauge magnetostrictive sensor level measurement temperature monitoring fuel management inventory control petroleum storage chemical storage ATEX certified hazardous location'
                        },
                        {
                            'title': 'OEL8000III Series Controllers',
                            'url': '/products',
                            'description': 'Industrial tank monitoring controllers with advanced features',
                            'content': 'OEL8000III series controllers tank monitoring industrial automation level measurement temperature sensors pressure monitoring alarm systems data logging communication protocols'
                        },
                        {
                            'title': 'Temperature Sensors & Monitoring',
                            'url': '/products',
                            'description': 'Temperature monitoring solutions for refrigerators, freezers, and industrial applications',
                            'content': 'temperature sensors monitoring refrigerators freezers coolers industrial temperature measurement thermal monitoring cold storage temperature control HVAC systems'
                        },
                        {
                            'title': 'Safety & Installation Services',
                            'url': '/support',
                            'description': 'Professional installation and safety services for tank monitoring systems',
                            'content': 'safety installation services ATEX certification hazardous area classification professional installation commissioning maintenance training technical support field service'
                        }
                    ]

                    # Enhanced search through page content
                    for page in searchable_pages:
                        search_lower = search_term.lower()
                        if (search_lower in page['title'].lower() or
                            search_lower in page['description'].lower() or
                            search_lower in page['content'].lower() or
                            any(word in page['content'].lower() for word in search_lower.split())):
                            page_results.append(page)

                    results['pages'] = page_results[:8]

            except Exception as e:
                _logger.error(f"Search error: {e}")

        total_results = (len(results['products']) + len(results['faqs']) +
                        len(results['downloads']) + len(results['pages']))

        return request.render('ai_omisol.search_results', {
            'search_term': search_term,
            'search_type': search_type,
            'results': results,
            'total_results': total_results
        })



    @http.route('/faq/mark_helpful', type='json', auth='public', website=True)
    def faq_mark_helpful(self, faq_id):
        """Mark FAQ as helpful"""
        try:
            faq = request.env['omisol.faq'].sudo().browse(int(faq_id))
            if faq.exists():
                faq.action_mark_helpful()
                faq.action_increment_view()
                return {'success': True, 'helpful_count': faq.helpful_count}
        except Exception as e:
            return {'success': False, 'error': str(e)}
        return {'success': False}

    @http.route('/faq/mark_not_helpful', type='json', auth='public', website=True)
    def faq_mark_not_helpful(self, faq_id):
        """Mark FAQ as not helpful"""
        try:
            faq = request.env['omisol.faq'].sudo().browse(int(faq_id))
            if faq.exists():
                faq.action_mark_not_helpful()
                faq.action_increment_view()
                return {'success': True, 'not_helpful_count': faq.not_helpful_count}
        except Exception as e:
            return {'success': False, 'error': str(e)}
        return {'success': False}

    @http.route('/contactus', type='http', auth="public", website=True)
    def contact_us_page(self, **kwargs):
        """Contact Us page"""
        try:
            values = {
                'page_title': 'Contact Us - Omisol Private Limited',
                'meta_description': 'Get in touch with Omisol for tank gauging solutions, technical support, and industrial automation services.',
            }
            return request.render('ai_omisol.contact_us_page', values)
        except Exception as e:
            values = {
                'error_message': str(e),
            }
            return request.render('ai_omisol.contact_us_page', values)

    @http.route('/courses', type='http', auth="public", website=True)
    def courses_page(self, **kwargs):
        """Courses page"""
        try:
            values = {
                'page_title': 'Training Courses - Omisol Private Limited',
                'meta_description': 'Professional training courses for OEL8000III Series Controller safety, installation, features, and operation.',
            }
            return request.render('ai_omisol.course_page', values)
        except Exception as e:
            values = {
                'error_message': str(e),
            }
            return request.render('ai_omisol.course_page', values)
