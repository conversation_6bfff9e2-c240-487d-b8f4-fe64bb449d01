# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase


class TestModuleLoading(TransactionCase):
    """Test that the module loads correctly"""

    def test_models_exist(self):
        """Test that all models are properly loaded"""
        # Test customer model
        customer_model = self.env['omisol.customer']
        self.assertTrue(customer_model)

        # Test customer user model
        customer_user_model = self.env['omisol.customer.user']
        self.assertTrue(customer_user_model)

        # Test FAQ models
        faq_model = self.env['omisol.faq']
        self.assertTrue(faq_model)

        faq_category_model = self.env['omisol.faq.category']
        self.assertTrue(faq_category_model)

        # Test live chat model
        livechat_model = self.env['omisol.livechat.session']
        self.assertTrue(livechat_model)

        # Test wizard model
        wizard_model = self.env['customer.import.wizard']
        self.assertTrue(wizard_model)

    def test_create_customer(self):
        """Test creating a customer"""
        customer = self.env['omisol.customer'].create({
            'name': 'Test Customer',
            'email': '<EMAIL>',
            'phone': '+91 9876543210',
            'customer_type': 'individual'
        })
        self.assertTrue(customer)
        self.assertEqual(customer.name, 'Test Customer')
        self.assertEqual(customer.email, '<EMAIL>')

    def test_create_faq_category(self):
        """Test creating FAQ category"""
        category = self.env['omisol.faq.category'].create({
            'name': 'Test Category',
            'description': 'Test description',
            'icon': 'fa-test'
        })
        self.assertTrue(category)
        self.assertEqual(category.name, 'Test Category')

    def test_create_faq(self):
        """Test creating FAQ"""
        category = self.env['omisol.faq.category'].create({
            'name': 'Test Category',
            'description': 'Test description',
            'icon': 'fa-test'
        })

        faq = self.env['omisol.faq'].create({
            'name': 'Test Question?',
            'answer': '<p>Test Answer</p>',
            'category_id': category.id
        })
        self.assertTrue(faq)
        self.assertEqual(faq.name, 'Test Question?')
        self.assertEqual(faq.category_id, category)

    def test_customer_user_count(self):
        """Test customer user count computation and storage"""
        customer = self.env['omisol.customer'].create({
            'name': 'Test Customer',
            'email': '<EMAIL>',
            'phone': '+91 9876543210',
            'customer_type': 'individual'
        })

        # Initially should have 0 users
        self.assertEqual(customer.user_count, 0)

        # Create a user for the customer
        customer_user = self.env['omisol.customer.user'].create({
            'customer_id': customer.id,
            'login': 'testuser',
            'password': 'testpass123',
            'is_active': True
        })

        # Should now have 1 user
        self.assertEqual(customer.user_count, 1)

        # Test that we can search by user_count (this would fail if not stored)
        customers_with_users = self.env['omisol.customer'].search([('user_count', '>', 0)])
        self.assertIn(customer, customers_with_users)
