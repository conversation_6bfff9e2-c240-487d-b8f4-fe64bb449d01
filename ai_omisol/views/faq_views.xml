<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Actions (defined first for references) -->
        <record id="action_omisol_faq" model="ir.actions.act_window">
            <field name="name">FAQs</field>
            <field name="res_model">omisol.faq</field>
            <field name="view_mode">list,form</field>
            <field name="context">{'search_default_published': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first FAQ!
                </p>
                <p>
                    Build a comprehensive FAQ section to help your customers find answers quickly.
                    FAQs will be displayed on your website automatically.
                </p>
            </field>
        </record>

        <record id="action_omisol_faq_category" model="ir.actions.act_window">
            <field name="name">FAQ Categories</field>
            <field name="res_model">omisol.faq.category</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first FAQ category!
                </p>
                <p>
                    Organize your FAQs into categories to make them easier to find and manage.
                </p>
            </field>
        </record>

        <!-- FAQ Category List View -->
        <record id="view_omisol_faq_category_tree" model="ir.ui.view">
            <field name="name">omisol.faq.category.list</field>
            <field name="model">omisol.faq.category</field>
            <field name="arch" type="xml">
                <list string="FAQ Categories" sample="1">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="description"/>
                    <field name="icon"/>
                    <field name="faq_count"/>
                    <field name="is_active" widget="boolean_toggle"/>
                </list>
            </field>
        </record>
        
        <!-- FAQ Category Form View -->
        <record id="view_omisol_faq_category_form" model="ir.ui.view">
            <field name="name">omisol.faq.category.form</field>
            <field name="model">omisol.faq.category</field>
            <field name="arch" type="xml">
                <form string="FAQ Category">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="%(action_omisol_faq)d" type="action" class="oe_stat_button" icon="fa-question-circle" context="{'search_default_category_id': id}">
                                <field name="faq_count" widget="statinfo" string="FAQs"/>
                            </button>
                        </div>
                        
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="sequence"/>
                                <field name="is_active"/>
                            </group>
                            <group>
                                <field name="icon"/>
                                <field name="color" widget="color"/>
                            </group>
                        </group>
                        
                        <group string="Description">
                            <field name="description" nolabel="1"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- FAQ List View -->
        <record id="view_omisol_faq_tree" model="ir.ui.view">
            <field name="name">omisol.faq.list</field>
            <field name="model">omisol.faq</field>
            <field name="arch" type="xml">
                <list string="FAQs" sample="1">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="category_id"/>
                    <field name="is_published" widget="boolean_toggle"/>
                    <field name="view_count"/>
                    <field name="helpful_count"/>
                    <field name="not_helpful_count"/>
                    <field name="write_date"/>
                </list>
            </field>
        </record>
        
        <!-- FAQ Form View -->
        <record id="view_omisol_faq_form" model="ir.ui.view">
            <field name="name">omisol.faq.form</field>
            <field name="model">omisol.faq</field>
            <field name="arch" type="xml">
                <form string="FAQ">
                    <header>
                        <button name="action_mark_helpful" type="object" string="Mark Helpful" class="btn-success" icon="fa-thumbs-up"/>
                        <button name="action_mark_not_helpful" type="object" string="Mark Not Helpful" class="btn-warning" icon="fa-thumbs-down"/>
                        <field name="is_published" widget="boolean_button" options="{'terminology': {'string_true': 'Published', 'string_false': 'Draft'}}"/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="name" placeholder="Enter your question here..."/>
                                <field name="category_id"/>
                                <field name="sequence"/>
                                <!-- Tags field removed - not implemented yet -->
                            </group>
                            <group>
                                <field name="view_count" readonly="1"/>
                                <field name="helpful_count" readonly="1"/>
                                <field name="not_helpful_count" readonly="1"/>
                                <field name="create_uid" readonly="1"/>
                                <field name="write_date" readonly="1"/>
                            </group>
                        </group>
                        
                        <group string="Answer">
                            <field name="answer" nolabel="1" widget="html"/>
                        </group>
                    </sheet>
                    <!-- Chatter disabled - requires Odoo service restart for mail.thread inheritance -->
                    <!-- <chatter>
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </chatter> -->
                </form>
            </field>
        </record>
        
        <!-- FAQ Search View -->
        <record id="view_omisol_faq_search" model="ir.ui.view">
            <field name="name">omisol.faq.search</field>
            <field name="model">omisol.faq</field>
            <field name="arch" type="xml">
                <search string="Search FAQs">
                    <field name="name" string="Question" filter_domain="['|', ('name', 'ilike', self), ('answer', 'ilike', self)]"/>
                    <field name="category_id"/>
                    <field name="create_uid"/>
                    
                    <filter string="Published" name="published" domain="[('is_published', '=', True)]"/>
                    <filter string="Draft" name="draft" domain="[('is_published', '=', False)]"/>
                    <filter string="Popular" name="popular" domain="[('view_count', '>', 10)]"/>
                    <filter string="Helpful" name="helpful" domain="[('helpful_count', '>', 0)]"/>
                    
                    <separator/>
                    <filter string="My FAQs" name="my_faqs" domain="[('create_uid', '=', uid)]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Category" name="group_category" context="{'group_by': 'category_id'}"/>
                        <filter string="Published Status" name="group_published" context="{'group_by': 'is_published'}"/>
                        <filter string="Created By" name="group_created_by" context="{'group_by': 'create_uid'}"/>
                        <filter string="Last Updated" name="group_last_updated" context="{'group_by': 'write_date:month'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Menu Items -->
        <menuitem id="menu_faq_root"
                  name="FAQ Management"
                  sequence="20"/>
        
        <menuitem id="menu_omisol_faq_category"
                  name="FAQ Categories"
                  parent="menu_faq_root"
                  action="action_omisol_faq_category"
                  sequence="10"/>
        
        <menuitem id="menu_omisol_faq"
                  name="FAQs"
                  parent="menu_faq_root"
                  action="action_omisol_faq"
                  sequence="20"/>
    </data>
</odoo>
