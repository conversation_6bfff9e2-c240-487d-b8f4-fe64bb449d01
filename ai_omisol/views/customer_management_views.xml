<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Customer Management Menu -->
        <menuitem id="menu_customer_management_root"
                  name="Customer Management"
                  sequence="10"/>
        
        <!-- Customer List View -->
        <record id="view_omisol_customer_tree" model="ir.ui.view">
            <field name="name">omisol.customer.list</field>
            <field name="model">omisol.customer</field>
            <field name="arch" type="xml">
                <list string="Customers" sample="1">
                    <field name="name"/>
                    <field name="email"/>
                    <field name="phone"/>
                    <field name="company_name"/>
                    <field name="customer_type"/>
                    <field name="industry"/>
                    <field name="status" widget="badge" decoration-success="status == 'active'" decoration-warning="status == 'prospect'" decoration-danger="status == 'blocked'"/>
                    <field name="user_count"/>
                    <field name="registration_date"/>
                    <field name="last_activity_date"/>
                </list>
            </field>
        </record>
        
        <!-- Customer Form View -->
        <record id="view_omisol_customer_form" model="ir.ui.view">
            <field name="name">omisol.customer.form</field>
            <field name="model">omisol.customer</field>
            <field name="arch" type="xml">
                <form string="Customer">
                    <header>
                        <button name="action_create_user" type="object" string="Create User" class="btn-primary" icon="fa-user-plus"/>
                        <button name="action_send_whatsapp" type="object" string="Send WhatsApp" class="btn-success" icon="fa-whatsapp" invisible="whatsapp_number == False"/>
                        <field name="status" widget="statusbar" statusbar_visible="prospect,active,inactive"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_users" type="object" class="oe_stat_button" icon="fa-users">
                                <field name="user_count" widget="statinfo" string="Users"/>
                            </button>
                        </div>
                        
                        <group>
                            <group name="customer_info">
                                <field name="name" placeholder="Customer Full Name"/>
                                <field name="email" widget="email"/>
                                <field name="phone" widget="phone"/>
                                <field name="whatsapp_number" readonly="1"/>
                                <field name="customer_type"/>
                                <field name="industry"/>
                            </group>
                            <group name="company_info">
                                <field name="company_name" placeholder="Company Name"/>
                                <field name="registration_date"/>
                                <field name="last_activity_date"/>
                            </group>
                        </group>
                        
                        <group string="Address Information">
                            <group>
                                <field name="address" placeholder="Street Address"/>
                                <field name="city"/>
                                <field name="state"/>
                            </group>
                            <group>
                                <field name="country_id"/>
                                <field name="zip_code"/>
                            </group>
                        </group>
                        
                        <group string="Additional Information">
                            <field name="notes" placeholder="Additional notes about the customer..."/>
                        </group>
                        
                        <notebook>
                            <page string="Associated Users" name="users">
                                <field name="user_ids" context="{'default_customer_id': id}">
                                    <list string="Users" editable="bottom">
                                        <field name="name"/>
                                        <field name="username"/>
                                        <field name="email"/>
                                        <field name="phone"/>
                                        <field name="status" widget="badge"/>
                                        <field name="last_login"/>
                                        <field name="login_count"/>
                                        <button name="action_reset_password" type="object" string="Reset Password" icon="fa-key" class="btn-link"/>
                                        <button name="action_send_whatsapp_credentials" type="object" string="WhatsApp" icon="fa-whatsapp" class="btn-link"/>
                                    </list>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>
        
        <!-- Customer Search View -->
        <record id="view_omisol_customer_search" model="ir.ui.view">
            <field name="name">omisol.customer.search</field>
            <field name="model">omisol.customer</field>
            <field name="arch" type="xml">
                <search string="Search Customers">
                    <field name="name" string="Customer" filter_domain="['|', ('name', 'ilike', self), ('company_name', 'ilike', self)]"/>
                    <field name="email"/>
                    <field name="phone"/>
                    <field name="customer_type"/>
                    <field name="industry"/>
                    <field name="status"/>
                    
                    <filter string="Active" name="active" domain="[('status', '=', 'active')]"/>
                    <filter string="Prospects" name="prospects" domain="[('status', '=', 'prospect')]"/>
                    <filter string="Companies" name="companies" domain="[('customer_type', '=', 'company')]"/>
                    <filter string="Individuals" name="individuals" domain="[('customer_type', '=', 'individual')]"/>
                    
                    <separator/>
                    <filter string="With Users" name="with_users" domain="[('user_count', '>', 0)]"/>
                    <filter string="Without Users" name="without_users" domain="[('user_count', '=', 0)]"/>
                    
                    <separator/>
                    <filter string="Recent" name="recent" domain="[('registration_date', '>=', (context_today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Customer Type" name="group_customer_type" context="{'group_by': 'customer_type'}"/>
                        <filter string="Industry" name="group_industry" context="{'group_by': 'industry'}"/>
                        <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                        <filter string="Registration Date" name="group_registration_date" context="{'group_by': 'registration_date:month'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Customer Action -->
        <record id="action_omisol_customer" model="ir.actions.act_window">
            <field name="name">Customers</field>
            <field name="res_model">omisol.customer</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="view_omisol_customer_search"/>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first customer!
                </p>
                <p>
                    Manage your customers and automatically generate user accounts for them.
                    You can send credentials via email or WhatsApp.
                </p>
            </field>
        </record>
        
        <!-- Customer User List View -->
        <record id="view_omisol_customer_user_tree" model="ir.ui.view">
            <field name="name">omisol.customer.user.list</field>
            <field name="model">omisol.customer.user</field>
            <field name="arch" type="xml">
                <list string="Customer Users" sample="1">
                    <field name="customer_id"/>
                    <field name="name"/>
                    <field name="username"/>
                    <field name="email"/>
                    <field name="phone"/>
                    <field name="status" widget="badge" decoration-success="status == 'active'" decoration-warning="status == 'inactive'" decoration-danger="status == 'suspended'"/>
                    <field name="last_login"/>
                    <field name="login_count"/>
                    <field name="create_date"/>
                </list>
            </field>
        </record>
        
        <!-- Customer User Form View -->
        <record id="view_omisol_customer_user_form" model="ir.ui.view">
            <field name="name">omisol.customer.user.form</field>
            <field name="model">omisol.customer.user</field>
            <field name="arch" type="xml">
                <form string="Customer User">
                    <header>
                        <button name="action_reset_password" type="object" string="Reset Password" class="btn-primary" icon="fa-key"/>
                        <button name="send_credentials_email" type="object" string="Send Email" class="btn-secondary" icon="fa-envelope"/>
                        <button name="action_send_whatsapp_credentials" type="object" string="Send WhatsApp" class="btn-success" icon="fa-whatsapp"/>
                        <field name="status" widget="statusbar" statusbar_visible="active,inactive,suspended"/>
                    </header>
                    <sheet>
                        <group>
                            <group name="user_info">
                                <field name="customer_id"/>
                                <field name="name"/>
                                <field name="username"/>
                                <field name="email" widget="email"/>
                                <field name="phone" widget="phone"/>
                            </group>
                            <group name="login_info">
                                <field name="password" password="True"/>
                                <field name="last_login"/>
                                <field name="login_count"/>
                                <field name="odoo_user_id"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>
        
        <!-- Customer User Search View -->
        <record id="view_omisol_customer_user_search" model="ir.ui.view">
            <field name="name">omisol.customer.user.search</field>
            <field name="model">omisol.customer.user</field>
            <field name="arch" type="xml">
                <search string="Search Customer Users">
                    <field name="name" string="User" filter_domain="['|', ('name', 'ilike', self), ('username', 'ilike', self)]"/>
                    <field name="customer_id"/>
                    <field name="email"/>
                    <field name="status"/>
                    
                    <filter string="Active" name="active" domain="[('status', '=', 'active')]"/>
                    <filter string="Inactive" name="inactive" domain="[('status', '=', 'inactive')]"/>
                    <filter string="Suspended" name="suspended" domain="[('status', '=', 'suspended')]"/>
                    
                    <separator/>
                    <filter string="Recent Logins" name="recent_logins" domain="[('last_login', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                    <filter string="Never Logged In" name="never_logged" domain="[('last_login', '=', False)]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Customer" name="group_customer" context="{'group_by': 'customer_id'}"/>
                        <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                        <filter string="Creation Date" name="group_create_date" context="{'group_by': 'create_date:month'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Customer User Action -->
        <record id="action_omisol_customer_user" model="ir.actions.act_window">
            <field name="name">Customer Users</field>
            <field name="res_model">omisol.customer.user</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="view_omisol_customer_user_search"/>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No customer users found!
                </p>
                <p>
                    Customer users are automatically created when you create users for customers.
                    Go to Customers to create new users.
                </p>
            </field>
        </record>
        
        <!-- Menu Items -->
        <menuitem id="menu_omisol_customer"
                  name="Customers"
                  parent="menu_customer_management_root"
                  action="action_omisol_customer"
                  sequence="10"/>
        
        <menuitem id="menu_omisol_customer_user"
                  name="Customer Users"
                  parent="menu_customer_management_root"
                  action="action_omisol_customer_user"
                  sequence="20"/>

        <!-- Live Chat Session List View -->
        <record id="view_omisol_livechat_session_tree" model="ir.ui.view">
            <field name="name">omisol.livechat.session.list</field>
            <field name="model">omisol.livechat.session</field>
            <field name="arch" type="xml">
                <list string="Live Chat Sessions" sample="1">
                    <field name="name"/>
                    <field name="customer_id"/>
                    <field name="customer_user_id"/>
                    <field name="operator_id"/>
                    <field name="status" widget="badge" decoration-warning="status == 'waiting'" decoration-success="status == 'active'" decoration-muted="status == 'closed'" decoration-danger="status == 'abandoned'"/>
                    <field name="start_time"/>
                    <field name="duration"/>
                    <field name="message_count"/>
                    <field name="rating" widget="priority"/>
                </list>
            </field>
        </record>

        <!-- Live Chat Session Form View -->
        <record id="view_omisol_livechat_session_form" model="ir.ui.view">
            <field name="name">omisol.livechat.session.form</field>
            <field name="model">omisol.livechat.session</field>
            <field name="arch" type="xml">
                <form string="Live Chat Session">
                    <header>
                        <button name="action_assign_to_me" type="object" string="Assign to Me" class="btn-primary" icon="fa-user" invisible="status != 'waiting'"/>
                        <button name="action_close_session" type="object" string="Close Session" class="btn-secondary" icon="fa-times" invisible="status in ['closed', 'abandoned']"/>
                        <button name="action_view_customer" type="object" string="View Customer" class="btn-info" icon="fa-user-circle"/>
                        <field name="status" widget="statusbar" statusbar_visible="waiting,active,closed"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>

                        <group>
                            <group name="customer_info">
                                <field name="customer_id" readonly="1"/>
                                <field name="customer_user_id" readonly="1"/>
                                <field name="session_uuid" readonly="1"/>
                            </group>
                            <group name="session_info">
                                <field name="operator_id"/>
                                <field name="start_time" readonly="1"/>
                                <field name="end_time" readonly="1"/>
                                <field name="duration" readonly="1"/>
                                <field name="message_count" readonly="1"/>
                            </group>
                        </group>

                        <group string="Customer Feedback" invisible="rating == False and feedback == False">
                            <group>
                                <field name="rating" widget="priority"/>
                            </group>
                            <group>
                                <field name="feedback" nolabel="1"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Live Chat Session Search View -->
        <record id="view_omisol_livechat_session_search" model="ir.ui.view">
            <field name="name">omisol.livechat.session.search</field>
            <field name="model">omisol.livechat.session</field>
            <field name="arch" type="xml">
                <search string="Search Chat Sessions">
                    <field name="name"/>
                    <field name="customer_id"/>
                    <field name="customer_user_id"/>
                    <field name="operator_id"/>
                    <field name="status"/>

                    <filter string="Waiting" name="waiting" domain="[('status', '=', 'waiting')]"/>
                    <filter string="Active" name="active" domain="[('status', '=', 'active')]"/>
                    <filter string="My Chats" name="my_chats" domain="[('operator_id', '=', uid)]"/>
                    <filter string="Unassigned" name="unassigned" domain="[('operator_id', '=', False)]"/>

                    <separator/>
                    <filter string="Today" name="today" domain="[('start_time', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                    <filter string="This Week" name="this_week" domain="[('start_time', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>

                    <group expand="0" string="Group By">
                        <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                        <filter string="Operator" name="group_operator" context="{'group_by': 'operator_id'}"/>
                        <filter string="Customer" name="group_customer" context="{'group_by': 'customer_id'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'start_time:day'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Live Chat Session Action -->
        <record id="action_omisol_livechat_session" model="ir.actions.act_window">
            <field name="name">Live Chat Sessions</field>
            <field name="res_model">omisol.livechat.session</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="view_omisol_livechat_session_search"/>
            <field name="context">{'search_default_waiting': 1, 'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No chat sessions found!
                </p>
                <p>
                    Live chat sessions will appear here when customers start chatting on the website.
                    Only logged-in customers can initiate chat sessions.
                </p>
            </field>
        </record>

        <menuitem id="menu_omisol_livechat"
                  name="Live Chat"
                  parent="menu_customer_management_root"
                  action="action_omisol_livechat_session"
                  sequence="30"/>
    </data>
</odoo>
