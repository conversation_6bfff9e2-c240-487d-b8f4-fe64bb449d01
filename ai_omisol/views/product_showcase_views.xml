<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Product Showcase Views -->
    <record id="view_product_showcase_tree" model="ir.ui.view">
        <field name="name">omisol.product.showcase.list</field>
        <field name="model">omisol.product.showcase</field>
        <field name="arch" type="xml">
            <list string="Product Showcase">
                <field name="sequence" widget="handle"/>
                <field name="image_small" widget="image" class="oe_avatar"/>
                <field name="name"/>
                <field name="category"/>
                <field name="is_featured"/>
                <field name="is_published"/>
            </list>
        </field>
    </record>

    <record id="view_product_showcase_form" model="ir.ui.view">
        <field name="name">omisol.product.showcase.form</field>
        <field name="model">omisol.product.showcase</field>
        <field name="arch" type="xml">
            <form string="Product Showcase">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="is_published" widget="boolean_button" options='{"terminology": {"string_true": "Published", "string_false": "Archived", "hover_true": "Archive", "hover_false": "Restore"}}'/>
                        </button>
                        <button name="action_go_to_website" type="object" class="oe_stat_button" icon="fa-globe" invisible="is_published == False">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Go to</span>
                                <span class="o_stat_text">Website</span>
                            </div>
                        </button>
                    </div>
                    <field name="image" widget="image" class="oe_avatar" options="{'preview_image': 'image_medium', 'size': [90, 90]}"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Product Name"/>
                        </h1>
                        <field name="short_description" placeholder="Short description for product cards"/>
                    </div>
                    <group>
                        <group>
                            <field name="category"/>
                            <field name="is_featured"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="website_meta_title"/>
                            <field name="website_meta_description"/>
                            <field name="website_meta_keywords"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description" placeholder="Detailed product description"/>
                        </page>
                        <page string="Technical Details">
                            <group>
                                <field name="specifications" placeholder="Technical specifications"/>
                                <field name="features" placeholder="Key features and benefits"/>
                                <field name="applications" placeholder="Applications and use cases"/>
                            </group>
                        </page>
                        <page string="Media &amp; Downloads">
                            <group>
                                <field name="video_url" placeholder="YouTube or Vimeo URL"/>
                                <field name="brochure" filename="brochure_filename"/>
                                <field name="brochure_filename" invisible="1"/>
                            </group>
                            <separator string="Product Downloads"/>
                            <field name="download_ids">
                                <list editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="file_type"/>
                                    <field name="file_data" filename="filename"/>
                                    <field name="filename"/>
                                    <field name="file_size"/>
                                    <field name="file_extension"/>
                                    <field name="description"/>
                                    <field name="is_active"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_product_showcase_kanban" model="ir.ui.view">
        <field name="name">omisol.product.showcase.kanban</field>
        <field name="model">omisol.product.showcase</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="id"/>
                <field name="name"/>
                <field name="image_medium"/>
                <field name="category"/>
                <field name="is_featured"/>
                <field name="is_published"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_image">
                                <img t-att-src="kanban_image('omisol.product.showcase', 'image_medium', record.id.raw_value)" alt="Product"/>
                            </div>
                            <div class="oe_kanban_details">
                                <strong class="o_kanban_record_title">
                                    <field name="name"/>
                                </strong>
                                <div class="o_kanban_record_body">
                                    <field name="category"/>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span t-if="record.is_featured.raw_value" class="badge badge-success">Featured</span>
                                        <span t-if="!record.is_published.raw_value" class="badge badge-secondary">Draft</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="action_product_showcase" model="ir.actions.act_window">
        <field name="name">Product Showcase</field>
        <field name="res_model">omisol.product.showcase</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first product showcase!
            </p>
            <p>
                Add products to showcase on your website. These products will be displayed
                in the products section and can be featured on the homepage.
            </p>
        </field>
    </record>

    <!-- Company Statistics Views -->
    <record id="view_company_statistics_tree" model="ir.ui.view">
        <field name="name">omisol.company.statistics.list</field>
        <field name="model">omisol.company.statistics</field>
        <field name="arch" type="xml">
            <list string="Company Statistics" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="value"/>
                <field name="suffix"/>
                <field name="icon"/>
                <field name="description"/>
                <field name="is_active"/>
            </list>
        </field>
    </record>

    <record id="view_company_statistics_form" model="ir.ui.view">
        <field name="name">omisol.company.statistics.form</field>
        <field name="model">omisol.company.statistics</field>
        <field name="arch" type="xml">
            <form string="Company Statistics">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="value"/>
                            <field name="suffix"/>
                        </group>
                        <group>
                            <field name="icon" placeholder="e.g., fa-users, fa-building, fa-chart-line"/>
                            <field name="sequence"/>
                            <field name="is_active"/>
                        </group>
                    </group>
                    <field name="description" placeholder="Brief description of this statistic"/>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_company_statistics" model="ir.actions.act_window">
        <field name="name">Company Statistics</field>
        <field name="res_model">omisol.company.statistics</field>
        <field name="view_mode">list,form</field>
    </record>

    <!-- Company Gallery Views -->
    <record id="view_company_gallery_tree" model="ir.ui.view">
        <field name="name">omisol.company.gallery.list</field>
        <field name="model">omisol.company.gallery</field>
        <field name="arch" type="xml">
            <list string="Company Gallery">
                <field name="sequence" widget="handle"/>
                <field name="image_small" widget="image" class="oe_avatar"/>
                <field name="name"/>
                <field name="category"/>
                <field name="is_published"/>
            </list>
        </field>
    </record>

    <record id="view_company_gallery_form" model="ir.ui.view">
        <field name="name">omisol.company.gallery.form</field>
        <field name="model">omisol.company.gallery</field>
        <field name="arch" type="xml">
            <form string="Company Gallery">
                <sheet>
                    <field name="image" widget="image" class="oe_avatar" options="{'preview_image': 'image_medium', 'size': [90, 90]}"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Image Title"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="category"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="is_published"/>
                        </group>
                    </group>
                    <field name="description" placeholder="Image description"/>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_company_gallery_kanban" model="ir.ui.view">
        <field name="name">omisol.company.gallery.kanban</field>
        <field name="model">omisol.company.gallery</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="id"/>
                <field name="name"/>
                <field name="image_medium"/>
                <field name="category"/>
                <field name="is_published"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_image">
                                <img t-att-src="kanban_image('omisol.company.gallery', 'image_medium', record.id.raw_value)" alt="Gallery Image"/>
                            </div>
                            <div class="oe_kanban_details">
                                <strong class="o_kanban_record_title">
                                    <field name="name"/>
                                </strong>
                                <div class="o_kanban_record_body">
                                    <field name="category"/>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span t-if="!record.is_published.raw_value" class="badge badge-secondary">Draft</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="action_company_gallery" model="ir.actions.act_window">
        <field name="name">Company Gallery</field>
        <field name="res_model">omisol.company.gallery</field>
        <field name="view_mode">kanban,list,form</field>
    </record>

    <!-- Company Certificates Views -->
    <record id="view_company_certificate_tree" model="ir.ui.view">
        <field name="name">omisol.company.certificate.list</field>
        <field name="model">omisol.company.certificate</field>
        <field name="arch" type="xml">
            <list string="Company Certificates">
                <field name="sequence" widget="handle"/>
                <field name="certificate_image" widget="image" class="oe_avatar"/>
                <field name="name"/>
                <field name="issuing_authority"/>
                <field name="issue_date"/>
                <field name="expiry_date"/>
                <field name="is_valid"/>
                <field name="is_active"/>
            </list>
        </field>
    </record>

    <record id="view_company_certificate_form" model="ir.ui.view">
        <field name="name">omisol.company.certificate.form</field>
        <field name="model">omisol.company.certificate</field>
        <field name="arch" type="xml">
            <form string="Company Certificate">
                <sheet>
                    <field name="certificate_image" widget="image" class="oe_avatar" options="{'preview_image': 'certificate_image', 'size': [90, 90]}"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Certificate Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="issuing_authority"/>
                            <field name="certificate_number"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="issue_date"/>
                            <field name="expiry_date"/>
                            <field name="is_active"/>
                            <field name="is_valid"/>
                        </group>
                    </group>
                    <field name="description" placeholder="Certificate description"/>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_company_certificate" model="ir.actions.act_window">
        <field name="name">Company Certificates</field>
        <field name="res_model">omisol.company.certificate</field>
        <field name="view_mode">list,form</field>
    </record>
</odoo>
