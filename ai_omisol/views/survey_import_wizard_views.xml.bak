<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Survey Import Wizard Form View -->
        <record id="survey_import_wizard_form" model="ir.ui.view">
            <field name="name">Survey Import Wizard</field>
            <field name="model">survey.import.wizard</field>
            <field name="arch" type="xml">
                <form string="Import Survey from CSV">
                    <header>
                        <button name="action_import_survey" string="Import Survey" type="object"
                                class="btn-primary" invisible="state != 'draft'"/>
                        <button name="action_view_survey" string="View Survey" type="object"
                                class="btn-primary" invisible="state != 'done'"/>
                        <button name="action_reset" string="Reset" type="object"
                                class="btn-secondary" invisible="state == 'draft'"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,importing,done,error"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Survey Title"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="description" placeholder="Survey Description"/>
                                <field name="access_mode"/>
                                <field name="users_login_required"/>
                            </group>
                        </group>
                        
                        <notebook invisible="state not in ['draft', 'error']">
                            <page string="CSV Files">
                                <group>
                                    <group string="Questions File">
                                        <field name="questions_file" filename="questions_filename"/>
                                        <field name="questions_filename" invisible="1"/>
                                    </group>
                                    <group string="Answers File">
                                        <field name="answers_file" filename="answers_filename"/>
                                        <field name="answers_filename" invisible="1"/>
                                    </group>
                                </group>
                                
                                <div class="alert alert-info" role="alert">
                                    <h4>CSV File Format Requirements:</h4>
                                    <p><strong>Questions CSV should contain:</strong></p>
                                    <ul>
                                        <li>sequence: Question number</li>
                                        <li>title: Question text</li>
                                        <li>question_type: Type of question (simple_choice, multiple_choice, etc.)</li>
                                        <li>chapter: Optional chapter grouping</li>
                                    </ul>
                                    <p><strong>Answers CSV should contain:</strong></p>
                                    <ul>
                                        <li>question_sequence: Question number this answer belongs to</li>
                                        <li>answer_sequence: Answer order</li>
                                        <li>answer_value: Answer text</li>
                                        <li>is_correct: true/false for correct answer</li>
                                    </ul>
                                </div>
                            </page>
                        </notebook>
                        
                        <div invisible="state not in ['done', 'error']">
                            <group>
                                <field name="result_message" readonly="1" widget="text"/>
                                <field name="survey_id" readonly="1" invisible="not survey_id"/>
                            </group>
                        </div>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Survey Import Wizard Action -->
        <record id="action_survey_import_wizard" model="ir.actions.act_window">
            <field name="name">Import Survey from CSV</field>
            <field name="res_model">survey.import.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="survey_import_wizard_form"/>
        </record>

        <!-- Menu Item -->
        <menuitem id="menu_survey_import" 
                  name="Import Survey" 
                  parent="survey.menu_survey_configuration" 
                  action="action_survey_import_wizard" 
                  sequence="10"/>

    </data>
</odoo>
