#!/usr/bin/env python3
"""
Step-by-step Survey Import - Create survey first, then add questions
"""

import xmlrpc.client
import csv
import sys
from collections import defaultdict

# Odoo connection details
url = "https://omnisol.arihantai.com"
db = "omnisol.arihantai.com"
username = "demo"
password = "demo"

def connect_to_odoo():
    """Connect to Odoo and return models proxy"""
    try:
        print("Connecting to Odoo server...")
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            raise Exception("Authentication failed. Please check credentials.")
        
        print(f"Successfully authenticated as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        return models, uid
    except Exception as e:
        print(f"Connection error: {e}")
        return None, None

def read_csv_data():
    """Read questions and answers from CSV files"""
    questions = []
    answers_by_question = defaultdict(list)
    
    try:
        # Read questions
        print("📖 Reading questions from CSV...")
        with open('survey_questions.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                questions.append({
                    'id': int(row['sequence']),
                    'title': row['title'].strip(),
                    'question_type': row['question_type'].strip(),
                    'sequence': int(row['sequence']),
                    'chapter': row.get('chapter', '').strip()
                })
        
        print(f"✅ Read {len(questions)} questions from CSV")
        
        # Read answers
        print("📖 Reading answers from CSV...")
        with open('survey_answers.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                question_id = int(row['question_sequence'])
                answers_by_question[question_id].append({
                    'value': row['answer_value'].strip(),
                    'sequence': int(row['answer_sequence']),
                    'is_correct': row['is_correct'].strip().lower() == 'true'
                })
        
        total_answers = sum(len(answers) for answers in answers_by_question.values())
        print(f"✅ Read {total_answers} answers for {len(answers_by_question)} questions from CSV")
        
        return questions, answers_by_question
        
    except FileNotFoundError as e:
        print(f"❌ CSV file not found: {e}")
        return [], {}
    except Exception as e:
        print(f"❌ Error reading CSV files: {e}")
        return [], {}

def create_survey(models, uid):
    """Create empty survey first"""
    try:
        print("\n🚀 Creating survey...")
        
        survey_data = {
            'title': 'OEL8000III Series Controller Safety, Installation, Features, and Operation Survey',
            'description': 'Comprehensive survey covering safety, installation, features, and operation of OEL8000III Series Controllers',
            'access_mode': 'public',
            'users_login_required': False
        }
        
        survey_id = models.execute_kw(db, uid, password, 'survey.survey', 'create', [[survey_data]])[0]
        print(f"✅ Survey created with ID: {survey_id}")
        
        return survey_id
        
    except Exception as e:
        print(f"❌ Error creating survey: {e}")
        return None

def add_questions_to_survey(models, uid, survey_id, questions, answers_by_question):
    """Add questions to survey one by one"""
    try:
        print(f"\n📝 Adding {len(questions)} questions to survey {survey_id}...")
        
        created_questions = 0
        created_answers = 0
        
        for q in questions:
            try:
                # Create question
                question_data = {
                    'title': q['title'],
                    'question_type': q['question_type'],
                    'sequence': q['sequence'],
                    'is_page': False,
                    'survey_id': survey_id
                }
                
                question_id = models.execute_kw(db, uid, password, 'survey.question', 'create', [[question_data]])[0]
                created_questions += 1
                
                # Add answers for this question
                question_answers = answers_by_question.get(q['id'], [])
                for answer in question_answers:
                    try:
                        answer_data = {
                            'value': answer['value'],
                            'sequence': answer['sequence'],
                            'is_correct': answer['is_correct'],
                            'question_id': question_id
                        }
                        
                        answer_id = models.execute_kw(db, uid, password, 'survey.question.answer', 'create', [[answer_data]])[0]
                        created_answers += 1
                        
                    except Exception as e:
                        print(f"    ❌ Error creating answer for question {q['sequence']}: {e}")
                
                if created_questions % 10 == 0:
                    print(f"    ✅ Created {created_questions} questions so far...")
                
            except Exception as e:
                print(f"    ❌ Error creating question {q['sequence']}: {e}")
        
        print(f"✅ Successfully created {created_questions} questions and {created_answers} answers")
        return created_questions, created_answers
        
    except Exception as e:
        print(f"❌ Error adding questions to survey: {e}")
        return 0, 0

def verify_survey_import(models, uid, survey_id):
    """Verify that the survey was imported correctly"""
    try:
        print(f"\n🔍 Verifying survey import for Survey ID {survey_id}...")
        
        # Get survey details
        survey_data = models.execute_kw(db, uid, password, 'survey.survey', 'read', [[survey_id], ['title', 'access_mode', 'users_login_required', 'question_ids']])
        if survey_data and len(survey_data) > 0:
            survey_info = survey_data[0]
            print(f"📊 Survey Title: {survey_info['title']}")
            print(f"🔓 Access Mode: {survey_info['access_mode']}")
            print(f"👤 Login Required: {survey_info['users_login_required']}")
            print(f"📝 Questions: {len(survey_info['question_ids'])}")
            
            # Get question details
            if survey_info['question_ids']:
                questions = models.execute_kw(db, uid, password, 'survey.question', 'read', [survey_info['question_ids'], ['title', 'question_type', 'suggested_answer_ids']])
                
                total_answers = 0
                for q in questions[:5]:  # Show first 5 questions
                    total_answers += len(q['suggested_answer_ids'])
                    print(f"  - {q['title'][:50]}... ({q['question_type']}): {len(q['suggested_answer_ids'])} answers")
                
                # Count all answers
                for q in questions:
                    total_answers += len(q['suggested_answer_ids'])
                
                print(f"📋 Total Answers: {total_answers}")
                
                if len(survey_info['question_ids']) > 0 and total_answers > 0:
                    print("✅ Survey import verification PASSED!")
                    return True
                else:
                    print("❌ Survey import verification FAILED - No questions or answers found!")
                    return False
            else:
                print("❌ Survey import verification FAILED - No questions found!")
                return False
        else:
            print("❌ Survey not found!")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying survey: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Starting Step-by-Step Survey Import")
    print("="*60)
    
    # Connect to Odoo
    models, uid = connect_to_odoo()
    if not models:
        print("❌ Failed to connect to Odoo")
        return False
    
    # Read CSV data
    questions, answers_by_question = read_csv_data()
    if not questions:
        print("❌ No questions found in CSV")
        return False
    
    # Create survey
    survey_id = create_survey(models, uid)
    if not survey_id:
        print("❌ Failed to create survey")
        return False
    
    # Add questions to survey
    created_questions, created_answers = add_questions_to_survey(models, uid, survey_id, questions, answers_by_question)
    if created_questions == 0:
        print("❌ Failed to add questions to survey")
        return False
    
    # Verify import
    success = verify_survey_import(models, uid, survey_id)
    
    if success:
        print(f"\n🎉 SUCCESS! Survey imported successfully with ID: {survey_id}")
        print(f"✅ Created {created_questions} questions and {created_answers} answers!")
    else:
        print(f"\n❌ Import verification failed for Survey ID: {survey_id}")
    
    return success

if __name__ == "__main__":
    main()
