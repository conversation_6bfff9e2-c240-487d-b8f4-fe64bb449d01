# -*- coding: utf-8 -*-

import base64
import csv
import io
import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class CustomerImportWizard(models.TransientModel):
    _name = 'customer.import.wizard'
    _description = 'Customer Import Wizard'

    file_data = fields.Binary(
        string='CSV File',
        required=True,
        help="Upload a CSV file with customer data"
    )
    
    filename = fields.Char(string='Filename')
    
    create_users = fields.Boolean(
        string='Create Users Automatically',
        default=True,
        help="Automatically create user accounts for imported customers"
    )
    
    send_emails = fields.Boolean(
        string='Send Welcome Emails',
        default=True,
        help="Send welcome emails with login credentials to customers"
    )
    
    update_existing = fields.Boolean(
        string='Update Existing Customers',
        default=False,
        help="Update existing customers if email already exists"
    )
    
    import_summary = fields.Text(
        string='Import Summary',
        readonly=True
    )
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('done', 'Done'),
        ('error', 'Error')
    ], default='draft')

    def action_import_customers(self):
        """Import customers from CSV file"""
        self.ensure_one()
        
        if not self.file_data:
            raise UserError(_("Please upload a CSV file."))
        
        try:
            # Decode the file
            file_content = base64.b64decode(self.file_data)
            csv_data = file_content.decode('utf-8')
            
            # Parse CSV
            csv_reader = csv.DictReader(io.StringIO(csv_data))
            
            # Validate headers
            required_headers = ['name', 'email']
            optional_headers = [
                'phone', 'company_name', 'address', 'city', 'state', 'zip_code',
                'customer_type', 'industry', 'notes'
            ]
            
            headers = csv_reader.fieldnames
            if not headers:
                raise UserError(_("CSV file appears to be empty or invalid."))
            
            missing_headers = [h for h in required_headers if h not in headers]
            if missing_headers:
                raise UserError(_("Missing required columns: {}").format(', '.join(missing_headers)))
            
            # Import data
            imported_count = 0
            updated_count = 0
            error_count = 0
            created_users = 0
            errors = []
            
            for row_num, row in enumerate(csv_reader, start=2):  # Start from 2 (header is row 1)
                try:
                    # Clean and validate data
                    customer_data = self._prepare_customer_data(row)
                    
                    # Check if customer exists
                    existing_customer = self.env['omisol.customer'].search([
                        ('email', '=', customer_data['email'])
                    ], limit=1)
                    
                    if existing_customer:
                        if self.update_existing:
                            existing_customer.write(customer_data)
                            updated_count += 1
                            customer = existing_customer
                        else:
                            errors.append(_("Row {}: Customer with email {} already exists").format(row_num, customer_data['email']))
                            error_count += 1
                            continue
                    else:
                        # Create new customer
                        customer = self.env['omisol.customer'].create(customer_data)
                        imported_count += 1
                    
                    # Create user if requested
                    if self.create_users and not customer.user_ids:
                        try:
                            customer.action_create_user()
                            created_users += 1
                        except Exception as e:
                            errors.append(_("Row {}: Failed to create user for {}: {}").format(row_num, customer.name, str(e)))
                    
                except Exception as e:
                    error_count += 1
                    errors.append(_("Row {}: {}").format(row_num, str(e)))
                    _logger.error("Error importing customer row %d: %s", row_num, str(e))
            
            # Prepare summary
            summary_lines = [
                _("Import completed successfully!"),
                "",
                _("Summary:"),
                _("- New customers imported: {}").format(imported_count),
                _("- Existing customers updated: {}").format(updated_count),
                _("- Users created: {}").format(created_users),
                _("- Errors: {}").format(error_count),
            ]
            
            if errors:
                summary_lines.extend(["", _("Errors:")] + errors[:10])  # Show first 10 errors
                if len(errors) > 10:
                    summary_lines.append(_("... and {} more errors").format(len(errors) - 10))
            
            self.import_summary = '\n'.join(summary_lines)
            self.state = 'done'
            
            return {
                'type': 'ir.actions.act_window',
                'res_model': 'customer.import.wizard',
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'new',
                'context': self.env.context,
            }
            
        except Exception as e:
            self.import_summary = _("Import failed: {}").format(str(e))
            self.state = 'error'
            _logger.error("Customer import failed: %s", str(e))
            raise UserError(_("Import failed: {}").format(str(e)))
    
    def _prepare_customer_data(self, row):
        """Prepare customer data from CSV row"""
        # Map customer types
        customer_type_mapping = {
            'individual': 'individual',
            'company': 'company',
            'distributor': 'distributor',
            'retailer': 'retailer',
        }
        
        # Map industries
        industry_mapping = {
            'manufacturing': 'manufacturing',
            'chemical': 'chemical',
            'pharmaceutical': 'pharmaceutical',
            'automotive': 'automotive',
            'construction': 'construction',
            'textile': 'textile',
            'food': 'food',
            'other': 'other',
        }
        
        # Prepare data
        data = {
            'name': row.get('name', '').strip(),
            'email': row.get('email', '').strip().lower(),
            'phone': row.get('phone', '').strip(),
            'company_name': row.get('company_name', '').strip(),
            'address': row.get('address', '').strip(),
            'city': row.get('city', '').strip(),
            'state': row.get('state', '').strip(),
            'zip_code': row.get('zip_code', '').strip(),
            'notes': row.get('notes', '').strip(),
        }
        
        # Handle customer type
        customer_type = row.get('customer_type', '').strip().lower()
        if customer_type in customer_type_mapping:
            data['customer_type'] = customer_type_mapping[customer_type]
        else:
            data['customer_type'] = 'individual'  # Default
        
        # Handle industry
        industry = row.get('industry', '').strip().lower()
        if industry in industry_mapping:
            data['industry'] = industry_mapping[industry]
        
        # Handle country
        country_name = row.get('country', '').strip()
        if country_name:
            country = self.env['res.country'].search([
                '|', ('name', 'ilike', country_name), ('code', 'ilike', country_name)
            ], limit=1)
            if country:
                data['country_id'] = country.id
        
        # Validate required fields
        if not data['name']:
            raise ValidationError(_("Customer name is required"))
        
        if not data['email']:
            raise ValidationError(_("Email is required"))
        
        # Remove empty values
        return {k: v for k, v in data.items() if v}
    
    def action_download_template(self):
        """Download CSV template"""
        template_data = [
            ['name', 'email', 'phone', 'company_name', 'address', 'city', 'state', 'zip_code', 'country', 'customer_type', 'industry', 'notes'],
            ['John Doe', '<EMAIL>', '+91 9876543210', 'ABC Company', '123 Main St', 'Mumbai', 'Maharashtra', '400001', 'India', 'company', 'manufacturing', 'Important client'],
            ['Jane Smith', '<EMAIL>', '+91 9876543211', '', '456 Oak Ave', 'Delhi', 'Delhi', '110001', 'India', 'individual', 'chemical', ''],
        ]
        
        # Create CSV content
        output = io.StringIO()
        writer = csv.writer(output)
        writer.writerows(template_data)
        csv_content = output.getvalue()
        output.close()
        
        # Encode to base64
        csv_base64 = base64.b64encode(csv_content.encode('utf-8'))
        
        # Create attachment
        attachment = self.env['ir.attachment'].create({
            'name': 'customer_import_template.csv',
            'type': 'binary',
            'datas': csv_base64,
            'mimetype': 'text/csv',
        })
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'new',
        }
    
    def action_close(self):
        """Close wizard"""
        return {'type': 'ir.actions.act_window_close'}
