<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Customer Import Wizard Form -->
        <record id="view_customer_import_wizard_form" model="ir.ui.view">
            <field name="name">customer.import.wizard.form</field>
            <field name="model">customer.import.wizard</field>
            <field name="arch" type="xml">
                <form string="Import Customers">
                    <sheet>
                        <div class="oe_title">
                            <h1>Import Customers from CSV</h1>
                        </div>
                        
                        <div invisible="state != 'draft'">
                            <group>
                                <group>
                                    <field name="file_data" filename="filename" required="state == 'draft'"/>
                                    <field name="filename" invisible="1"/>
                                </group>
                                <group>
                                    <field name="create_users"/>
                                    <field name="send_emails" invisible="create_users == False"/>
                                    <field name="update_existing"/>
                                </group>
                            </group>
                            
                            <div class="alert alert-info" role="alert">
                                <h4>CSV Format Requirements:</h4>
                                <ul>
                                    <li><strong>Required columns:</strong> name, email</li>
                                    <li><strong>Optional columns:</strong> phone, company_name, address, city, state, zip_code, country, customer_type, industry, notes</li>
                                    <li><strong>Customer Types:</strong> individual, company, distributor, retailer</li>
                                    <li><strong>Industries:</strong> manufacturing, chemical, pharmaceutical, automotive, construction, textile, food, other</li>
                                </ul>
                                <p>
                                    <button name="action_download_template" type="object" string="Download Template" class="btn-link" icon="fa-download"/>
                                </p>
                            </div>
                        </div>
                        
                        <div invisible="state in ['draft']">
                            <group>
                                <field name="import_summary" widget="text" readonly="1"/>
                            </group>
                        </div>
                        
                        <field name="state" invisible="1"/>
                    </sheet>
                    
                    <footer>
                        <button name="action_import_customers" type="object" string="Import Customers" class="btn-primary" invisible="state != 'draft'"/>
                        <button name="action_close" type="object" string="Close" class="btn-secondary" invisible="state == 'draft'"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" invisible="state != 'draft'"/>
                    </footer>
                </form>
            </field>
        </record>
        
        <!-- Customer Import Wizard Action -->
        <record id="action_customer_import_wizard" model="ir.actions.act_window">
            <field name="name">Import Customers</field>
            <field name="res_model">customer.import.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="binding_model_id" ref="model_omisol_customer"/>
            <field name="binding_view_types">list</field>
        </record>
        
        <!-- Add Import button to customer list view -->
        <record id="view_omisol_customer_tree_import" model="ir.ui.view">
            <field name="name">omisol.customer.list.import</field>
            <field name="model">omisol.customer</field>
            <field name="inherit_id" ref="view_omisol_customer_tree"/>
            <field name="arch" type="xml">
                <list position="attributes">
                    <attribute name="import">false</attribute>
                </list>
            </field>
        </record>
    </data>
</odoo>
