#!/usr/bin/env python3
"""
Import Survey Questions from CSV using XML-RPC
Reads the CSV files and imports questions and answers via XML-RPC
"""

import xmlrpc.client
import csv
import sys

# Odoo connection details
url = "https://omnisol.arihantai.com"
db = "omnisol.arihantai.com"
username = "demo"
password = "demo"

def connect_to_odoo():
    """Connect to Odoo and return models proxy"""
    try:
        print("Connecting to Odoo server...")
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            raise Exception("Authentication failed. Please check credentials.")
        
        print(f"Successfully authenticated as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        return models, uid
    except Exception as e:
        print(f"Connection error: {e}")
        return None, None

def create_survey(models, uid):
    """Create the main survey"""
    try:
        survey_data = {
            'title': 'OEL8000III Series Controller Safety, Installation, Features, and Operation Survey',
            'description': 'Comprehensive certification survey for OEL8000III Series Controller covering safety protocols, installation procedures, advanced features, and operational diagnostics.',
            'access_mode': 'public',
            'users_login_required': False,
            'attempts_limit': 3,
            'time_limit': 60.0,
            'scoring_type': 'scoring_with_answers',
            'certification': True,
            'is_attempts_limited': True,
            'is_time_limited': True,
        }
        
        survey_id = models.execute_kw(db, uid, password, 'survey.survey', 'create', [[survey_data]])[0]
        print(f"✅ Survey created with ID: {survey_id}")
        return survey_id
    except Exception as e:
        print(f"❌ Error creating survey: {e}")
        return None

def read_questions_from_csv():
    """Read questions from CSV file"""
    questions = []
    try:
        with open('survey_questions.csv', 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                questions.append({
                    'sequence': int(row['sequence']),
                    'title': row['title'],
                    'question_type': row['question_type'],
                    'is_page': row['is_page'].upper() == 'TRUE',
                    'chapter': row['chapter']
                })
        print(f"✅ Read {len(questions)} questions from CSV")
        return questions
    except Exception as e:
        print(f"❌ Error reading questions CSV: {e}")
        return []

def read_answers_from_csv():
    """Read answers from CSV file"""
    answers = {}
    try:
        with open('survey_answers.csv', 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                question_seq = int(row['question_sequence'])
                if question_seq not in answers:
                    answers[question_seq] = []
                
                answers[question_seq].append({
                    'answer_value': row['answer_value'],
                    'is_correct': row['is_correct'].upper() == 'TRUE',
                    'answer_sequence': int(row['answer_sequence'])
                })
        
        total_answers = sum(len(ans_list) for ans_list in answers.values())
        print(f"✅ Read {total_answers} answers for {len(answers)} questions from CSV")
        return answers
    except Exception as e:
        print(f"❌ Error reading answers CSV: {e}")
        return {}

def import_questions_and_answers(models, uid, survey_id, questions, answers):
    """Import questions and answers using XML-RPC with proper verification"""
    try:
        print(f"\n📝 Starting import of {len(questions)} questions...")
        imported_questions = 0
        imported_answers = 0

        for i, question_data in enumerate(questions, 1):
            try:
                # Create question using Odoo 18 compatible method
                question_vals = {
                    'survey_id': survey_id,
                    'title': question_data['title'],
                    'question_type': question_data['question_type'],
                    'sequence': question_data['sequence'],
                    'is_page': question_data['is_page'],
                    'description': f"Chapter: {question_data['chapter']}",
                }

                # Try multiple API signatures for Odoo 18 compatibility
                question_id = None
                try:
                    # Method 1: New API with list of dicts
                    question_id = models.execute_kw(db, uid, password, 'survey.question', 'create', [[question_vals]])[0]
                except Exception as e1:
                    try:
                        # Method 2: Old API with single dict
                        question_id = models.execute_kw(db, uid, password, 'survey.question', 'create', [question_vals])
                    except Exception as e2:
                        try:
                            # Method 3: Direct API call
                            question_id = models.execute_kw(db, uid, password, 'survey.question', 'create', question_vals)
                        except Exception as e3:
                            print(f"      ❌ All create methods failed:")
                            print(f"         Method 1: {e1}")
                            print(f"         Method 2: {e2}")
                            print(f"         Method 3: {e3}")
                            continue

                # Verify question was created
                question_exists = models.execute_kw(db, uid, password, 'survey.question', 'search_count', [[('id', '=', question_id)]])
                if question_exists:
                    imported_questions += 1
                    print(f"  ✅ Question {i}/{len(questions)}: {question_data['title'][:50]}... (ID: {question_id})")

                    # Add answers if available
                    question_seq = question_data['sequence']
                    if question_seq in answers:
                        question_answers = answers[question_seq]
                        print(f"    📋 Adding {len(question_answers)} answers...")

                        for answer_data in question_answers:
                            try:
                                answer_vals = {
                                    'question_id': question_id,
                                    'value': answer_data['answer_value'],
                                    'is_correct': answer_data['is_correct'],
                                    'sequence': answer_data['answer_sequence'],
                                }

                                # Try multiple API signatures for Odoo 18 compatibility
                                answer_id = None
                                try:
                                    # Method 1: New API with list of dicts
                                    answer_id = models.execute_kw(db, uid, password, 'survey.question.answer', 'create', [[answer_vals]])[0]
                                except Exception as e1:
                                    try:
                                        # Method 2: Old API with single dict
                                        answer_id = models.execute_kw(db, uid, password, 'survey.question.answer', 'create', [answer_vals])
                                    except Exception as e2:
                                        try:
                                            # Method 3: Direct API call
                                            answer_id = models.execute_kw(db, uid, password, 'survey.question.answer', 'create', answer_vals)
                                        except Exception as e3:
                                            print(f"            ❌ All answer create methods failed:")
                                            print(f"               Method 1: {e1}")
                                            print(f"               Method 2: {e2}")
                                            print(f"               Method 3: {e3}")
                                            continue

                                # Verify answer was created
                                answer_exists = models.execute_kw(db, uid, password, 'survey.question.answer', 'search_count', [[('id', '=', answer_id)]])
                                if answer_exists:
                                    imported_answers += 1
                                    correct_mark = "✓" if answer_data['is_correct'] else "✗"
                                    print(f"      {correct_mark} {answer_data['answer_value'][:40]}... (ID: {answer_id})")
                                else:
                                    print(f"      ❌ Answer creation failed: {answer_data['answer_value'][:40]}...")
                            except Exception as e:
                                print(f"      ❌ Error creating answer: {e}")
                                continue
                else:
                    print(f"  ❌ Question creation failed: {question_data['title'][:50]}...")

            except Exception as e:
                print(f"    ❌ Error importing question {i}: {e}")
                continue

        print(f"\n🎉 Import completed!")
        print(f"📊 Successfully imported: {imported_questions} questions, {imported_answers} answers")
        return imported_questions > 0

    except Exception as e:
        print(f"❌ Error during import: {e}")
        return False

def verify_survey_import(models, uid, survey_id):
    """Verify that the survey import was successful"""
    try:
        print(f"\n🔍 Verifying survey import for Survey ID {survey_id}...")

        # Check survey exists
        survey_count = models.execute_kw(db, uid, password, 'survey.survey', 'search_count', [[('id', '=', survey_id)]])
        if survey_count == 0:
            print("❌ Survey not found!")
            return False

        # Count questions
        question_count = models.execute_kw(db, uid, password, 'survey.question', 'search_count', [[('survey_id', '=', survey_id)]])
        print(f"📝 Questions found: {question_count}")

        # Count answers
        question_ids = models.execute_kw(db, uid, password, 'survey.question', 'search', [[('survey_id', '=', survey_id)]])
        total_answers = 0
        if question_ids:
            total_answers = models.execute_kw(db, uid, password, 'survey.question.answer', 'search_count', [[('question_id', 'in', question_ids)]])
        print(f"📋 Answers found: {total_answers}")

        # Get survey details
        survey_data = models.execute_kw(db, uid, password, 'survey.survey', 'read', [[survey_id], ['title', 'access_mode', 'users_login_required']])
        if survey_data and len(survey_data) > 0:
            survey_info = survey_data[0]
            print(f"📊 Survey Title: {survey_info['title']}")
            print(f"🔓 Access Mode: {survey_info['access_mode']}")
            print(f"👤 Login Required: {survey_info['users_login_required']}")

        success = question_count > 0
        if success:
            print("✅ Survey import verification PASSED!")
        else:
            print("❌ Survey import verification FAILED - No questions found!")

        return success

    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

def main():
    """Main import function"""
    print("🚀 Starting CSV-based Survey Import")
    print("="*60)

    # Connect to Odoo
    models, uid = connect_to_odoo()
    if not models:
        print("❌ Failed to connect to Odoo")
        return False

    # Read CSV files
    questions = read_questions_from_csv()
    answers = read_answers_from_csv()

    if not questions:
        print("❌ No questions found in CSV")
        return False

    # Create survey
    survey_id = create_survey(models, uid)
    if not survey_id:
        print("❌ Failed to create survey")
        return False

    # Import questions and answers
    success = import_questions_and_answers(models, uid, survey_id, questions, answers)

    # Verify the import
    verification_success = verify_survey_import(models, uid, survey_id)

    if success and verification_success:
        print("\n" + "="*60)
        print("🎉 CSV IMPORT COMPLETED AND VERIFIED SUCCESSFULLY!")
        print("="*60)
        print(f"Survey ID: {survey_id}")
        print(f"Questions to import: {len(questions)}")
        print(f"Total answers to import: {sum(len(ans_list) for ans_list in answers.values())}")
        print("\nYou can now access the survey in Odoo backend:")
        print(f"Apps → Survey → Surveys → Survey ID {survey_id}")
    else:
        print("\n❌ Import failed or verification failed. Check the errors above.")

    return success and verification_success

if __name__ == "__main__":
    main()
