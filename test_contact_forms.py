#!/usr/bin/env python3
"""
Playwright test script for profectusaccounts.com contact forms
Tests form submission and verifies data in Odoo database
"""

import asyncio
import xmlrpc.client
from playwright.async_api import async_playwright
import time
import random
import string
from datetime import datetime

# Configuration
WEBSITE_URL = "http://localhost:8069"  # Adjust if different
CONTACT_PAGE = "/contactus"
DATABASE_CONFIG = {
    'url': 'http://localhost:8069',
    'db': 'profectusaccounts.com',
    'username': 'demo',
    'password': 'demo'
}

def generate_test_data():
    """Generate unique test data for form submission"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    random_suffix = ''.join(random.choices(string.ascii_lowercase, k=4))
    
    return {
        'name': f'Test User {timestamp}',
        'email': f'test_{timestamp}_{random_suffix}@example.com',
        'phone': f'******-{random.randint(100, 999)}-{random.randint(1000, 9999)}',
        'company': f'Test Company {timestamp}',
        'subject': f'Test Inquiry {timestamp}',
        'message': f'This is a test message submitted at {datetime.now().strftime("%Y-%m-%d %H:%M:%S")} to verify the contact form functionality.'
    }

def connect_to_odoo():
    """Connect to Odoo database"""
    try:
        common = xmlrpc.client.ServerProxy(f'{DATABASE_CONFIG["url"]}/xmlrpc/2/common')
        uid = common.authenticate(
            DATABASE_CONFIG['db'], 
            DATABASE_CONFIG['username'], 
            DATABASE_CONFIG['password'], 
            {}
        )
        
        if not uid:
            print("❌ Failed to authenticate with Odoo")
            return None, None
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f'{DATABASE_CONFIG["url"]}/xmlrpc/2/object')
        return models, uid
        
    except Exception as e:
        print(f"❌ Odoo connection error: {e}")
        return None, None

def check_contact_created(models, uid, test_email, test_name):
    """Check if contact was created in Odoo"""
    try:
        # Search for contact by email
        contacts = models.execute_kw(
            DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
            'res.partner', 'search_read',
            [[('email', '=', test_email)]],
            {'fields': ['name', 'email', 'phone', 'parent_name', 'function', 'comment', 'create_date']}
        )
        
        if contacts:
            contact = contacts[0]
            print(f"✅ Contact found in database:")
            print(f"   ID: {contact['id']}")
            print(f"   Name: {contact['name']}")
            print(f"   Email: {contact['email']}")
            print(f"   Phone: {contact['phone']}")
            print(f"   Company: {contact['parent_name']}")
            print(f"   Subject: {contact['function']}")
            print(f"   Created: {contact['create_date']}")
            
            # Check message content
            if contact['comment']:
                message_preview = contact['comment'][:100] + "..." if len(contact['comment']) > 100 else contact['comment']
                print(f"   Message: {message_preview}")
            
            return contact
        else:
            print(f"❌ No contact found with email: {test_email}")
            
            # Search by name as fallback
            name_contacts = models.execute_kw(
                DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
                'res.partner', 'search_read',
                [[('name', 'ilike', test_name)]],
                {'fields': ['name', 'email', 'create_date']}
            )
            
            if name_contacts:
                print(f"ℹ️ Found contacts with similar name:")
                for contact in name_contacts:
                    print(f"   • {contact['name']} - {contact['email']} - {contact['create_date']}")
            
            return None
        
    except Exception as e:
        print(f"❌ Error checking contact: {e}")
        return None

def check_mail_records(models, uid, test_email):
    """Check if any mail records were created"""
    try:
        # Check mail.mail records
        try:
            mails = models.execute_kw(
                DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
                'mail.mail', 'search_read',
                [[('email_from', 'ilike', test_email)]],
                {'fields': ['subject', 'email_from', 'email_to', 'state', 'create_date']}
            )
            
            if mails:
                print(f"✅ Found {len(mails)} mail record(s):")
                for mail in mails:
                    print(f"   • Subject: {mail['subject']}")
                    print(f"   • From: {mail['email_from']}")
                    print(f"   • To: {mail['email_to']}")
                    print(f"   • State: {mail['state']}")
                    print(f"   • Created: {mail['create_date']}")
                return mails
            else:
                print(f"ℹ️ No mail records found for: {test_email}")
                
        except Exception as e:
            print(f"ℹ️ Cannot access mail.mail records: {e}")
        
        # Check mail.message records
        try:
            messages = models.execute_kw(
                DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
                'mail.message', 'search_read',
                [[('email_from', 'ilike', test_email)]],
                {'fields': ['subject', 'email_from', 'body', 'create_date']}
            )
            
            if messages:
                print(f"✅ Found {len(messages)} message record(s):")
                for msg in messages:
                    print(f"   • Subject: {msg['subject']}")
                    print(f"   • From: {msg['email_from']}")
                    print(f"   • Created: {msg['create_date']}")
                return messages
            else:
                print(f"ℹ️ No message records found for: {test_email}")
                
        except Exception as e:
            print(f"ℹ️ Cannot access mail.message records: {e}")
        
        return None
        
    except Exception as e:
        print(f"❌ Error checking mail records: {e}")
        return None

async def test_contact_form_submission(page, test_data):
    """Test contact form submission using Playwright"""
    try:
        print(f"\n🌐 Testing contact form submission...")
        print(f"   URL: {WEBSITE_URL}{CONTACT_PAGE}")
        
        # Navigate to contact page
        await page.goto(f"{WEBSITE_URL}{CONTACT_PAGE}")
        await page.wait_for_load_state('networkidle')
        
        # Take screenshot of the page
        await page.screenshot(path='contact_page_before.png')
        print(f"   📸 Screenshot saved: contact_page_before.png")
        
        # Check if contact form exists
        form_selector = 'form[data-model_name], form#contactus_form, form[action*="/website/form/"]'
        form = await page.query_selector(form_selector)
        
        if not form:
            print(f"❌ Contact form not found on page")
            return False
        
        print(f"✅ Contact form found")
        
        # Fill form fields
        print(f"   📝 Filling form with test data...")
        
        # Try different field selectors for each field
        field_mappings = {
            'name': ['input[name="name"]', '#contact1', 'input[data-fill-with="name"]'],
            'email': ['input[name="email"]', 'input[name="email_from"]', '#contact3', 'input[type="email"]'],
            'phone': ['input[name="phone"]', '#contact2', 'input[type="tel"]'],
            'company': ['input[name="parent_name"]', 'input[name="company"]', '#contact4'],
            'subject': ['input[name="function"]', 'input[name="subject"]', '#contact5'],
            'message': ['textarea[name="comment"]', 'textarea[name="description"]', 'textarea[name="Message"]', '#contact6']
        }
        
        filled_fields = {}
        
        for field_name, selectors in field_mappings.items():
            field_filled = False
            for selector in selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        if field_name == 'message':
                            await element.fill(test_data['message'])
                        else:
                            await element.fill(test_data[field_name])
                        
                        filled_fields[field_name] = selector
                        print(f"      ✅ {field_name}: {selector}")
                        field_filled = True
                        break
                except Exception as e:
                    continue
            
            if not field_filled:
                print(f"      ⚠️ {field_name}: Field not found")
        
        # Take screenshot after filling
        await page.screenshot(path='contact_page_filled.png')
        print(f"   📸 Screenshot saved: contact_page_filled.png")
        
        # Submit form
        print(f"   🚀 Submitting form...")
        
        # Try different submit button selectors
        submit_selectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            '.s_website_form_send',
            'a[role="button"].btn',
            'button.btn-primary'
        ]
        
        submitted = False
        for selector in submit_selectors:
            try:
                submit_btn = await page.query_selector(selector)
                if submit_btn:
                    await submit_btn.click()
                    print(f"      ✅ Clicked submit button: {selector}")
                    submitted = True
                    break
            except Exception as e:
                continue
        
        if not submitted:
            print(f"      ❌ Could not find submit button")
            return False
        
        # Wait for response
        print(f"   ⏳ Waiting for form submission response...")
        await page.wait_for_timeout(3000)  # Wait 3 seconds
        
        # Check current URL
        current_url = page.url
        print(f"   🔗 Current URL: {current_url}")
        
        # Take screenshot after submission
        await page.screenshot(path='contact_page_after.png')
        print(f"   📸 Screenshot saved: contact_page_after.png")
        
        # Check if redirected to success page
        if 'thank-you' in current_url or 'success' in current_url:
            print(f"   ✅ Redirected to success page")
            return True
        elif current_url == f"{WEBSITE_URL}{CONTACT_PAGE}":
            print(f"   ⚠️ Still on contact page - checking for success message")
            
            # Look for success messages
            success_selectors = [
                '.alert-success',
                '.s_website_form_result',
                '#s_website_form_result',
                '.o_website_form_success'
            ]
            
            for selector in success_selectors:
                element = await page.query_selector(selector)
                if element:
                    text = await element.text_content()
                    if text and ('success' in text.lower() or 'thank' in text.lower()):
                        print(f"   ✅ Success message found: {text}")
                        return True
            
            print(f"   ⚠️ No clear success indication found")
            return False
        else:
            print(f"   ⚠️ Unexpected redirect to: {current_url}")
            return False
        
    except Exception as e:
        print(f"❌ Error during form submission: {e}")
        await page.screenshot(path='contact_page_error.png')
        return False

async def run_form_tests():
    """Run comprehensive form tests"""
    print("🚀 Starting Contact Form Tests for profectusaccounts.com")
    print("=" * 60)
    
    # Generate test data
    test_data = generate_test_data()
    print(f"📋 Generated test data:")
    for key, value in test_data.items():
        print(f"   {key}: {value}")
    
    # Connect to Odoo
    models, uid = connect_to_odoo()
    if not models:
        print("❌ Cannot proceed without Odoo connection")
        return False
    
    # Run Playwright tests
    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(headless=False)  # Set to True for headless
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # Test form submission
            form_success = await test_contact_form_submission(page, test_data)
            
            # Wait a bit for database processing
            print(f"\n⏳ Waiting for database processing...")
            await asyncio.sleep(5)
            
            # Check database for results
            print(f"\n🔍 Checking database for submitted data...")
            contact = check_contact_created(models, uid, test_data['email'], test_data['name'])
            mail_records = check_mail_records(models, uid, test_data['email'])
            
            # Summary
            print(f"\n" + "=" * 60)
            print(f"📊 TEST RESULTS SUMMARY")
            print(f"=" * 60)
            
            if form_success:
                print(f"✅ Form submission: SUCCESS")
            else:
                print(f"❌ Form submission: FAILED")
            
            if contact:
                print(f"✅ Database record: CREATED")
                print(f"   Contact ID: {contact['id']}")
                print(f"   Model: res.partner")
            else:
                print(f"❌ Database record: NOT FOUND")
            
            if mail_records:
                print(f"✅ Email records: {len(mail_records)} found")
            else:
                print(f"ℹ️ Email records: None found")
            
            # Overall assessment
            if form_success and contact:
                print(f"\n🎉 OVERALL RESULT: FORMS ARE WORKING!")
                print(f"   • Form submits successfully")
                print(f"   • Data is stored in database")
                print(f"   • Contact records are created")
                
                # Cleanup test data
                try:
                    models.execute_kw(
                        DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
                        'res.partner', 'unlink', [contact['id']]
                    )
                    print(f"   🧹 Test data cleaned up")
                except:
                    print(f"   ⚠️ Could not clean up test data")
                
                return True
            else:
                print(f"\n⚠️ OVERALL RESULT: FORMS NEED ATTENTION")
                if not form_success:
                    print(f"   • Form submission failed")
                if not contact:
                    print(f"   • Database record not created")
                return False
            
        finally:
            await browser.close()

async def main():
    """Main test function"""
    try:
        success = await run_form_tests()
        
        print(f"\n🎯 RECOMMENDATIONS:")
        if success:
            print(f"   • Forms are working correctly")
            print(f"   • No further action needed")
            print(f"   • Consider adding email notifications")
        else:
            print(f"   • Check form field mappings")
            print(f"   • Verify database permissions")
            print(f"   • Run SQL update scripts if not done")
            print(f"   • Check browser console for JavaScript errors")
        
        print(f"\n📁 Files created:")
        print(f"   • contact_page_before.png - Page before filling")
        print(f"   • contact_page_filled.png - Page after filling")
        print(f"   • contact_page_after.png - Page after submission")
        
    except Exception as e:
        print(f"❌ Test execution error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
