#!/usr/bin/env python3
"""
Final Solution: Upgrade IMCA modules in correct order
1. Remove view extensions temporarily
2. Upgrade modules to load model extensions
3. Add back view extensions
4. Upgrade again to load view extensions
"""

import xmlrpc.client
import time
import sys
import logging
import os
import shutil

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

IMCA_MODULES = [
    'imca_groups',
    'imca_services', 
    'imca_client_documents',
    'imca_crednetials_manager',
    'imca_dsc_management'
]

class FinalUpgrader:
    def __init__(self):
        self.common = None
        self.models = None
        self.uid = None
        
    def connect(self):
        """Connect to Odoo"""
        try:
            logger.info(f"Connecting to Odoo at {URL}")
            self.common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
            self.models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
            
            self.uid = self.common.authenticate(DB, USERNAME, PASSWORD, {})
            if not self.uid:
                raise Exception("Authentication failed")
                
            logger.info(f"Successfully connected as user ID: {self.uid}")
            return True
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    def backup_view_files(self):
        """Backup view files that contain partner extensions"""
        logger.info("Backing up view files...")
        
        view_files = [
            'imca_services/views/services_views.xml',
            'imca_client_documents/views/client_documents_views.xml',
            'imca_crednetials_manager/views/credentials_views.xml',
            'imca_dsc_management/views/dsc_management_views.xml'
        ]
        
        for view_file in view_files:
            if os.path.exists(view_file):
                backup_file = view_file + '.backup'
                shutil.copy2(view_file, backup_file)
                logger.info(f"Backed up {view_file} to {backup_file}")
    
    def remove_partner_extensions_from_views(self):
        """Temporarily remove partner form extensions from view files"""
        logger.info("Temporarily removing partner form extensions...")
        
        # Remove partner extension from imca_services
        with open('imca_services/views/services_views.xml', 'r') as f:
            content = f.read()
        
        # Remove the partner form extension block
        lines = content.split('\n')
        new_lines = []
        skip_lines = False
        
        for line in lines:
            if 'view_partner_form_services' in line:
                skip_lines = True
            elif skip_lines and '</record>' in line:
                skip_lines = False
                continue
            elif not skip_lines:
                new_lines.append(line)
        
        with open('imca_services/views/services_views.xml', 'w') as f:
            f.write('\n'.join(new_lines))
        
        # Do the same for other modules
        modules_to_fix = [
            ('imca_client_documents/views/client_documents_views.xml', 'view_partner_form_client_documents'),
            ('imca_crednetials_manager/views/credentials_views.xml', 'view_partner_form_credentials'),
            ('imca_dsc_management/views/dsc_management_views.xml', 'view_partner_form_dsc')
        ]
        
        for file_path, view_id in modules_to_fix:
            with open(file_path, 'r') as f:
                content = f.read()
            
            lines = content.split('\n')
            new_lines = []
            skip_lines = False
            
            for line in lines:
                if view_id in line:
                    skip_lines = True
                elif skip_lines and '</record>' in line:
                    skip_lines = False
                    continue
                elif not skip_lines:
                    new_lines.append(line)
            
            with open(file_path, 'w') as f:
                f.write('\n'.join(new_lines))
            
            logger.info(f"Removed partner extension from {file_path}")
    
    def restore_view_files(self):
        """Restore view files from backup"""
        logger.info("Restoring view files from backup...")
        
        view_files = [
            'imca_services/views/services_views.xml',
            'imca_client_documents/views/client_documents_views.xml',
            'imca_crednetials_manager/views/credentials_views.xml',
            'imca_dsc_management/views/dsc_management_views.xml'
        ]
        
        for view_file in view_files:
            backup_file = view_file + '.backup'
            if os.path.exists(backup_file):
                shutil.copy2(backup_file, view_file)
                logger.info(f"Restored {view_file} from backup")
    
    def upgrade_module(self, module_name):
        """Upgrade a specific module"""
        try:
            logger.info(f"Upgrading module: {module_name}")
            
            # Search for the module
            module_ids = self.models.execute_kw(
                DB, self.uid, PASSWORD,
                'ir.module.module', 'search',
                [('name', '=', module_name)]
            )
            
            if not module_ids:
                logger.error(f"Module {module_name} not found")
                return False
            
            module_id = module_ids[0]
            
            # Get current state
            module_data = self.models.execute_kw(
                DB, self.uid, PASSWORD,
                'ir.module.module', 'read',
                [module_id], {'fields': ['state']}
            )[0]
            
            logger.info(f"Module {module_name} state: {module_data['state']}")
            
            if module_data['state'] == 'installed':
                # Upgrade the module
                result = self.models.execute_kw(
                    DB, self.uid, PASSWORD,
                    'ir.module.module', 'button_immediate_upgrade',
                    [[module_id]]
                )
                logger.info(f"✅ Upgrade initiated for {module_name}")
                return True
            else:
                logger.warning(f"Module {module_name} not in installed state")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error upgrading {module_name}: {e}")
            return False
    
    def test_partner_fields(self):
        """Test if partner fields exist"""
        logger.info("Testing partner field access...")
        
        fields_to_test = [
            'x_service_ids',
            'x_client_document_ids', 
            'x_credential_ids',
            'x_dsc_ids'
        ]
        
        results = {}
        
        for field_name in fields_to_test:
            try:
                # Try to search partners with this field
                partner_ids = self.models.execute_kw(
                    DB, self.uid, PASSWORD,
                    'res.partner', 'search',
                    [[]], {'limit': 1}
                )
                
                if partner_ids:
                    partner_data = self.models.execute_kw(
                        DB, self.uid, PASSWORD,
                        'res.partner', 'read',
                        [partner_ids[0]], {'fields': ['name', field_name]}
                    )
                    logger.info(f"✅ Field {field_name} exists")
                    results[field_name] = True
                else:
                    logger.warning(f"No partners found to test {field_name}")
                    results[field_name] = False
                    
            except Exception as e:
                logger.error(f"❌ Field {field_name} test failed: {str(e)[:100]}...")
                results[field_name] = False
        
        return results

def main():
    """Main execution"""
    logger.info("=" * 60)
    logger.info("FINAL IMCA MODULES UPGRADE SOLUTION")
    logger.info("=" * 60)
    
    upgrader = FinalUpgrader()
    
    # Connect
    if not upgrader.connect():
        logger.error("Failed to connect")
        sys.exit(1)
    
    # Step 1: Backup view files
    upgrader.backup_view_files()
    
    # Step 2: Remove partner extensions temporarily
    upgrader.remove_partner_extensions_from_views()
    
    # Step 3: Upgrade all modules (this will load model extensions)
    logger.info("\n" + "=" * 40)
    logger.info("STEP 1: UPGRADING MODULES (MODEL EXTENSIONS)")
    logger.info("=" * 40)
    
    first_upgrade_results = {}
    for module in IMCA_MODULES:
        first_upgrade_results[module] = upgrader.upgrade_module(module)
        time.sleep(2)
    
    # Wait for upgrades to complete
    logger.info("Waiting 15 seconds for model upgrades to complete...")
    time.sleep(15)
    
    # Step 4: Test if partner fields now exist
    logger.info("\n" + "=" * 40)
    logger.info("TESTING PARTNER FIELDS AFTER MODEL UPGRADE")
    logger.info("=" * 40)
    field_results = upgrader.test_partner_fields()
    
    # Step 5: Restore view files
    upgrader.restore_view_files()
    
    # Step 6: Upgrade modules again (this will load view extensions)
    logger.info("\n" + "=" * 40)
    logger.info("STEP 2: UPGRADING MODULES (VIEW EXTENSIONS)")
    logger.info("=" * 40)
    
    second_upgrade_results = {}
    for module in IMCA_MODULES:
        second_upgrade_results[module] = upgrader.upgrade_module(module)
        time.sleep(2)
    
    # Wait for final upgrades to complete
    logger.info("Waiting 15 seconds for view upgrades to complete...")
    time.sleep(15)
    
    # Final summary
    logger.info("\n" + "=" * 60)
    logger.info("FINAL SUMMARY")
    logger.info("=" * 60)
    
    logger.info("First Upgrade (Models):")
    for module, success in first_upgrade_results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        logger.info(f"  {module}: {status}")
    
    logger.info("\nPartner Fields Test:")
    for field, exists in field_results.items():
        status = "✅ EXISTS" if exists else "❌ MISSING"
        logger.info(f"  {field}: {status}")
    
    logger.info("\nSecond Upgrade (Views):")
    for module, success in second_upgrade_results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        logger.info(f"  {module}: {status}")
    
    successful_fields = sum(field_results.values())
    successful_final_upgrades = sum(second_upgrade_results.values())
    
    if successful_fields == len(field_results) and successful_final_upgrades == len(IMCA_MODULES):
        logger.info("\n🎉 SUCCESS! All modules upgraded and partner extensions should now be visible!")
        logger.info("\n📋 Next Steps:")
        logger.info("1. Open a partner record in Odoo")
        logger.info("2. Check for new tabs: Groups, Services, Documents, Credentials, DSC Management")
        logger.info("3. Verify the CA Management menu structure")
    else:
        logger.warning(f"\n⚠️  Partial success: {successful_fields}/{len(field_results)} fields, {successful_final_upgrades}/{len(IMCA_MODULES)} final upgrades")
    
    logger.info("\nUpgrade process completed!")

if __name__ == "__main__":
    main()
