#!/usr/bin/env python3
"""
Complete test runner for profectusaccounts.com contact forms
Runs analysis first, then comprehensive tests
"""

import asyncio
import subprocess
import sys
import os
from datetime import datetime

def print_header(title):
    """Print formatted header"""
    print(f"\n{'='*60}")
    print(f"{title}")
    print(f"{'='*60}")

def print_section(title):
    """Print formatted section"""
    print(f"\n{'-'*50}")
    print(f"{title}")
    print(f"{'-'*50}")

def check_requirements():
    """Check if all requirements are installed"""
    print_section("Checking Requirements")
    
    try:
        import playwright
        print("✅ Playwright is installed")
    except ImportError:
        print("❌ Playwright not installed")
        print("   Run: pip install playwright")
        print("   Then: playwright install chromium")
        return False
    
    try:
        import xmlrpc.client
        print("✅ xmlrpc.client is available")
    except ImportError:
        print("❌ xmlrpc.client not available")
        return False
    
    return True

async def run_analysis():
    """Run the form analysis"""
    print_section("Running Form Analysis")
    
    try:
        # Import and run the analysis
        from analyze_current_forms import main as analyze_main
        await analyze_main()
        return True
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        return False

async def run_form_tests():
    """Run the main form tests"""
    print_section("Running Form Tests")
    
    try:
        # Import and run the tests
        from test_contact_forms import main as test_main
        await test_main()
        return True
    except Exception as e:
        print(f"❌ Tests failed: {e}")
        return False

def generate_test_report():
    """Generate a comprehensive test report"""
    print_section("Generating Test Report")
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    report = f"""
PROFECTUSACCOUNTS.COM CONTACT FORM TEST REPORT
==============================================
Generated: {timestamp}

TEST EXECUTION SUMMARY:
• Analysis Phase: Completed
• Form Testing Phase: Completed
• Database Verification: Completed

FILES GENERATED:
• current_contact_page.png - Current page state
• contact_page_before.png - Page before form filling
• contact_page_filled.png - Page after form filling  
• contact_page_after.png - Page after submission
• contact_page_error.png - Error state (if any)

WHAT WAS TESTED:
1. Database connectivity to profectusaccounts.com
2. Contact page accessibility at /contactus
3. Form field detection and mapping
4. Form submission process
5. Success page redirection
6. Database record creation verification
7. Email record checking

MODELS TESTED:
• res.partner - For contact storage
• mail.mail - For email handling
• mail.message - For message records

RECOMMENDATIONS:
Based on test results, check the console output above for:
• Form submission success/failure
• Database record creation
• Field mapping accuracy
• Success page redirection
• Any error messages or issues

NEXT STEPS:
1. Review all generated screenshots
2. Check database for test contact records
3. Verify form field mappings are correct
4. Ensure success pages are working
5. Set up email notifications if needed

For detailed results, see the console output above.
    """
    
    with open('test_report.txt', 'w') as f:
        f.write(report)
    
    print("📄 Test report saved: test_report.txt")

async def main():
    """Main test execution function"""
    print_header("PROFECTUSACCOUNTS.COM CONTACT FORM TESTING SUITE")
    
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Target: profectusaccounts.com contact forms")
    print(f"🔗 URL: http://localhost:8069/contactus")
    print(f"💾 Database: profectusaccounts.com")
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements not met. Please install missing dependencies.")
        return False
    
    try:
        # Step 1: Run analysis
        print_header("STEP 1: FORM ANALYSIS")
        analysis_success = await run_analysis()
        
        if not analysis_success:
            print("⚠️ Analysis had issues, but continuing with tests...")
        
        # Step 2: Run comprehensive tests
        print_header("STEP 2: COMPREHENSIVE TESTING")
        test_success = await run_form_tests()
        
        # Step 3: Generate report
        print_header("STEP 3: GENERATING REPORT")
        generate_test_report()
        
        # Final summary
        print_header("FINAL RESULTS")
        
        if test_success:
            print("🎉 TESTING COMPLETED SUCCESSFULLY!")
            print("\n✅ What worked:")
            print("   • Form analysis completed")
            print("   • Form submission tested")
            print("   • Database verification performed")
            print("   • Screenshots captured")
            print("   • Test report generated")
            
            print("\n📋 Review these files:")
            print("   • test_report.txt - Comprehensive report")
            print("   • *.png files - Screenshots of each step")
            print("   • Console output above - Detailed results")
            
        else:
            print("⚠️ TESTING COMPLETED WITH ISSUES")
            print("\n❌ Issues found:")
            print("   • Check console output for specific errors")
            print("   • Review screenshots for visual issues")
            print("   • Verify database connectivity")
            print("   • Check form field mappings")
            
            print("\n🔧 Troubleshooting:")
            print("   1. Ensure Odoo is running at http://localhost:8069")
            print("   2. Verify profectusaccounts.com database exists")
            print("   3. Check demo user has proper permissions")
            print("   4. Run SQL update scripts if not done")
        
        print(f"\n🕐 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return test_success
        
    except KeyboardInterrupt:
        print("\n⚠️ Testing interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        sys.exit(1)
