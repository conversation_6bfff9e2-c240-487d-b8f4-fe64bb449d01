#!/usr/bin/env python3
"""
Corrected XML-RPC script for upgrading IMCA modules
Fixes the parameter passing issues and uses proper Odoo 18 XML-RPC syntax
"""

import xmlrpc.client
import time
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'http://jnj18.arihantai.com:8069'
DB = 'jnj18.arihantai.com'
USERNAME = 'admin'
PASSWORD = 'admin'

# JNJ module to upgrade/install
JNJ_MODULES = [
    'jnj_bearing_website'
]

class OdooXMLRPCClient:
    def __init__(self, url, db, username, password):
        self.url = url
        self.db = db
        self.username = username
        self.password = password
        self.uid = None
        self.common = None
        self.models = None
        
    def connect(self):
        """Establish connection to Odoo"""
        try:
            logger.info(f"Connecting to Odoo at {self.url}")
            self.common = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/common')
            self.models = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/object')
            
            # Test version first
            version = self.common.version()
            logger.info(f"Odoo version: {version}")
            
            # Authenticate
            self.uid = self.common.authenticate(self.db, self.username, self.password, {})
            if not self.uid:
                raise Exception("Authentication failed")
                
            logger.info(f"Successfully connected as user ID: {self.uid}")
            return True
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    def execute_kw(self, model, method, args=None, kwargs=None):
        """Execute a method with proper parameter handling"""
        if args is None:
            args = []
        if kwargs is None:
            kwargs = {}
            
        try:
            return self.models.execute_kw(
                self.db, self.uid, self.password,
                model, method, args, kwargs
            )
        except Exception as e:
            logger.error(f"Error executing {model}.{method}: {e}")
            raise
    
    def search_modules(self, module_name):
        """Search for a module by name"""
        try:
            logger.info(f"Searching for module: {module_name}")
            
            # Use proper domain format for Odoo 18
            domain = [('name', '=', module_name)]
            
            module_ids = self.execute_kw(
                'ir.module.module', 
                'search', 
                [domain]
            )
            
            logger.info(f"Found module IDs for {module_name}: {module_ids}")
            return module_ids
            
        except Exception as e:
            logger.error(f"Error searching for module {module_name}: {e}")
            return []
    
    def read_module_info(self, module_ids):
        """Read module information"""
        try:
            if not module_ids:
                return []
                
            # Read with specific fields - don't use 'fields' as a parameter name
            fields_to_read = ['name', 'state', 'installed_version', 'latest_version']
            
            module_data = self.execute_kw(
                'ir.module.module',
                'read',
                [module_ids],
                {'fields': fields_to_read}
            )
            
            return module_data
            
        except Exception as e:
            logger.error(f"Error reading module info: {e}")
            # Try without fields parameter
            try:
                logger.info("Retrying read without fields parameter...")
                module_data = self.execute_kw(
                    'ir.module.module',
                    'read',
                    [module_ids]
                )
                return module_data
            except Exception as e2:
                logger.error(f"Error reading module info (retry): {e2}")
                return []
    
    def upgrade_module(self, module_name):
        """Upgrade a specific module using proper button methods"""
        try:
            logger.info(f"Starting upgrade process for: {module_name}")
            
            # Search for the module
            module_ids = self.search_modules(module_name)
            if not module_ids:
                logger.error(f"Module {module_name} not found")
                return False
            
            module_id = module_ids[0]
            logger.info(f"Module {module_name} ID: {module_id}")
            
            # Read module information
            module_data = self.read_module_info([module_id])
            if not module_data:
                logger.error(f"Could not read module data for {module_name}")
                return False
            
            module_info = module_data[0]
            current_state = module_info.get('state', 'unknown')
            logger.info(f"Module {module_name} current state: {current_state}")
            
            # Perform appropriate action based on state
            if current_state == 'installed':
                logger.info(f"Upgrading installed module: {module_name}")
                try:
                    # Use button_immediate_upgrade method
                    result = self.execute_kw(
                        'ir.module.module',
                        'button_immediate_upgrade',
                        [[module_id]]  # Pass as list of IDs
                    )
                    logger.info(f"Upgrade initiated for {module_name}, result: {result}")
                    return True
                except Exception as e:
                    logger.error(f"Error during upgrade of {module_name}: {e}")
                    return False
                    
            elif current_state == 'uninstalled':
                logger.info(f"Installing uninstalled module: {module_name}")
                try:
                    # Use button_immediate_install method
                    result = self.execute_kw(
                        'ir.module.module',
                        'button_immediate_install',
                        [[module_id]]  # Pass as list of IDs
                    )
                    logger.info(f"Installation initiated for {module_name}, result: {result}")
                    return True
                except Exception as e:
                    logger.error(f"Error during installation of {module_name}: {e}")
                    return False
                    
            elif current_state == 'to upgrade':
                logger.info(f"Module {module_name} already marked for upgrade")
                return True
                
            elif current_state == 'to install':
                logger.info(f"Module {module_name} already marked for installation")
                return True
                
            else:
                logger.warning(f"Module {module_name} in unexpected state: {current_state}")
                return False
                
        except Exception as e:
            logger.error(f"Error in upgrade process for {module_name}: {e}")
            return False
    
    def check_website_functionality(self):
        """Check if website functionality is working"""
        try:
            logger.info("Checking website functionality...")

            # Check if website pages exist
            domain = [('url', 'in', ['/', '/products', '/contact'])]

            page_ids = self.execute_kw(
                'website.page',
                'search',
                [domain]
            )

            if page_ids:
                logger.info(f"Found {len(page_ids)} website pages")

                # Read page information
                pages = self.execute_kw(
                    'website.page',
                    'read',
                    [page_ids],
                    {'fields': ['name', 'url', 'website_published']}
                )

                functionality_working = False
                for page in pages:
                    if page.get('website_published'):
                        logger.info(f"✅ Found published page: {page['name']} ({page['url']})")
                        functionality_working = True

                return functionality_working
            else:
                logger.warning("No website pages found")
                return False

        except Exception as e:
            logger.error(f"Error checking website functionality: {e}")
            return False
    
    def test_models_access(self):
        """Test access to JNJ Bearing models"""
        models_to_test = {
            'jnj_bearing_website.enquiry': 'Enquiries',
            'jnj_bearing_website.product_enquiry': 'Product Enquiries',
            'jnj_bearing_website.category_headers': 'Category Headers',
            'jnj_bearing_website.category_parent_headers': 'Category Parent Headers',
            'jnj_bearing_website.generate_barcode_batch': 'Barcode Batches',
            'jnj_bearing_website.generated_barcodes': 'Generated Barcodes',
            'jnj_bearing_website.data_modelling': 'Data Modelling',
            'jnj_bearing_website.documents': 'Documents'
        }
        
        results = {}
        
        for model_name, description in models_to_test.items():
            try:
                logger.info(f"Testing {description} model ({model_name})...")
                
                # Use search_count to test model access
                count = self.execute_kw(
                    model_name,
                    'search_count',
                    [[]]  # Empty domain
                )
                
                logger.info(f"✅ {description}: {count} records found")
                results[model_name] = True
                
            except Exception as e:
                logger.error(f"❌ {description} model error: {e}")
                results[model_name] = False
        
        return results

def main():
    """Main execution function"""
    logger.info("=" * 60)
    logger.info("JNJ BEARING WEBSITE MODULE INSTALLATION SCRIPT")
    logger.info("=" * 60)
    
    # Initialize client
    client = OdooXMLRPCClient(URL, DB, USERNAME, PASSWORD)
    
    # Connect to Odoo
    if not client.connect():
        logger.error("Failed to connect to Odoo. Exiting.")
        sys.exit(1)

    # Update app list before proceeding
    try:
        logger.info("Updating app list via ir.module.module.update_list ...")
        client.execute_kw('ir.module.module', 'update_list', [])
        logger.info("App list updated successfully.")
    except Exception as e:
        logger.error(f"Failed to update app list: {e}")

    # Test basic functionality first
    logger.info("\n" + "=" * 40)
    logger.info("TESTING MODEL ACCESS")
    logger.info("=" * 40)
    model_results = client.test_models_access()
    
    # Upgrade modules
    logger.info("\n" + "=" * 40)
    logger.info("UPGRADING JNJ BEARING MODULES")
    logger.info("=" * 40)

    upgrade_results = {}
    for module in JNJ_MODULES:
        logger.info(f"\n--- Processing {module} ---")
        upgrade_results[module] = client.upgrade_module(module)
        time.sleep(3)  # Wait between operations
    
    # Wait for operations to complete
    logger.info("\n⏳ Waiting 15 seconds for operations to complete...")
    time.sleep(15)
    
    # Check website functionality
    logger.info("\n" + "=" * 40)
    logger.info("CHECKING WEBSITE FUNCTIONALITY")
    logger.info("=" * 40)
    website_working = client.check_website_functionality()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("FINAL SUMMARY")
    logger.info("=" * 60)
    
    logger.info("Module Upgrade Results:")
    successful_upgrades = 0
    for module, success in upgrade_results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        logger.info(f"  {module}: {status}")
        if success:
            successful_upgrades += 1
    
    logger.info("\nModel Access Results:")
    accessible_models = 0
    for model, success in model_results.items():
        status = "✅ ACCESSIBLE" if success else "❌ FAILED"
        logger.info(f"  {model}: {status}")
        if success:
            accessible_models += 1
    
    logger.info(f"\nWebsite Functionality: {'✅ WORKING' if website_working else '❌ NOT WORKING'}")

    logger.info(f"\nOverall Results:")
    logger.info(f"  Modules upgraded: {successful_upgrades}/{len(JNJ_MODULES)}")
    logger.info(f"  Models accessible: {accessible_models}/{len(model_results)}")

    if successful_upgrades == len(JNJ_MODULES) and website_working:
        logger.info("\n🎉 SUCCESS! All modules upgraded and website functionality working!")
    elif successful_upgrades > 0:
        logger.info("\n✅ Partial success. Some modules upgraded successfully.")
    else:
        logger.warning("\n⚠️  No modules were successfully upgraded.")
    
    logger.info("\nScript completed.")

if __name__ == "__main__":
    main()
