#!/usr/bin/env python3
"""
IMCA Modules Upgrade Script
Connects to Odoo via XML-RPC to upgrade modules and check for errors
"""

import xmlrpc.client
import time
import sys
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('imca_upgrade.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

# IMCA modules to upgrade
IMCA_MODULES = [
    'imca_groups',
    'imca_services', 
    'imca_client_documents',
    'imca_crednetials_manager',
    'imca_dsc_management'
]

class OdooXMLRPCClient:
    def __init__(self, url, db, username, password):
        self.url = url
        self.db = db
        self.username = username
        self.password = password
        self.uid = None
        self.common = None
        self.models = None
        
    def connect(self):
        """Establish connection to Odoo"""
        try:
            logger.info(f"Connecting to Odoo at {self.url}")
            self.common = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/common')
            self.models = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/object')
            
            # Authenticate
            self.uid = self.common.authenticate(self.db, self.username, self.password, {})
            if not self.uid:
                raise Exception("Authentication failed")
                
            logger.info(f"Successfully connected as user ID: {self.uid}")
            return True
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    def execute(self, model, method, *args, **kwargs):
        """Execute a method on Odoo model"""
        try:
            return self.models.execute_kw(
                self.db, self.uid, self.password,
                model, method, args, kwargs
            )
        except Exception as e:
            logger.error(f"Error executing {model}.{method}: {e}")
            raise
    
    def get_module_info(self, module_name):
        """Get module information"""
        try:
            modules = self.execute('ir.module.module', 'search_read', 
                                 [('name', '=', module_name)], 
                                 {'fields': ['name', 'state', 'installed_version']})
            return modules[0] if modules else None
        except Exception as e:
            logger.error(f"Error getting module info for {module_name}: {e}")
            return None
    
    def upgrade_module(self, module_name):
        """Upgrade a specific module"""
        try:
            logger.info(f"Starting upgrade for module: {module_name}")
            
            # Get module info
            module_info = self.get_module_info(module_name)
            if not module_info:
                logger.error(f"Module {module_name} not found")
                return False
                
            logger.info(f"Module {module_name} current state: {module_info['state']}")
            
            # If module is installed, upgrade it
            if module_info['state'] == 'installed':
                module_id = module_info['id']
                
                # Mark for upgrade
                self.execute('ir.module.module', 'button_immediate_upgrade', [module_id])
                logger.info(f"Module {module_name} marked for upgrade")
                
                # Wait a bit for the upgrade to process
                time.sleep(2)
                
                # Check new state
                updated_info = self.get_module_info(module_name)
                logger.info(f"Module {module_name} new state: {updated_info['state']}")
                
                return True
            else:
                logger.warning(f"Module {module_name} is not installed (state: {module_info['state']})")
                return False
                
        except Exception as e:
            logger.error(f"Error upgrading module {module_name}: {e}")
            return False
    
    def check_logs(self, limit=50):
        """Check recent logs for errors"""
        try:
            logger.info("Checking recent logs for errors...")
            
            # Get recent log entries
            logs = self.execute('ir.logging', 'search_read',
                              [('level', 'in', ['ERROR', 'CRITICAL'])],
                              {'fields': ['create_date', 'name', 'level', 'message', 'path', 'line'],
                               'order': 'create_date desc',
                               'limit': limit})
            
            if logs:
                logger.warning(f"Found {len(logs)} recent error/critical log entries:")
                for log in logs:
                    logger.warning(f"[{log['create_date']}] {log['level']}: {log['name']} - {log['message']}")
                    if log['path']:
                        logger.warning(f"  Location: {log['path']}:{log['line']}")
            else:
                logger.info("No recent error logs found")
                
            return logs
            
        except Exception as e:
            logger.error(f"Error checking logs: {e}")
            return []
    
    def test_partner_access(self):
        """Test if partner model can be accessed with new fields"""
        try:
            logger.info("Testing partner model access...")
            
            # Try to read a partner with new fields
            partners = self.execute('res.partner', 'search_read',
                                  [('is_company', '=', False)],
                                  {'fields': ['name', 'x_group_id'], 'limit': 1})
            
            if partners:
                partner = partners[0]
                logger.info(f"Successfully accessed partner: {partner['name']}")
                logger.info(f"Group field value: {partner.get('x_group_id', 'Not found')}")
                return True
            else:
                logger.warning("No partners found to test")
                return False
                
        except Exception as e:
            logger.error(f"Error testing partner access: {e}")
            return False
    
    def test_imca_models(self):
        """Test access to IMCA models"""
        model_tests = {
            'x_groups': 'Groups model',
            'x_services': 'Services model', 
            'x_client_documents': 'Client Documents model',
            'x_credentials': 'Credentials model',
            'x_dsc': 'DSC Management model'
        }
        
        results = {}
        
        for model, description in model_tests.items():
            try:
                logger.info(f"Testing {description}...")
                
                # Try to search the model
                records = self.execute(model, 'search_read', [], {'limit': 1})
                results[model] = True
                logger.info(f"✓ {description} accessible")
                
            except Exception as e:
                results[model] = False
                logger.error(f"✗ {description} failed: {e}")
        
        return results

def main():
    """Main execution function"""
    logger.info("=" * 60)
    logger.info("IMCA Modules Upgrade Script Started")
    logger.info("=" * 60)
    
    # Initialize client
    client = OdooXMLRPCClient(URL, DB, USERNAME, PASSWORD)
    
    # Connect to Odoo
    if not client.connect():
        logger.error("Failed to connect to Odoo. Exiting.")
        sys.exit(1)
    
    # Check initial logs
    logger.info("\n" + "=" * 40)
    logger.info("CHECKING INITIAL LOGS")
    logger.info("=" * 40)
    initial_logs = client.check_logs(20)
    
    # Upgrade modules
    logger.info("\n" + "=" * 40)
    logger.info("UPGRADING IMCA MODULES")
    logger.info("=" * 40)
    
    upgrade_results = {}
    for module in IMCA_MODULES:
        upgrade_results[module] = client.upgrade_module(module)
        time.sleep(1)  # Small delay between upgrades
    
    # Wait for upgrades to complete
    logger.info("Waiting 10 seconds for upgrades to complete...")
    time.sleep(10)
    
    # Check logs after upgrade
    logger.info("\n" + "=" * 40)
    logger.info("CHECKING LOGS AFTER UPGRADE")
    logger.info("=" * 40)
    post_upgrade_logs = client.check_logs(30)
    
    # Test model access
    logger.info("\n" + "=" * 40)
    logger.info("TESTING MODEL ACCESS")
    logger.info("=" * 40)
    
    partner_test = client.test_partner_access()
    model_tests = client.test_imca_models()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("UPGRADE SUMMARY")
    logger.info("=" * 60)
    
    logger.info("Module Upgrade Results:")
    for module, success in upgrade_results.items():
        status = "✓ SUCCESS" if success else "✗ FAILED"
        logger.info(f"  {module}: {status}")
    
    logger.info(f"\nPartner Model Test: {'✓ PASSED' if partner_test else '✗ FAILED'}")
    
    logger.info("\nIMCA Model Tests:")
    for model, success in model_tests.items():
        status = "✓ PASSED" if success else "✗ FAILED"
        logger.info(f"  {model}: {status}")
    
    logger.info(f"\nTotal Error Logs Found: {len(post_upgrade_logs)}")
    
    if all(upgrade_results.values()) and partner_test and all(model_tests.values()):
        logger.info("\n🎉 ALL TESTS PASSED! IMCA modules upgraded successfully.")
    else:
        logger.warning("\n⚠️  Some issues detected. Please review the logs above.")
    
    logger.info("\nUpgrade script completed.")

if __name__ == "__main__":
    main()
