#!/usr/bin/env python3
"""
Final Status Check Script
Check all implemented features and provide comprehensive status report
"""

import xmlrpc.client
import time
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

def connect_to_odoo():
    """Connect to Odoo"""
    try:
        logger.info(f"Connecting to Odoo at {URL}")
        common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
        models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
        
        uid = common.authenticate(DB, USERNAME, PASSWORD, {})
        if not uid:
            raise Exception("Authentication failed")
            
        logger.info(f"Successfully connected as user ID: {uid}")
        return common, models, uid
        
    except Exception as e:
        logger.error(f"Connection failed: {e}")
        return None, None, None

def check_task_management_status(models, uid):
    """Check task management status"""
    logger.info("=" * 60)
    logger.info("TASK MANAGEMENT STATUS")
    logger.info("=" * 60)
    
    status = {}
    
    # 1. Check deadline field requirement
    try:
        task_fields = models.execute_kw(
            DB, uid, PASSWORD,
            'project.task', 'fields_get',
            ['deadline'], {'attributes': ['string', 'required', 'type']}
        )
        
        if 'deadline' in task_fields:
            deadline_field = task_fields['deadline']
            is_required = deadline_field.get('required', False)
            status['deadline_required'] = is_required
            logger.info(f"1. ✅ Deadline field required: {is_required}")
            if not is_required:
                logger.info("   🎯 Task Logs module should be activatable now!")
        else:
            status['deadline_required'] = None
            logger.info("1. ❌ Deadline field not found")
    except Exception as e:
        status['deadline_required'] = None
        logger.error(f"1. ❌ Error checking deadline field: {e}")
    
    # 2. Check dashboard functionality
    try:
        dashboard_actions = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.actions.client', 'search_read',
            [[('tag', '=', 'task_billing_dashboard')]],
            {'fields': ['name', 'tag']}
        )
        
        status['dashboard_action'] = len(dashboard_actions) > 0
        if dashboard_actions:
            logger.info(f"2. ✅ Task Billing Dashboard action exists: {dashboard_actions[0]['name']}")
        else:
            logger.info("2. ❌ Task Billing Dashboard action not found")
    except Exception as e:
        status['dashboard_action'] = False
        logger.error(f"2. ❌ Error checking dashboard action: {e}")
    
    return status

def check_credentials_status(models, uid):
    """Check credentials management status"""
    logger.info("=" * 60)
    logger.info("CREDENTIALS MANAGEMENT STATUS")
    logger.info("=" * 60)
    
    status = {}
    
    # 1. Check credential types model
    try:
        credential_types = models.execute_kw(
            DB, uid, PASSWORD,
            'x_credential_types', 'search_count',
            [[]]
        )
        
        status['credential_types_model'] = True
        status['credential_types_count'] = credential_types
        logger.info(f"1. ✅ Credential Types model exists with {credential_types} records")
    except Exception as e:
        status['credential_types_model'] = False
        status['credential_types_count'] = 0
        logger.error(f"1. ❌ Error accessing credential types: {e}")
    
    # 2. Check credential types menu
    try:
        menus = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.ui.menu', 'search_read',
            [[('name', '=', 'Credential Types')]],
            {'fields': ['name', 'parent_id']}
        )
        
        status['credential_types_menu'] = len(menus) > 0
        if menus:
            logger.info(f"2. ✅ Credential Types menu exists under: {menus[0]['parent_id'][1] if menus[0]['parent_id'] else 'Root'}")
        else:
            logger.info("2. ❌ Credential Types menu not found")
    except Exception as e:
        status['credential_types_menu'] = False
        logger.error(f"2. ❌ Error checking credential types menu: {e}")
    
    # 3. Check credentials model fields
    try:
        cred_fields = models.execute_kw(
            DB, uid, PASSWORD,
            'x_credentials', 'fields_get',
            [], {'attributes': ['string', 'type']}
        )
        
        has_type_id = 'x_type_id' in cred_fields
        has_legacy_type = 'x_type' in cred_fields
        
        status['credentials_many2one'] = has_type_id
        status['credentials_legacy'] = has_legacy_type
        
        logger.info(f"3. ✅ Credentials model fields:")
        logger.info(f"   - x_type_id (many2one): {'✅' if has_type_id else '❌'}")
        logger.info(f"   - x_type (legacy): {'✅' if has_legacy_type else '❌'}")
    except Exception as e:
        status['credentials_many2one'] = False
        status['credentials_legacy'] = False
        logger.error(f"3. ❌ Error checking credentials fields: {e}")
    
    return status

def check_partner_integration_status(models, uid):
    """Check partner integration status"""
    logger.info("=" * 60)
    logger.info("PARTNER INTEGRATION STATUS")
    logger.info("=" * 60)
    
    status = {}
    
    # Check partner fields
    try:
        partner_fields = models.execute_kw(
            DB, uid, PASSWORD,
            'res.partner', 'fields_get',
            [], {'attributes': ['string', 'type']}
        )
        
        required_fields = [
            'x_group_id',
            'x_service_ids',
            'x_client_document_ids',
            'x_credential_ids',
            'x_dsc_ids',
            'x_auto_task'
        ]
        
        status['partner_fields'] = {}
        logger.info("1. ✅ Partner integration fields:")
        for field in required_fields:
            exists = field in partner_fields
            status['partner_fields'][field] = exists
            logger.info(f"   - {field}: {'✅' if exists else '❌'}")
    except Exception as e:
        status['partner_fields'] = {}
        logger.error(f"1. ❌ Error checking partner fields: {e}")
    
    return status

def check_services_integration(models, uid):
    """Check services integration"""
    logger.info("=" * 60)
    logger.info("SERVICES INTEGRATION ANALYSIS")
    logger.info("=" * 60)
    
    status = {}
    
    try:
        # Check services model
        services_count = models.execute_kw(
            DB, uid, PASSWORD,
            'x_services', 'search_count',
            [[]]
        )
        
        status['services_count'] = services_count
        logger.info(f"1. ✅ Services model: {services_count} records")
        
        # Check services fields
        services_fields = models.execute_kw(
            DB, uid, PASSWORD,
            'x_services', 'fields_get',
            [], {'attributes': ['string', 'type']}
        )
        
        key_fields = ['x_partner_id', 'x_agreed_fee', 'x_planned_hours', 'x_execute_every']
        status['services_fields'] = {}
        logger.info("2. ✅ Services integration fields:")
        for field in key_fields:
            exists = field in services_fields
            status['services_fields'][field] = exists
            logger.info(f"   - {field}: {'✅' if exists else '❌'}")
            
        # Sample services data
        if services_count > 0:
            sample_services = models.execute_kw(
                DB, uid, PASSWORD,
                'x_services', 'search_read',
                [[]],
                {'fields': ['name', 'x_partner_id', 'x_agreed_fee'], 'limit': 3}
            )
            
            logger.info("3. ✅ Sample services:")
            for service in sample_services:
                partner_name = service['x_partner_id'][1] if service['x_partner_id'] else 'No Client'
                logger.info(f"   - {service['name']}: {partner_name} (Fee: {service['x_agreed_fee']})")
        
    except Exception as e:
        status['services_count'] = 0
        status['services_fields'] = {}
        logger.error(f"❌ Error checking services integration: {e}")
    
    return status

def main():
    """Main execution function"""
    logger.info("=" * 60)
    logger.info("COMPREHENSIVE CA MANAGEMENT SYSTEM STATUS CHECK")
    logger.info("=" * 60)
    
    # Connect to Odoo
    common, models, uid = connect_to_odoo()
    if not models:
        logger.error("Failed to connect to Odoo. Exiting.")
        sys.exit(1)
    
    # Check all components
    task_status = check_task_management_status(models, uid)
    credentials_status = check_credentials_status(models, uid)
    partner_status = check_partner_integration_status(models, uid)
    services_status = check_services_integration(models, uid)
    
    # Final summary
    logger.info("\n" + "=" * 60)
    logger.info("FINAL COMPREHENSIVE SUMMARY")
    logger.info("=" * 60)
    
    logger.info("\n🎯 TASK MANAGEMENT:")
    logger.info(f"   ✅ Deadline field optional: {not task_status.get('deadline_required', True)}")
    logger.info(f"   ✅ Dashboard action exists: {task_status.get('dashboard_action', False)}")
    logger.info(f"   🎯 Task Logs module ready: {not task_status.get('deadline_required', True)}")
    
    logger.info("\n🔐 CREDENTIALS MANAGEMENT:")
    logger.info(f"   ✅ Credential Types model: {credentials_status.get('credential_types_model', False)}")
    logger.info(f"   ✅ Credential Types menu: {credentials_status.get('credential_types_menu', False)}")
    logger.info(f"   ✅ Many2one relationship: {credentials_status.get('credentials_many2one', False)}")
    logger.info(f"   ✅ Legacy field support: {credentials_status.get('credentials_legacy', False)}")
    
    logger.info("\n👥 PARTNER INTEGRATION:")
    partner_fields = partner_status.get('partner_fields', {})
    working_fields = sum(1 for v in partner_fields.values() if v)
    total_fields = len(partner_fields)
    logger.info(f"   ✅ Partner fields: {working_fields}/{total_fields} working")
    
    logger.info("\n🔧 SERVICES INTEGRATION:")
    logger.info(f"   ✅ Services records: {services_status.get('services_count', 0)}")
    services_fields = services_status.get('services_fields', {})
    working_services_fields = sum(1 for v in services_fields.values() if v)
    total_services_fields = len(services_fields)
    logger.info(f"   ✅ Services fields: {working_services_fields}/{total_services_fields} working")
    
    # Overall status
    overall_score = 0
    total_checks = 0
    
    # Task management (2 checks)
    if not task_status.get('deadline_required', True): overall_score += 1
    if task_status.get('dashboard_action', False): overall_score += 1
    total_checks += 2
    
    # Credentials (4 checks)
    if credentials_status.get('credential_types_model', False): overall_score += 1
    if credentials_status.get('credential_types_menu', False): overall_score += 1
    if credentials_status.get('credentials_many2one', False): overall_score += 1
    if credentials_status.get('credentials_legacy', False): overall_score += 1
    total_checks += 4
    
    # Partner integration (1 check)
    if working_fields >= total_fields * 0.8: overall_score += 1  # 80% of fields working
    total_checks += 1
    
    # Services integration (1 check)
    if services_status.get('services_count', 0) > 0: overall_score += 1
    total_checks += 1
    
    logger.info(f"\n🏆 OVERALL STATUS: {overall_score}/{total_checks} ({(overall_score/total_checks)*100:.1f}%)")
    
    if overall_score >= total_checks * 0.8:
        logger.info("🎉 EXCELLENT! CA Management system is working well!")
    elif overall_score >= total_checks * 0.6:
        logger.info("👍 GOOD! Most features are working correctly!")
    else:
        logger.info("⚠️ NEEDS ATTENTION! Some features require fixes!")
    
    logger.info("\n📋 NEXT STEPS:")
    logger.info("1. ✅ Try activating the Task Logs module")
    logger.info("2. ✅ Test Credential Types menu in CA Management")
    logger.info("3. ✅ Check partner forms for all 5 tabs")
    logger.info("4. ✅ Test services integration with clients")
    logger.info("5. ✅ Install demo data module for testing")
    
    logger.info("\nStatus check completed!")

if __name__ == "__main__":
    main()
