<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Demo Individual Clients -->
        <record id="client_rajesh_sharma" model="res.partner">
            <field name="name">Demo Client - <PERSON><PERSON></field>
            <field name="is_company">False</field>
            <field name="customer_rank">1</field>
            <field name="supplier_rank">0</field>
            <field name="email"><EMAIL></field>
            <field name="phone">+91-9876543210</field>
            <field name="mobile">+91-9876543210</field>
            <field name="street">123, MG Road</field>
            <field name="city">Mumbai</field>
            <field name="state_id" ref="base.state_in_mh"/>
            <field name="zip">400001</field>
            <field name="country_id" ref="base.in"/>
            <field name="x_group_id" ref="group_individual_clients"/>
            <field name="x_auto_task">True</field>
            <field name="comment">Individual client with salary income and investments. Requires annual ITR filing and tax planning services.</field>
        </record>

        <record id="client_priya_patel" model="res.partner">
            <field name="name">Demo Client - Priya Patel</field>
            <field name="is_company">False</field>
            <field name="customer_rank">1</field>
            <field name="supplier_rank">0</field>
            <field name="email"><EMAIL></field>
            <field name="phone">+91-9876543211</field>
            <field name="mobile">+91-9876543211</field>
            <field name="street">456, Ring Road</field>
            <field name="city">Ahmedabad</field>
            <field name="state_id" ref="base.state_in_gj"/>
            <field name="zip">380001</field>
            <field name="country_id" ref="base.in"/>
            <field name="x_group_id" ref="group_individual_clients"/>
            <field name="x_auto_task">True</field>
            <field name="comment">Freelance consultant with multiple income sources. Needs quarterly advance tax and annual compliance.</field>
        </record>

        <!-- Demo Small Business Clients -->
        <record id="client_tech_solutions" model="res.partner">
            <field name="name">Demo Client - Tech Solutions Pvt Ltd</field>
            <field name="is_company">True</field>
            <field name="customer_rank">1</field>
            <field name="supplier_rank">0</field>
            <field name="email"><EMAIL></field>
            <field name="phone">+91-9876543212</field>
            <field name="street">789, IT Park</field>
            <field name="city">Bangalore</field>
            <field name="state_id" ref="base.state_in_ka"/>
            <field name="zip">560001</field>
            <field name="country_id" ref="base.in"/>
            <field name="x_group_id" ref="group_small_business"/>
            <field name="x_auto_task">True</field>
            <field name="comment">IT services company with 25 employees. Requires monthly GST, quarterly TDS, and annual compliance.</field>
        </record>

        <record id="client_green_foods" model="res.partner">
            <field name="name">Demo Client - Green Foods Trading Co</field>
            <field name="is_company">True</field>
            <field name="customer_rank">1</field>
            <field name="supplier_rank">0</field>
            <field name="email"><EMAIL></field>
            <field name="phone">+91-9876543213</field>
            <field name="street">321, Market Street</field>
            <field name="city">Delhi</field>
            <field name="state_id" ref="base.state_in_dl"/>
            <field name="zip">110001</field>
            <field name="country_id" ref="base.in"/>
            <field name="x_group_id" ref="group_small_business"/>
            <field name="x_auto_task">True</field>
            <field name="comment">Food trading business with interstate operations. Complex GST compliance and inventory management.</field>
        </record>

        <!-- Demo Corporate Clients -->
        <record id="client_mega_industries" model="res.partner">
            <field name="name">Demo Client - Mega Industries Limited</field>
            <field name="is_company">True</field>
            <field name="customer_rank">1</field>
            <field name="supplier_rank">0</field>
            <field name="email"><EMAIL></field>
            <field name="phone">+91-9876543214</field>
            <field name="street">555, Industrial Area</field>
            <field name="city">Chennai</field>
            <field name="state_id" ref="base.state_in_tn"/>
            <field name="zip">600001</field>
            <field name="country_id" ref="base.in"/>
            <field name="x_group_id" ref="group_corporate_clients"/>
            <field name="x_auto_task">True</field>
            <field name="comment">Large manufacturing company with multiple subsidiaries. Requires comprehensive audit and compliance services.</field>
        </record>

        <!-- Demo Startup Clients -->
        <record id="client_innovate_startup" model="res.partner">
            <field name="name">Demo Client - Innovate Startup Pvt Ltd</field>
            <field name="is_company">True</field>
            <field name="customer_rank">1</field>
            <field name="supplier_rank">0</field>
            <field name="email"><EMAIL></field>
            <field name="phone">+91-9876543215</field>
            <field name="street">101, Startup Hub</field>
            <field name="city">Hyderabad</field>
            <field name="state_id" ref="base.state_in_ap"/>
            <field name="zip">500001</field>
            <field name="country_id" ref="base.in"/>
            <field name="x_group_id" ref="group_startup_clients"/>
            <field name="x_auto_task">True</field>
            <field name="comment">Early-stage startup with funding rounds. Needs startup compliance, ESOP management, and investor reporting.</field>
        </record>

        <!-- Demo NGO Client -->
        <record id="client_helping_hands_ngo" model="res.partner">
            <field name="name">Demo Client - Helping Hands NGO</field>
            <field name="is_company">True</field>
            <field name="customer_rank">1</field>
            <field name="supplier_rank">0</field>
            <field name="email"><EMAIL></field>
            <field name="phone">+91-9876543216</field>
            <field name="street">202, Social Sector</field>
            <field name="city">Pune</field>
            <field name="state_id" ref="base.state_in_mh"/>
            <field name="zip">411001</field>
            <field name="country_id" ref="base.in"/>
            <field name="x_group_id" ref="group_ngo_clients"/>
            <field name="x_auto_task">True</field>
            <field name="comment">Non-profit organization with 12A and 80G registrations. Requires NGO compliance and donor reporting.</field>
        </record>

    </data>
</odoo>
