<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo DSC for Tech Solutions -->
        <record id="dsc_tech_director" model="x_dsc">
            <field name="name">Director DSC - Tech Solutions</field>
            <field name="x_partner_id" ref="client_tech_solutions"/>
            <field name="x_certificate_number">DSC123456789</field>
            <field name="x_issued_date">2023-01-15</field>
            <field name="x_expiry_date">2025-01-14</field>
            <field name="x_issuing_authority">eMudhra</field>
            <field name="x_certificate_type">Class 3 Individual</field>
            <field name="x_status">Active</field>
        </record>
        
        <!-- Demo DSC for Mega Industries -->
        <record id="dsc_mega_director1" model="x_dsc">
            <field name="name">Managing Director DSC - Mega Industries</field>
            <field name="x_partner_id" ref="client_mega_industries"/>
            <field name="x_certificate_number">DSC987654321</field>
            <field name="x_issued_date">2023-03-10</field>
            <field name="x_expiry_date">2025-03-09</field>
            <field name="x_issuing_authority">Sify</field>
            <field name="x_certificate_type">Class 3 Individual</field>
            <field name="x_status">Active</field>
        </record>
        
        <record id="dsc_mega_director2" model="x_dsc">
            <field name="name">Finance Director DSC - Mega Industries</field>
            <field name="x_partner_id" ref="client_mega_industries"/>
            <field name="x_certificate_number">DSC456789123</field>
            <field name="x_issued_date">2023-02-20</field>
            <field name="x_expiry_date">2025-02-19</field>
            <field name="x_issuing_authority">NCode</field>
            <field name="x_certificate_type">Class 3 Individual</field>
            <field name="x_status">Active</field>
        </record>
        
        <!-- Demo DSC for Innovate Startup -->
        <record id="dsc_innovate_founder" model="x_dsc">
            <field name="name">Founder DSC - Innovate Startup</field>
            <field name="x_partner_id" ref="client_innovate_startup"/>
            <field name="x_certificate_number">DSC789123456</field>
            <field name="x_issued_date">2023-06-01</field>
            <field name="x_expiry_date">2025-05-31</field>
            <field name="x_issuing_authority">eMudhra</field>
            <field name="x_certificate_type">Class 3 Individual</field>
            <field name="x_status">Active</field>
        </record>
        
    </data>
</odoo>
