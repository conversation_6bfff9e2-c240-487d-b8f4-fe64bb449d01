<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo Services for <PERSON><PERSON> -->
        <record id="service_rajesh_itr" model="x_services">
            <field name="name">Income Tax Return Filing</field>
            <field name="x_partner_id" ref="client_rajesh_sharma"/>
            <field name="x_agreed_fee">5000</field>
            <field name="x_planned_hours">3</field>
            <field name="x_execute_every">12</field>
            <field name="x_task_description">Prepare and file Income Tax Return for <PERSON><PERSON>. Collect all investment proofs, Form 16, and other income details.</field>
        </record>
        
        <record id="service_rajesh_tax_planning" model="x_services">
            <field name="name">Tax Planning</field>
            <field name="x_partner_id" ref="client_rajesh_sharma"/>
            <field name="x_agreed_fee">3000</field>
            <field name="x_planned_hours">2</field>
            <field name="x_execute_every">12</field>
            <field name="x_task_description">Provide tax planning advice for optimizing investments and reducing tax liability.</field>
        </record>
        
        <!-- Demo Services for <PERSON><PERSON> Patel -->
        <record id="service_priya_advance_tax" model="x_services">
            <field name="name">Quarterly Advance Tax</field>
            <field name="x_partner_id" ref="client_priya_patel"/>
            <field name="x_agreed_fee">2000</field>
            <field name="x_planned_hours">1.5</field>
            <field name="x_execute_every">3</field>
            <field name="x_task_description">Calculate and file quarterly advance tax for Priya Patel based on freelance income.</field>
        </record>
        
        <record id="service_priya_itr" model="x_services">
            <field name="name">Income Tax Return Filing</field>
            <field name="x_partner_id" ref="client_priya_patel"/>
            <field name="x_agreed_fee">7000</field>
            <field name="x_planned_hours">4</field>
            <field name="x_execute_every">12</field>
            <field name="x_task_description">Prepare and file Income Tax Return for Priya Patel. Include all freelance income, expenses, and TDS certificates.</field>
        </record>
        
        <!-- Demo Services for Tech Solutions -->
        <record id="service_tech_gst" model="x_services">
            <field name="name">Monthly GST Filing</field>
            <field name="x_partner_id" ref="client_tech_solutions"/>
            <field name="x_agreed_fee">3500</field>
            <field name="x_planned_hours">2.5</field>
            <field name="x_execute_every">1</field>
            <field name="x_task_description">Prepare and file monthly GSTR-1 and GSTR-3B returns for Tech Solutions Pvt Ltd.</field>
        </record>
        
        <record id="service_tech_tds" model="x_services">
            <field name="name">Quarterly TDS Filing</field>
            <field name="x_partner_id" ref="client_tech_solutions"/>
            <field name="x_agreed_fee">2500</field>
            <field name="x_planned_hours">2</field>
            <field name="x_execute_every">3</field>
            <field name="x_task_description">Prepare and file quarterly TDS returns for Tech Solutions Pvt Ltd.</field>
        </record>
        
        <record id="service_tech_annual" model="x_services">
            <field name="name">Annual Compliance</field>
            <field name="x_partner_id" ref="client_tech_solutions"/>
            <field name="x_agreed_fee">15000</field>
            <field name="x_planned_hours">10</field>
            <field name="x_execute_every">12</field>
            <field name="x_task_description">Complete annual compliance including financial statements, income tax return, and annual GST reconciliation.</field>
        </record>
        
        <!-- Demo Services for Green Foods -->
        <record id="service_green_gst" model="x_services">
            <field name="name">Monthly GST Filing</field>
            <field name="x_partner_id" ref="client_green_foods"/>
            <field name="x_agreed_fee">4000</field>
            <field name="x_planned_hours">3</field>
            <field name="x_execute_every">1</field>
            <field name="x_task_description">Prepare and file monthly GST returns for Green Foods Trading Co with interstate transactions.</field>
        </record>
        
        <record id="service_green_inventory" model="x_services">
            <field name="name">Inventory Management</field>
            <field name="x_partner_id" ref="client_green_foods"/>
            <field name="x_agreed_fee">5000</field>
            <field name="x_planned_hours">4</field>
            <field name="x_execute_every">1</field>
            <field name="x_task_description">Maintain and reconcile inventory records for GST compliance.</field>
        </record>
        
        <!-- Demo Services for Mega Industries -->
        <record id="service_mega_audit" model="x_services">
            <field name="name">Statutory Audit</field>
            <field name="x_partner_id" ref="client_mega_industries"/>
            <field name="x_agreed_fee">50000</field>
            <field name="x_planned_hours">40</field>
            <field name="x_execute_every">12</field>
            <field name="x_task_description">Conduct statutory audit for Mega Industries Limited and its subsidiaries.</field>
        </record>
        
        <record id="service_mega_tax" model="x_services">
            <field name="name">Tax Audit</field>
            <field name="x_partner_id" ref="client_mega_industries"/>
            <field name="x_agreed_fee">30000</field>
            <field name="x_planned_hours">25</field>
            <field name="x_execute_every">12</field>
            <field name="x_task_description">Conduct tax audit and prepare Form 3CD for Mega Industries Limited.</field>
        </record>
        
        <!-- Demo Services for Innovate Startup -->
        <record id="service_innovate_compliance" model="x_services">
            <field name="name">Startup Compliance</field>
            <field name="x_partner_id" ref="client_innovate_startup"/>
            <field name="x_agreed_fee">10000</field>
            <field name="x_planned_hours">8</field>
            <field name="x_execute_every">3</field>
            <field name="x_task_description">Handle startup compliance requirements including DPIIT registration and tax exemptions.</field>
        </record>
        
        <record id="service_innovate_esop" model="x_services">
            <field name="name">ESOP Management</field>
            <field name="x_partner_id" ref="client_innovate_startup"/>
            <field name="x_agreed_fee">15000</field>
            <field name="x_planned_hours">10</field>
            <field name="x_execute_every">6</field>
            <field name="x_task_description">Manage ESOP pool, vesting schedules, and tax implications for employees.</field>
        </record>
        
        <!-- Demo Services for Helping Hands NGO -->
        <record id="service_ngo_compliance" model="x_services">
            <field name="name">NGO Compliance</field>
            <field name="x_partner_id" ref="client_helping_hands_ngo"/>
            <field name="x_agreed_fee">8000</field>
            <field name="x_planned_hours">6</field>
            <field name="x_execute_every">3</field>
            <field name="x_task_description">Ensure compliance with 12A and 80G requirements, FCRA regulations, and other NGO-specific compliances.</field>
        </record>
        
        <record id="service_ngo_donor" model="x_services">
            <field name="name">Donor Reporting</field>
            <field name="x_partner_id" ref="client_helping_hands_ngo"/>
            <field name="x_agreed_fee">5000</field>
            <field name="x_planned_hours">4</field>
            <field name="x_execute_every">3</field>
            <field name="x_task_description">Prepare donor reports, utilization certificates, and financial statements for grant compliance.</field>
        </record>
        
    </data>
</odoo>
