<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo Credentials for <PERSON><PERSON> -->
        <record id="credential_raj<PERSON>_income_tax" model="x_credentials">
            <field name="name">Income Tax Portal</field>
            <field name="x_partner_id" ref="client_rajesh_sharma"/>
            <field name="x_type">Income Tax Portal</field>
            <field name="x_username">**********</field>
            <field name="x_password">Demo@123</field>
            <field name="x_url">https://www.incometax.gov.in/</field>
            <field name="notes">PAN-based login for Income Tax e-filing portal</field>
            <field name="is_active">True</field>
        </record>
        
        <!-- Demo Credentials for Priya Patel -->
        <record id="credential_priya_income_tax" model="x_credentials">
            <field name="name">Income Tax Portal</field>
            <field name="x_partner_id" ref="client_priya_patel"/>
            <field name="x_type">Income Tax Portal</field>
            <field name="x_username">**********</field>
            <field name="x_password">Demo@456</field>
            <field name="x_url">https://www.incometax.gov.in/</field>
            <field name="notes">PAN-based login for Income Tax e-filing portal</field>
            <field name="is_active">True</field>
        </record>

        <record id="credential_priya_gst" model="x_credentials">
            <field name="name">GST Portal</field>
            <field name="x_partner_id" ref="client_priya_patel"/>
            <field name="x_type">GST Portal</field>
            <field name="x_username">27**********1ZA</field>
            <field name="x_password">Demo@789</field>
            <field name="x_url">https://www.gst.gov.in/</field>
            <field name="notes">GSTIN-based login for GST portal</field>
            <field name="is_active">True</field>
        </record>
        
        <!-- Demo Credentials for Tech Solutions -->
        <record id="credential_tech_gst" model="x_credentials">
            <field name="name">GST Portal</field>
            <field name="x_partner_id" ref="client_tech_solutions"/>
            <field name="x_type">GST Portal</field>
            <field name="x_username">29AABCT1234Z1ZP</field>
            <field name="x_password">TechDemo@123</field>
            <field name="x_url">https://www.gst.gov.in/</field>
            <field name="notes">GSTIN-based login for GST portal</field>
            <field name="is_active">True</field>
        </record>
        
        <record id="credential_tech_mca" model="x_credentials">
            <field name="name">MCA Portal</field>
            <field name="x_partner_id" ref="client_tech_solutions"/>
            <field name="x_type">MCA Portal</field>
            <field name="x_username"><EMAIL></field>
            <field name="x_password">MCADemo@456</field>
            <field name="x_url">https://www.mca.gov.in/</field>
            <field name="notes">Email-based login for MCA portal</field>
            <field name="is_active">True</field>
        </record>

        <record id="credential_tech_tds" model="x_credentials">
            <field name="name">TDS Portal</field>
            <field name="x_partner_id" ref="client_tech_solutions"/>
            <field name="x_type">TDS Portal</field>
            <field name="x_username">TANT12345A</field>
            <field name="x_password">TDSDemo@789</field>
            <field name="x_url">https://www.tdscpc.gov.in/</field>
            <field name="notes">TAN-based login for TDS portal</field>
            <field name="is_active">True</field>
        </record>
        
        <!-- Demo Credentials for Green Foods -->
        <record id="credential_green_gst" model="x_credentials">
            <field name="name">GST Portal</field>
            <field name="x_partner_id" ref="client_green_foods"/>
            <field name="x_type">GST Portal</field>
            <field name="x_username">07AADCG9876R1ZQ</field>
            <field name="x_password">GreenDemo@123</field>
            <field name="x_url">https://www.gst.gov.in/</field>
            <field name="notes">GSTIN-based login for GST portal</field>
            <field name="is_active">True</field>
        </record>
        
        <!-- Demo Credentials for Mega Industries -->
        <record id="credential_mega_mca" model="x_credentials">
            <field name="name">MCA Portal</field>
            <field name="x_partner_id" ref="client_mega_industries"/>
            <field name="x_type">MCA Portal</field>
            <field name="x_username"><EMAIL></field>
            <field name="x_password">MegaDemo@123</field>
            <field name="x_url">https://www.mca.gov.in/</field>
            <field name="notes">Email-based login for MCA portal</field>
            <field name="is_active">True</field>
        </record>

        <record id="credential_mega_roc" model="x_credentials">
            <field name="name">ROC Portal</field>
            <field name="x_partner_id" ref="client_mega_industries"/>
            <field name="x_type">ROC Portal</field>
            <field name="x_username">U12345TN2010PLC123456</field>
            <field name="x_password">ROCDemo@456</field>
            <field name="x_url">https://www.mca.gov.in/</field>
            <field name="notes">CIN-based login for ROC filings</field>
            <field name="is_active">True</field>
        </record>

        <!-- Demo Credentials for Innovate Startup -->
        <record id="credential_innovate_startup_india" model="x_credentials">
            <field name="name">Startup India Portal</field>
            <field name="x_partner_id" ref="client_innovate_startup"/>
            <field name="x_type">Startup Portal</field>
            <field name="x_username"><EMAIL></field>
            <field name="x_password">StartupDemo@123</field>
            <field name="x_url">https://www.startupindia.gov.in/</field>
            <field name="notes">Email-based login for Startup India portal</field>
            <field name="is_active">True</field>
        </record>

        <!-- Demo Credentials for Helping Hands NGO -->
        <record id="credential_ngo_fcra" model="x_credentials">
            <field name="name">FCRA Portal</field>
            <field name="x_partner_id" ref="client_helping_hands_ngo"/>
            <field name="x_type">FCRA Portal</field>
            <field name="x_username">FCRA123456</field>
            <field name="x_password">FCRADemo@123</field>
            <field name="x_url">https://fcraonline.nic.in/</field>
            <field name="notes">FCRA registration number based login</field>
            <field name="is_active">True</field>
        </record>
        
    </data>
</odoo>
