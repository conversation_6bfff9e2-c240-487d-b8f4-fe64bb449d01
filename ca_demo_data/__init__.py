# -*- coding: utf-8 -*-

from . import models

def uninstall_hook(cr, registry):
    """
    This hook is called when the module is uninstalled.
    It will delete all demo data created by this module.
    """
    import logging
    _logger = logging.getLogger(__name__)
    _logger.info('Uninstalling CA Demo Data module and removing demo records...')
    
    # The actual deletion is handled by the uninstall_demo_data method in models/models.py
    # which is called via the uninstall button in the module's configuration
