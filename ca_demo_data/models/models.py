# -*- coding: utf-8 -*-

from odoo import models, api, _
import logging

_logger = logging.getLogger(__name__)


class CADemoDataManager(models.TransientModel):
    _name = 'ca.demo.data.manager'
    _description = 'CA Demo Data Manager'

    @api.model
    def uninstall_demo_data(self):
        """
        Remove all demo data created by this module.
        This method is called when the module is uninstalled.
        """
        _logger.info('Starting CA Demo Data cleanup...')
        
        try:
            # Delete demo tasks (created by this module)
            demo_tasks = self.env['project.task'].search([
                ('name', 'ilike', 'Demo:'),
                ('description', 'ilike', 'Created by CA Demo Data module')
            ])
            if demo_tasks:
                demo_tasks.unlink()
                _logger.info(f'Deleted {len(demo_tasks)} demo tasks')
            
            # Delete demo credentials
            demo_credentials = self.env['x_credentials'].search([
                ('name', 'ilike', 'Demo')
            ])
            if demo_credentials:
                demo_credentials.unlink()
                _logger.info(f'Deleted {len(demo_credentials)} demo credentials')
            
            # Delete demo documents
            demo_documents = self.env['x_client_documents'].search([
                ('x_document_name', 'ilike', 'Demo')
            ])
            if demo_documents:
                demo_documents.unlink()
                _logger.info(f'Deleted {len(demo_documents)} demo documents')
            
            # Delete demo DSC records
            demo_dsc = self.env['x_dsc'].search([
                ('name', 'ilike', 'Demo')
            ])
            if demo_dsc:
                demo_dsc.unlink()
                _logger.info(f'Deleted {len(demo_dsc)} demo DSC records')
            
            # Delete demo services
            demo_services = self.env['x_services'].search([
                ('name', 'ilike', 'Demo')
            ])
            if demo_services:
                demo_services.unlink()
                _logger.info(f'Deleted {len(demo_services)} demo services')
            
            # Delete demo clients (partners created by this module)
            demo_clients = self.env['res.partner'].search([
                ('name', 'ilike', 'Demo Client'),
                ('is_company', '=', False)
            ])
            if demo_clients:
                demo_clients.unlink()
                _logger.info(f'Deleted {len(demo_clients)} demo clients')
            
            # Delete demo groups
            demo_groups = self.env['x_groups'].search([
                ('name', 'ilike', 'Demo')
            ])
            if demo_groups:
                demo_groups.unlink()
                _logger.info(f'Deleted {len(demo_groups)} demo groups')
            
            _logger.info('CA Demo Data cleanup completed successfully')
            
        except Exception as e:
            _logger.error(f'Error during CA Demo Data cleanup: {e}')
            raise

    @api.model
    def create_comprehensive_demo_data(self):
        """
        Create comprehensive demo data for CA Management system.
        This method can be called manually to regenerate demo data.
        """
        _logger.info('Creating comprehensive CA Demo Data...')
        
        try:
            # This method can be used to programmatically create demo data
            # if needed beyond the XML data files
            pass
            
        except Exception as e:
            _logger.error(f'Error creating CA Demo Data: {e}')
            raise


class ResPartner(models.Model):
    _inherit = 'res.partner'

    def action_view_ca_services(self):
        """Action to view services for this client"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': f'Services for {self.name}',
            'res_model': 'x_services',
            'view_mode': 'list,form',
            'domain': [('x_partner_id', '=', self.id)],
            'context': {'default_x_partner_id': self.id},
            'target': 'current',
        }

    def action_view_ca_credentials(self):
        """Action to view credentials for this client"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': f'Credentials for {self.name}',
            'res_model': 'x_credentials',
            'view_mode': 'list,form',
            'domain': [('x_partner_id', '=', self.id)],
            'context': {'default_x_partner_id': self.id},
            'target': 'current',
        }

    def action_view_ca_documents(self):
        """Action to view documents for this client"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': f'Documents for {self.name}',
            'res_model': 'x_client_documents',
            'view_mode': 'list,form',
            'domain': [('x_client', '=', self.id)],
            'context': {'default_x_client': self.id},
            'target': 'current',
        }
