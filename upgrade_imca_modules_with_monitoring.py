#!/usr/bin/env python3
"""
Comprehensive IMCA Modules Upgrade Script with Error Monitoring and Resolution
For Odoo 18 - CA Professionals Portal

This script:
1. Connects to Odoo via XML-RPC
2. Upgrades all IMCA modules
3. Monitors logs for errors
4. Attempts to fix common issues
5. Verifies successful implementation
"""

import xmlrpc.client
import time
import sys
import logging
import datetime
import re
import json
import os
from pprint import pformat

# Configure logging
log_file = f"imca_upgrade_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

# IMCA modules to upgrade
IMCA_MODULES = [
    'imca_groups',
    'imca_services', 
    'imca_client_documents',
    'imca_crednetials_manager',
    'imca_dsc_management'
]

# Common error patterns and fixes
ERROR_PATTERNS = {
    r"IndexError: string index out of range": {
        "description": "Domain expression syntax error",
        "fix_function": "fix_domain_syntax_error"
    },
    r"Field (.*?) does not exist": {
        "description": "Missing field in model",
        "fix_function": "fix_missing_field"
    },
    r"Invalid field '(.*)' on model '(.*)'": {
        "description": "Invalid field reference",
        "fix_function": "fix_invalid_field"
    }
}

class OdooUpgrader:
    def __init__(self, url, db, username, password):
        self.url = url
        self.db = db
        self.username = username
        self.password = password
        self.uid = None
        self.common = None
        self.models = None
        self.error_count = 0
        self.fixed_count = 0
        
    def connect(self):
        """Establish connection to Odoo"""
        try:
            logger.info(f"Connecting to Odoo at {self.url}")
            self.common = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/common')
            self.models = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/object')
            
            # Authenticate
            self.uid = self.common.authenticate(self.db, self.username, self.password, {})
            if not self.uid:
                raise Exception("Authentication failed")
                
            logger.info(f"Successfully connected as user ID: {self.uid}")
            return True
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    def execute(self, model, method, *args, **kwargs):
        """Execute a method on Odoo model with error handling"""
        try:
            return self.models.execute_kw(
                self.db, self.uid, self.password,
                model, method, args, kwargs
            )
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error executing {model}.{method}: {error_msg}")
            self.error_count += 1
            
            # Try to fix known errors
            fixed = self.attempt_fix(model, method, args, kwargs, error_msg)
            if fixed:
                self.fixed_count += 1
                # Retry the operation
                try:
                    return self.models.execute_kw(
                        self.db, self.uid, self.password,
                        model, method, args, kwargs
                    )
                except Exception as e2:
                    logger.error(f"Still failed after fix: {e2}")
            
            raise
    
    def attempt_fix(self, model, method, args, kwargs, error_msg):
        """Attempt to fix known errors based on error patterns"""
        for pattern, error_info in ERROR_PATTERNS.items():
            match = re.search(pattern, error_msg)
            if match:
                logger.info(f"Detected error: {error_info['description']}")
                fix_method = getattr(self, error_info['fix_function'], None)
                if fix_method:
                    return fix_method(model, method, args, kwargs, match)
        return False
    
    def fix_domain_syntax_error(self, model, method, args, kwargs, match):
        """Fix domain syntax errors"""
        logger.info("Attempting to fix domain syntax error")
        
        # If this is a search method with domain
        if method in ['search', 'search_read', 'search_count'] and args:
            domain = args[0]
            if isinstance(domain, list):
                # Common issue: Double brackets in domain
                if domain and isinstance(domain[0], list):
                    logger.info(f"Converting domain from {domain} to proper format")
                    # Convert [[...], [...]] to [(...), (...)]
                    fixed_domain = []
                    for criterion in domain:
                        if isinstance(criterion, list) and len(criterion) == 3:
                            fixed_domain.append(tuple(criterion))
                        else:
                            fixed_domain.append(criterion)
                    
                    # Replace the domain in args
                    args_list = list(args)
                    args_list[0] = fixed_domain
                    args = tuple(args_list)
                    logger.info(f"Fixed domain: {fixed_domain}")
                    return True
        
        return False
    
    def fix_missing_field(self, model, method, args, kwargs, match):
        """Fix missing field errors"""
        if match and match.group(1):
            field_name = match.group(1)
            logger.info(f"Field {field_name} is missing in model {model}")
            
            # If this is a search with a missing field, remove that criterion
            if method in ['search', 'search_read', 'search_count'] and args:
                domain = args[0]
                if isinstance(domain, list):
                    new_domain = []
                    for criterion in domain:
                        if isinstance(criterion, (list, tuple)) and len(criterion) == 3 and criterion[0] != field_name:
                            new_domain.append(criterion)
                    
                    # Replace the domain in args
                    args_list = list(args)
                    args_list[0] = new_domain
                    args = tuple(args_list)
                    logger.info(f"Removed missing field from domain: {new_domain}")
                    return True
                    
            # If this is a read with missing fields, remove them
            elif method == 'read' and kwargs and 'fields' in kwargs:
                fields = kwargs['fields']
                if isinstance(fields, list) and field_name in fields:
                    fields.remove(field_name)
                    logger.info(f"Removed missing field from read fields: {fields}")
                    return True
        
        return False
    
    def fix_invalid_field(self, model, method, args, kwargs, match):
        """Fix invalid field references"""
        if match and len(match.groups()) >= 2:
            field_name = match.group(1)
            model_name = match.group(2)
            logger.info(f"Invalid field '{field_name}' on model '{model_name}'")
            
            # If this is in kwargs, remove it
            if kwargs and field_name in kwargs:
                del kwargs[field_name]
                logger.info(f"Removed invalid field from kwargs: {field_name}")
                return True
        
        return False
    
    def get_module_info(self, module_name):
        """Get module information"""
        try:
            module_ids = self.execute('ir.module.module', 'search', 
                                   [('name', '=', module_name)])
            
            if not module_ids:
                logger.warning(f"Module {module_name} not found")
                return None
                
            module_data = self.execute('ir.module.module', 'read', 
                                    module_ids, {'fields': ['name', 'state', 'installed_version']})
            
            return module_data[0] if module_data else None
            
        except Exception as e:
            logger.error(f"Error getting module info for {module_name}: {e}")
            return None
    
    def upgrade_module(self, module_name):
        """Upgrade a specific module"""
        try:
            logger.info(f"Starting upgrade for module: {module_name}")
            
            # Get module info
            module_info = self.get_module_info(module_name)
            if not module_info:
                logger.error(f"Module {module_name} not found")
                return False
                
            logger.info(f"Module {module_name} current state: {module_info['state']}")
            
            # If module is installed, upgrade it
            if module_info['state'] == 'installed':
                module_id = module_info['id']
                
                # Mark for upgrade
                self.execute('ir.module.module', 'button_immediate_upgrade', [module_id])
                logger.info(f"Module {module_name} marked for upgrade")
                
                # Wait a bit for the upgrade to process
                time.sleep(3)
                
                # Check new state
                updated_info = self.get_module_info(module_name)
                if updated_info:
                    logger.info(f"Module {module_name} new state: {updated_info['state']}")
                    return True
                else:
                    logger.warning(f"Couldn't verify new state for {module_name}")
                    return False
            elif module_info['state'] == 'uninstalled':
                module_id = module_info['id']
                
                # Install the module
                self.execute('ir.module.module', 'button_immediate_install', [module_id])
                logger.info(f"Module {module_name} marked for installation")
                
                # Wait a bit for the installation to process
                time.sleep(3)
                
                # Check new state
                updated_info = self.get_module_info(module_name)
                if updated_info:
                    logger.info(f"Module {module_name} new state: {updated_info['state']}")
                    return True
                else:
                    logger.warning(f"Couldn't verify new state for {module_name}")
                    return False
            else:
                logger.warning(f"Module {module_name} is in state: {module_info['state']}")
                return False
                
        except Exception as e:
            logger.error(f"Error upgrading module {module_name}: {e}")
            return False
    
    def check_logs(self, limit=20):
        """Check recent logs for errors"""
        try:
            logger.info("Checking recent logs for errors...")
            
            # Get recent log entries
            log_ids = self.execute('ir.logging', 'search',
                                [('level', 'in', ['ERROR', 'CRITICAL'])],
                                {'order': 'create_date desc', 'limit': limit})
            
            if log_ids:
                logs = self.execute('ir.logging', 'read',
                                 log_ids,
                                 {'fields': ['create_date', 'name', 'level', 'message', 'path', 'line']})
                
                logger.warning(f"Found {len(logs)} recent error/critical log entries:")
                for log in logs:
                    logger.warning(f"[{log['create_date']}] {log['level']}: {log['name']} - {log['message'][:100]}...")
                    if log.get('path'):
                        logger.warning(f"  Location: {log['path']}:{log.get('line', 'N/A')}")
                return logs
            else:
                logger.info("No recent error logs found")
                return []
                
        except Exception as e:
            logger.error(f"Error checking logs: {e}")
            return []
    
    def test_partner_form(self):
        """Test if partner form has our extensions"""
        try:
            logger.info("Testing partner form view extensions...")
            
            # Get partner form views
            view_ids = self.execute('ir.ui.view', 'search',
                                  [('model', '=', 'res.partner'), 
                                   ('type', '=', 'form'),
                                   '|', ('name', 'ilike', 'groups'), 
                                        ('name', 'ilike', 'services')])
            
            if view_ids:
                views = self.execute('ir.ui.view', 'read',
                                   view_ids,
                                   {'fields': ['name', 'model', 'arch']})
                
                logger.info(f"Found {len(views)} partner form extensions:")
                for view in views:
                    logger.info(f"View: {view['name']}")
                    
                    # Check for our extensions in the view arch
                    arch = view.get('arch', '')
                    if 'x_group_id' in arch or 'x_service_ids' in arch:
                        logger.info("✅ Partner form extensions found!")
                        return True
                
                logger.warning("❌ Partner form extensions not found in views")
                return False
            else:
                logger.warning("No partner form extensions found")
                return False
                
        except Exception as e:
            logger.error(f"Error testing partner form: {e}")
            return False

def main():
    """Main execution function"""
    logger.info("=" * 60)
    logger.info("IMCA Modules Upgrade Script Started")
    logger.info("=" * 60)
    
    # Initialize client
    upgrader = OdooUpgrader(URL, DB, USERNAME, PASSWORD)
    
    # Connect to Odoo
    if not upgrader.connect():
        logger.error("Failed to connect to Odoo. Exiting.")
        sys.exit(1)
    
    # Check initial logs
    logger.info("\n" + "=" * 40)
    logger.info("CHECKING INITIAL LOGS")
    logger.info("=" * 40)
    initial_logs = upgrader.check_logs(10)
    
    # Upgrade modules
    logger.info("\n" + "=" * 40)
    logger.info("UPGRADING IMCA MODULES")
    logger.info("=" * 40)
    
    upgrade_results = {}
    for module in IMCA_MODULES:
        upgrade_results[module] = upgrader.upgrade_module(module)
        time.sleep(2)  # Small delay between upgrades
    
    # Wait for upgrades to complete
    logger.info("Waiting 10 seconds for upgrades to complete...")
    time.sleep(10)
    
    # Check logs after upgrade
    logger.info("\n" + "=" * 40)
    logger.info("CHECKING LOGS AFTER UPGRADE")
    logger.info("=" * 40)
    post_upgrade_logs = upgrader.check_logs(15)
    
    # Test partner form
    logger.info("\n" + "=" * 40)
    logger.info("TESTING PARTNER FORM EXTENSIONS")
    logger.info("=" * 40)
    partner_form_ok = upgrader.test_partner_form()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("UPGRADE SUMMARY")
    logger.info("=" * 60)
    
    logger.info("Module Upgrade Results:")
    for module, success in upgrade_results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        logger.info(f"  {module}: {status}")
    
    logger.info(f"\nPartner Form Extensions: {'✅ FOUND' if partner_form_ok else '❌ NOT FOUND'}")
    logger.info(f"Errors Encountered: {upgrader.error_count}")
    logger.info(f"Errors Fixed: {upgrader.fixed_count}")
    logger.info(f"Error Logs Found: {len(post_upgrade_logs)}")
    
    if all(upgrade_results.values()) and partner_form_ok:
        logger.info("\n🎉 ALL TESTS PASSED! IMCA modules upgraded successfully.")
    else:
        logger.warning("\n⚠️  Some issues detected. Please review the logs above.")
    
    logger.info(f"\nLog file saved to: {log_file}")
    logger.info("\nUpgrade script completed.")

if __name__ == "__main__":
    main()
