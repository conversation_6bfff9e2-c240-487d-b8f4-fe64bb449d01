<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Task Checklist Wizard Form View -->
        <record id="view_task_checklist_wizard_form" model="ir.ui.view">
            <field name="name">task.checklist.wizard.form</field>
            <field name="model">task.checklist.wizard</field>
            <field name="arch" type="xml">
                <form string="Apply Checklist Template">
                    <group>
                        <field name="task_id" readonly="1"/>
                        <field name="template_id" required="1"/>
                        <field name="replace_existing"/>
                    </group>
                    <footer>
                        <button name="action_apply_template" type="object" 
                                string="Apply Template" class="oe_highlight"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>
        
        <!-- Action for the wizard -->
        <record id="action_task_checklist_wizard" model="ir.actions.act_window">
            <field name="name">Apply Checklist Template</field>
            <field name="res_model">task.checklist.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
        
    </data>
</odoo>
