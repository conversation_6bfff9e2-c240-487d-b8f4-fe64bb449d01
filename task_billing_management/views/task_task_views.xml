<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Enhanced Task Form View -->
        <record id="view_task_form_enhanced" model="ir.ui.view">
            <field name="name">project.task.form.enhanced</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_form2"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='button_box']" position="inside">
                    <button name="action_view_invoices" type="object"
                            class="oe_stat_button" icon="fa-money"
                            invisible="billing_type == 'non_billable'">
                        <field name="invoice_count" widget="statinfo" string="Invoices"/>
                    </button>
                    <button name="action_create_invoice" type="object"
                            class="oe_stat_button" icon="fa-plus-circle"
                            invisible="billing_type == 'non_billable' or invoice_count > 0"
                            string="Create Invoice"/>
                </xpath>
                
                <xpath expr="//field[@name='name']" position="after">
                    <field name="deadline"/>
                </xpath>
                
                <xpath expr="//field[@name='user_ids']" position="after">
                    <field name="task_priority" widget="priority"/>
                </xpath>
                
                <xpath expr="//field[@name='description']" position="before">
                    <group string="Billing Information">
                        <field name="billing_type" widget="radio" options="{'horizontal': true}"/>
                        <field name="billing_amount" invisible="billing_type == 'non_billable'"/>
                        <field name="currency_id" invisible="billing_type == 'non_billable'"/>
                        <field name="linked_service_id" invisible="billing_type != 'annually_billable'"
                               domain="[('active', '=', True)]"/>
                        <field name="auto_generate_invoice" invisible="billing_type != 'billable'"/>
                    </group>
                    
                    <group string="Time Management">
                        <field name="start_time"/>
                        <field name="end_time"/>
                        <field name="estimated_hours"/>
                        <field name="actual_hours" readonly="1"/>
                    </group>
                    
                    <group string="Communication">
                        <field name="send_assignment_notification"/>
                        <field name="send_completion_notification"/>
                        <field name="customer_notification_email"/>
                    </group>
                </xpath>
                
                <xpath expr="//field[@name='description']" position="after">
                    <notebook>
                        <page string="Checklist" name="checklist">
                            <div class="oe_button_box" style="margin-bottom: 10px;">
                                <button name="%(action_task_checklist_wizard)d" type="action"
                                        string="Apply Template" class="btn-primary"
                                        context="{'default_task_id': id}"/>
                            </div>
                            <field name="checklist_ids" nolabel="1">
                                <list editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="is_completed" widget="boolean_toggle"/>
                                    <field name="assigned_to"/>
                                    <field name="due_date"/>
                                    <field name="priority" widget="priority"/>
                                    <field name="completed_by" readonly="1"/>
                                    <field name="completed_date" readonly="1"/>
                                </list>
                            </field>
                            <div class="oe_clear">
                                <field name="checklist_progress" widget="progressbar" string="Progress"/>
                            </div>
                        </page>
                        <page string="Invoices" name="invoices" invisible="billing_type == 'non_billable'">
                            <field name="invoice_ids" nolabel="1" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="invoice_date"/>
                                    <field name="amount_total"/>
                                    <field name="state"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </xpath>
            </field>
        </record>
        
        <!-- Enhanced Task List View -->
        <record id="view_task_list_enhanced" model="ir.ui.view">
            <field name="name">project.task.list.enhanced</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_tree2"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="billing_type" optional="show"/>
                    <field name="billing_amount" optional="show" sum="Total Amount"/>
                    <field name="deadline" optional="show"/>
                    <field name="checklist_progress" widget="progressbar" optional="hide"/>
                </xpath>

                <xpath expr="//list" position="attributes">
                    <attribute name="decoration-danger">deadline and deadline &lt; current_date and not stage_id.fold</attribute>
                    <attribute name="decoration-warning">deadline and deadline &lt; (current_date + 3) and not stage_id.fold</attribute>
                    <attribute name="decoration-success">stage_id.fold</attribute>
                </xpath>
            </field>
        </record>
        
        <!-- Enhanced Task Kanban View -->
        <record id="view_task_kanban_enhanced" model="ir.ui.view">
            <field name="name">project.task.kanban.enhanced</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_kanban"/>
            <field name="arch" type="xml">
                <!-- Add billing fields to kanban -->
                <xpath expr="//kanban" position="inside">
                    <field name="billing_type"/>
                    <field name="billing_amount"/>
                    <field name="deadline"/>
                    <field name="checklist_ids"/>
                    <field name="checklist_progress"/>
                </xpath>
            </field>
        </record>

        <!-- Task Search View -->
        <record id="view_task_search_enhanced" model="ir.ui.view">
            <field name="name">project.task.search.enhanced</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_search_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="billing_type"/>
                    <field name="billing_amount"/>
                    <field name="linked_service_id"/>
                    <field name="deadline"/>
                    <field name="start_time"/>
                    <field name="end_time"/>
                    <field name="task_priority"/>
                    <field name="estimated_hours"/>
                    <field name="actual_hours"/>
                    <field name="customer_notification_email"/>
                </xpath>

                <xpath expr="//filter[@name='my_tasks']" position="after">
                    <separator/>
                    <filter string="Billable Tasks" name="billable_tasks"
                            domain="[('billing_type', '=', 'billable')]"/>
                    <filter string="Non-Billable Tasks" name="non_billable_tasks"
                            domain="[('billing_type', '=', 'non_billable')]"/>
                    <filter string="Annually Billable Tasks" name="annually_billable_tasks"
                            domain="[('billing_type', '=', 'annually_billable')]"/>
                    <separator/>
                    <filter string="High Priority" name="high_priority_tasks"
                            domain="[('task_priority', 'in', ['2', '3'])]"/>
                    <filter string="Auto Invoice" name="auto_invoice_tasks"
                            domain="[('auto_generate_invoice', '=', True)]"/>
                    <separator/>
                    <filter string="Overdue Tasks" name="overdue_tasks"
                            domain="[('deadline', '&lt;', context_today()), ('stage_id.fold', '=', False)]"/>
                    <filter string="Due Today" name="due_today"
                            domain="[('deadline', '&gt;=', context_today()), ('deadline', '&lt;', (context_today() + 1))]"/>
                    <filter string="Due This Week" name="due_this_week"
                            domain="[('deadline', '&gt;=', context_today()), ('deadline', '&lt;=', (context_today() + 7))]"/>
                    <filter string="Due Next Week" name="due_next_week"
                            domain="[('deadline', '&gt;=', (context_today() + 7)), ('deadline', '&lt;=', (context_today() + 14))]"/>
                    <separator/>
                    <filter string="With Invoices" name="with_invoices"
                            domain="[('invoice_ids', '!=', False)]"/>
                    <filter string="Without Invoices" name="without_invoices"
                            domain="[('billing_type', '!=', 'non_billable'), ('invoice_ids', '=', False)]"/>
                    <separator/>
                    <filter string="Started Tasks" name="started_tasks"
                            domain="[('start_time', '!=', False)]"/>
                    <filter string="Not Started" name="not_started_tasks"
                            domain="[('start_time', '=', False)]"/>
                    <filter string="Completed Tasks" name="completed_tasks"
                            domain="[('stage_id.fold', '=', True)]"/>
                </xpath>

                <xpath expr="//group" position="inside">
                    <filter string="Billing Type" name="group_billing_type"
                            context="{'group_by': 'billing_type'}"/>
                    <filter string="Priority" name="group_priority"
                            context="{'group_by': 'task_priority'}"/>
                    <filter string="Deadline" name="group_deadline"
                            context="{'group_by': 'deadline:week'}"/>
                    <filter string="Start Date" name="group_start_date"
                            context="{'group_by': 'start_time:week'}"/>
                    <filter string="Assigned User" name="group_assigned_user"
                            context="{'group_by': 'user_ids'}"/>
                    <filter string="Service" name="group_service"
                            context="{'group_by': 'linked_service_id'}"/>
                </xpath>
            </field>
        </record>
        
    </data>
</odoo>
