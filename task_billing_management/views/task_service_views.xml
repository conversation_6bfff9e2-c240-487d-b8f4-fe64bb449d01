<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Task Service Form View -->
        <record id="view_task_service_form" model="ir.ui.view">
            <field name="name">task.service.form</field>
            <field name="model">task.service</field>
            <field name="arch" type="xml">
                <form string="Task Service">
                    <header>
                        <button name="action_create_recurring_task" type="object" 
                                string="Create Task" class="oe_highlight"/>
                        <button name="action_generate_annual_invoice" type="object" 
                                string="Generate Invoice" class="btn-secondary"
                                invisible="not product_id"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_tasks" type="object"
                                    class="oe_stat_button" icon="fa-tasks">
                                <field name="task_count" widget="statinfo" string="Tasks"/>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1><field name="name" placeholder="Service Name"/></h1>
                        </div>
                        
                        <group>
                            <group string="Basic Information">
                                <field name="description"/>
                                <field name="service_type" widget="radio"/>
                                <field name="active"/>
                            </group>
                            <group string="Billing Configuration">
                                <field name="annual_amount"/>
                                <field name="currency_id"/>
                                <field name="billing_frequency"/>
                                <field name="product_id"/>
                            </group>
                        </group>
                        
                        <group>
                            <group string="Billing Dates">
                                <field name="last_billing_date" readonly="1"/>
                                <field name="next_billing_date"/>
                            </group>
                            <group string="Analytics">
                                <field name="total_billed_amount" readonly="1"/>
                                <field name="task_count" readonly="1"/>
                            </group>
                        </group>
                        
                        <group string="Automation">
                            <field name="auto_create_tasks"/>
                            <field name="task_template_id" invisible="not auto_create_tasks"
                                   domain="[('active', '=', True)]"/>
                        </group>
                        
                        <notebook>
                            <page string="Related Tasks" name="tasks">
                                <field name="task_ids" nolabel="1" readonly="1">
                                    <list>
                                        <field name="name"/>
                                        <field name="user_ids" widget="many2many_tags"/>
                                        <field name="stage_id"/>
                                        <field name="deadline"/>
                                        <field name="billing_amount"/>
                                    </list>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>
        
        <!-- Task Service List View -->
        <record id="view_task_service_list" model="ir.ui.view">
            <field name="name">task.service.list</field>
            <field name="model">task.service</field>
            <field name="arch" type="xml">
                <list string="Task Services">
                    <field name="name"/>
                    <field name="service_type"/>
                    <field name="annual_amount" sum="Total Amount"/>
                    <field name="billing_frequency"/>
                    <field name="next_billing_date"/>
                    <field name="task_count"/>
                    <field name="total_billed_amount" sum="Total Billed"/>
                    <field name="active"/>
                </list>
            </field>
        </record>
        
        <!-- Task Service Kanban View -->
        <record id="view_task_service_kanban" model="ir.ui.view">
            <field name="name">task.service.kanban</field>
            <field name="model">task.service</field>
            <field name="arch" type="xml">
                <kanban string="Task Services">
                    <field name="name"/>
                    <field name="annual_amount"/>
                    <field name="task_count"/>
                    <field name="service_type"/>
                    <field name="next_billing_date"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="oe_kanban_content">
                                    <div class="o_kanban_record_top">
                                        <div class="o_kanban_record_headings">
                                            <strong class="o_kanban_record_title">
                                                <field name="name"/>
                                            </strong>
                                        </div>
                                        <span class="badge badge-pill" 
                                              t-attf-class="badge-#{record.service_type.raw_value == 'default' ? 'info' : 'secondary'}">
                                            <field name="service_type"/>
                                        </span>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div class="o_kanban_tags_section">
                                            <span class="o_kanban_tag">
                                                <i class="fa fa-money"/> <field name="annual_amount"/>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="o_kanban_record_bottom">
                                        <div class="oe_kanban_bottom_left">
                                            <span><i class="fa fa-tasks"/> <field name="task_count"/> tasks</span>
                                        </div>
                                        <div class="oe_kanban_bottom_right">
                                            <span t-if="record.next_billing_date.raw_value">
                                                Next: <field name="next_billing_date"/>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        
        <!-- Task Service Search View -->
        <record id="view_task_service_search" model="ir.ui.view">
            <field name="name">task.service.search</field>
            <field name="model">task.service</field>
            <field name="arch" type="xml">
                <search string="Task Services">
                    <field name="name"/>
                    <field name="description"/>
                    <field name="service_type"/>
                    <field name="billing_frequency"/>
                    <field name="annual_amount"/>
                    <field name="product_id"/>
                    <field name="next_billing_date"/>
                    <field name="last_billing_date"/>
                    <field name="task_template_id"/>

                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <filter string="Default Services" name="default_services"
                            domain="[('service_type', '=', 'default')]"/>
                    <filter string="Changeable Services" name="changeable_services"
                            domain="[('service_type', '=', 'changeable')]"/>
                    <separator/>
                    <filter string="Auto Create Tasks" name="auto_create_tasks"
                            domain="[('auto_create_tasks', '=', True)]"/>
                    <filter string="With Product" name="with_product"
                            domain="[('product_id', '!=', False)]"/>
                    <filter string="Without Product" name="without_product"
                            domain="[('product_id', '=', False)]"/>
                    <separator/>
                    <filter string="Due for Billing" name="due_billing"
                            domain="[('next_billing_date', '&lt;=', context_today())]"/>
                    <filter string="Due This Week" name="due_this_week"
                            domain="[('next_billing_date', '&gt;=', context_today()), ('next_billing_date', '&lt;=', (context_today() + 7))]"/>
                    <filter string="Due Next Month" name="due_next_month"
                            domain="[('next_billing_date', '&gt;=', (context_today() + 30)), ('next_billing_date', '&lt;=', (context_today() + 60))]"/>
                    <separator/>
                    <filter string="High Value (>10000)" name="high_value"
                            domain="[('annual_amount', '&gt;', 10000)]"/>
                    <filter string="Medium Value (5000-10000)" name="medium_value"
                            domain="[('annual_amount', '&gt;=', 5000), ('annual_amount', '&lt;=', 10000)]"/>
                    <filter string="Low Value (&lt;5000)" name="low_value"
                            domain="[('annual_amount', '&lt;', 5000)]"/>
                    <separator/>
                    <filter string="Monthly Billing" name="monthly_billing"
                            domain="[('billing_frequency', '=', 'monthly')]"/>
                    <filter string="Quarterly Billing" name="quarterly_billing"
                            domain="[('billing_frequency', '=', 'quarterly')]"/>
                    <filter string="Annual Billing" name="annual_billing"
                            domain="[('billing_frequency', '=', 'annually')]"/>

                    <group expand="0" string="Group By">
                        <filter string="Service Type" name="group_service_type"
                                context="{'group_by': 'service_type'}"/>
                        <filter string="Billing Frequency" name="group_billing_frequency"
                                context="{'group_by': 'billing_frequency'}"/>
                        <filter string="Next Billing Date" name="group_next_billing"
                                context="{'group_by': 'next_billing_date:month'}"/>
                        <filter string="Product" name="group_product"
                                context="{'group_by': 'product_id'}"/>
                        <filter string="Active Status" name="group_active"
                                context="{'group_by': 'active'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Actions -->
        <record id="action_task_service" model="ir.actions.act_window">
            <field name="name">Task Services</field>
            <field name="res_model">task.service</field>
            <field name="view_mode">kanban,list,form</field>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first task service!
                </p>
                <p>
                    Task services are used for annually billable tasks. Define the service name,
                    annual amount, and billing frequency to automate your recurring billing.
                </p>
            </field>
        </record>
        
    </data>
</odoo>
