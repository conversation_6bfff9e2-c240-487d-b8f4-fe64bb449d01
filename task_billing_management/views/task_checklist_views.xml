<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Task Checklist Template Form View -->
        <record id="view_task_checklist_template_form" model="ir.ui.view">
            <field name="name">task.checklist.template.form</field>
            <field name="model">task.checklist.template</field>
            <field name="arch" type="xml">
                <form string="Checklist Template">
                    <sheet>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1><field name="name" placeholder="Template Name"/></h1>
                        </div>
                        
                        <group>
                            <field name="description"/>
                            <field name="active"/>
                        </group>
                        
                        <notebook>
                            <page string="Checklist Items" name="items">
                                <field name="checklist_item_ids" nolabel="1">
                                    <list editable="bottom">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="description"/>
                                        <field name="priority" widget="priority"/>
                                        <field name="estimated_hours"/>
                                    </list>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Task Checklist Template List View -->
        <record id="view_task_checklist_template_list" model="ir.ui.view">
            <field name="name">task.checklist.template.list</field>
            <field name="model">task.checklist.template</field>
            <field name="arch" type="xml">
                <list string="Checklist Templates">
                    <field name="name"/>
                    <field name="description"/>
                    <field name="active"/>
                </list>
            </field>
        </record>
        
        <!-- Task Checklist Template Search View -->
        <record id="view_task_checklist_template_search" model="ir.ui.view">
            <field name="name">task.checklist.template.search</field>
            <field name="model">task.checklist.template</field>
            <field name="arch" type="xml">
                <search string="Checklist Templates">
                    <field name="name"/>
                    <field name="description"/>
                    <field name="checklist_item_ids" string="Template Items"/>

                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <filter string="With Items" name="with_items"
                            domain="[('checklist_item_ids', '!=', False)]"/>
                    <filter string="Empty Templates" name="empty_templates"
                            domain="[('checklist_item_ids', '=', False)]"/>
                    <separator/>
                    <filter string="High Priority Items" name="high_priority_items"
                            domain="[('checklist_item_ids.priority', 'in', ['2', '3'])]"/>
                    <filter string="With Estimated Hours" name="with_estimated_hours"
                            domain="[('checklist_item_ids.estimated_hours', '>', 0)]"/>

                    <group expand="0" string="Group By">
                        <filter string="Active Status" name="group_active" context="{'group_by': 'active'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Task Checklist Form View -->
        <record id="view_task_checklist_form" model="ir.ui.view">
            <field name="name">task.checklist.form</field>
            <field name="model">task.checklist</field>
            <field name="arch" type="xml">
                <form string="Checklist Item">
                    <header>
                        <button name="action_toggle_completion" type="object" 
                                string="Mark Complete" class="oe_highlight"
                                invisible="is_completed"/>
                        <button name="action_toggle_completion" type="object" 
                                string="Mark Incomplete" class="btn-secondary"
                                invisible="not is_completed"/>
                        <field name="is_completed" widget="statusbar" 
                               statusbar_visible="0,1" options="{'clickable': '1'}"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1><field name="name" placeholder="Checklist Item"/></h1>
                        </div>
                        
                        <group>
                            <group string="Basic Information">
                                <field name="task_id"/>
                                <field name="sequence"/>
                                <field name="priority" widget="priority"/>
                                <field name="assigned_to"/>
                                <field name="due_date"/>
                            </group>
                            <group string="Progress">
                                <field name="estimated_hours"/>
                                <field name="actual_hours"/>
                                <field name="completed_by" readonly="1"/>
                                <field name="completed_date" readonly="1"/>
                            </group>
                        </group>
                        
                        <group string="Description">
                            <field name="description" nolabel="1"/>
                        </group>
                        
                        <group string="Notes">
                            <field name="notes" nolabel="1"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Task Checklist List View -->
        <record id="view_task_checklist_list" model="ir.ui.view">
            <field name="name">task.checklist.list</field>
            <field name="model">task.checklist</field>
            <field name="arch" type="xml">
                <list string="Task Checklist" editable="bottom">
                    <field name="sequence" widget="handle"/>
                    <field name="task_id"/>
                    <field name="name"/>
                    <field name="is_completed" widget="boolean_toggle"/>
                    <field name="assigned_to"/>
                    <field name="due_date"/>
                    <field name="priority" widget="priority"/>
                    <field name="completed_by" readonly="1"/>
                    <field name="completed_date" readonly="1"/>
                </list>
            </field>
        </record>
        
        <!-- Task Checklist Kanban View -->
        <record id="view_task_checklist_kanban" model="ir.ui.view">
            <field name="name">task.checklist.kanban</field>
            <field name="model">task.checklist</field>
            <field name="arch" type="xml">
                <kanban string="Task Checklist" default_group_by="is_completed">
                    <field name="name"/>
                    <field name="task_id"/>
                    <field name="is_completed"/>
                    <field name="assigned_to"/>
                    <field name="due_date"/>
                    <field name="priority"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="oe_kanban_content">
                                    <div class="o_kanban_record_top">
                                        <div class="o_kanban_record_headings">
                                            <strong class="o_kanban_record_title">
                                                <field name="name"/>
                                            </strong>
                                        </div>
                                        <span class="badge badge-pill" 
                                              t-attf-class="badge-#{record.priority.raw_value == '3' ? 'danger' : record.priority.raw_value == '2' ? 'warning' : 'secondary'}">
                                            Priority: <field name="priority"/>
                                        </span>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div>Task: <field name="task_id"/></div>
                                        <div t-if="record.assigned_to.raw_value">
                                            Assigned to: <field name="assigned_to"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_record_bottom">
                                        <div class="oe_kanban_bottom_left">
                                            <span t-if="record.due_date.raw_value">
                                                Due: <field name="due_date"/>
                                            </span>
                                        </div>
                                        <div class="oe_kanban_bottom_right">
                                            <button name="action_toggle_completion" type="object" 
                                                    class="btn btn-sm btn-primary"
                                                    t-if="!record.is_completed.raw_value">
                                                Complete
                                            </button>
                                            <span class="badge badge-success" 
                                                  t-if="record.is_completed.raw_value">
                                                ✓ Done
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        
        <!-- Task Checklist Search View -->
        <record id="view_task_checklist_search" model="ir.ui.view">
            <field name="name">task.checklist.search</field>
            <field name="model">task.checklist</field>
            <field name="arch" type="xml">
                <search string="Task Checklist">
                    <field name="name"/>
                    <field name="description"/>
                    <field name="task_id"/>
                    <field name="assigned_to"/>
                    <field name="due_date"/>
                    <field name="priority"/>
                    <field name="estimated_hours"/>
                    <field name="actual_hours"/>
                    <field name="completed_by"/>
                    <field name="notes"/>

                    <filter string="Completed" name="completed" domain="[('is_completed', '=', True)]"/>
                    <filter string="Pending" name="pending" domain="[('is_completed', '=', False)]"/>
                    <separator/>
                    <filter string="My Items" name="my_items" domain="[('assigned_to', '=', uid)]"/>
                    <filter string="Unassigned" name="unassigned" domain="[('assigned_to', '=', False)]"/>
                    <separator/>
                    <filter string="High Priority" name="high_priority"
                            domain="[('priority', 'in', ['2', '3'])]"/>
                    <filter string="Critical Priority" name="critical_priority"
                            domain="[('priority', '=', '3')]"/>
                    <separator/>
                    <filter string="Overdue" name="overdue"
                            domain="[('due_date', '&lt;', context_today()), ('is_completed', '=', False)]"/>
                    <filter string="Due Today" name="due_today"
                            domain="[('due_date', '=', context_today()), ('is_completed', '=', False)]"/>
                    <filter string="Due This Week" name="due_this_week"
                            domain="[('due_date', '&gt;=', context_today()), ('due_date', '&lt;=', (context_today() + 7)), ('is_completed', '=', False)]"/>
                    <filter string="Due Next Week" name="due_next_week"
                            domain="[('due_date', '&gt;=', (context_today() + 7)), ('due_date', '&lt;=', (context_today() + 14)), ('is_completed', '=', False)]"/>
                    <separator/>
                    <filter string="With Estimated Hours" name="with_estimated_hours"
                            domain="[('estimated_hours', '&gt;', 0)]"/>
                    <filter string="With Actual Hours" name="with_actual_hours"
                            domain="[('actual_hours', '&gt;', 0)]"/>
                    <filter string="Over Estimated" name="over_estimated"
                            domain="[('actual_hours', '&gt;', 0), ('estimated_hours', '&gt;', 0), ('actual_hours', '&gt;', 'estimated_hours')]"/>
                    <separator/>
                    <filter string="With Notes" name="with_notes"
                            domain="[('notes', '!=', False)]"/>

                    <group expand="0" string="Group By">
                        <filter string="Task" name="group_task" context="{'group_by': 'task_id'}"/>
                        <filter string="Assigned To" name="group_assigned" context="{'group_by': 'assigned_to'}"/>
                        <filter string="Completion Status" name="group_completion" context="{'group_by': 'is_completed'}"/>
                        <filter string="Priority" name="group_priority" context="{'group_by': 'priority'}"/>
                        <filter string="Due Date" name="group_due_date" context="{'group_by': 'due_date:week'}"/>
                        <filter string="Completed By" name="group_completed_by" context="{'group_by': 'completed_by'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Actions -->
        <record id="action_task_checklist_template" model="ir.actions.act_window">
            <field name="name">Checklist Templates</field>
            <field name="res_model">task.checklist.template</field>
            <field name="view_mode">list,form</field>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first checklist template!
                </p>
                <p>
                    Checklist templates help you standardize task workflows by defining
                    common checklist items that can be applied to multiple tasks.
                </p>
            </field>
        </record>

        <record id="action_task_checklist" model="ir.actions.act_window">
            <field name="name">Task Checklist</field>
            <field name="res_model">task.checklist</field>
            <field name="view_mode">kanban,list,form</field>
            <field name="context">{'search_default_pending': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No checklist items found!
                </p>
                <p>
                    Checklist items are created within tasks to track detailed progress
                    and ensure all necessary steps are completed.
                </p>
            </field>
        </record>
        
    </data>
</odoo>
