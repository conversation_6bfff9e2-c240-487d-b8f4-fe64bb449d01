<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Main Menu -->
        <menuitem id="menu_task_billing_root" 
                  name="Task Billing" 
                  sequence="15"
                  web_icon="task_billing_management,static/description/icon.png"/>
        
        <!-- Dashboard Menu -->
        <menuitem id="menu_task_billing_dashboard" 
                  name="Dashboard" 
                  parent="menu_task_billing_root" 
                  action="action_task_billing_dashboard_client"
                  sequence="1"/>
        
        <!-- Tasks Menu -->
        <menuitem id="menu_task_billing_tasks" 
                  name="Tasks" 
                  parent="menu_task_billing_root" 
                  sequence="10"/>
        
        <menuitem id="menu_task_billing_all_tasks" 
                  name="All Tasks" 
                  parent="menu_task_billing_tasks" 
                  action="project.act_project_project_2_project_task_all"
                  sequence="1"/>
        
        <menuitem id="menu_task_billing_my_tasks" 
                  name="My Tasks" 
                  parent="menu_task_billing_tasks" 
                  action="project.action_view_my_task"
                  sequence="2"/>
        
        <menuitem id="menu_task_billing_billable_tasks" 
                  name="Billable Tasks" 
                  parent="menu_task_billing_tasks" 
                  action="project.act_project_project_2_project_task_all"
                  sequence="3"/>
        
        <menuitem id="menu_task_billing_overdue_tasks" 
                  name="Overdue Tasks" 
                  parent="menu_task_billing_tasks" 
                  action="project.act_project_project_2_project_task_all"
                  sequence="4"/>
        
        <!-- Services Menu -->
        <menuitem id="menu_task_billing_services" 
                  name="Services" 
                  parent="menu_task_billing_root" 
                  action="action_task_service"
                  sequence="20"/>
        
        <!-- Checklist Menu -->
        <menuitem id="menu_task_billing_checklist" 
                  name="Checklist" 
                  parent="menu_task_billing_root" 
                  sequence="30"/>
        
        <menuitem id="menu_task_billing_checklist_items" 
                  name="Checklist Items" 
                  parent="menu_task_billing_checklist" 
                  action="action_task_checklist"
                  sequence="1"/>
        
        <menuitem id="menu_task_billing_checklist_templates" 
                  name="Checklist Templates" 
                  parent="menu_task_billing_checklist" 
                  action="action_task_checklist_template"
                  sequence="2"/>
        
        <!-- Configuration Menu -->
        <menuitem id="menu_task_billing_config" 
                  name="Configuration" 
                  parent="menu_task_billing_root" 
                  sequence="90"
                  groups="task_billing_management.group_task_billing_admin"/>
        
        <menuitem id="menu_task_billing_config_services" 
                  name="Services Configuration" 
                  parent="menu_task_billing_config" 
                  action="action_task_service"
                  sequence="1"/>
        
        <menuitem id="menu_task_billing_config_templates" 
                  name="Checklist Templates" 
                  parent="menu_task_billing_config" 
                  action="action_task_checklist_template"
                  sequence="2"/>
        
        <!-- Update existing task menu items with filters -->
        <record id="action_billable_tasks" model="ir.actions.act_window">
            <field name="name">Billable Tasks</field>
            <field name="res_model">project.task</field>
            <field name="view_mode">kanban,list,form,calendar</field>
            <field name="context">{'search_default_billable_tasks': 1}</field>
            <field name="domain">[('billing_type', '=', 'billable')]</field>
        </record>

        <record id="action_overdue_tasks" model="ir.actions.act_window">
            <field name="name">Overdue Tasks</field>
            <field name="res_model">project.task</field>
            <field name="view_mode">kanban,list,form,calendar</field>
            <field name="context">{'search_default_overdue_tasks': 1}</field>
            <field name="domain">[('deadline', '&lt;', context_today()), ('stage_id.fold', '=', False)]</field>
        </record>
        
        <!-- Update menu items with correct actions -->
        <record id="menu_task_billing_billable_tasks" model="ir.ui.menu">
            <field name="action" ref="action_billable_tasks"/>
        </record>
        
        <record id="menu_task_billing_overdue_tasks" model="ir.ui.menu">
            <field name="action" ref="action_overdue_tasks"/>
        </record>
        
    </data>
</odoo>
