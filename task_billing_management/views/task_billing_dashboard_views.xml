<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Task Billing Dashboard -->
        <record id="view_task_billing_dashboard" model="ir.ui.view">
            <field name="name">task.billing.dashboard</field>
            <field name="model">project.task</field>
            <field name="arch" type="xml">
                <form string="Task Billing Dashboard" create="false" edit="false" delete="false">
                    <sheet>
                        <div class="o_dashboard">
                            <h1 class="o_dashboard_title">Task Management &amp; Billing Dashboard</h1>
                            
                            <!-- KPI Cards -->
                            <div class="row o_dashboard_kpi">
                                <div class="col-lg-3 col-md-6 col-sm-12">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 class="card-title">Total Tasks</h4>
                                                    <h2 class="mb-0" id="total_tasks">0</h2>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fa fa-tasks fa-3x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-lg-3 col-md-6 col-sm-12">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 class="card-title">Billable Tasks</h4>
                                                    <h2 class="mb-0" id="billable_tasks">0</h2>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fa fa-money fa-3x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-lg-3 col-md-6 col-sm-12">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 class="card-title">Overdue Tasks</h4>
                                                    <h2 class="mb-0" id="overdue_tasks">0</h2>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fa fa-exclamation-triangle fa-3x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-lg-3 col-md-6 col-sm-12">
                                    <div class="card bg-info text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 class="card-title">Total Revenue</h4>
                                                    <h2 class="mb-0" id="total_revenue">$0</h2>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fa fa-dollar fa-3x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Charts Row -->
                            <div class="row mt-4">
                                <div class="col-lg-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title">Tasks by Billing Type</h5>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="billing_type_chart" width="400" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-lg-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title">Task Completion Status</h5>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="completion_chart" width="400" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Quick Actions -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title">Quick Actions</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <button class="btn btn-primary btn-block" 
                                                            onclick="window.location.href='/web#action=project.act_project_project_2_project_task_all&amp;view_type=form'">
                                                        <i class="fa fa-plus"></i> Create Task
                                                    </button>
                                                </div>
                                                <div class="col-md-3">
                                                    <button class="btn btn-success btn-block" 
                                                            onclick="window.location.href='/web#action=task_billing_management.action_task_service&amp;view_type=form'">
                                                        <i class="fa fa-cog"></i> Manage Services
                                                    </button>
                                                </div>
                                                <div class="col-md-3">
                                                    <button class="btn btn-info btn-block" 
                                                            onclick="window.location.href='/web#action=project.act_project_project_2_project_task_all&amp;search_default_billable_tasks=1'">
                                                        <i class="fa fa-money"></i> Billable Tasks
                                                    </button>
                                                </div>
                                                <div class="col-md-3">
                                                    <button class="btn btn-warning btn-block" 
                                                            onclick="window.location.href='/web#action=project.act_project_project_2_project_task_all&amp;search_default_overdue_tasks=1'">
                                                        <i class="fa fa-clock-o"></i> Overdue Tasks
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Recent Tasks Table -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title">Recent Tasks</h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="recent_tasks_table">
                                                <!-- Table will be populated by JavaScript -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Dashboard Action -->
        <record id="action_task_billing_dashboard" model="ir.actions.act_window">
            <field name="name">Task Billing Dashboard</field>
            <field name="res_model">project.task</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_task_billing_dashboard"/>
            <field name="target">current</field>
            <field name="context">{}</field>
        </record>
        
        <!-- Dashboard Client Action -->
        <record id="action_task_billing_dashboard_client" model="ir.actions.client">
            <field name="name">Task Billing Dashboard</field>
            <field name="tag">task_billing_dashboard</field>
        </record>
        
    </data>
</odoo>
