<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo Task Services -->
        <record id="demo_service_web_development" model="task.service">
            <field name="name">Web Development Service</field>
            <field name="description">Annual web development and maintenance service</field>
            <field name="annual_amount">12000.00</field>
            <field name="service_type">changeable</field>
            <field name="billing_frequency">annually</field>
            <field name="next_billing_date" eval="(datetime.date.today() + datetime.timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="auto_create_tasks">True</field>
            <field name="product_id" ref="product_task_service"/>
        </record>
        
        <record id="demo_service_consulting" model="task.service">
            <field name="name">IT Consulting Service</field>
            <field name="description">Monthly IT consulting and support service</field>
            <field name="annual_amount">6000.00</field>
            <field name="service_type">default</field>
            <field name="billing_frequency">monthly</field>
            <field name="next_billing_date" eval="(datetime.date.today() + datetime.timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="auto_create_tasks">False</field>
            <field name="product_id" ref="product_task_service"/>
        </record>
        
        <record id="demo_service_maintenance" model="task.service">
            <field name="name">System Maintenance</field>
            <field name="description">Quarterly system maintenance and updates</field>
            <field name="annual_amount">3600.00</field>
            <field name="service_type">default</field>
            <field name="billing_frequency">quarterly</field>
            <field name="next_billing_date" eval="(datetime.date.today() + datetime.timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="auto_create_tasks">True</field>
            <field name="product_id" ref="product_task_service"/>
        </record>
        
        <!-- Demo Projects (if needed) -->
        <record id="demo_project_web" model="project.project">
            <field name="name">Web Development Project</field>
            <field name="description">Demo project for web development tasks</field>
        </record>
        
        <record id="demo_project_consulting" model="project.project">
            <field name="name">IT Consulting Project</field>
            <field name="description">Demo project for consulting tasks</field>
        </record>
        
        <!-- Demo Tasks -->
        <record id="demo_task_billable_1" model="project.task">
            <field name="name">Website Homepage Redesign</field>
            <field name="description">Redesign the company homepage with modern UI/UX</field>
            <field name="project_id" ref="demo_project_web"/>
            <field name="billing_type">billable</field>
            <field name="billing_amount">2500.00</field>
            <field name="deadline" eval="(datetime.datetime.now() + datetime.timedelta(days=14)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="start_time" eval="datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="estimated_hours">40</field>
            <field name="task_priority">2</field>
            <field name="auto_generate_invoice">True</field>
            <field name="send_assignment_notification">True</field>
            <field name="send_completion_notification">True</field>
        </record>
        
        <record id="demo_task_annually_billable_1" model="project.task">
            <field name="name">Annual Web Maintenance - Q1</field>
            <field name="description">Quarterly web maintenance and updates</field>
            <field name="project_id" ref="demo_project_web"/>
            <field name="billing_type">annually_billable</field>
            <field name="linked_service_id" ref="demo_service_web_development"/>
            <field name="billing_amount">3000.00</field>
            <field name="deadline" eval="(datetime.datetime.now() + datetime.timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="start_time" eval="datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="estimated_hours">20</field>
            <field name="task_priority">1</field>
            <field name="send_assignment_notification">True</field>
            <field name="send_completion_notification">True</field>
        </record>
        
        <record id="demo_task_non_billable_1" model="project.task">
            <field name="name">Internal Team Meeting</field>
            <field name="description">Weekly team sync meeting</field>
            <field name="project_id" ref="demo_project_consulting"/>
            <field name="billing_type">non_billable</field>
            <field name="deadline" eval="(datetime.datetime.now() + datetime.timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="start_time" eval="datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="estimated_hours">2</field>
            <field name="task_priority">1</field>
            <field name="send_assignment_notification">False</field>
            <field name="send_completion_notification">False</field>
        </record>
        
        <record id="demo_task_billable_2" model="project.task">
            <field name="name">Database Optimization</field>
            <field name="description">Optimize database queries and improve performance</field>
            <field name="project_id" ref="demo_project_consulting"/>
            <field name="billing_type">billable</field>
            <field name="billing_amount">1800.00</field>
            <field name="deadline" eval="(datetime.datetime.now() + datetime.timedelta(days=21)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="start_time" eval="datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="estimated_hours">30</field>
            <field name="task_priority">3</field>
            <field name="auto_generate_invoice">False</field>
            <field name="send_assignment_notification">True</field>
            <field name="send_completion_notification">True</field>
        </record>
        
        <!-- Demo Checklist Items -->
        <record id="demo_checklist_1" model="task.checklist">
            <field name="task_id" ref="demo_task_billable_1"/>
            <field name="sequence">10</field>
            <field name="name">Analyze current homepage design</field>
            <field name="description">Review existing design and identify improvement areas</field>
            <field name="priority">2</field>
            <field name="estimated_hours">4</field>
            <field name="due_date" eval="(datetime.date.today() + datetime.timedelta(days=2)).strftime('%Y-%m-%d')"/>
        </record>
        
        <record id="demo_checklist_2" model="task.checklist">
            <field name="task_id" ref="demo_task_billable_1"/>
            <field name="sequence">20</field>
            <field name="name">Create wireframes</field>
            <field name="description">Design wireframes for new homepage layout</field>
            <field name="priority">2</field>
            <field name="estimated_hours">8</field>
            <field name="due_date" eval="(datetime.date.today() + datetime.timedelta(days=5)).strftime('%Y-%m-%d')"/>
        </record>
        
        <record id="demo_checklist_3" model="task.checklist">
            <field name="task_id" ref="demo_task_billable_1"/>
            <field name="sequence">30</field>
            <field name="name">Implement responsive design</field>
            <field name="description">Code the responsive homepage design</field>
            <field name="priority">3</field>
            <field name="estimated_hours">20</field>
            <field name="due_date" eval="(datetime.date.today() + datetime.timedelta(days=10)).strftime('%Y-%m-%d')"/>
        </record>
        
        <record id="demo_checklist_4" model="task.checklist">
            <field name="task_id" ref="demo_task_billable_1"/>
            <field name="sequence">40</field>
            <field name="name">Test across browsers</field>
            <field name="description">Test the new design across different browsers and devices</field>
            <field name="priority">2</field>
            <field name="estimated_hours">6</field>
            <field name="due_date" eval="(datetime.date.today() + datetime.timedelta(days=12)).strftime('%Y-%m-%d')"/>
        </record>
        
        <record id="demo_checklist_5" model="task.checklist">
            <field name="task_id" ref="demo_task_billable_1"/>
            <field name="sequence">50</field>
            <field name="name">Deploy to production</field>
            <field name="description">Deploy the new homepage to production server</field>
            <field name="priority">3</field>
            <field name="estimated_hours">2</field>
            <field name="due_date" eval="(datetime.date.today() + datetime.timedelta(days=14)).strftime('%Y-%m-%d')"/>
        </record>
        
        <!-- Demo checklist for annually billable task -->
        <record id="demo_checklist_annual_1" model="task.checklist">
            <field name="task_id" ref="demo_task_annually_billable_1"/>
            <field name="sequence">10</field>
            <field name="name">Security updates</field>
            <field name="description">Apply latest security patches and updates</field>
            <field name="priority">3</field>
            <field name="estimated_hours">4</field>
            <field name="due_date" eval="(datetime.date.today() + datetime.timedelta(days=7)).strftime('%Y-%m-%d')"/>
        </record>
        
        <record id="demo_checklist_annual_2" model="task.checklist">
            <field name="task_id" ref="demo_task_annually_billable_1"/>
            <field name="sequence">20</field>
            <field name="name">Performance optimization</field>
            <field name="description">Optimize website performance and loading speed</field>
            <field name="priority">2</field>
            <field name="estimated_hours">8</field>
            <field name="due_date" eval="(datetime.date.today() + datetime.timedelta(days=15)).strftime('%Y-%m-%d')"/>
        </record>
        
        <record id="demo_checklist_annual_3" model="task.checklist">
            <field name="task_id" ref="demo_task_annually_billable_1"/>
            <field name="sequence">30</field>
            <field name="name">Backup verification</field>
            <field name="description">Verify and test backup systems</field>
            <field name="priority">2</field>
            <field name="estimated_hours">3</field>
            <field name="due_date" eval="(datetime.date.today() + datetime.timedelta(days=20)).strftime('%Y-%m-%d')"/>
        </record>
        
        <record id="demo_checklist_annual_4" model="task.checklist">
            <field name="task_id" ref="demo_task_annually_billable_1"/>
            <field name="sequence">40</field>
            <field name="name">Content review</field>
            <field name="description">Review and update website content</field>
            <field name="priority">1</field>
            <field name="estimated_hours">5</field>
            <field name="due_date" eval="(datetime.date.today() + datetime.timedelta(days=25)).strftime('%Y-%m-%d')"/>
        </record>
        
    </data>
</odoo>
