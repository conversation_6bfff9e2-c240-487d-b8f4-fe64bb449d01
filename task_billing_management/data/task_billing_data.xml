<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Default Product for Task Services -->
        <record id="product_task_service" model="product.product">
            <field name="name">Task Service</field>
            <field name="type">service</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="list_price">0.0</field>
            <field name="standard_price">0.0</field>
            <field name="description">Default product for task billing services</field>
            <field name="invoice_policy">order</field>
            <field name="sale_ok">True</field>
            <field name="purchase_ok">False</field>
        </record>
        
        <!-- Email Templates -->
        <record id="email_template_task_assignment" model="mail.template">
            <field name="name">Task Assignment Notification</field>
            <field name="model_id" ref="project.model_project_task"/>
            <field name="subject">Task Assigned: ${object.name}</field>
            <field name="email_to">${ctx.get('user').partner_id.email}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p>Hello ${ctx.get('user').name},</p>
                    <p>You have been assigned a new task:</p>
                    <h3>${object.name}</h3>
                    <p><strong>Description:</strong> ${object.description or 'No description provided'}</p>
                    <p><strong>Deadline:</strong> ${object.deadline}</p>
                    <p><strong>Billing Type:</strong> ${object.billing_type}</p>
                    % if object.billing_type == 'billable':
                    <p><strong>Billing Amount:</strong> ${object.billing_amount} ${object.currency_id.name}</p>
                    % endif
                    <p>Please log in to your account to view the task details and start working on it.</p>
                    <p>Best regards,<br/>Task Management System</p>
                </div>
            </field>
        </record>
        
        <record id="email_template_task_completion" model="mail.template">
            <field name="name">Task Completion Notification</field>
            <field name="model_id" ref="project.model_project_task"/>
            <field name="subject">Task Completed: ${object.name}</field>
            <field name="email_to">${ctx.get('user').partner_id.email}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p>Hello ${ctx.get('user').name},</p>
                    <p>The following task has been completed:</p>
                    <h3>${object.name}</h3>
                    <p><strong>Completed Date:</strong> ${datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p><strong>Billing Type:</strong> ${object.billing_type}</p>
                    % if object.billing_type == 'billable':
                    <p><strong>Billing Amount:</strong> ${object.billing_amount} ${object.currency_id.name}</p>
                    % endif
                    % if object.checklist_progress:
                    <p><strong>Checklist Progress:</strong> ${object.checklist_progress}%</p>
                    % endif
                    <p>Thank you for your excellent work!</p>
                    <p>Best regards,<br/>Task Management System</p>
                </div>
            </field>
        </record>
        
        <record id="email_template_customer_notification" model="mail.template">
            <field name="name">Customer Task Notification</field>
            <field name="model_id" ref="project.model_project_task"/>
            <field name="subject">Task Update: ${object.name}</field>
            <field name="email_to">${ctx.get('customer_email')}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p>Dear Customer,</p>
                    <p>We would like to inform you about the completion of the following task:</p>
                    <h3>${object.name}</h3>
                    <p><strong>Description:</strong> ${object.description or 'No description provided'}</p>
                    <p><strong>Completed Date:</strong> ${datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    % if object.billing_type == 'billable':
                    <p><strong>Service Amount:</strong> ${object.billing_amount} ${object.currency_id.name}</p>
                    % endif
                    <p>If you have any questions or concerns, please don't hesitate to contact us.</p>
                    <p>Best regards,<br/>Your Service Team</p>
                </div>
            </field>
        </record>
        
        <!-- Default Checklist Templates -->
        <record id="checklist_template_development" model="task.checklist.template">
            <field name="name">Software Development</field>
            <field name="description">Standard checklist for software development tasks</field>
        </record>
        
        <record id="checklist_item_dev_1" model="task.checklist.template.item">
            <field name="template_id" ref="checklist_template_development"/>
            <field name="sequence">10</field>
            <field name="name">Analyze requirements</field>
            <field name="description">Review and understand the task requirements</field>
            <field name="priority">2</field>
            <field name="estimated_hours">2</field>
        </record>
        
        <record id="checklist_item_dev_2" model="task.checklist.template.item">
            <field name="template_id" ref="checklist_template_development"/>
            <field name="sequence">20</field>
            <field name="name">Design solution</field>
            <field name="description">Create technical design and approach</field>
            <field name="priority">2</field>
            <field name="estimated_hours">4</field>
        </record>
        
        <record id="checklist_item_dev_3" model="task.checklist.template.item">
            <field name="template_id" ref="checklist_template_development"/>
            <field name="sequence">30</field>
            <field name="name">Implement solution</field>
            <field name="description">Code the solution according to design</field>
            <field name="priority">3</field>
            <field name="estimated_hours">8</field>
        </record>
        
        <record id="checklist_item_dev_4" model="task.checklist.template.item">
            <field name="template_id" ref="checklist_template_development"/>
            <field name="sequence">40</field>
            <field name="name">Test implementation</field>
            <field name="description">Test the solution thoroughly</field>
            <field name="priority">3</field>
            <field name="estimated_hours">3</field>
        </record>
        
        <record id="checklist_item_dev_5" model="task.checklist.template.item">
            <field name="template_id" ref="checklist_template_development"/>
            <field name="sequence">50</field>
            <field name="name">Document solution</field>
            <field name="description">Create documentation for the solution</field>
            <field name="priority">1</field>
            <field name="estimated_hours">2</field>
        </record>
        
        <record id="checklist_item_dev_6" model="task.checklist.template.item">
            <field name="template_id" ref="checklist_template_development"/>
            <field name="sequence">60</field>
            <field name="name">Get approval</field>
            <field name="description">Get final approval from stakeholders</field>
            <field name="priority">2</field>
            <field name="estimated_hours">1</field>
        </record>
        
        <!-- General Task Template -->
        <record id="checklist_template_general" model="task.checklist.template">
            <field name="name">General Task</field>
            <field name="description">Standard checklist for general tasks</field>
        </record>
        
        <record id="checklist_item_gen_1" model="task.checklist.template.item">
            <field name="template_id" ref="checklist_template_general"/>
            <field name="sequence">10</field>
            <field name="name">Plan task</field>
            <field name="description">Plan the approach and timeline</field>
            <field name="priority">2</field>
            <field name="estimated_hours">1</field>
        </record>
        
        <record id="checklist_item_gen_2" model="task.checklist.template.item">
            <field name="template_id" ref="checklist_template_general"/>
            <field name="sequence">20</field>
            <field name="name">Execute task</field>
            <field name="description">Execute the main task activities</field>
            <field name="priority">3</field>
            <field name="estimated_hours">4</field>
        </record>
        
        <record id="checklist_item_gen_3" model="task.checklist.template.item">
            <field name="template_id" ref="checklist_template_general"/>
            <field name="sequence">30</field>
            <field name="name">Review and validate</field>
            <field name="description">Review the work and validate results</field>
            <field name="priority">2</field>
            <field name="estimated_hours">1</field>
        </record>
        
        <record id="checklist_item_gen_4" model="task.checklist.template.item">
            <field name="template_id" ref="checklist_template_general"/>
            <field name="sequence">40</field>
            <field name="name">Complete documentation</field>
            <field name="description">Document the completed work</field>
            <field name="priority">1</field>
            <field name="estimated_hours">1</field>
        </record>
        
        <!-- Cron Jobs -->
        <record id="cron_create_recurring_tasks" model="ir.cron">
            <field name="name">Create Recurring Tasks</field>
            <field name="model_id" ref="model_task_service"/>
            <field name="state">code</field>
            <field name="code">model.cron_create_recurring_tasks()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="active">True</field>
        </record>

        <record id="cron_generate_annual_invoices" model="ir.cron">
            <field name="name">Generate Annual Invoices</field>
            <field name="model_id" ref="model_task_service"/>
            <field name="state">code</field>
            <field name="code">model.cron_generate_annual_invoices()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="active">True</field>
        </record>
        
    </data>
</odoo>
