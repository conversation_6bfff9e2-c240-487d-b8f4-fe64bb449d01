<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Security Groups -->
        <record id="group_task_billing_employee" model="res.groups">
            <field name="name">Task Billing: Employee</field>
            <field name="category_id" ref="base.module_category_project"/>
            <field name="comment">Employee can create and self-assign tasks</field>
        </record>
        
        <record id="group_task_billing_manager" model="res.groups">
            <field name="name">Task Billing: Manager</field>
            <field name="category_id" ref="base.module_category_project"/>
            <field name="implied_ids" eval="[(4, ref('group_task_billing_employee'))]"/>
            <field name="comment">Manager can access own tasks and tasks of team members</field>
        </record>
        
        <record id="group_task_billing_admin" model="res.groups">
            <field name="name">Task Billing: Admin</field>
            <field name="category_id" ref="base.module_category_project"/>
            <field name="implied_ids" eval="[(4, ref('group_task_billing_manager'))]"/>
            <field name="comment">Ad<PERSON> has full access to all project managers and their tasks</field>
        </record>
        
        <!-- Record Rules for Tasks -->
        <record id="task_rule_employee" model="ir.rule">
            <field name="name">Task: Employee Access</field>
            <field name="model_id" ref="project.model_project_task"/>
            <field name="domain_force">[('user_ids', 'in', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_task_billing_employee'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="task_rule_manager" model="ir.rule">
            <field name="name">Task: Manager Access</field>
            <field name="model_id" ref="project.model_project_task"/>
            <field name="domain_force">['|', ('user_ids', 'in', user.id), ('create_uid', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_task_billing_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="task_rule_admin" model="ir.rule">
            <field name="name">Task: Admin Access</field>
            <field name="model_id" ref="project.model_project_task"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_task_billing_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <!-- Record Rules for Task Services -->
        <record id="task_service_rule_employee" model="ir.rule">
            <field name="name">Task Service: Employee Access</field>
            <field name="model_id" ref="model_task_service"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_task_billing_employee'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="task_service_rule_manager" model="ir.rule">
            <field name="name">Task Service: Manager Access</field>
            <field name="model_id" ref="model_task_service"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_task_billing_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="task_service_rule_admin" model="ir.rule">
            <field name="name">Task Service: Admin Access</field>
            <field name="model_id" ref="model_task_service"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_task_billing_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <!-- Record Rules for Task Checklist -->
        <record id="task_checklist_rule_employee" model="ir.rule">
            <field name="name">Task Checklist: Employee Access</field>
            <field name="model_id" ref="model_task_checklist"/>
            <field name="domain_force">[('task_id.user_ids', 'in', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_task_billing_employee'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="task_checklist_rule_manager" model="ir.rule">
            <field name="name">Task Checklist: Manager Access</field>
            <field name="model_id" ref="model_task_checklist"/>
            <field name="domain_force">['|', ('task_id.user_ids', 'in', user.id), ('task_id.create_uid', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_task_billing_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="task_checklist_rule_admin" model="ir.rule">
            <field name="name">Task Checklist: Admin Access</field>
            <field name="model_id" ref="model_task_checklist"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_task_billing_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <!-- Invoice Access Rules -->
        <record id="invoice_billing_access" model="ir.rule">
            <field name="name">Invoice: Task Billing Access</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="domain_force">['|', ('task_id', '=', False), '|', ('task_id.user_ids', 'in', user.id), ('task_id.create_uid', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_task_billing_employee'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
    </data>
</odoo>
