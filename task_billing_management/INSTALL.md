# Installation Guide - Task Management & Billing Integration

## Prerequisites

- Odoo 18.0 Community Edition
- Python 3.10 or higher
- PostgreSQL 12 or higher
- Administrator access to Odoo instance

## Installation Steps

### 1. Copy Module Files
Copy the entire `task_billing_management` folder to your Odoo addons directory:
```bash
cp -r task_billing_management /path/to/odoo/addons/
```

### 2. Update Apps List
1. Log in to Odoo as Administrator
2. Go to **Settings** > **Apps**
3. Click **Update Apps List**
4. Wait for the update to complete

### 3. Install the Module
1. In the Apps menu, search for "Task Management & Billing Integration"
2. Click **Install**
3. Wait for installation to complete

### 4. Verify Installation
After installation, you should see:
- New menu item "Task Billing" in the main navigation
- Enhanced task forms with billing fields
- Dashboard with analytics
- Service management interface

## Post-Installation Configuration

### 1. Set Up User Roles
Assign users to appropriate security groups:
1. Go to **Settings** > **Users & Companies** > **Users**
2. Edit each user
3. In the **Access Rights** tab, assign one of:
   - **Task Billing: Employee** - Basic task access
   - **Task Billing: Manager** - Team management access
   - **Task Billing: Admin** - Full system access

### 2. Configure Services (Optional)
For annually billable tasks:
1. Go to **Task Billing** > **Services**
2. Create services with annual amounts
3. Link products for invoicing

### 3. Set Up Email Templates (Optional)
Customize notification templates:
1. Go to **Settings** > **Technical** > **Email** > **Templates**
2. Search for "Task" templates
3. Customize as needed

### 4. Create Checklist Templates (Optional)
Standardize workflows:
1. Go to **Task Billing** > **Checklist** > **Checklist Templates**
2. Create templates for common task types

## Verification Checklist

After installation, verify these features work:

- [ ] Create a new task with billing information
- [ ] Set up a task service for annually billable tasks
- [ ] Create checklist items for a task
- [ ] Apply a checklist template to a task
- [ ] Generate an invoice from a billable task
- [ ] View the dashboard analytics
- [ ] Test email notifications (if configured)

## Troubleshooting

### Common Issues

**Module not appearing in Apps list:**
- Ensure the module is in the correct addons directory
- Check file permissions
- Update the apps list again

**Installation fails:**
- Check Odoo logs for specific errors
- Ensure all dependencies are installed
- Verify database permissions

**Views not loading correctly:**
- Clear browser cache
- Restart Odoo server
- Check for JavaScript errors in browser console

**Permission errors:**
- Verify user is assigned to correct security group
- Check record rules in Security settings

### Getting Help

If you encounter issues:
1. Check the Odoo server logs
2. Verify all dependencies are installed
3. Ensure proper user permissions
4. Contact support: <EMAIL>

## Uninstallation

To remove the module:
1. Go to **Settings** > **Apps**
2. Search for "Task Management & Billing Integration"
3. Click **Uninstall**
4. Confirm the uninstallation

**Warning:** Uninstalling will remove all module data including tasks, services, and checklists.

## Upgrade Instructions

To upgrade to a newer version:
1. Stop the Odoo server
2. Replace the module files
3. Start the Odoo server
4. Go to **Settings** > **Apps**
5. Find the module and click **Upgrade**

---

**Task Management & Billing Integration v18.*********  
For Odoo 18 Community Edition
