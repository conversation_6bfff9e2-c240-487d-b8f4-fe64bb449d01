# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class TestTaskBilling(TransactionCase):

    def setUp(self):
        super(TestTaskBilling, self).setUp()
        
        # Create test project
        self.project = self.env['project.project'].create({
            'name': 'Test Project',
            'description': 'Test project for billing'
        })
        
        # Create test service
        self.service = self.env['task.service'].create({
            'name': 'Test Service',
            'description': 'Test service for annually billable tasks',
            'annual_amount': 12000.00,
            'service_type': 'changeable',
            'billing_frequency': 'annually'
        })
        
        # Create test user
        self.test_user = self.env['res.users'].create({
            'name': 'Test User',
            'login': 'testuser',
            'email': '<EMAIL>'
        })

    def test_billable_task_creation(self):
        """Test creating a billable task"""
        task = self.env['project.task'].create({
            'name': 'Test Billable Task',
            'project_id': self.project.id,
            'billing_type': 'billable',
            'billing_amount': 1500.00,
            'deadline': datetime.now() + timedelta(days=7),
            'user_ids': [(4, self.test_user.id)]
        })
        
        self.assertEqual(task.billing_type, 'billable')
        self.assertEqual(task.billing_amount, 1500.00)
        self.assertTrue(task.deadline)

    def test_annually_billable_task_creation(self):
        """Test creating an annually billable task"""
        task = self.env['project.task'].create({
            'name': 'Test Annually Billable Task',
            'project_id': self.project.id,
            'billing_type': 'annually_billable',
            'linked_service_id': self.service.id,
            'deadline': datetime.now() + timedelta(days=30),
            'user_ids': [(4, self.test_user.id)]
        })
        
        self.assertEqual(task.billing_type, 'annually_billable')
        self.assertEqual(task.linked_service_id, self.service)
        self.assertEqual(task.billing_amount, self.service.annual_amount)

    def test_non_billable_task_creation(self):
        """Test creating a non-billable task"""
        task = self.env['project.task'].create({
            'name': 'Test Non-Billable Task',
            'project_id': self.project.id,
            'billing_type': 'non_billable',
            'deadline': datetime.now() + timedelta(days=3),
            'user_ids': [(4, self.test_user.id)]
        })
        
        self.assertEqual(task.billing_type, 'non_billable')
        self.assertEqual(task.billing_amount, 0)

    def test_task_validation(self):
        """Test task validation constraints"""
        # Test billable task without amount
        with self.assertRaises(ValidationError):
            self.env['project.task'].create({
                'name': 'Invalid Billable Task',
                'project_id': self.project.id,
                'billing_type': 'billable',
                'billing_amount': 0,  # Should fail validation
                'deadline': datetime.now() + timedelta(days=7)
            })
        
        # Test annually billable task without service
        with self.assertRaises(ValidationError):
            self.env['project.task'].create({
                'name': 'Invalid Annually Billable Task',
                'project_id': self.project.id,
                'billing_type': 'annually_billable',
                'deadline': datetime.now() + timedelta(days=7)
                # Missing linked_service_id - should fail validation
            })

    def test_checklist_creation(self):
        """Test checklist item creation and progress calculation"""
        task = self.env['project.task'].create({
            'name': 'Test Task with Checklist',
            'project_id': self.project.id,
            'billing_type': 'non_billable',
            'deadline': datetime.now() + timedelta(days=7)
        })
        
        # Create checklist items
        checklist1 = self.env['task.checklist'].create({
            'name': 'Checklist Item 1',
            'task_id': task.id,
            'is_completed': False
        })
        
        checklist2 = self.env['task.checklist'].create({
            'name': 'Checklist Item 2',
            'task_id': task.id,
            'is_completed': True
        })
        
        # Check progress calculation
        task._compute_checklist_progress()
        self.assertEqual(task.checklist_progress, 50.0)  # 1 out of 2 completed

    def test_service_creation(self):
        """Test service creation and validation"""
        service = self.env['task.service'].create({
            'name': 'Another Test Service',
            'description': 'Another test service',
            'annual_amount': 6000.00,
            'service_type': 'default',
            'billing_frequency': 'quarterly'
        })
        
        self.assertEqual(service.name, 'Another Test Service')
        self.assertEqual(service.annual_amount, 6000.00)
        self.assertEqual(service.service_type, 'default')

    def test_checklist_template_application(self):
        """Test applying checklist template to task"""
        # Create template
        template = self.env['task.checklist.template'].create({
            'name': 'Test Template',
            'description': 'Test checklist template'
        })
        
        # Create template items
        self.env['task.checklist.template.item'].create({
            'template_id': template.id,
            'name': 'Template Item 1',
            'sequence': 10,
            'priority': '2'
        })
        
        self.env['task.checklist.template.item'].create({
            'template_id': template.id,
            'name': 'Template Item 2',
            'sequence': 20,
            'priority': '1'
        })
        
        # Create task
        task = self.env['project.task'].create({
            'name': 'Test Task for Template',
            'project_id': self.project.id,
            'billing_type': 'non_billable',
            'deadline': datetime.now() + timedelta(days=7)
        })
        
        # Apply template
        template.apply_to_task(task.id)
        
        # Check that checklist items were created
        self.assertEqual(len(task.checklist_ids), 2)
        self.assertEqual(task.checklist_ids[0].name, 'Template Item 1')
        self.assertEqual(task.checklist_ids[1].name, 'Template Item 2')

    def test_dashboard_data(self):
        """Test dashboard data retrieval"""
        # Create various tasks
        self.env['project.task'].create({
            'name': 'Billable Task',
            'project_id': self.project.id,
            'billing_type': 'billable',
            'billing_amount': 1000.00,
            'deadline': datetime.now() + timedelta(days=7)
        })
        
        self.env['project.task'].create({
            'name': 'Non-Billable Task',
            'project_id': self.project.id,
            'billing_type': 'non_billable',
            'deadline': datetime.now() + timedelta(days=7)
        })
        
        # Get dashboard data
        data = self.env['project.task'].get_task_dashboard_data()
        
        self.assertIn('total_tasks', data)
        self.assertIn('billable_tasks', data)
        self.assertIn('non_billable_tasks', data)
        self.assertIn('total_billable_amount', data)
        
        self.assertGreaterEqual(data['total_tasks'], 2)
        self.assertGreaterEqual(data['billable_tasks'], 1)
        self.assertGreaterEqual(data['non_billable_tasks'], 1)
