/* Task Billing Management Styles */

/* Dashboard Styles */
.o_dashboard {
    padding: 20px;
}

.o_dashboard_title {
    color: #2c3e50;
    margin-bottom: 30px;
    text-align: center;
}

.o_dashboard_kpi {
    margin-bottom: 30px;
}

.o_dashboard_kpi .card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.o_dashboard_kpi .card:hover {
    transform: translateY(-5px);
}

.o_dashboard_kpi .card-body {
    padding: 20px;
}

.o_dashboard_kpi .card-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
}

.o_dashboard_kpi h2 {
    font-size: 32px;
    font-weight: bold;
}

/* Task Billing Type Badges */
.o_kanban_record .badge {
    font-size: 11px;
    padding: 4px 8px;
}

.badge-billable {
    background-color: #28a745;
    color: white;
}

.badge-non_billable {
    background-color: #6c757d;
    color: white;
}

.badge-annually_billable {
    background-color: #17a2b8;
    color: white;
}

/* Task Priority Colors */
.o_priority_star {
    color: #ffc107;
}

.o_priority_star.fa-star {
    color: #ffc107;
}

.o_priority_star.fa-star-o {
    color: #dee2e6;
}

/* Checklist Progress Bar */
.o_progressbar {
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.o_progressbar .o_progressbar_value {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
    transition: width 0.3s ease;
}

/* Task Form Enhancements */
.o_form_view .o_group {
    margin-bottom: 20px;
}

.o_form_view .o_group .o_td_label {
    font-weight: 600;
    color: #495057;
}

/* Billing Information Section */
.o_group[string="Billing Information"] {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
}

.o_group[string="Billing Information"] .o_td_label {
    color: #28a745;
}

/* Time Management Section */
.o_group[string="Time Management"] {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
}

.o_group[string="Time Management"] .o_td_label {
    color: #856404;
}

/* Communication Section */
.o_group[string="Communication"] {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
    padding: 15px;
}

.o_group[string="Communication"] .o_td_label {
    color: #004085;
}

/* Kanban Card Enhancements */
.o_kanban_card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.o_kanban_card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Deadline Indicators */
.o_kanban_record[data-deadline="overdue"] {
    border-left: 4px solid #dc3545;
}

.o_kanban_record[data-deadline="due_soon"] {
    border-left: 4px solid #ffc107;
}

.o_kanban_record[data-deadline="on_time"] {
    border-left: 4px solid #28a745;
}

/* Smart Button Styling */
.oe_stat_button {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.oe_stat_button:hover {
    transform: scale(1.05);
}

.oe_stat_button .o_stat_info {
    font-weight: 600;
}

/* Checklist Styling */
.o_list_table .o_data_row.o_checklist_completed {
    background-color: #d4edda;
    text-decoration: line-through;
    opacity: 0.7;
}

.o_boolean_toggle {
    cursor: pointer;
}

/* Service Card Styling */
.o_kanban_card .o_kanban_record_title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.o_kanban_tag {
    background-color: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin: 2px;
    display: inline-block;
}

/* Dashboard Charts */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 10px 10px 0 0;
}

.card-title {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

/* Quick Actions */
.btn-block {
    margin-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .o_dashboard_kpi .col-lg-3 {
        margin-bottom: 15px;
    }
    
    .o_dashboard_title {
        font-size: 24px;
    }
    
    .o_dashboard_kpi h2 {
        font-size: 24px;
    }
}

/* Animation for loading states */
.o_loading {
    opacity: 0.6;
    pointer-events: none;
}

.o_loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom scrollbar for lists */
.o_list_view::-webkit-scrollbar {
    width: 8px;
}

.o_list_view::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.o_list_view::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.o_list_view::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
