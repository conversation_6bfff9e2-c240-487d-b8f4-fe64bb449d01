/** @odoo-module **/

import { Component, onWillStart, useState, onMounted } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

export class TaskBillingDashboard extends Component {
    static template = "task_billing_management.DashboardTemplate";

    setup() {
        this.orm = useService("orm");
        this.action = useService("action");
        this.state = useState({
            dashboardData: {},
            loading: true,
        });

        onWillStart(async () => {
            await this.loadDashboardData();
        });

        onMounted(() => {
            this.updateKPICards();
            this.renderCharts();
            this.loadRecentTasks();
        });
    }

    async loadDashboardData() {
        try {
            this.state.loading = true;
            const data = await this.orm.call(
                "project.task",
                "get_task_dashboard_data",
                []
            );
            this.state.dashboardData = data;
            this.state.loading = false;
        } catch (error) {
            console.error("Error loading dashboard data:", error);
            this.state.loading = false;
        }
    }

    updateKPICards() {
        const data = this.state.dashboardData;
        
        // Update KPI cards
        const totalTasksEl = document.getElementById('total_tasks');
        const billableTasksEl = document.getElementById('billable_tasks');
        const overdueTasksEl = document.getElementById('overdue_tasks');
        const totalRevenueEl = document.getElementById('total_revenue');
        
        if (totalTasksEl) totalTasksEl.textContent = data.total_tasks || 0;
        if (billableTasksEl) billableTasksEl.textContent = data.billable_tasks || 0;
        if (overdueTasksEl) overdueTasksEl.textContent = data.overdue_tasks || 0;
        if (totalRevenueEl) {
            const revenue = data.invoiced_amount || 0;
            totalRevenueEl.textContent = `$${revenue.toLocaleString()}`;
        }
    }

    renderCharts() {
        this.renderBillingTypeChart();
        this.renderCompletionChart();
    }

    renderBillingTypeChart() {
        const canvas = document.getElementById('billing_type_chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = this.state.dashboardData;
        
        // Simple pie chart implementation
        const chartData = [
            { label: 'Billable', value: data.billable_tasks || 0, color: '#28a745' },
            { label: 'Non-Billable', value: data.non_billable_tasks || 0, color: '#6c757d' },
            { label: 'Annually Billable', value: data.annually_billable_tasks || 0, color: '#17a2b8' }
        ];

        this.drawPieChart(ctx, chartData, canvas.width, canvas.height);
    }

    renderCompletionChart() {
        const canvas = document.getElementById('completion_chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = this.state.dashboardData;
        
        const completed = data.completed_tasks || 0;
        const total = data.total_tasks || 0;
        const pending = total - completed;
        
        const chartData = [
            { label: 'Completed', value: completed, color: '#28a745' },
            { label: 'Pending', value: pending, color: '#ffc107' }
        ];

        this.drawPieChart(ctx, chartData, canvas.width, canvas.height);
    }

    drawPieChart(ctx, data, width, height) {
        const centerX = width / 2;
        const centerY = height / 2;
        const radius = Math.min(width, height) / 2 - 20;
        
        const total = data.reduce((sum, item) => sum + item.value, 0);
        if (total === 0) {
            // Draw empty state
            ctx.fillStyle = '#e9ecef';
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = '#6c757d';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('No data', centerX, centerY);
            return;
        }
        
        let currentAngle = -Math.PI / 2; // Start from top
        
        data.forEach(item => {
            const sliceAngle = (item.value / total) * 2 * Math.PI;
            
            // Draw slice
            ctx.fillStyle = item.color;
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            ctx.fill();
            
            // Draw label
            if (item.value > 0) {
                const labelAngle = currentAngle + sliceAngle / 2;
                const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
                const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(item.value.toString(), labelX, labelY);
            }
            
            currentAngle += sliceAngle;
        });
        
        // Draw legend
        let legendY = height - 60;
        data.forEach(item => {
            ctx.fillStyle = item.color;
            ctx.fillRect(10, legendY, 15, 15);
            
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`${item.label}: ${item.value}`, 30, legendY + 12);
            
            legendY += 20;
        });
    }

    async loadRecentTasks() {
        try {
            const tasks = await this.orm.searchRead(
                "project.task",
                [],
                ["name", "billing_type", "billing_amount", "deadline", "stage_id"],
                { limit: 10, order: "create_date desc" }
            );
            
            this.renderRecentTasksTable(tasks);
        } catch (error) {
            console.error("Error loading recent tasks:", error);
        }
    }

    renderRecentTasksTable(tasks) {
        const container = document.getElementById('recent_tasks_table');
        if (!container) return;
        
        if (tasks.length === 0) {
            container.innerHTML = '<p class="text-muted">No recent tasks found.</p>';
            return;
        }
        
        let tableHTML = `
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Task Name</th>
                        <th>Billing Type</th>
                        <th>Amount</th>
                        <th>Deadline</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        tasks.forEach(task => {
            const billingTypeClass = task.billing_type === 'billable' ? 'badge-success' : 
                                   task.billing_type === 'annually_billable' ? 'badge-info' : 'badge-secondary';
            
            const amount = task.billing_type === 'non_billable' ? '-' : 
                          `$${(task.billing_amount || 0).toLocaleString()}`;
            
            const deadline = task.deadline ? new Date(task.deadline).toLocaleDateString() : '-';
            
            tableHTML += `
                <tr>
                    <td>${task.name}</td>
                    <td><span class="badge ${billingTypeClass}">${task.billing_type.replace('_', ' ')}</span></td>
                    <td>${amount}</td>
                    <td>${deadline}</td>
                    <td>${task.stage_id[1] || 'Unknown'}</td>
                </tr>
            `;
        });
        
        tableHTML += '</tbody></table>';
        container.innerHTML = tableHTML;
    }

    async refreshDashboard() {
        await this.loadDashboardData();
    }

    // Action handlers for quick actions
    async openTaskForm() {
        this.action.doAction({
            type: 'ir.actions.act_window',
            name: 'Create Task',
            res_model: 'project.task',
            view_mode: 'form',
            views: [[false, 'form']],
            target: 'current',
        });
    }

    async openServices() {
        this.action.doAction({
            type: 'ir.actions.act_window',
            name: 'Task Services',
            res_model: 'task.service',
            view_mode: 'kanban,list,form',
            target: 'current',
        });
    }

    async openBillableTasks() {
        this.action.doAction({
            type: 'ir.actions.act_window',
            name: 'Billable Tasks',
            res_model: 'project.task',
            view_mode: 'kanban,list,form',
            domain: [['billing_type', '=', 'billable']],
            target: 'current',
        });
    }

    async openOverdueTasks() {
        this.action.doAction({
            type: 'ir.actions.act_window',
            name: 'Overdue Tasks',
            res_model: 'project.task',
            view_mode: 'kanban,list,form',
            domain: [['deadline', '<', new Date().toISOString().split('T')[0]], ['stage_id.fold', '=', false]],
            target: 'current',
        });
    }
}

// Register the dashboard component
registry.category("actions").add("task_billing_dashboard", TaskBillingDashboard);
