<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    
    <t t-name="task_billing_management.DashboardTemplate" owl="1">
        <div class="o_dashboard">
            <h1 class="o_dashboard_title">Task Management &amp; Billing Dashboard</h1>
            
            <!-- Loading State -->
            <div t-if="state.loading" class="text-center p-5">
                <i class="fa fa-spinner fa-spin fa-3x"></i>
                <p class="mt-3">Loading dashboard data...</p>
            </div>
            
            <!-- Dashboard Content -->
            <div t-else="">
                <!-- KPI Cards -->
                <div class="row o_dashboard_kpi">
                    <div class="col-lg-3 col-md-6 col-sm-12">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">Total Tasks</h4>
                                        <h2 class="mb-0" t-esc="state.dashboardData.total_tasks || 0"/>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fa fa-tasks fa-3x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 col-sm-12">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">Billable Tasks</h4>
                                        <h2 class="mb-0" t-esc="state.dashboardData.billable_tasks || 0"/>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fa fa-money fa-3x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 col-sm-12">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">Overdue Tasks</h4>
                                        <h2 class="mb-0" t-esc="state.dashboardData.overdue_tasks || 0"/>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fa fa-exclamation-triangle fa-3x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 col-sm-12">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">Total Revenue</h4>
                                        <h2 class="mb-0">$<t t-esc="(state.dashboardData.invoiced_amount || 0).toLocaleString()"/></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fa fa-dollar fa-3x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts Row -->
                <div class="row mt-4">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Tasks by Billing Type</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="billing_type_chart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Task Completion Status</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="completion_chart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <button class="btn btn-primary btn-block" t-on-click="openTaskForm">
                                            <i class="fa fa-plus"></i> Create Task
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-success btn-block" t-on-click="openServices">
                                            <i class="fa fa-cog"></i> Manage Services
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-info btn-block" t-on-click="openBillableTasks">
                                            <i class="fa fa-money"></i> Billable Tasks
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-warning btn-block" t-on-click="openOverdueTasks">
                                            <i class="fa fa-clock-o"></i> Overdue Tasks
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Tasks Table -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Recent Tasks</h5>
                            </div>
                            <div class="card-body">
                                <div id="recent_tasks_table">
                                    <!-- Table will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
    
</templates>
