# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import timedelta


class TaskService(models.Model):
    _name = 'task.service'
    _description = 'Task Service for Annually Billable Tasks'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'
    _rec_name = 'name'

    name = fields.Char(string='Service Name', required=True, tracking=True)
    description = fields.Text(string='Description')
    
    annual_amount = fields.Monetary(
        string='Annual Amount',
        currency_field='currency_id',
        required=True,
        help="Amount to be billed annually for this service"
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id,
        required=True
    )
    
    service_type = fields.Selection([
        ('default', 'Default'),
        ('changeable', 'Changeable')
    ], string='Service Type', default='changeable', required=True,
       help="Default: Amount cannot be changed in tasks\nChangeable: Amount can be modified in tasks")
    
    active = fields.Boolean(string='Active', default=True)
    
    # Relations
    task_ids = fields.One2many(
        'project.task',
        'linked_service_id',
        string='Related Tasks'
    )
    task_count = fields.Integer(
        string='Task Count',
        compute='_compute_task_count'
    )
    
    # Product relation for invoicing
    product_id = fields.Many2one(
        'product.product',
        string='Related Product',
        domain=[('type', '=', 'service')],
        help="Product used for invoicing this service"
    )
    
    # Billing configuration
    billing_frequency = fields.Selection([
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('semi_annually', 'Semi-Annually'),
        ('annually', 'Annually')
    ], string='Billing Frequency', default='annually',
       help="How often this service should be billed")
    
    next_billing_date = fields.Date(
        string='Next Billing Date',
        help="Next date when this service should be billed"
    )
    
    auto_create_tasks = fields.Boolean(
        string='Auto Create Tasks',
        default=False,
        help="Automatically create recurring tasks for this service"
    )
    
    task_template_id = fields.Many2one(
        'project.task',
        string='Task Template',
        help="Template task to use when auto-creating tasks"
    )
    
    # Analytics
    total_billed_amount = fields.Monetary(
        string='Total Billed Amount',
        compute='_compute_total_billed_amount',
        currency_field='currency_id'
    )
    
    last_billing_date = fields.Date(
        string='Last Billing Date',
        help="Last date when this service was billed"
    )
    
    @api.depends('task_ids')
    def _compute_task_count(self):
        for service in self:
            service.task_count = len(service.task_ids)
    
    @api.depends('task_ids.invoice_ids.amount_total', 'task_ids.invoice_ids.state')
    def _compute_total_billed_amount(self):
        for service in self:
            invoices = service.task_ids.mapped('invoice_ids').filtered(lambda i: i.state == 'posted')
            service.total_billed_amount = sum(invoices.mapped('amount_total'))
    
    @api.constrains('annual_amount')
    def _check_annual_amount(self):
        for service in self:
            if service.annual_amount <= 0:
                raise ValidationError(_("Annual amount must be greater than zero."))
    
    @api.onchange('billing_frequency')
    def _onchange_billing_frequency(self):
        if self.billing_frequency and not self.next_billing_date:
            self.next_billing_date = fields.Date.today()
    
    def action_view_tasks(self):
        """Smart button action to view related tasks"""
        self.ensure_one()
        action = self.env.ref('project.act_project_project_2_project_task_all').read()[0]
        if len(self.task_ids) > 1:
            action['domain'] = [('id', 'in', self.task_ids.ids)]
        elif len(self.task_ids) == 1:
            action['views'] = [(self.env.ref('project.view_task_form2').id, 'form')]
            action['res_id'] = self.task_ids.ids[0]
        else:
            action = {'type': 'ir.actions.act_window_close'}
        return action
    
    def action_create_recurring_task(self):
        """Create a new task for this service"""
        self.ensure_one()
        
        task_vals = {
            'name': f"{self.name} - {fields.Date.today()}",
            'description': self.description,
            'billing_type': 'annually_billable',
            'linked_service_id': self.id,
            'billing_amount': self.annual_amount,
            'deadline': fields.Datetime.now() + timedelta(days=30),  # Default 30 days deadline
        }
        
        # Copy from template if available
        if self.task_template_id:
            template_vals = self.task_template_id.copy_data()[0]
            template_vals.update(task_vals)
            task_vals = template_vals
        
        task = self.env['project.task'].create(task_vals)
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('New Task'),
            'view_mode': 'form',
            'res_model': 'project.task',
            'res_id': task.id,
            'target': 'current',
        }
    
    def action_generate_annual_invoice(self):
        """Generate annual invoice for this service"""
        self.ensure_one()
        
        if not self.product_id:
            raise ValidationError(_("Please configure a product for this service before generating invoice."))
        
        # Find or create customer (this would need to be enhanced based on business logic)
        customer = self.env['res.partner'].search([('is_company', '=', True)], limit=1)
        if not customer:
            raise ValidationError(_("No customer found. Please create a customer first."))
        
        # Create invoice
        invoice_vals = {
            'move_type': 'out_invoice',
            'partner_id': customer.id,
            'invoice_date': fields.Date.today(),
            'ref': f"Annual Service: {self.name}",
        }
        
        invoice = self.env['account.move'].create(invoice_vals)
        
        # Create invoice line
        line_vals = {
            'move_id': invoice.id,
            'product_id': self.product_id.id,
            'name': f"Annual Service: {self.name}",
            'quantity': 1,
            'price_unit': self.annual_amount,
        }
        
        self.env['account.move.line'].create(line_vals)
        
        # Update billing dates
        self.last_billing_date = fields.Date.today()
        if self.billing_frequency == 'annually':
            self.next_billing_date = fields.Date.today() + timedelta(days=365)
        elif self.billing_frequency == 'semi_annually':
            self.next_billing_date = fields.Date.today() + timedelta(days=182)
        elif self.billing_frequency == 'quarterly':
            self.next_billing_date = fields.Date.today() + timedelta(days=91)
        elif self.billing_frequency == 'monthly':
            self.next_billing_date = fields.Date.today() + timedelta(days=30)
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Annual Invoice'),
            'view_mode': 'form',
            'res_model': 'account.move',
            'res_id': invoice.id,
            'target': 'current',
        }
    
    @api.model
    def cron_create_recurring_tasks(self):
        """Cron job to create recurring tasks for services"""
        services = self.search([
            ('auto_create_tasks', '=', True),
            ('next_billing_date', '<=', fields.Date.today())
        ])
        
        for service in services:
            service.action_create_recurring_task()
    
    @api.model
    def cron_generate_annual_invoices(self):
        """Cron job to generate annual invoices for services"""
        services = self.search([
            ('next_billing_date', '<=', fields.Date.today()),
            ('product_id', '!=', False)
        ])
        
        for service in services:
            try:
                service.action_generate_annual_invoice()
            except Exception as e:
                # Log error but continue with other services
                self.env['ir.logging'].create({
                    'name': 'task.service',
                    'type': 'server',
                    'level': 'ERROR',
                    'message': f"Failed to generate annual invoice for service {service.name}: {str(e)}",
                    'path': 'task_billing_management',
                    'func': 'cron_generate_annual_invoices',
                    'line': '1',
                })
