# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class TaskChecklist(models.Model):
    _name = 'task.checklist'
    _description = 'Task Checklist Item'
    _order = 'sequence, id'
    _rec_name = 'name'

    name = fields.Char(string='Checklist Item', required=True)
    description = fields.Text(string='Description')
    
    task_id = fields.Many2one(
        'project.task',
        string='Task',
        required=True,
        ondelete='cascade'
    )
    
    is_completed = fields.Boolean(
        string='Completed',
        default=False
    )
    
    sequence = fields.Integer(string='Sequence', default=10)
    
    completed_date = fields.Datetime(
        string='Completed Date',
        readonly=True
    )
    
    completed_by = fields.Many2one(
        'res.users',
        string='Completed By',
        readonly=True
    )
    
    due_date = fields.Date(string='Due Date')
    
    priority = fields.Selection([
        ('0', 'Low'),
        ('1', 'Normal'),
        ('2', 'High'),
        ('3', 'Critical')
    ], string='Priority', default='1')
    
    assigned_to = fields.Many2one(
        'res.users',
        string='Assigned To',
        help="User responsible for this checklist item"
    )
    
    notes = fields.Text(string='Notes')
    
    # Progress tracking
    estimated_hours = fields.Float(string='Estimated Hours')
    actual_hours = fields.Float(string='Actual Hours')
    
    @api.onchange('is_completed')
    def _onchange_is_completed(self):
        if self.is_completed:
            self.completed_date = fields.Datetime.now()
            self.completed_by = self.env.user
        else:
            self.completed_date = False
            self.completed_by = False
    
    def write(self, vals):
        """Override write to track completion"""
        result = super(TaskChecklist, self).write(vals)
        
        if 'is_completed' in vals:
            for item in self:
                if item.is_completed and not item.completed_date:
                    item.completed_date = fields.Datetime.now()
                    item.completed_by = self.env.user
                elif not item.is_completed:
                    item.completed_date = False
                    item.completed_by = False
                
                # Update task progress
                item.task_id._compute_checklist_progress()
                
                # Log in task chatter
                if item.is_completed:
                    item.task_id.message_post(
                        body=_("Checklist item completed: %s") % item.name,
                        message_type='notification'
                    )
        
        return result
    
    def action_toggle_completion(self):
        """Toggle completion status"""
        self.ensure_one()
        self.is_completed = not self.is_completed
        return True
    
    @api.model
    def create_default_checklist(self, task_id, checklist_items=None):
        """Create default checklist items for a task"""
        if not checklist_items:
            checklist_items = [
                'Review task requirements',
                'Plan implementation approach',
                'Execute task',
                'Test and validate results',
                'Document completion',
                'Get approval'
            ]
        
        sequence = 10
        for item_name in checklist_items:
            self.create({
                'name': item_name,
                'task_id': task_id,
                'sequence': sequence
            })
            sequence += 10
        
        return True


class TaskChecklistTemplate(models.Model):
    _name = 'task.checklist.template'
    _description = 'Task Checklist Template'
    _order = 'name'

    name = fields.Char(string='Template Name', required=True)
    description = fields.Text(string='Description')
    
    checklist_item_ids = fields.One2many(
        'task.checklist.template.item',
        'template_id',
        string='Checklist Items'
    )
    
    active = fields.Boolean(string='Active', default=True)
    
    def apply_to_task(self, task_id):
        """Apply this template to a task"""
        task = self.env['project.task'].browse(task_id)
        if not task.exists():
            raise ValidationError(_("Task not found."))
        
        for item in self.checklist_item_ids:
            self.env['task.checklist'].create({
                'name': item.name,
                'description': item.description,
                'task_id': task_id,
                'sequence': item.sequence,
                'priority': item.priority,
                'estimated_hours': item.estimated_hours,
            })
        
        return True


class TaskChecklistTemplateItem(models.Model):
    _name = 'task.checklist.template.item'
    _description = 'Task Checklist Template Item'
    _order = 'sequence, id'

    name = fields.Char(string='Item Name', required=True)
    description = fields.Text(string='Description')
    
    template_id = fields.Many2one(
        'task.checklist.template',
        string='Template',
        required=True,
        ondelete='cascade'
    )
    
    sequence = fields.Integer(string='Sequence', default=10)
    
    priority = fields.Selection([
        ('0', 'Low'),
        ('1', 'Normal'),
        ('2', 'High'),
        ('3', 'Critical')
    ], string='Priority', default='1')
    
    estimated_hours = fields.Float(string='Estimated Hours')
