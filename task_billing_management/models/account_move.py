# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class Account<PERSON>ove(models.Model):
    _inherit = 'account.move'

    task_id = fields.Many2one(
        'project.task',
        string='Related Task',
        help="Task that generated this invoice"
    )
    
    task_billing_type = fields.Selection(
        related='task_id.billing_type',
        string='Task Billing Type',
        readonly=True
    )
    
    def action_view_task(self):
        """Smart button action to view related task"""
        self.ensure_one()
        if not self.task_id:
            return {'type': 'ir.actions.act_window_close'}
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Related Task'),
            'view_mode': 'form',
            'res_model': 'project.task',
            'res_id': self.task_id.id,
            'target': 'current',
        }
