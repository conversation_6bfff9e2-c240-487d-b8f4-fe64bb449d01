# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import logging

_logger = logging.getLogger(__name__)


class TaskTask(models.Model):
    _inherit = 'project.task'
    _description = 'Enhanced Task with Billing Integration'

    # Billing Fields
    billing_type = fields.Selection([
        ('billable', 'Billable'),
        ('non_billable', 'Non-Billable'),
        ('annually_billable', 'Annually Billable')
    ], string='Billing Type', default='non_billable', required=True, tracking=True)
    
    billing_amount = fields.Monetary(
        string='Billing Amount', 
        currency_field='currency_id',
        help="Amount to be billed for this task"
    )
    
    currency_id = fields.Many2one(
        'res.currency', 
        string='Currency',
        default=lambda self: self.env.company.currency_id
    )
    
    linked_service_id = fields.Many2one(
        'task.service', 
        string='Linked Service',
        help="Service associated with this task for annually billable tasks"
    )
    
    auto_generate_invoice = fields.Boolean(
        string='Auto Generate Invoice',
        default=False,
        help="Automatically generate invoice when task is completed"
    )
    
    # Time Fields
    start_time = fields.Datetime(string='Start Time', tracking=True)
    end_time = fields.Datetime(string='End Time', tracking=True)
    deadline = fields.Datetime(string='Deadline', tracking=True)
    
    # Invoice Relations
    invoice_ids = fields.One2many(
        'account.move', 
        'task_id', 
        string='Invoices',
        domain=[('move_type', '=', 'out_invoice')]
    )
    invoice_count = fields.Integer(
        string='Invoice Count',
        compute='_compute_invoice_count'
    )
    
    # Task Checklist
    checklist_ids = fields.One2many(
        'task.checklist', 
        'task_id', 
        string='Task Checklist'
    )
    checklist_progress = fields.Float(
        string='Checklist Progress (%)',
        compute='_compute_checklist_progress',
        store=True
    )
    
    # Communication Fields
    send_assignment_notification = fields.Boolean(
        string='Send Assignment Notification',
        default=True
    )
    send_completion_notification = fields.Boolean(
        string='Send Completion Notification',
        default=True
    )
    customer_notification_email = fields.Char(
        string='Customer Notification Email',
        help="Email to notify customer about task updates"
    )
    
    # Enhanced Fields
    task_priority = fields.Selection([
        ('0', 'Low'),
        ('1', 'Normal'),
        ('2', 'High'),
        ('3', 'Urgent')
    ], string='Priority', default='1')
    
    estimated_hours = fields.Float(string='Estimated Hours')
    actual_hours = fields.Float(string='Actual Hours', compute='_compute_actual_hours', store=True)
    
    @api.depends('start_time', 'end_time')
    def _compute_actual_hours(self):
        for task in self:
            # Try to use timesheet_ids if available (from hr_timesheet module)
            if 'timesheet_ids' in task._fields and hasattr(task, 'timesheet_ids') and task.timesheet_ids:
                task.actual_hours = sum(task.timesheet_ids.mapped('unit_amount'))
            else:
                # Fallback: calculate from start_time and end_time if available
                if task.start_time and task.end_time:
                    time_diff = task.end_time - task.start_time
                    task.actual_hours = time_diff.total_seconds() / 3600.0
                else:
                    task.actual_hours = 0.0
    
    @api.depends('invoice_ids')
    def _compute_invoice_count(self):
        for task in self:
            task.invoice_count = len(task.invoice_ids)
    
    @api.depends('checklist_ids.is_completed')
    def _compute_checklist_progress(self):
        for task in self:
            if task.checklist_ids:
                completed = len(task.checklist_ids.filtered('is_completed'))
                total = len(task.checklist_ids)
                task.checklist_progress = (completed / total) * 100 if total > 0 else 0
            else:
                task.checklist_progress = 0
    
    @api.constrains('start_time', 'end_time', 'deadline')
    def _check_time_consistency(self):
        for task in self:
            if task.start_time and task.end_time and task.start_time >= task.end_time:
                raise ValidationError(_("Start time must be before end time."))
            if task.deadline and task.start_time and task.start_time > task.deadline:
                raise ValidationError(_("Start time cannot be after deadline."))
    
    @api.constrains('billing_type', 'billing_amount', 'linked_service_id')
    def _check_billing_consistency(self):
        for task in self:
            if task.billing_type == 'billable' and not task.billing_amount:
                raise ValidationError(_("Billing amount is required for billable tasks."))
            if task.billing_type == 'annually_billable' and not task.linked_service_id:
                raise ValidationError(_("Linked service is required for annually billable tasks."))
    
    @api.onchange('billing_type')
    def _onchange_billing_type(self):
        if self.billing_type == 'non_billable':
            self.billing_amount = 0
            self.linked_service_id = False
            self.auto_generate_invoice = False
        elif self.billing_type == 'annually_billable':
            self.auto_generate_invoice = False
    
    @api.onchange('linked_service_id')
    def _onchange_linked_service_id(self):
        if self.linked_service_id and self.billing_type == 'annually_billable':
            self.billing_amount = self.linked_service_id.annual_amount
    
    def action_view_invoices(self):
        """Smart button action to view task invoices"""
        self.ensure_one()
        action = self.env.ref('account.action_move_out_invoice_type').read()[0]
        if len(self.invoice_ids) > 1:
            action['domain'] = [('id', 'in', self.invoice_ids.ids)]
        elif len(self.invoice_ids) == 1:
            action['views'] = [(self.env.ref('account.view_move_form').id, 'form')]
            action['res_id'] = self.invoice_ids.ids[0]
        else:
            action = {'type': 'ir.actions.act_window_close'}
        return action
    
    def action_create_invoice(self):
        """Create invoice for billable task"""
        self.ensure_one()
        if self.billing_type == 'non_billable':
            raise UserError(_("Cannot create invoice for non-billable task."))
        
        if not self.partner_id:
            raise UserError(_("Customer is required to create invoice."))
        
        # Create invoice
        invoice_vals = self._prepare_invoice_vals()
        invoice = self.env['account.move'].create(invoice_vals)
        
        # Create invoice line
        line_vals = self._prepare_invoice_line_vals(invoice)
        self.env['account.move.line'].create(line_vals)
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Invoice'),
            'view_mode': 'form',
            'res_model': 'account.move',
            'res_id': invoice.id,
            'target': 'current',
        }
    
    def _prepare_invoice_vals(self):
        """Prepare invoice values"""
        return {
            'move_type': 'out_invoice',
            'partner_id': self.partner_id.id,
            'task_id': self.id,
            'invoice_date': fields.Date.today(),
            'ref': f"Task: {self.name}",
        }
    
    def _prepare_invoice_line_vals(self, invoice):
        """Prepare invoice line values"""
        product = self.env.ref('task_billing_management.product_task_service', raise_if_not_found=False)
        if not product:
            product = self.env['product.product'].search([('type', '=', 'service')], limit=1)
        
        return {
            'move_id': invoice.id,
            'product_id': product.id if product else False,
            'name': f"Task: {self.name}",
            'quantity': 1,
            'price_unit': self.billing_amount,
            'account_id': product.property_account_income_id.id if product and product.property_account_income_id else self.env['account.account'].search([('account_type', '=', 'income')], limit=1).id,
        }

    def write(self, vals):
        """Override write to handle notifications and auto-invoice generation"""
        # Store old values for comparison
        old_user_ids = {task.id: task.user_ids.ids for task in self}
        old_stage_ids = {task.id: task.stage_id.id for task in self}

        result = super(TaskTask, self).write(vals)

        # Handle assignment notifications
        if 'user_ids' in vals:
            for task in self:
                if task.send_assignment_notification:
                    new_users = set(task.user_ids.ids) - set(old_user_ids.get(task.id, []))
                    if new_users:
                        task._send_assignment_notification(new_users)

        # Handle completion notifications and auto-invoice
        if 'stage_id' in vals:
            for task in self:
                old_stage = self.env['project.task.type'].browse(old_stage_ids.get(task.id))
                if not old_stage.fold and task.stage_id.fold:  # Task completed
                    if task.send_completion_notification:
                        task._send_completion_notification()
                    if task.auto_generate_invoice and task.billing_type == 'billable':
                        task._auto_generate_invoice()

        return result

    def _send_assignment_notification(self, user_ids):
        """Send notification when task is assigned"""
        users = self.env['res.users'].browse(user_ids)
        for user in users:
            if user.partner_id.email:
                template = self.env.ref('task_billing_management.email_template_task_assignment', raise_if_not_found=False)
                if template:
                    template.with_context(user=user).send_mail(self.id, force_send=True)

        # Log assignment in chatter
        user_names = ', '.join(users.mapped('name'))
        self.message_post(
            body=_("Task assigned to: %s") % user_names,
            message_type='notification'
        )

    def _send_completion_notification(self):
        """Send notification when task is completed"""
        # Notify assigned users
        for user in self.user_ids:
            if user.partner_id.email:
                template = self.env.ref('task_billing_management.email_template_task_completion', raise_if_not_found=False)
                if template:
                    template.with_context(user=user).send_mail(self.id, force_send=True)

        # Notify customer if email provided
        if self.customer_notification_email:
            template = self.env.ref('task_billing_management.email_template_customer_notification', raise_if_not_found=False)
            if template:
                template.with_context(customer_email=self.customer_notification_email).send_mail(self.id, force_send=True)

        # Log completion in chatter
        self.message_post(
            body=_("Task completed successfully!"),
            message_type='notification'
        )

    def _auto_generate_invoice(self):
        """Automatically generate invoice for completed billable task"""
        try:
            if self.billing_type == 'billable' and self.billing_amount > 0:
                self.action_create_invoice()
                self.message_post(
                    body=_("Invoice automatically generated for completed task."),
                    message_type='notification'
                )
        except Exception as e:
            _logger.error(f"Failed to auto-generate invoice for task {self.id}: {str(e)}")
            self.message_post(
                body=_("Failed to automatically generate invoice: %s") % str(e),
                message_type='notification'
            )

    @api.model
    def get_task_dashboard_data(self):
        """Get dashboard data for task analytics"""
        domain = []
        if not self.env.user.has_group('task_billing_management.group_task_billing_admin'):
            if self.env.user.has_group('task_billing_management.group_task_billing_manager'):
                # Manager can see own tasks and team tasks
                domain = ['|', ('user_ids', 'in', self.env.user.id), ('create_uid', '=', self.env.user.id)]
            else:
                # Employee can see only own tasks
                domain = [('user_ids', 'in', self.env.user.id)]

        tasks = self.search(domain)

        return {
            'total_tasks': len(tasks),
            'billable_tasks': len(tasks.filtered(lambda t: t.billing_type == 'billable')),
            'non_billable_tasks': len(tasks.filtered(lambda t: t.billing_type == 'non_billable')),
            'annually_billable_tasks': len(tasks.filtered(lambda t: t.billing_type == 'annually_billable')),
            'completed_tasks': len(tasks.filtered(lambda t: t.stage_id.fold)),
            'overdue_tasks': len(tasks.filtered(lambda t: t.deadline and t.deadline < fields.Datetime.now() and not t.stage_id.fold)),
            'total_billable_amount': sum(tasks.filtered(lambda t: t.billing_type == 'billable').mapped('billing_amount')),
            'invoiced_amount': sum(tasks.mapped('invoice_ids').filtered(lambda i: i.state == 'posted').mapped('amount_total')),
        }
