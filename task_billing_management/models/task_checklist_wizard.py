# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class TaskChecklistWizard(models.TransientModel):
    _name = 'task.checklist.wizard'
    _description = 'Apply Checklist Template to Task'

    task_id = fields.Many2one(
        'project.task',
        string='Task',
        required=True
    )
    
    template_id = fields.Many2one(
        'task.checklist.template',
        string='Checklist Template',
        required=True,
        domain=[('active', '=', True)]
    )
    
    replace_existing = fields.Boolean(
        string='Replace Existing Checklist',
        default=False,
        help="If checked, existing checklist items will be removed before applying template"
    )
    
    def action_apply_template(self):
        """Apply the selected template to the task"""
        self.ensure_one()
        
        if not self.task_id or not self.template_id:
            raise UserError(_("Task and template are required."))
        
        # Remove existing checklist items if requested
        if self.replace_existing:
            self.task_id.checklist_ids.unlink()
        
        # Apply template
        self.template_id.apply_to_task(self.task_id.id)
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Task'),
            'view_mode': 'form',
            'res_model': 'project.task',
            'res_id': self.task_id.id,
            'target': 'current',
        }
