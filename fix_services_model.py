#!/usr/bin/env python3
"""
Fix Services Model Script
This script creates the x_partner_id field in the x_services model
and then creates the x_service_ids field in the res.partner model
"""

import xmlrpc.client
import time
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

def connect_to_odoo():
    """Connect to Odoo"""
    try:
        logger.info(f"Connecting to Odoo at {URL}")
        common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
        models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
        
        uid = common.authenticate(DB, USERNAME, PASSWORD, {})
        if not uid:
            raise Exception("Authentication failed")
            
        logger.info(f"Successfully connected as user ID: {uid}")
        return common, models, uid
        
    except Exception as e:
        logger.error(f"Connection failed: {e}")
        return None, None, None

def get_model_id(models, uid, model_name):
    """Get the ID of a model by name"""
    try:
        model_ids = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.model', 'search',
            [[('model', '=', model_name)]]
        )
        
        if not model_ids:
            logger.error(f"Model {model_name} not found")
            return False
        
        return model_ids[0]
        
    except Exception as e:
        logger.error(f"Error getting model ID for {model_name}: {e}")
        return False

def create_field(models, uid, field_info):
    """Create a field on a model"""
    try:
        # Get the model ID
        model_id = get_model_id(models, uid, field_info['model_id'])
        if not model_id:
            return False
        
        # Check if field already exists
        field_domain = [
            ('model_id', '=', model_id),
            ('name', '=', field_info['name'])
        ]
        
        existing_fields = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.model.fields', 'search',
            [field_domain]
        )
        
        if existing_fields:
            logger.info(f"Field {field_info['name']} already exists")
            return True
        
        # Get relation model ID if needed
        if field_info['ttype'] in ['many2one', 'one2many', 'many2many']:
            relation_model_id = get_model_id(models, uid, field_info['relation'])
            if not relation_model_id:
                return False
        
        # Prepare field values
        field_values = {
            'model_id': model_id,
            'name': field_info['name'],
            'field_description': field_info['field_description'],
            'ttype': field_info['ttype'],
            'state': 'manual'
        }
        
        # Add relation fields if needed
        if field_info['ttype'] in ['many2one', 'one2many', 'many2many']:
            field_values['relation'] = field_info['relation']
        
        if field_info['ttype'] == 'one2many' and 'relation_field' in field_info:
            field_values['relation_field'] = field_info['relation_field']
        
        # Create the field
        field_id = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.model.fields', 'create',
            [field_values]
        )
        
        logger.info(f"✅ Created field {field_info['name']} with ID {field_id}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating field {field_info['name']}: {e}")
        return False

def test_field_exists(models, uid, model, field_name):
    """Test if a field exists in a model"""
    try:
        # Get model fields
        model_fields = models.execute_kw(
            DB, uid, PASSWORD,
            model, 'fields_get',
            [], {'attributes': ['string', 'relation']}
        )
        
        if field_name in model_fields:
            field_info = model_fields[field_name]
            logger.info(f"✅ Field {field_name} exists in {model}: {field_info.get('string', 'No description')}")
            return True
        else:
            logger.warning(f"❌ Field {field_name} does not exist in {model}")
            return False
                
    except Exception as e:
        logger.error(f"❌ Error testing field {field_name} in {model}: {e}")
        return False

def upgrade_module(models, uid, module_name):
    """Upgrade a specific module"""
    try:
        logger.info(f"Upgrading module: {module_name}")
        
        # Search for the module
        module_ids = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.module.module', 'search',
            [[('name', '=', module_name)]]
        )
        
        if not module_ids:
            logger.error(f"Module {module_name} not found")
            return False
        
        module_id = module_ids[0]
        
        # Get current state
        module_data = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.module.module', 'read',
            [module_id], {'fields': ['state']}
        )[0]
        
        current_state = module_data['state']
        logger.info(f"Module {module_name} current state: {current_state}")
        
        if current_state == 'installed':
            # Upgrade the module
            try:
                result = models.execute_kw(
                    DB, uid, PASSWORD,
                    'ir.module.module', 'button_immediate_upgrade',
                    [[module_id]]
                )
                logger.info(f"✅ Upgrade initiated for {module_name}")
                return True
            except Exception as e:
                logger.error(f"❌ Error during upgrade of {module_name}: {e}")
                return False
        else:
            logger.warning(f"Module {module_name} not in installed state: {current_state}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error upgrading {module_name}: {e}")
        return False

def main():
    """Main execution function"""
    logger.info("=" * 60)
    logger.info("FIX SERVICES MODEL SCRIPT")
    logger.info("=" * 60)
    
    # Connect to Odoo
    common, models, uid = connect_to_odoo()
    if not models:
        logger.error("Failed to connect to Odoo. Exiting.")
        sys.exit(1)
    
    # Test if x_partner_id field exists in x_services model
    logger.info("\n" + "=" * 40)
    logger.info("TESTING x_partner_id FIELD IN x_services MODEL")
    logger.info("=" * 40)
    
    partner_id_exists = test_field_exists(models, uid, 'x_services', 'x_partner_id')
    
    # Create x_partner_id field in x_services model if it doesn't exist
    if not partner_id_exists:
        logger.info("\n" + "=" * 40)
        logger.info("CREATING x_partner_id FIELD IN x_services MODEL")
        logger.info("=" * 40)
        
        partner_id_field = {
            'name': 'x_partner_id',
            'field_description': 'Client',
            'ttype': 'many2one',
            'relation': 'res.partner',
            'model_id': 'x_services'
        }
        
        create_field(models, uid, partner_id_field)
    
    # Create x_service_ids field in res.partner model
    logger.info("\n" + "=" * 40)
    logger.info("CREATING x_service_ids FIELD IN res.partner MODEL")
    logger.info("=" * 40)
    
    service_ids_field = {
        'name': 'x_service_ids',
        'field_description': 'Services',
        'ttype': 'one2many',
        'relation': 'x_services',
        'relation_field': 'x_partner_id',
        'model_id': 'res.partner'
    }
    
    create_field(models, uid, service_ids_field)
    
    # Test if x_service_ids field exists in res.partner model
    logger.info("\n" + "=" * 40)
    logger.info("TESTING x_service_ids FIELD IN res.partner MODEL")
    logger.info("=" * 40)
    
    service_ids_exists = test_field_exists(models, uid, 'res.partner', 'x_service_ids')
    
    # Upgrade imca_services module
    if service_ids_exists:
        logger.info("\n" + "=" * 40)
        logger.info("UPGRADING imca_services MODULE")
        logger.info("=" * 40)
        
        upgrade_module(models, uid, 'imca_services')
    
    # Final summary
    logger.info("\n" + "=" * 60)
    logger.info("FINAL SUMMARY")
    logger.info("=" * 60)
    
    logger.info(f"x_partner_id field in x_services model: {'✅ EXISTS' if partner_id_exists else '❌ MISSING'}")
    logger.info(f"x_service_ids field in res.partner model: {'✅ EXISTS' if service_ids_exists else '❌ MISSING'}")
    
    if partner_id_exists and service_ids_exists:
        logger.info("\n🎉 SUCCESS! All fields are now available!")
        logger.info("\n📋 Next Steps:")
        logger.info("1. Open a partner record in Odoo")
        logger.info("2. Check for new tabs: Groups, Services, Documents, Credentials, DSC Management")
        logger.info("3. Verify the CA Management menu structure")
    else:
        logger.warning(f"\n⚠️  Partial success: Some fields are still missing")
    
    logger.info("\nScript completed!")

if __name__ == "__main__":
    main()
