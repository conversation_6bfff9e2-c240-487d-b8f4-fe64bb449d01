from odoo import http
from odoo.http import request
import re

class BearingSearchController(http.Controller):
    @http.route('/bearing/search', type='json', auth='public', website=True)
    def search_products(self, search_term, page=1):
        items_per_page = 20
        domain = []
        scored_products = []
        search_terms = re.findall(r'\w+', search_term.lower())

        # Search in product name and description
        products = request.env['product.template'].sudo().search(domain)

        for product in products:
            score = 0
            matches = {
                'housing': product.x_housing_designation or '',
                'compatible': product.x_compatible_bearings or '',
                'name': product.name or '',
                'description': product.description_sale or ''
            }

            for field, value in matches.items():
                if not value:
                    continue
                value = value.lower()
                # Exact match
                if search_term.lower() in value:
                    score += 10
                    if value.startswith(search_term.lower()):
                        score += 5
                # Individual word matches
                for term in search_terms:
                    if term in value:
                        score += 3
                        if value.startswith(term):
                            score += 2

            if score > 0:
                scored_products.append({
                    'id': product.id,
                    'name': product.name,
                    'description': product.description_sale,
                    'housing_designation': product.x_housing_designation,
                    'compatible_bearings': product.x_compatible_bearings,
                    'score': score
                })

        # Sort by score and return results for the current page
        scored_products.sort(key=lambda x: x['score'], reverse=True)
        total_products = len(scored_products)
        start_index = (page - 1) * items_per_page
        end_index = start_index + items_per_page
        return scored_products[start_index:end_index], total_products
