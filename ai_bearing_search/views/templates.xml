<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Extend ai_bearing_website templates -->
    <!-- <template id="bearing_search_extension" inherit_id="ai_bearing_website.category_template" active="True" customize_show="True" name="Bearing Search">
        <xpath expr="//div[hasclass('category-section')]" position="attributes">
            <attribute name="class">category-section s_three_columns</attribute>
        </xpath> -->
        
        <!-- Add data attributes to product cards -->
        <!-- <xpath expr="//div[hasclass('product-card')]" position="attributes">
            <attribute name="t-att-data-housing">product.x_housing_designation</attribute>
            <attribute name="t-att-data-compatible">product.x_compatible_bearings</attribute>
        </xpath>
    </template> -->

    <!-- Extend ai_jnjbearing_website templates -->
    <template id="jnj_search_extension" inherit_id="ai_jnjbearing_website.jnj_products_template" active="True" customize_show="True" name="JNJ Search">
        <xpath expr="//section[hasclass('s_three_columns')]" position="attributes">
            <attribute name="class">s_three_columns category-section</attribute>
        </xpath>
        
        <!-- Add data attributes to category cards -->
        <!-- <xpath expr="//div[hasclass('card')]" position="attributes">
            <attribute name="t-att-data-housing">product.product.x_housing_designation</attribute>
            <attribute name="t-att-data-compatible">product.product.x_compatible_bearings</attribute>
        </xpath>-->
    </template> 
</odoo>
