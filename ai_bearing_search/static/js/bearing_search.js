// document.addEventListener('DOMContentLoaded', function() {
//     // Initialize variables
//     let searchTimeout = null;
//     let originalContent = null;
//     const itemsPerPage = 20;
//     let currentPage = 1;
//     let filteredProducts = [];

//     // Initialize the search functionality
//     function initSearch() {
//         const searchContainer = document.createElement('div');
//         searchContainer.className = 'bearing-search-container';
//         searchContainer.innerHTML = `
//             <div class="search-box">
//                 <i class="fa fa-search search-icon"></i>
//                 <input type="text" class="search-input" placeholder="Search bearings...">
//                 <i class="fa fa-times search-reset"></i>
//             </div>
//         `;

//         // Add styles
//         const style = document.createElement('style');
//         style.textContent = `
//             .bearing-search-container {
//                 max-width: 800px;
//                 margin: 1rem auto;
//                 padding: 0 1rem;
//             }
//             .search-box {
//                 display: flex;
//                 align-items: center;
//                 background: white;
//                 border: 1px solid #ddd;
//                 border-radius: 8px;
//                 padding: 0.5rem 1rem;
//                 box-shadow: 0 2px 4px rgba(0,0,0,0.1);
//                 margin-bottom: 1rem;
//             }
//             .search-input {
//                 flex: 1;
//                 border: none;
//                 padding: 0.5rem;
//                 font-size: 1rem;
//                 outline: none;
//             }
//             .search-icon, .search-reset {
//                 color: #666;
//                 cursor: pointer;
//                 padding: 0.5rem;
//             }
//             .search-icon:hover, .search-reset:hover {
//                 color: #000;
//             }
//             .highlight {
//                 background: #ffd700;
//                 padding: 0.1em 0.2em;
//                 border-radius: 2px;
//             }
//             tr.hidden-row {
//                 display: none;
//             }
//         `;
//         document.head.appendChild(style);

//         // Insert search container before the filter section
//         const filterSection = document.querySelector('.filter-section');
//         if (filterSection) {
//             filterSection.parentNode.insertBefore(searchContainer, filterSection);

//             // Add event listeners
//             const searchInput = searchContainer.querySelector('.search-input');
//             const resetButton = searchContainer.querySelector('.search-reset');

//             searchInput.addEventListener('input', handleSearch);
//             resetButton.addEventListener('click', resetSearch);
//         }
//     }

//     // Handle search input
//     function handleSearch(event) {
//         const searchTerm = event.target.value.trim().toLowerCase();
        
//         if (searchTimeout) {
//             clearTimeout(searchTimeout);
//         }

//         searchTimeout = setTimeout(() => {
//             if (searchTerm) {
//                 performSearch(searchTerm);
//             } else {
//                 resetSearch();
//             }
//         }, 300);
//     }

//     // Perform search across table rows
//     function performSearch(searchTerm) {
//         const tableBody = document.getElementById('productsTableBody');
//         const rows = tableBody.getElementsByTagName('tr');
//         filteredProducts = [];

//         for (let row of rows) {
//             const cells = row.getElementsByTagName('td');
//             let matchFound = false;
//             let searchContent = '';

//             // Combine all cell contents for searching
//             for (let cell of cells) {
//                 searchContent += ' ' + cell.textContent;
//             }
//             searchContent = searchContent.toLowerCase();

//             // Check if any search term matches
//             const terms = searchTerm.split(/\s+/);
//             let score = 0;
            
//             terms.forEach(term => {
//                 if (searchContent.includes(term)) {
//                     score += 1;
//                     matchFound = true;
//                 }
//             });

//             if (matchFound) {
//                 filteredProducts.push({
//                     element: row,
//                     score: score
//                 });
//                 highlightText(row, terms);
//             }
            
//             row.classList.toggle('hidden-row', !matchFound);
//         }

//         // Sort by relevance
//         filteredProducts.sort((a, b) => b.score - a.score);
        
//         // Update pagination
//         updatePagination();
//         showCurrentPage();
//     }

//     // Highlight matching text
//     function highlightText(row, terms) {
//         const cells = row.getElementsByTagName('td');
        
//         for (let cell of cells) {
//             let content = cell.textContent;
//             terms.forEach(term => {
//                 const regex = new RegExp(`(${term})`, 'gi');
//                 content = content.replace(regex, '<span class="highlight">$1</span>');
//             });
//             cell.innerHTML = content;
//         }
//     }

//     // Update pagination based on filtered results
//     function updatePagination() {
//         const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
//         const pagination = document.getElementById('productPagination');
        
//         if (pagination) {
//             pagination.innerHTML = '';
            
//             // Previous button
//             const prevLi = document.createElement('li');
//             prevLi.className = 'page-item' + (currentPage === 1 ? ' disabled' : '');
//             prevLi.innerHTML = '<a class="page-link" href="#">Previous</a>';
//             prevLi.onclick = () => {
//                 if (currentPage > 1) {
//                     currentPage--;
//                     showCurrentPage();
//                 }
//             };
//             pagination.appendChild(prevLi);

//             // Page numbers
//             for (let i = 1; i <= totalPages; i++) {
//                 const li = document.createElement('li');
//                 li.className = 'page-item' + (i === currentPage ? ' active' : '');
//                 li.innerHTML = `<a class="page-link" href="#">${i}</a>`;
//                 li.onclick = () => {
//                     currentPage = i;
//                     showCurrentPage();
//                 };
//                 pagination.appendChild(li);
//             }

//             // Next button
//             const nextLi = document.createElement('li');
//             nextLi.className = 'page-item' + (currentPage === totalPages ? ' disabled' : '');
//             nextLi.innerHTML = '<a class="page-link" href="#">Next</a>';
//             nextLi.onclick = () => {
//                 if (currentPage < totalPages) {
//                     currentPage++;
//                     showCurrentPage();
//                 }
//             };
//             pagination.appendChild(nextLi);
//         }
//     }

//     // Show current page of results
//     function showCurrentPage() {
//         const startIndex = (currentPage - 1) * itemsPerPage;
//         const endIndex = startIndex + itemsPerPage;
        
//         filteredProducts.forEach((item, index) => {
//             item.element.classList.toggle('hidden-row', 
//                 index < startIndex || index >= endIndex);
//         });
//     }

//     // Reset search
//     function resetSearch() {
//         const searchInput = document.querySelector('.search-input');
//         searchInput.value = '';
        
//         // Show all rows and remove highlights
//         const tableBody = document.getElementById('productsTableBody');
//         const rows = tableBody.getElementsByTagName('tr');
        
//         for (let row of rows) {
//             row.classList.remove('hidden-row');
//             const cells = row.getElementsByTagName('td');
//             for (let cell of cells) {
//                 cell.innerHTML = cell.textContent; // Remove highlights
//             }
//         }

//         // Reset pagination
//         currentPage = 1;
//         filteredProducts = [];
//         updatePagination();
//     }

//     // Initialize search when DOM is loaded
//     initSearch();
// });




document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    let searchTimeout = null;
    let originalContent = null;
    let contentSection = null;
    let currentPage = 1;
    let itemsPerPage = 20; // Will be adjusted based on template type
    let filteredItems = [];
    let isTableLayout = false;

    // Initialize the search functionality
    function initSearch() {
        // Detect template type
        isTableLayout = !!document.getElementById('productsTable');
        itemsPerPage = isTableLayout ? 20 : 8; // More items per page for table layout

        const searchContainer = document.createElement('div');
        searchContainer.className = 'bearing-search-container';
        searchContainer.innerHTML = `
            <div class="search-box">
                <i class="fa fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search products...">
                <i class="fa fa-times search-reset"></i>
            </div>
        `;

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .bearing-search-container {
                max-width: 800px;
                margin: 2rem auto;
                padding: 0 1rem;
            }
            .search-box {
                display: flex;
                align-items: center;
                background: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 0.5rem 1rem;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .search-input {
                flex: 1;
                border: none;
                padding: 0.5rem;
                font-size: 1rem;
                outline: none;
            }
            .search-icon, .search-reset {
                color: #666;
                cursor: pointer;
                padding: 0.5rem;
            }
            .search-icon:hover, .search-reset:hover {
                color: #000;
            }
            .highlight {
                background: #ffd700;
                padding: 0.1em 0.2em;
                border-radius: 2px;
            }
            .hidden-item {
                display: none !important;
            }
            .pagination-container {
                display: flex;
                justify-content: center;
                margin-top: 2rem;
                margin-bottom: 2rem;
            }
            .pagination-button {
                background: #fff;
                border: 1px solid #ddd;
                padding: 0.5rem 1rem;
                margin: 0 0.25rem;
                cursor: pointer;
                border-radius: 4px;
            }
            .pagination-button.active {
                background: #007bff;
                color: white;
                border-color: #007bff;
            }
            .pagination-button:hover:not(.active) {
                background: #f0f0f0;
            }
            .no-results {
                text-align: center;
                padding: 2rem;
                color: #666;
                width: 100%;
            }
        `;
        document.head.appendChild(style);

        // Insert search container in appropriate location
        let targetSection;
        if (isTableLayout) {
            targetSection = document.querySelector('.filter-section');
            contentSection = document.querySelector('.s_products');
        } else {
            targetSection = document.querySelector('.s_cover');
            contentSection = document.querySelector('.s_three_columns');
        }

        if (targetSection) {
            targetSection.parentNode.insertBefore(searchContainer, targetSection.nextSibling);

            // Add event listeners
            const searchInput = searchContainer.querySelector('.search-input');
            const resetButton = searchContainer.querySelector('.search-reset');

            searchInput.addEventListener('input', handleSearch);
            resetButton.addEventListener('click', resetSearch);
        }
    }

    // Handle search input
    function handleSearch(event) {
        const searchTerm = event.target.value.trim().toLowerCase();
        
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        searchTimeout = setTimeout(() => {
            if (searchTerm) {
                performSearch(searchTerm);
            } else {
                resetSearch();
            }
        }, 300);
    }

    // Perform search
    function performSearch(searchTerm) {
        filteredItems = [];
        let hasResults = false;

        if (isTableLayout) {
            // Search in table rows
            const rows = document.querySelectorAll('#productsTableBody tr');
            rows.forEach(row => {
                const cells = row.getElementsByTagName('td');
                let searchContent = Array.from(cells).map(cell => cell.textContent).join(' ').toLowerCase();
                processSearchItem(row, searchContent, searchTerm);
            });
        } else {
            // Search in cards
            const cards = document.querySelectorAll('.s_three_columns .card');
            cards.forEach(card => {
                const title = card.querySelector('.card-title')?.textContent || '';
                const description = card.querySelector('.card-text')?.textContent || '';
                const searchContent = `${title} ${description}`.toLowerCase();
                processSearchItem(card.closest('.s_col_no_bgcolor'), searchContent, searchTerm);
            });
        }

        // Sort by relevance
        filteredItems.sort((a, b) => b.score - a.score);

        // Update UI
        updateSearchResults(searchTerm);
    }

    // Process individual search item
    function processSearchItem(element, searchContent, searchTerm) {
        const terms = searchTerm.split(/\s+/);
        let score = 0;
        let matchFound = false;

        terms.forEach(term => {
            if (searchContent.includes(term)) {
                score += 1;
                matchFound = true;
            }
        });

        if (matchFound) {
            filteredItems.push({
                element: element,
                score: score
            });
            highlightText(element, terms);
        }
        
        element.classList.toggle('hidden-item', !matchFound);
    }

    // Update search results in UI
    function updateSearchResults(searchTerm) {
        // Show/hide no results message
        const noResultsElem = document.querySelector('.no-results');
        if (filteredItems.length === 0) {
            if (!noResultsElem) {
                const message = document.createElement('div');
                message.className = 'no-results';
                message.textContent = 'No matching items found';
                contentSection.appendChild(message);
            }
        } else if (noResultsElem) {
            noResultsElem.remove();
        }

        // Update pagination
        currentPage = 1;
        updatePagination();
        showCurrentPage();
    }

    // Highlight matching text
    function highlightText(element, terms) {
        if (isTableLayout) {
            const cells = element.getElementsByTagName('td');
            Array.from(cells).forEach(cell => {
                let content = cell.textContent;
                terms.forEach(term => {
                    const regex = new RegExp(`(${term})`, 'gi');
                    content = content.replace(regex, '<span class="highlight">$1</span>');
                });
                cell.innerHTML = content;
            });
        } else {
            const elements = [
                element.querySelector('.card-title'),
                element.querySelector('.card-text')
            ];
            elements.forEach(el => {
                if (el) {
                    let content = el.textContent;
                    terms.forEach(term => {
                        const regex = new RegExp(`(${term})`, 'gi');
                        content = content.replace(regex, '<span class="highlight">$1</span>');
                    });
                    el.innerHTML = content;
                }
            });
        }
    }

    // Update pagination controls
    function updatePagination() {
        const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
        
        // Remove existing pagination
        const existingPagination = document.querySelector('.pagination-container');
        if (existingPagination) {
            existingPagination.remove();
        }

        if (totalPages > 1) {
            const paginationContainer = document.createElement('div');
            paginationContainer.className = 'pagination-container';

            // Add pagination buttons
            const prevButton = createPaginationButton('Previous', () => {
                if (currentPage > 1) {
                    currentPage--;
                    showCurrentPage();
                }
            });
            paginationContainer.appendChild(prevButton);

            for (let i = 1; i <= totalPages; i++) {
                const pageButton = createPaginationButton(i.toString(), () => {
                    currentPage = i;
                    showCurrentPage();
                }, i === currentPage);
                paginationContainer.appendChild(pageButton);
            }

            const nextButton = createPaginationButton('Next', () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    showCurrentPage();
                }
            });
            paginationContainer.appendChild(nextButton);

            contentSection.appendChild(paginationContainer);
        }
    }

    // Create pagination button
    function createPaginationButton(text, onClick, isActive = false) {
        const button = document.createElement('button');
        button.className = `pagination-button${isActive ? ' active' : ''}`;
        button.textContent = text;
        button.onclick = onClick;
        return button;
    }

    // Show current page of results
    function showCurrentPage() {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;

        filteredItems.forEach((item, index) => {
            item.element.classList.toggle('hidden-item', 
                index < startIndex || index >= endIndex);
        });

        // Update pagination buttons
        updatePaginationButtons();
    }

    // Update pagination button states
    function updatePaginationButtons() {
        const buttons = document.querySelectorAll('.pagination-button');
        const totalPages = Math.ceil(filteredItems.length / itemsPerPage);

        buttons.forEach(button => {
            if (button.textContent === 'Previous') {
                button.disabled = currentPage === 1;
            } else if (button.textContent === 'Next') {
                button.disabled = currentPage === totalPages;
            } else {
                button.classList.toggle('active', parseInt(button.textContent) === currentPage);
            }
        });
    }

    // Reset search
    function resetSearch() {
        const searchInput = document.querySelector('.search-input');
        searchInput.value = '';
        
        if (isTableLayout) {
            // Reset table rows
            const rows = document.querySelectorAll('#productsTableBody tr');
            rows.forEach(row => {
                row.classList.remove('hidden-item');
                const cells = row.getElementsByTagName('td');
                Array.from(cells).forEach(cell => {
                    cell.innerHTML = cell.textContent;
                });
            });
        } else {
            // Reset cards
            const cards = document.querySelectorAll('.s_three_columns .card');
            cards.forEach(card => {
                const container = card.closest('.s_col_no_bgcolor');
                container.classList.remove('hidden-item');
                
                const elements = [
                    card.querySelector('.card-title'),
                    card.querySelector('.card-text')
                ];
                elements.forEach(element => {
                    if (element) {
                        element.innerHTML = element.textContent;
                    }
                });
            });
        }

        // Clean up UI elements
        const pagination = document.querySelector('.pagination-container');
        if (pagination) pagination.remove();
        
        const noResults = document.querySelector('.no-results');
        if (noResults) noResults.remove();

        // Reset state
        filteredItems = [];
        currentPage = 1;
    }

    // Initialize search when DOM is loaded
    initSearch();
});