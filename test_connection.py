#!/usr/bin/env python3
"""
Simple connection test to Odoo
"""

import xmlrpc.client
import sys

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

def test_connection():
    try:
        print(f"Testing connection to {URL}")
        print(f"Database: {DB}")
        print(f"Username: {USERNAME}")
        
        # Test common endpoint
        common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
        print("Common endpoint connected successfully")
        
        # Test version
        version = common.version()
        print(f"Odoo version: {version}")
        
        # Test authentication
        uid = common.authenticate(DB, USERNAME, PASSWORD, {})
        if uid:
            print(f"Authentication successful! User ID: {uid}")
            
            # Test models endpoint
            models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
            print("Models endpoint connected successfully")
            
            # Test simple query
            partner_count = models.execute_kw(
                DB, uid, PASSWORD,
                'res.partner', 'search_count', [[]]
            )
            print(f"Total partners in database: {partner_count}")
            
            return True
        else:
            print("Authentication failed!")
            return False
            
    except Exception as e:
        print(f"Connection error: {e}")
        return False

if __name__ == "__main__":
    if test_connection():
        print("✓ Connection test successful!")
        sys.exit(0)
    else:
        print("✗ Connection test failed!")
        sys.exit(1)
