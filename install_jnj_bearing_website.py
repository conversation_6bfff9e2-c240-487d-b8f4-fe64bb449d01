#!/usr/bin/env python3
"""
J&J Bearing Website Module Installation Script
Installs and configures the jnj_bearing_website module on Odoo 18
"""

import xmlrpc.client
import time
import sys
import logging
from datetime import datetime

# Configure logging
log_filename = f"jnj_bearing_website_install_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'http://jnj18.arihantai.com:8069'
DB = 'jnj18.arihantai.com'
USERNAME = 'admin'
PASSWORD = 'admin'

# Module to install
MODULE_NAME = 'jnj_bearing_website'

class JNJBearingInstaller:
    def __init__(self, url, db, username, password):
        self.url = url
        self.db = db
        self.username = username
        self.password = password
        self.uid = None
        self.common = None
        self.models = None
        
    def connect(self):
        """Establish connection to Odoo"""
        try:
            logger.info(f"Connecting to Odoo at {self.url}")
            self.common = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/common')
            self.models = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/object')
            
            # Test version first
            version = self.common.version()
            logger.info(f"Odoo version: {version}")
            
            # Authenticate
            self.uid = self.common.authenticate(self.db, self.username, self.password, {})
            if not self.uid:
                raise Exception("Authentication failed")
                
            logger.info(f"Successfully connected as user ID: {self.uid}")
            return True
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    def execute_kw(self, model, method, args=None, kwargs=None):
        """Execute a method with proper parameter handling"""
        if args is None:
            args = []
        if kwargs is None:
            kwargs = {}
            
        try:
            return self.models.execute_kw(
                self.db, self.uid, self.password,
                model, method, args, kwargs
            )
        except Exception as e:
            logger.error(f"Error executing {model}.{method}: {e}")
            raise
    
    def update_module_list(self):
        """Update the module list"""
        try:
            logger.info("Updating module list...")
            self.execute_kw('ir.module.module', 'update_list', [])
            logger.info("Module list updated successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to update module list: {e}")
            return False
    
    def check_module_exists(self):
        """Check if the module exists in the system"""
        try:
            logger.info(f"Checking if module {MODULE_NAME} exists...")
            
            module_ids = self.execute_kw(
                'ir.module.module',
                'search',
                [[('name', '=', MODULE_NAME)]]
            )
            
            if module_ids:
                logger.info(f"✅ Module {MODULE_NAME} found in system")
                return module_ids[0]
            else:
                logger.error(f"❌ Module {MODULE_NAME} not found in system")
                return None
                
        except Exception as e:
            logger.error(f"Error checking module existence: {e}")
            return None
    
    def get_module_info(self, module_id):
        """Get module information"""
        try:
            module_data = self.execute_kw(
                'ir.module.module',
                'read',
                [[module_id]],
                {'fields': ['name', 'state', 'summary', 'author', 'latest_version']}
            )
            
            if module_data:
                info = module_data[0]
                logger.info(f"Module Info:")
                logger.info(f"  Name: {info.get('name')}")
                logger.info(f"  State: {info.get('state')}")
                logger.info(f"  Version: {info.get('latest_version')}")
                logger.info(f"  Author: {info.get('author')}")
                logger.info(f"  Summary: {info.get('summary')}")
                return info
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting module info: {e}")
            return None
    
    def install_module(self, module_id):
        """Install the module"""
        try:
            logger.info(f"Installing module {MODULE_NAME}...")
            
            # Use button_immediate_install method
            result = self.execute_kw(
                'ir.module.module',
                'button_immediate_install',
                [[module_id]]
            )
            
            logger.info(f"Installation initiated, result: {result}")
            return True
            
        except Exception as e:
            logger.error(f"Error during installation: {e}")
            return False
    
    def upgrade_module(self, module_id):
        """Upgrade the module if already installed"""
        try:
            logger.info(f"Upgrading module {MODULE_NAME}...")
            
            # Use button_immediate_upgrade method
            result = self.execute_kw(
                'ir.module.module',
                'button_immediate_upgrade',
                [[module_id]]
            )
            
            logger.info(f"Upgrade initiated, result: {result}")
            return True
            
        except Exception as e:
            logger.error(f"Error during upgrade: {e}")
            return False
    
    def verify_installation(self):
        """Verify that the module was installed correctly"""
        try:
            logger.info("Verifying installation...")
            
            # Check module state
            module_ids = self.execute_kw(
                'ir.module.module',
                'search',
                [[('name', '=', MODULE_NAME)]]
            )
            
            if not module_ids:
                logger.error("Module not found after installation")
                return False
            
            module_data = self.execute_kw(
                'ir.module.module',
                'read',
                [[module_ids[0]]],
                {'fields': ['state']}
            )
            
            if module_data and module_data[0].get('state') == 'installed':
                logger.info("✅ Module successfully installed")
                return True
            else:
                logger.error(f"❌ Module state: {module_data[0].get('state') if module_data else 'unknown'}")
                return False
                
        except Exception as e:
            logger.error(f"Error verifying installation: {e}")
            return False
    
    def test_models(self):
        """Test access to module models"""
        models_to_test = [
            'jnj_bearing_website.enquiry',
            'jnj_bearing_website.product_enquiry',
            'jnj_bearing_website.category_headers',
            'jnj_bearing_website.category_parent_headers'
        ]
        
        results = {}
        
        for model_name in models_to_test:
            try:
                logger.info(f"Testing model: {model_name}")
                
                count = self.execute_kw(
                    model_name,
                    'search_count',
                    [[]]
                )
                
                logger.info(f"✅ {model_name}: {count} records")
                results[model_name] = True
                
            except Exception as e:
                logger.error(f"❌ {model_name}: {e}")
                results[model_name] = False
        
        return results
    
    def test_website_pages(self):
        """Test website pages"""
        try:
            logger.info("Testing website pages...")
            
            # Check for homepage
            page_ids = self.execute_kw(
                'website.page',
                'search',
                [[('url', '=', '/')]]
            )
            
            if page_ids:
                logger.info("✅ Homepage found")
                return True
            else:
                logger.warning("❌ Homepage not found")
                return False
                
        except Exception as e:
            logger.error(f"Error testing website pages: {e}")
            return False

def main():
    """Main execution function"""
    logger.info("=" * 60)
    logger.info("J&J BEARING WEBSITE MODULE INSTALLATION")
    logger.info("=" * 60)
    
    # Initialize installer
    installer = JNJBearingInstaller(URL, DB, USERNAME, PASSWORD)
    
    # Connect to Odoo
    if not installer.connect():
        logger.error("Failed to connect to Odoo. Exiting.")
        sys.exit(1)
    
    # Update module list
    if not installer.update_module_list():
        logger.error("Failed to update module list. Continuing anyway...")
    
    # Check if module exists
    module_id = installer.check_module_exists()
    if not module_id:
        logger.error("Module not found. Please ensure the module is in the addons path.")
        sys.exit(1)
    
    # Get module info
    module_info = installer.get_module_info(module_id)
    if not module_info:
        logger.error("Could not retrieve module information.")
        sys.exit(1)
    
    # Install or upgrade based on current state
    current_state = module_info.get('state')
    success = False
    
    if current_state == 'uninstalled':
        logger.info("Module is uninstalled. Installing...")
        success = installer.install_module(module_id)
    elif current_state == 'installed':
        logger.info("Module is already installed. Upgrading...")
        success = installer.upgrade_module(module_id)
    elif current_state in ['to install', 'to upgrade']:
        logger.info(f"Module is marked {current_state}. Waiting for completion...")
        success = True
    else:
        logger.warning(f"Module in unexpected state: {current_state}")
    
    if not success:
        logger.error("Installation/upgrade failed.")
        sys.exit(1)
    
    # Wait for installation to complete
    logger.info("Waiting for installation to complete...")
    time.sleep(10)
    
    # Verify installation
    if installer.verify_installation():
        logger.info("✅ Installation verified successfully")
    else:
        logger.error("❌ Installation verification failed")
        sys.exit(1)
    
    # Test models
    logger.info("\nTesting module models...")
    model_results = installer.test_models()
    working_models = sum(1 for result in model_results.values() if result)
    
    # Test website
    logger.info("\nTesting website functionality...")
    website_working = installer.test_website_pages()
    
    # Final summary
    logger.info("\n" + "=" * 60)
    logger.info("INSTALLATION SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Module: {MODULE_NAME}")
    logger.info(f"Status: ✅ INSTALLED")
    logger.info(f"Models working: {working_models}/{len(model_results)}")
    logger.info(f"Website: {'✅ WORKING' if website_working else '❌ NOT WORKING'}")
    logger.info(f"Log file: {log_filename}")
    
    if working_models == len(model_results) and website_working:
        logger.info("\n🎉 SUCCESS! J&J Bearing Website module installed and working perfectly!")
    else:
        logger.warning("\n⚠️ Installation completed but some features may not be working correctly.")
    
    logger.info("\nInstallation completed.")

if __name__ == "__main__":
    main()
