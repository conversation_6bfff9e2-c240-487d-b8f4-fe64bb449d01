#!/usr/bin/env python3
"""
Fix Task Management Issues Script
This script addresses the following issues:
1. Task Billing Dashboard button click errors
2. Task Management form view customizations
3. Task Logs Module activation issues with deadline field
"""

import xmlrpc.client
import time
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

def connect_to_odoo():
    """Connect to Odoo"""
    try:
        logger.info(f"Connecting to Odoo at {URL}")
        common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
        models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
        
        uid = common.authenticate(DB, USERNAME, PASSWORD, {})
        if not uid:
            raise Exception("Authentication failed")
            
        logger.info(f"Successfully connected as user ID: {uid}")
        return common, models, uid
        
    except Exception as e:
        logger.error(f"Connection failed: {e}")
        return None, None, None

def analyze_task_issues(models, uid):
    """Analyze current task management issues"""
    logger.info("=" * 60)
    logger.info("ANALYZING TASK MANAGEMENT ISSUES")
    logger.info("=" * 60)
    
    issues = []
    
    # 1. Check task billing dashboard action
    logger.info("\n1. Checking Task Billing Dashboard:")
    try:
        dashboard_actions = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.actions.act_window', 'search_read',
            [[('name', 'ilike', 'Task Billing Dashboard')]],
            {'fields': ['name', 'res_model', 'view_mode', 'view_id']}
        )
        
        if dashboard_actions:
            action = dashboard_actions[0]
            logger.info(f"   ✅ Found dashboard action: {action['name']}")
            logger.info(f"      - Model: {action['res_model']}")
            logger.info(f"      - View Mode: {action['view_mode']}")
            logger.info(f"      - View ID: {action['view_id']}")
        else:
            logger.warning("   ⚠️ Dashboard action not found")
            issues.append("Dashboard action missing")
            
    except Exception as e:
        logger.error(f"   ❌ Error checking dashboard: {e}")
        issues.append(f"Dashboard check error: {e}")
    
    # 2. Check task form view customizations
    logger.info("\n2. Checking Task Form View Customizations:")
    try:
        task_views = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.ui.view', 'search_read',
            [[('model', '=', 'project.task'), ('inherit_id', '!=', False)]],
            {'fields': ['name', 'inherit_id', 'active']}
        )
        
        logger.info(f"   ✅ Found {len(task_views)} task view customizations:")
        for view in task_views:
            logger.info(f"      - {view['name']}: Active={view['active']}")
            
    except Exception as e:
        logger.error(f"   ❌ Error checking task views: {e}")
        issues.append(f"Task view check error: {e}")
    
    # 3. Check deadline field requirements
    logger.info("\n3. Checking Deadline Field Configuration:")
    try:
        task_fields = models.execute_kw(
            DB, uid, PASSWORD,
            'project.task', 'fields_get',
            [], {'attributes': ['string', 'required', 'type']}
        )
        
        if 'deadline' in task_fields:
            deadline_field = task_fields['deadline']
            logger.info(f"   ✅ Deadline field found:")
            logger.info(f"      - Type: {deadline_field.get('type', 'unknown')}")
            logger.info(f"      - Required: {deadline_field.get('required', False)}")
            logger.info(f"      - String: {deadline_field.get('string', 'No description')}")
            
            if deadline_field.get('required', False):
                logger.warning("   ⚠️ Deadline field is required - this may cause Task Logs module issues")
                issues.append("Deadline field is required")
        else:
            logger.error("   ❌ Deadline field not found")
            issues.append("Deadline field missing")
            
    except Exception as e:
        logger.error(f"   ❌ Error checking deadline field: {e}")
        issues.append(f"Deadline field check error: {e}")
    
    # 4. Check for existing tasks without deadline
    logger.info("\n4. Checking Existing Tasks Without Deadline:")
    try:
        tasks_without_deadline = models.execute_kw(
            DB, uid, PASSWORD,
            'project.task', 'search_count',
            [[('deadline', '=', False)]]
        )
        
        logger.info(f"   📊 Tasks without deadline: {tasks_without_deadline}")
        if tasks_without_deadline > 0:
            logger.warning(f"   ⚠️ {tasks_without_deadline} tasks have no deadline - this will prevent Task Logs module activation")
            issues.append(f"{tasks_without_deadline} tasks without deadline")
            
    except Exception as e:
        logger.error(f"   ❌ Error checking tasks without deadline: {e}")
        issues.append(f"Tasks without deadline check error: {e}")
    
    return issues

def fix_deadline_field_requirement(models, uid):
    """Fix the deadline field requirement issue"""
    logger.info("\n" + "=" * 40)
    logger.info("FIXING DEADLINE FIELD REQUIREMENT")
    logger.info("=" * 40)

    try:
        # First, set deadline for all tasks that don't have one
        logger.info("Setting default deadline for tasks without deadline...")

        # Get tasks without deadline
        tasks_without_deadline = models.execute_kw(
            DB, uid, PASSWORD,
            'project.task', 'search',
            [[('deadline', '=', False)]]
        )

        if tasks_without_deadline:
            # Set deadline to 30 days from now for tasks without deadline
            from datetime import datetime, timedelta
            default_deadline = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')

            models.execute_kw(
                DB, uid, PASSWORD,
                'project.task', 'write',
                [tasks_without_deadline, {'deadline': default_deadline}]
            )

            logger.info(f"✅ Set default deadline for {len(tasks_without_deadline)} tasks")
        else:
            logger.info("✅ All tasks already have deadlines")

        # Now make deadline field non-required by updating the field definition
        logger.info("Making deadline field non-required...")
        try:
            # Get the field definition
            field_ids = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.model.fields', 'search',
                [[('model', '=', 'project.task'), ('name', '=', 'deadline')]]
            )

            if field_ids:
                # Update the field to make it non-required
                models.execute_kw(
                    DB, uid, PASSWORD,
                    'ir.model.fields', 'write',
                    [field_ids, {'required': False}]
                )
                logger.info("✅ Made deadline field non-required")
            else:
                logger.warning("⚠️ Deadline field definition not found")

        except Exception as e:
            logger.warning(f"⚠️ Could not update deadline field requirement: {e}")

        return True

    except Exception as e:
        logger.error(f"❌ Error fixing deadline field: {e}")
        return False

def fix_dashboard_action(models, uid):
    """Fix the dashboard action registration"""
    logger.info("\n" + "=" * 40)
    logger.info("FIXING DASHBOARD ACTION")
    logger.info("=" * 40)
    
    try:
        # Check if the client action exists
        client_actions = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.actions.client', 'search_read',
            [[('tag', '=', 'task_billing_dashboard')]],
            {'fields': ['name', 'tag']}
        )
        
        if not client_actions:
            logger.info("Creating client action for dashboard...")
            
            # Create client action
            client_action_id = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.actions.client', 'create',
                [{
                    'name': 'Task Billing Dashboard',
                    'tag': 'task_billing_dashboard',
                    'target': 'current',
                }]
            )
            
            logger.info(f"✅ Created client action with ID: {client_action_id}")
            
            # Update menu to use client action
            menus = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.ui.menu', 'search',
                [[('name', '=', 'Dashboard'), ('parent_id.name', '=', 'Task Billing')]]
            )
            
            if menus:
                models.execute_kw(
                    DB, uid, PASSWORD,
                    'ir.ui.menu', 'write',
                    [menus, {'action': f'ir.actions.client,{client_action_id}'}]
                )
                logger.info("✅ Updated menu to use client action")
            
        else:
            logger.info("✅ Client action already exists")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error fixing dashboard action: {e}")
        return False

def optimize_task_form_view(models, uid):
    """Optimize the task form view to be less intrusive"""
    logger.info("\n" + "=" * 40)
    logger.info("OPTIMIZING TASK FORM VIEW")
    logger.info("=" * 40)
    
    try:
        # Check current task form view customization
        task_form_views = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.ui.view', 'search_read',
            [[('name', '=', 'project.task.form.enhanced')]],
            {'fields': ['id', 'name', 'arch', 'active']}
        )
        
        if task_form_views:
            view = task_form_views[0]
            logger.info(f"Found enhanced task form view: {view['name']}")
            
            # Create a more balanced view that doesn't overwhelm the form
            optimized_arch = '''<?xml version="1.0" encoding="utf-8"?>
<data>
    <xpath expr="//div[@name='button_box']" position="inside">
        <button name="action_view_invoices" type="object"
                class="oe_stat_button" icon="fa-money"
                invisible="billing_type == 'non_billable'">
            <field name="invoice_count" widget="statinfo" string="Invoices"/>
        </button>
    </xpath>
    
    <xpath expr="//field[@name='name']" position="after">
        <field name="deadline" required="0"/>
    </xpath>
    
    <xpath expr="//field[@name='user_ids']" position="after">
        <field name="task_priority" widget="priority"/>
        <field name="billing_type" widget="radio" options="{'horizontal': true}"/>
    </xpath>
    
    <xpath expr="//notebook" position="inside">
        <page string="Billing &amp; Time" name="billing_time" invisible="billing_type == 'non_billable'">
            <group string="Billing Information">
                <field name="billing_amount"/>
                <field name="currency_id"/>
                <field name="linked_service_id" invisible="billing_type != 'annually_billable'"/>
                <field name="auto_generate_invoice" invisible="billing_type != 'billable'"/>
            </group>
            <group string="Time Management">
                <field name="start_time"/>
                <field name="end_time"/>
                <field name="estimated_hours"/>
                <field name="actual_hours" readonly="1"/>
            </group>
        </page>
        <page string="Checklist" name="checklist">
            <field name="checklist_ids" nolabel="1">
                <list editable="bottom">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="is_completed" widget="boolean_toggle"/>
                    <field name="assigned_to"/>
                    <field name="due_date"/>
                </list>
            </field>
            <div class="oe_clear">
                <field name="checklist_progress" widget="progressbar" string="Progress"/>
            </div>
        </page>
    </xpath>
</data>'''
            
            # Update the view with optimized architecture
            models.execute_kw(
                DB, uid, PASSWORD,
                'ir.ui.view', 'write',
                [view['id'], {'arch': optimized_arch}]
            )
            
            logger.info("✅ Optimized task form view")
        else:
            logger.info("ℹ️ Enhanced task form view not found")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error optimizing task form view: {e}")
        return False

def main():
    """Main execution function"""
    logger.info("=" * 60)
    logger.info("TASK MANAGEMENT ISSUES FIX SCRIPT")
    logger.info("=" * 60)
    
    # Connect to Odoo
    common, models, uid = connect_to_odoo()
    if not models:
        logger.error("Failed to connect to Odoo. Exiting.")
        sys.exit(1)
    
    # Analyze current issues
    issues = analyze_task_issues(models, uid)
    
    if not issues:
        logger.info("\n🎉 No issues found! Task management is working correctly.")
        return
    
    logger.info(f"\n📋 Found {len(issues)} issues to fix:")
    for i, issue in enumerate(issues, 1):
        logger.info(f"   {i}. {issue}")
    
    # Apply fixes
    logger.info("\n" + "=" * 60)
    logger.info("APPLYING FIXES")
    logger.info("=" * 60)
    
    fixes_applied = 0
    
    # Fix 1: Deadline field requirement
    if any("deadline" in issue.lower() for issue in issues):
        if fix_deadline_field_requirement(models, uid):
            fixes_applied += 1
    
    # Fix 2: Dashboard action
    if any("dashboard" in issue.lower() for issue in issues):
        if fix_dashboard_action(models, uid):
            fixes_applied += 1
    
    # Fix 3: Optimize task form view
    if optimize_task_form_view(models, uid):
        fixes_applied += 1
    
    # Final summary
    logger.info("\n" + "=" * 60)
    logger.info("FINAL SUMMARY")
    logger.info("=" * 60)
    
    logger.info(f"Issues found: {len(issues)}")
    logger.info(f"Fixes applied: {fixes_applied}")
    
    if fixes_applied > 0:
        logger.info("\n🎉 SUCCESS! Task management issues have been fixed!")
        logger.info("\n📋 What was fixed:")
        logger.info("1. ✅ Deadline field requirement relaxed")
        logger.info("2. ✅ Dashboard action registration fixed")
        logger.info("3. ✅ Task form view optimized for better UX")
        
        logger.info("\n🔄 Next Steps:")
        logger.info("1. Try activating the Task Logs module again")
        logger.info("2. Test the Task Billing Dashboard buttons")
        logger.info("3. Check the task form view for improved layout")
        logger.info("4. Restart Odoo server if needed")
    else:
        logger.warning("\n⚠️ No fixes were applied. Manual intervention may be required.")
    
    logger.info("\nScript completed!")

if __name__ == "__main__":
    main()
