#!/usr/bin/env python3
"""
Check what the demo user can actually do in profectusaccounts.com
"""

import xmlrpc.client

# Working connection details
url = 'http://localhost:8069'
db = 'profectusaccounts.com'
username = 'demo'
password = 'demo'

def connect_to_odoo():
    """Connect to profectusaccounts.com database"""
    try:
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        if not uid:
            print("❌ Authentication failed")
            return None, None
        
        print(f"✅ Connected to profectusaccounts.com as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        return models, uid
        
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return None, None

def check_user_permissions(models, uid):
    """Check what the current user can do"""
    try:
        print("\n🔍 Checking User Permissions...")
        print("-" * 50)
        
        # Get user info
        user_info = models.execute_kw(db, uid, password, 'res.users', 'read',
                                    [uid], {'fields': ['name', 'login', 'groups_id']})
        
        if user_info:
            user = user_info[0]
            print(f"   👤 User: {user['name']} ({user['login']})")
            print(f"   🏷️ Groups: {len(user['groups_id'])} groups")
            
            # Get group names
            if user['groups_id']:
                groups = models.execute_kw(db, uid, password, 'res.groups', 'read',
                                         [user['groups_id']], {'fields': ['name']})
                print("   📋 User Groups:")
                for group in groups:
                    print(f"      • {group['name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking user permissions: {e}")
        return False

def test_model_access(models, uid):
    """Test access to various models"""
    try:
        print("\n🔍 Testing Model Access...")
        print("-" * 50)
        
        # Models to test
        test_models = [
            'res.partner',
            'mail.message',
            'website.visitor', 
            'ir.attachment',
            'mail.activity',
            'project.task',
            'crm.lead',
            'mail.mail',
            'website.form',
            'helpdesk.ticket'
        ]
        
        accessible_models = []
        
        for model in test_models:
            try:
                # Test read access
                count = models.execute_kw(db, uid, password, model, 'search_count', [[]])
                
                # Test create access
                can_create = False
                try:
                    # Try to get fields to see if we can create
                    fields = models.execute_kw(db, uid, password, model, 'fields_get', [], 
                                             {'attributes': ['required', 'readonly']})
                    
                    # Try a minimal create test (this might fail but tells us about permissions)
                    if model == 'res.partner':
                        test_data = {'name': 'Test Contact Form Entry', 'is_company': False}
                    elif model == 'mail.message':
                        test_data = {'body': 'Test message from contact form', 'message_type': 'comment'}
                    elif model == 'website.visitor':
                        test_data = {'access_token': 'test_token_123'}
                    elif model == 'mail.activity':
                        test_data = {'summary': 'Contact form follow-up', 'res_model': 'res.partner'}
                    else:
                        # Skip create test for complex models
                        test_data = None
                    
                    if test_data:
                        try:
                            test_id = models.execute_kw(db, uid, password, model, 'create', [test_data])
                            can_create = True
                            # Clean up test record
                            try:
                                models.execute_kw(db, uid, password, model, 'unlink', [test_id])
                            except:
                                pass
                        except Exception as create_error:
                            if "access" in str(create_error).lower():
                                can_create = False
                            else:
                                # Might be a field validation error, which means we have create access
                                can_create = True
                
                except Exception as field_error:
                    pass
                
                status = "✅ READ" + (" + CREATE" if can_create else " ONLY")
                print(f"   {status} {model}: {count} records")
                
                if can_create:
                    accessible_models.append(model)
                
            except Exception as e:
                print(f"   ❌ NO ACCESS {model}: {str(e)[:100]}...")
        
        return accessible_models
        
    except Exception as e:
        print(f"❌ Error testing model access: {e}")
        return []

def check_website_form_handling(models, uid):
    """Check how website forms are currently handled"""
    try:
        print("\n🔍 Checking Website Form Handling...")
        print("-" * 50)
        
        # Check if there are any existing form submissions
        try:
            # Look for recent website visitors (might contain form data)
            visitors = models.execute_kw(db, uid, password, 'website.visitor', 'search_read',
                                       [[]], {'fields': ['access_token', 'create_date', 'partner_id'], 
                                              'limit': 5, 'order': 'create_date desc'})
            
            print(f"   📊 Recent website visitors: {len(visitors)}")
            for visitor in visitors:
                partner = visitor['partner_id'][1] if visitor['partner_id'] else 'Anonymous'
                print(f"      • {visitor['create_date']} - {partner}")
        
        except Exception as e:
            print(f"   ⚠️ Error checking visitors: {e}")
        
        # Check for any mail messages that might be from forms
        try:
            messages = models.execute_kw(db, uid, password, 'mail.message', 'search_read',
                                       [[('message_type', '=', 'email')]], 
                                       {'fields': ['subject', 'body', 'create_date'], 
                                        'limit': 5, 'order': 'create_date desc'})
            
            print(f"   📧 Recent email messages: {len(messages)}")
            for msg in messages:
                subject = msg['subject'] or 'No subject'
                print(f"      • {msg['create_date']} - {subject}")
        
        except Exception as e:
            print(f"   ⚠️ Error checking messages: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking form handling: {e}")
        return False

def suggest_solution(models, uid, accessible_models):
    """Suggest a solution based on available permissions"""
    try:
        print("\n💡 Suggested Solution...")
        print("-" * 50)
        
        if 'res.partner' in accessible_models:
            print("   ✅ SOLUTION 1: Use res.partner model")
            print("      • Create contact records from form submissions")
            print("      • Store form data in partner notes/comments")
            print("      • Use partner categories to mark as 'Website Inquiries'")
            
        if 'mail.message' in accessible_models:
            print("   ✅ SOLUTION 2: Use mail.message model")
            print("      • Create message records from form submissions")
            print("      • Store form data in message body")
            print("      • Link to a dummy partner or use system user")
            
        if 'mail.activity' in accessible_models:
            print("   ✅ SOLUTION 3: Use mail.activity model")
            print("      • Create activities from form submissions")
            print("      • Assign to admin users for follow-up")
            print("      • Store form data in activity summary/note")
        
        if not accessible_models:
            print("   ⚠️ LIMITED OPTIONS:")
            print("      • Demo user has very limited create permissions")
            print("      • May need to use existing website form infrastructure")
            print("      • Consider updating form to use available endpoints")
        
        # Check if we can at least update views
        try:
            # Test if we can read view structure
            views = models.execute_kw(db, uid, password, 'ir.ui.view', 'search_read',
                                    [[('key', '=', 'website.contactus')]], 
                                    {'fields': ['id', 'name'], 'limit': 1})
            
            if views:
                print("   📝 VIEW UPDATE OPTION:")
                print("      • Can read contact form views")
                print("      • May be able to suggest view modifications")
                print("      • Admin access needed for actual updates")
        
        except Exception as e:
            print(f"   ⚠️ View access limited: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error suggesting solution: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Checking Demo User Permissions in profectusaccounts.com")
    print("=" * 60)
    
    # Connect to database
    models, uid = connect_to_odoo()
    if not models:
        return False
    
    # Check user permissions
    check_user_permissions(models, uid)
    
    # Test model access
    accessible_models = test_model_access(models, uid)
    
    # Check current form handling
    check_website_form_handling(models, uid)
    
    # Suggest solution
    suggest_solution(models, uid, accessible_models)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 PERMISSION ANALYSIS SUMMARY")
    print("=" * 60)
    print(f"✅ Models with CREATE access: {len(accessible_models)}")
    for model in accessible_models:
        print(f"   • {model}")
    
    print("\n🎯 NEXT STEPS:")
    if accessible_models:
        print("1. Choose the most appropriate model for form storage")
        print("2. Create a form handling solution using available models")
        print("3. Test form submission with the chosen approach")
        print("4. Set up email notifications if possible")
    else:
        print("1. Contact administrator for proper user permissions")
        print("2. Request access to form handling models")
        print("3. Consider using existing Odoo form infrastructure")
        print("4. Test with admin user credentials")

if __name__ == "__main__":
    main()
