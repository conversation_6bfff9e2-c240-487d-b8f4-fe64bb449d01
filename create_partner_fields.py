#!/usr/bin/env python3
"""
Create Partner Fields Script
This script directly creates the missing partner fields in the database
"""

import xmlrpc.client
import time
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

# Partner fields to create
PARTNER_FIELDS = [
    {
        'name': 'x_service_ids',
        'field_description': 'Services',
        'ttype': 'one2many',
        'relation': 'x_services',
        'relation_field': 'x_partner_id',
        'model_id': 'res.partner'
    },
    {
        'name': 'x_client_document_ids',
        'field_description': 'Client Documents',
        'ttype': 'one2many',
        'relation': 'x_client_documents',
        'relation_field': 'x_client',
        'model_id': 'res.partner'
    },
    {
        'name': 'x_credential_ids',
        'field_description': 'Credentials',
        'ttype': 'one2many',
        'relation': 'x_credentials',
        'relation_field': 'x_partner_id',
        'model_id': 'res.partner'
    },
    {
        'name': 'x_dsc_ids',
        'field_description': 'DSC Management',
        'ttype': 'one2many',
        'relation': 'x_dsc',
        'relation_field': 'x_manager',
        'model_id': 'res.partner'
    }
]

def connect_to_odoo():
    """Connect to Odoo"""
    try:
        logger.info(f"Connecting to Odoo at {URL}")
        common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
        models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
        
        uid = common.authenticate(DB, USERNAME, PASSWORD, {})
        if not uid:
            raise Exception("Authentication failed")
            
        logger.info(f"Successfully connected as user ID: {uid}")
        return common, models, uid
        
    except Exception as e:
        logger.error(f"Connection failed: {e}")
        return None, None, None

def get_model_id(models, uid, model_name):
    """Get the ID of a model by name"""
    try:
        model_ids = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.model', 'search',
            [[('model', '=', model_name)]]
        )
        
        if not model_ids:
            logger.error(f"Model {model_name} not found")
            return False
        
        return model_ids[0]
        
    except Exception as e:
        logger.error(f"Error getting model ID for {model_name}: {e}")
        return False

def create_partner_field(models, uid, field_info):
    """Create a field on res.partner model"""
    try:
        # Get the model ID
        model_id = get_model_id(models, uid, field_info['model_id'])
        if not model_id:
            return False
        
        # Check if field already exists
        field_domain = [
            ('model_id', '=', model_id),
            ('name', '=', field_info['name'])
        ]
        
        existing_fields = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.model.fields', 'search',
            [field_domain]
        )
        
        if existing_fields:
            logger.info(f"Field {field_info['name']} already exists")
            return True
        
        # Get relation model ID if needed
        if field_info['ttype'] in ['many2one', 'one2many', 'many2many']:
            relation_model_id = get_model_id(models, uid, field_info['relation'])
            if not relation_model_id:
                return False
        
        # Prepare field values
        field_values = {
            'model_id': model_id,
            'name': field_info['name'],
            'field_description': field_info['field_description'],
            'ttype': field_info['ttype'],
            'state': 'manual'
        }
        
        # Add relation fields if needed
        if field_info['ttype'] in ['many2one', 'one2many', 'many2many']:
            field_values['relation'] = field_info['relation']
        
        if field_info['ttype'] == 'one2many' and 'relation_field' in field_info:
            field_values['relation_field'] = field_info['relation_field']
        
        # Create the field
        field_id = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.model.fields', 'create',
            [field_values]
        )
        
        logger.info(f"✅ Created field {field_info['name']} with ID {field_id}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating field {field_info['name']}: {e}")
        return False

def test_partner_fields(models, uid):
    """Test if partner fields exist"""
    logger.info("Testing partner field access...")
    
    fields_to_test = [
        'x_group_id',
        'x_service_ids',
        'x_client_document_ids', 
        'x_credential_ids',
        'x_dsc_ids'
    ]
    
    results = {}
    
    try:
        # Get partner fields
        partner_fields = models.execute_kw(
            DB, uid, PASSWORD,
            'res.partner', 'fields_get',
            [], {'attributes': ['string', 'relation']}
        )
        
        for field_name in fields_to_test:
            if field_name in partner_fields:
                field_info = partner_fields[field_name]
                logger.info(f"✅ Field {field_name} exists: {field_info.get('string', 'No description')}")
                results[field_name] = True
            else:
                logger.warning(f"❌ Field {field_name} does not exist in res.partner")
                results[field_name] = False
                
    except Exception as e:
        logger.error(f"❌ Error testing partner fields: {e}")
        
    return results

def upgrade_modules(models, uid):
    """Upgrade all IMCA modules"""
    logger.info("Upgrading IMCA modules...")
    
    modules = [
        'imca_groups',
        'imca_services', 
        'imca_client_documents',
        'imca_crednetials_manager',
        'imca_dsc_management'
    ]
    
    results = {}
    
    for module in modules:
        try:
            logger.info(f"Upgrading module: {module}")
            
            # Search for the module
            module_ids = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.module.module', 'search',
                [[('name', '=', module)]]
            )
            
            if not module_ids:
                logger.error(f"Module {module} not found")
                results[module] = False
                continue
            
            module_id = module_ids[0]
            
            # Get current state
            module_data = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.module.module', 'read',
                [module_id], {'fields': ['state']}
            )[0]
            
            current_state = module_data['state']
            logger.info(f"Module {module} current state: {current_state}")
            
            if current_state == 'installed':
                # Upgrade the module
                try:
                    result = models.execute_kw(
                        DB, uid, PASSWORD,
                        'ir.module.module', 'button_immediate_upgrade',
                        [[module_id]]
                    )
                    logger.info(f"✅ Upgrade initiated for {module}")
                    results[module] = True
                except Exception as e:
                    logger.error(f"❌ Error during upgrade of {module}: {e}")
                    results[module] = False
            else:
                logger.warning(f"Module {module} not in installed state: {current_state}")
                results[module] = False
                
            # Wait between upgrades
            time.sleep(3)
                
        except Exception as e:
            logger.error(f"❌ Error upgrading {module}: {e}")
            results[module] = False
    
    return results

def check_partner_views(models, uid):
    """Check if partner views are properly loaded"""
    logger.info("Checking partner views...")
    
    view_ids_to_check = [
        'view_partner_form_services',
        'view_partner_form_client_documents',
        'view_partner_form_credentials',
        'view_partner_form_dsc'
    ]
    
    results = {}
    
    try:
        for view_id in view_ids_to_check:
            try:
                # Search for the view
                views = models.execute_kw(
                    DB, uid, PASSWORD,
                    'ir.ui.view', 'search_read',
                    [[('name', '=', view_id)]],
                    {'fields': ['name', 'model', 'active']}
                )
                
                if views:
                    view = views[0]
                    logger.info(f"✅ View {view_id} found: {view}")
                    results[view_id] = True
                else:
                    logger.warning(f"❌ View {view_id} not found")
                    results[view_id] = False
                    
            except Exception as e:
                logger.error(f"❌ Error checking view {view_id}: {e}")
                results[view_id] = False
                
    except Exception as e:
        logger.error(f"❌ Error checking partner views: {e}")
        
    return results

def main():
    """Main execution function"""
    logger.info("=" * 60)
    logger.info("CREATE PARTNER FIELDS SCRIPT")
    logger.info("=" * 60)
    
    # Connect to Odoo
    common, models, uid = connect_to_odoo()
    if not models:
        logger.error("Failed to connect to Odoo. Exiting.")
        sys.exit(1)
    
    # Test partner fields before
    logger.info("\n" + "=" * 40)
    logger.info("TESTING PARTNER FIELDS BEFORE")
    logger.info("=" * 40)
    
    before_results = test_partner_fields(models, uid)
    
    # Create partner fields
    logger.info("\n" + "=" * 40)
    logger.info("CREATING PARTNER FIELDS")
    logger.info("=" * 40)
    
    field_creation_results = {}
    for field_info in PARTNER_FIELDS:
        field_creation_results[field_info['name']] = create_partner_field(models, uid, field_info)
    
    # Test partner fields after
    logger.info("\n" + "=" * 40)
    logger.info("TESTING PARTNER FIELDS AFTER")
    logger.info("=" * 40)
    
    after_results = test_partner_fields(models, uid)
    
    # Upgrade modules
    logger.info("\n" + "=" * 40)
    logger.info("UPGRADING MODULES")
    logger.info("=" * 40)
    
    upgrade_results = upgrade_modules(models, uid)
    
    # Wait for upgrades to complete
    logger.info("\n⏳ Waiting 15 seconds for upgrades to complete...")
    time.sleep(15)
    
    # Check partner views
    logger.info("\n" + "=" * 40)
    logger.info("CHECKING PARTNER VIEWS")
    logger.info("=" * 40)
    
    view_results = check_partner_views(models, uid)
    
    # Final summary
    logger.info("\n" + "=" * 60)
    logger.info("FINAL SUMMARY")
    logger.info("=" * 60)
    
    logger.info("Partner Fields Before:")
    for field, exists in before_results.items():
        status = "✅ EXISTS" if exists else "❌ MISSING"
        logger.info(f"  {field}: {status}")
    
    logger.info("\nField Creation Results:")
    for field, success in field_creation_results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        logger.info(f"  {field}: {status}")
    
    logger.info("\nPartner Fields After:")
    for field, exists in after_results.items():
        status = "✅ EXISTS" if exists else "❌ MISSING"
        logger.info(f"  {field}: {status}")
    
    logger.info("\nModule Upgrade Results:")
    for module, success in upgrade_results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        logger.info(f"  {module}: {status}")
    
    logger.info("\nPartner Views Test:")
    for view, exists in view_results.items():
        status = "✅ EXISTS" if exists else "❌ MISSING"
        logger.info(f"  {view}: {status}")
    
    # Calculate success rates
    successful_fields_after = sum(after_results.values())
    successful_views = sum(view_results.values())
    successful_upgrades = sum(upgrade_results.values())
    
    logger.info(f"\nOverall Results:")
    logger.info(f"  Partner fields: {successful_fields_after}/{len(after_results)}")
    logger.info(f"  Partner views: {successful_views}/{len(view_results)}")
    logger.info(f"  Module upgrades: {successful_upgrades}/{len(upgrade_results)}")
    
    if successful_fields_after == len(after_results) and successful_views == len(view_results):
        logger.info("\n🎉 SUCCESS! All partner fields and views are now available!")
        logger.info("\n📋 Next Steps:")
        logger.info("1. Open a partner record in Odoo")
        logger.info("2. Check for new tabs: Groups, Services, Documents, Credentials, DSC Management")
        logger.info("3. Verify the CA Management menu structure")
    else:
        logger.warning(f"\n⚠️  Partial success: {successful_fields_after}/{len(after_results)} fields, {successful_views}/{len(view_results)} views")
    
    logger.info("\nScript completed!")

if __name__ == "__main__":
    main()
