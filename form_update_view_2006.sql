-- Update contact form view Contact Us (ID: 2006)
-- This changes the form to use res.partner instead of mail.mail

UPDATE ir_ui_view SET arch_db = '<t name="Contact Us" t-name="website.contactus">
        <t t-call="website.layout">
            <t t-set="logged_partner" t-value="request.env[''website.visitor'']._get_visitor_from_request().partner_id"/>
            <t t-set="contactus_form_values" t-value="{                 ''email_to'': res_company.email,                 ''name'': request.params.get(''name'', ''''),                 ''phone'': request.params.get(''phone'', ''''),                 ''email_from'': request.params.get(''email_from'', ''''),                 ''company'': request.params.get(''company'', ''''),                 ''subject'': request.params.get(''subject'', ''''),             }"/>
            <span class="hidden" data-for="contactus_form" t-att-data-values="contactus_form_values"/>
            <div id="wrap" class="oe_structure oe_empty"><section class="s_kickoff o_cc o_cc5 s_parallax_no_overflow_hidden pt232 pb88 o_colored_level oe_img_bg o_bg_img_center" data-scroll-background-ratio="0" data-oe-shape-data="{&quot;shape&quot;:&quot;web_editor/Origins/14_001&quot;,&quot;colors&quot;:{&quot;c3&quot;:&quot;o-color-4&quot;,&quot;c4&quot;:&quot;rgba(255, 0, 0, 0)&quot;},&quot;flip&quot;:[],&quot;showOnMobile&quot;:true,&quot;shapeAnimationSpeed&quot;:&quot;0&quot;}" data-snippet="s_kickoff" data-name="Kickoff" style="background-image: url(&quot;/web/image/1030-1e30228d/two-confident-business-man-shaking-hands-meeting-office-success-dealing-greeting-partner-concept-min.webp&quot;); background-position: 50% 37.1595%;" data-mimetype="image/webp" data-original-id="1029" data-original-src="/web/image/1029-58115f04/two-confident-business-man-shaking-hands-meeting-office-success-dealing-greeting-partner-concept-min.jpg" data-mimetype-before-conversion="image/jpeg" data-resize-width="1920">
        
        <div class="o_we_bg_filter bg-black-50"/>
        <div class="o_we_shape o_web_editor_Origins_14_001 o_shape_show_mobile" style="background-image: url(&quot;/web_editor/shape/web_editor%2FOrigins%2F14_001.svg?c3=o-color-4&amp;c4=rgba(255%2C%200%2C%200%2C%200)&quot;);"/>
        <div class="container">
            <p class="lead">Let''s Connect,</p>
            <h1 class="display-1">Contact Us</h1>
        </div>
        
    </section>
                
                <section class="s_text_block pt40 pb40 o_colored_level" data-snippet="s_text_block" data-name="Text">
                    <div class="s_allow_columns container">
                        <div class="row">
                            <div class="col-lg-7 mt-4 mt-lg-0 o_colored_level">
                                <p>Reach out to us, and our team will provide the right solutions tailored to your business needs. Fill out the form, and we’ll get back to you promptly.&amp;nbsp;<br/></p><section class="s_website_form" data-vcss="001" data-snippet="s_website_form" data-name="Form">
                                    <div class="container">
                                        <form id="contactus_form" action="/website/form/" method="post" enctype="multipart/form-data" class="o_mark_required" data-mark="*" data-model_name="res.partner" data-success-mode="redirect" data-success-page="/contactus-thank-you" data-pre-fill="true">
                                            <div class="s_website_form_rows row s_col_no_bgcolor">
                                                <div class="mb-3 col-lg-6 s_website_form_field s_website_form_custom s_website_form_required" data-type="char" data-name="Field">
                                                    <label class="s_website_form_label" style="width: 200px" for="contact1">
                                                        <span class="s_website_form_label_content">Name</span>
                                                        <span class="s_website_form_mark"> *</span>
                                                    </label>
                                                    <input id="contact1" type="text" class="form-control s_website_form_input" name="name" required="" data-fill-with="name"/>
                                                </div>
                                                <div class="mb-3 col-lg-6 s_website_form_field s_website_form_custom" data-type="char" data-name="Field">
                                                    <label class="s_website_form_label" style="width: 200px" for="contact2">
                                                        <span class="s_website_form_label_content">Phone Number</span>
                                                    </label>
                                                    <input id="contact2" type="tel" class="form-control s_website_form_input" name="phone" data-fill-with="phone"/>
                                                </div>
                                                <div data-name="Field" class="s_website_form_field mb-3 col-lg-6 s_website_form_required" data-type="email"><label class="s_website_form_label" style="width: 200px" for="op4r8yjieqx"><span class="s_website_form_label_content">Email</span><span class="s_website_form_mark">       *</span></label><input class="form-control s_website_form_input" type="email" name="email" required="1" placeholder="<EMAIL>" id="op4r8yjieqx" data-fill-with="email"/></div>
                                                <div class="mb-3 col-lg-6 s_website_form_field s_website_form_custom" data-type="char" data-name="Field">
                                                    <label class="s_website_form_label" style="width: 200px" for="contact4">
                                                        <span class="s_website_form_label_content">Company</span>
                                                    </label>
                                                    <input id="contact4" type="text" class="form-control s_website_form_input" name="parent_name" data-fill-with="commercial_company_name"/>
                                                </div>
                                                <div class="mb-3 col-12 s_website_form_field s_website_form_required s_website_form_model_required" data-type="char" data-name="Field">
                                                    <label class="s_website_form_label" style="width: 200px" for="contact5">
                                                        <span class="s_website_form_label_content">Subject</span>
                                                        <span class="s_website_form_mark"> *</span>
                                                    </label>
                                                    <input id="contact5" type="text" class="form-control s_website_form_input" name="subject" required=""/>
                                                </div>
                                                <div data-name="Field" class="s_website_form_field mb-3 col-12 s_website_form_custom s_website_form_required" data-type="text"><label class="s_website_form_label" style="width: 200px" for="o8xaeuma1bim"><span class="s_website_form_label_content">Message</span><span class="s_website_form_mark">         *</span></label><textarea class="form-control s_website_form_input" name="comment" required="1" placeholder="" id="o8xaeuma1bim" rows="8"/></div>
                                                
                                                <div data-name="Field" class="s_website_form_field mb-3 col-12 s_website_form_dnone"><div class="row s_col_no_resize s_col_no_bgcolor"><label class="col-form-label col-sm-auto s_website_form_label" style="width: 200px"><span class="s_website_form_label_content"/></label><div class="col-sm"><input type="hidden" class="form-control s_website_form_input" name="function" value="<EMAIL>"/><input type="hidden" value="e3660681a8f89b76a5c8014d3656275e12b55b8327514b407ab888dc4ba31769" class="form-control s_website_form_input s_website_form_custom" name="website_form_signature"/></div></div></div><div class="mb-0 py-2 col-12 s_website_form_submit text-start s_website_form_no_submit_label" data-name="Submit Button">
                                                    <div style="width: 200px;" class="s_website_form_label"/>
                                                    <a href="#" role="button" class="s_website_form_send btn btn-primary">Submit</a>
                                                    <span id="s_website_form_result"/>
                                                </div>
                                            </div>
                                        </form>
                                        
                                        
                                        
                                        <style>
    .error-message {
        color: red;
        font-size: 12px;
        margin-top: 3px;
        display: none;
    }
</style>
                                    </div>
                                </section>
                            </div>
                            <div class="col-lg-4 offset-lg-1 mt-4 mt-lg-0 o_colored_level">
                                <h5>Profectus Accounts</h5>
                                <ul class="list-unstyled mb-0 ps-2">
                                    <li style="text-align: left;"><i class="fa fa-map-marker fa-fw me-2"/><span class="o_force_ltr">230-231,Sangath mall-1, Sarkhej - Gandhinagar Hwy, Opp. Vishwakarma eng. College Sabarmati, Motera, Ahmedabad, Gujarat 380005</span></li>
                                    <li><i class="fa fa-phone fa-fw me-2"/><span class="o_force_ltr">+91 94271 28563</span></li>
                                    <li><i class="fa fa-1x fa-fw fa-envelope me-2"/><a href="mailto:<EMAIL>"><EMAIL></a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                <script>
document.addEventListener("DOMContentLoaded", function () {
    const form = document.getElementById("contactus_form");
    const submitBtn = document.querySelector(".s_website_form_send");

    if (!form || !submitBtn) return;

    const fields = {
        name: {
            element: form.querySelector(''[name="name"]''),
            pattern: /^[a-zA-Z\s]+$/,
            message: "Only A-Z, a-z characters allowed"
        },
        phone: {
            element: form.querySelector(''[name="phone"]''),
            pattern: /^[0-9]{10,15}$/,
            message: "Enter 10 to 15 digit numbers only"
        },
        email: {
            element: form.querySelector(''[name="email"]''),
            pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            message: "Enter a valid email address (e.g. <EMAIL>)"
        },
        company: {
            element: form.querySelector(''[name="parent_name"]''),
            pattern: /^[a-zA-Z\s]*$/,
            message: "Only A-Z, a-z characters allowed"
        },
        subject: {
            element: form.querySelector(''[name="subject"]''),
            pattern: /^[a-zA-Z0-9\s.,!?''"-]+$/,
            message: "Only letters, numbers and basic symbols allowed"
        },
        message: {
            element: form.querySelector(''[name="comment"]''),
            pattern: /.+/,
            message: "This field is required"
        }
    };

    function setError(fieldObj, message = '''') {
        const el = fieldObj.element;
        let errorEl = el.nextElementSibling;
        if (!errorEl || !errorEl.classList.contains("error-message")) {
            errorEl = document.createElement("div");
            errorEl.classList.add("error-message");
            el.parentNode.insertBefore(errorEl, el.nextSibling);
        }
        errorEl.textContent = message;
        errorEl.style.display = message ? "block" : "none";
        el.style.borderColor = message ? "red" : "#ccc";
    }

    submitBtn.addEventListener("click", function (e) {
        e.preventDefault();
        let isValid = true;

        Object.keys(fields).forEach(key =&gt; {
            const element = fields[key].element;
            const pattern = fields[key].pattern;
            const message = fields[key].message;
            const value = element.value.trim();
            const required = key !== "company"; // optional

            if ((required &amp;&amp; !value) || (value &amp;&amp; pattern &amp;&amp; !pattern.test(value))) {
                setError(fields[key], message);
                isValid = false;
            } else {
                setError(fields[key], '''');
            }
        });

        if (isValid) {
            form.submit();
        } else {
            alert("Please fix the highlighted fields before submitting.");
        }
    });
});
</script>

    

                    
                    
                    
                    
                </section>
            </div>
            
        </t>
    </t>' WHERE id = 2006;
