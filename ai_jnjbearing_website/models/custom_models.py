# -*- coding: utf-8 -*-
from odoo import models, fields, api
from odoo.addons.http_routing.models.ir_http import slug, unslug
import logging

_logger = logging.getLogger(__name__)

class x_category_headers(models.Model):
    _name = 'ai_jnjbearing_website.model_x_category_headers'
    _description = 'Category Headers'

    x_category_id = fields.Many2one('product.public.category', string='Category', store=True)    
    x_category_parent_header_id = fields.Many2one('ai_jnjbearing_website.model_x_category_parent_headers', string='Category Parent Header', store=True)
    x_is_searchable = fields.Boolean(string='Is Searchable in Website?', store=True)
    x_label = fields.Char(string='Label', store=True)
    x_name = fields.Char(string='Name', store=True)
    x_search_type = fields.Selection([
        ('search', 'Search'),
        ('number_filter', 'Number Filter'),
        ('checkbox_filter', 'Checkbox Filter')
    ], string='Search Type', store=True)
    x_sequence = fields.Integer(string='Sequence', store=True)
    x_visible_in_table = fields.Boolean(string='Visible in Table (On Website)?', store=True)

class x_category_parent_headers(models.Model):
    _name = 'ai_jnjbearing_website.model_x_category_parent_headers'
    _description = 'Category Parent Headers'
    
    x_category_headers = fields.One2many(
        'ai_jnjbearing_website.model_x_category_headers',
        'x_category_parent_header_id',  # This should match the field name in x_category_headers
        string='Category Headers',
        store=True
    )
    x_category_id = fields.Many2one('product.public.category', string='Category', store=True)
    x_name = fields.Char(string='Name', store=True)
    x_sequence = fields.Integer(string='Sequence', store=True)

class x_data(models.Model):
    _name = 'ai_jnjbearing_website.model_x_data'
    _description = 'Data Modelling'

    x_data = fields.Text(string='Data', store=True)
    x_data_file = fields.Binary(string='Data File', store=True)
    x_data_mapping = fields.Text(string='Data Mapping', store=True)
    x_json = fields.Text(string='JSON Data', store=True)
    x_model_id = fields.Many2one(string='Model', store=True, relation='ir.model')
    x_name = fields.Char(string='Name', store=True)
    x_resolve_relations = fields.Boolean(string='Resolve Relations?', store=True)
    x_skip_keys = fields.Char(string='Skip Keys (Comma Separated)', store=True)

class x_documents(models.Model):
    _name = 'ai_jnjbearing_website.model_x_documents'
    _description = 'Documents'

    x_code = fields.Text(string='Code', store=True)
    x_model = fields.Many2one(string='Model', store=True, relation='ir.model')
    x_name = fields.Char(string='Name', store=True)
    x_print_name = fields.Char(string='Print Name', store=True)
    x_report = fields.Many2one(string='Report', store=True, relation='ir.actions.report')
    x_template_name = fields.Char(string='Template Name', store=True)
    x_view = fields.Many2one(string='View', store=True, relation='ir.ui.view')
    x_view_name = fields.Char(string='View Name', store=True)

class x_enquiry(models.Model):
    _name = 'ai_jnjbearing_website.model_x_enquiry'
    _description = 'Enquiry'

    x_address = fields.Text(string='Address', store=True)
    x_city = fields.Char(string='City', store=True)
    x_company_name = fields.Char(string='Company Name', store=True)
    x_country = fields.Char(string='Country', store=True)
    x_email = fields.Char(string='Email', store=True)
    x_last_name = fields.Char(string='Last Name', store=True)
    x_message = fields.Text(string='Message', store=True)
    x_name = fields.Char(string='Name', store=True)
    x_phone = fields.Integer(string='Phone no.', store=True)
    x_state = fields.Char(string='State', store=True)
    x_subject = fields.Char(string='Subject', store=True)

class x_generate_barcode_batch(models.Model):
    _name = 'ai_jnjbearing_website.model_x_generate_barcode_batch'
    _description = 'Generate Barcode Batch'

    x_csv_file = fields.Binary(string='CSV File', store=True)
    x_generated_barcodes = fields.One2many(
        'ai_jnjbearing_website.model_x_generated_barcodes',
        'x_generate_barcode_batch_id',  # This should match the field name in x_generated_barcodes
        string='Generated Barcodes',
        store=True
    )
    x_inspec_by = fields.Char(string='Inspection By', store=True)
    x_name = fields.Char(string='Name', store=True)
    x_number = fields.Integer(string='Number of Products', store=True)
    x_product_id = fields.Many2one('product.template', string='Select Product', store=True)

class x_generated_barcodes(models.Model):
    _name = 'ai_jnjbearing_website.model_x_generated_barcodes'
    _description = 'Generated Barcodes'

    x_generate_barcode_batch_id = fields.Many2one('ai_jnjbearing_website.model_x_generate_barcode_batch', string='Generate Barcode Batch Ref', store=True)
    x_msg = fields.Boolean(string='Message', store=True)
    x_name = fields.Char(string='Name', store=True)
    x_no_of_visits = fields.Integer(string='No Of Visits', store=True)
    x_product_code = fields.Char(string='Product Code', store=True)
    x_product_id = fields.Many2one('product.template', string='Product Ref', readonly=True, store=True)
    x_token = fields.Char(string='Token', store=True)


class x_product_enquiry(models.Model):
    _name = 'ai_jnjbearing_website.model_x_product_enquiry'
    _description = 'Product Enquiry'

    x_company_name = fields.Char(string='Company Nmae', store=True)
    x_email = fields.Char(string='Email', store=True)
    x_name = fields.Char(string='Name', store=True)
    x_phone = fields.Char(string='Phone No', store=True)
    x_qty = fields.Integer(string='Quantity', store=True)

class Product_template(models.Model):
    _inherit = 'product.template'

    
    x_website_url = fields.Char(string='Website URL', compute='_compute_x_website_url', store=True)
    
    @api.depends('name')
    def _compute_x_website_url(self):
        for product in self:
            if product.id:
                # _logger.info(f"Product ID: {"/shop/%s" % slug(product)}")
                product.x_website_url = "/shop/%s" % slug(product)
                
    @api.model
    def create(self, vals):
        product = super(Product_template, self).create(vals)
        product.x_website_url = "/shop/%s" % slug(product)
        return product
    
    x_description = fields.Text(string='Description', store=True)
    x_adapter_sleeve = fields.Char(string='Adapter Sleeve', store=True)
    x_appro_bearing = fields.Char(string='Appropriate Bearing', store=True)
    x_bearing_adapter_sleeve_designation = fields.Char(string='Bearing And Adapter Sleeve Designation', store=True)
    x_bearing_seating_Ca = fields.Integer(string='Ca', store=True)
    x_bearing_seating_Da = fields.Integer(string='Da', store=True)
    x_bearing_seating_db = fields.Integer(string='db', store=True)
    x_felt_strip = fields.Char(string='Felt Strip', store=True)
    x_housing_desi = fields.Char(string='Housing Designation', store=True)
    x_housing_dimensions_A = fields.Integer(string='A', store=True)
    x_housing_dimensions_A1 = fields.Integer(string='A1', store=True)
    x_housing_dimensions_A2 = fields.Integer(string='A2', store=True)
    x_housing_dimensions_A3 = fields.Integer(string='A3', store=True)
    x_housing_dimensions_B = fields.Char(string='B', store=True)
    x_housing_dimensions_Ca = fields.Integer(string='Ca', store=True)
    x_housing_dimensions_D = fields.Char(string='D', store=True)
    x_housing_dimensions_Da = fields.Integer(string='Da', store=True)
    x_housing_dimensions_E = fields.Char(string='E', store=True)
    x_housing_dimensions_G = fields.Float(string='G', store=True)
    x_housing_dimensions_H = fields.Integer(string='H', store=True)
    x_housing_dimensions_H1 = fields.Integer(string='H1', store=True)
    x_housing_dimensions_H2 = fields.Integer(string='H2', store=True)
    x_housing_dimensions_I = fields.Integer(string='Housing Dimension I', store=True)
    x_housing_dimensions_J = fields.Integer(string='J', store=True)
    x_housing_dimensions_J1 = fields.Integer(string='J1', store=True)
    x_housing_dimensions_L = fields.Integer(string='L', store=True)
    x_housing_dimensions_N = fields.Integer(string='N', store=True)
    x_housing_dimensions_N1 = fields.Integer(string='N1', store=True)
    x_housing_dimensions_S = fields.Char(string='S', store=True)
    x_housing_dimensions_V = fields.Char(string='V', store=True)
    x_housing_dimensions_a = fields.Integer(string='Housing Dimension a', store=True)
    x_housing_dimensions_b = fields.Integer(string='Housing Dimension b', store=True)
    x_housing_dimensions_c = fields.Integer(string='Housing Dimension c', store=True)
    x_housing_dimensions_h = fields.Integer(string='Housing Dimension h', store=True)
    x_housing_dimensions_m = fields.Integer(string='Housing Dimension m', store=True)
    x_housing_dimensions_n = fields.Integer(string='Housing Dimension n', store=True)
    x_housing_dimensions_s = fields.Integer(string='Housing Dimension s', store=True)
    x_housing_dimensions_s_sd3300 = fields.Char(string='s 3300', store=True)
    x_housing_dimensions_u = fields.Integer(string='Housing Dimension u', store=True)
    x_housing_dimensions_v = fields.Integer(string='Housing Dimension v', store=True)
    x_housing_without_seals = fields.Char(string='Housing Without Seals', store=True)
    x_qty = fields.Char(string='Qty', store=True)
    x_rings_no_desg = fields.Char(string='Locating rings No. & Designations', store=True)
    x_rubber_orings = fields.Integer(string='2 Rubber O-rings', store=True)
    x_shaft_dia_in = fields.Float(string='Shaft Dia In', store=True)
    x_shift_dia_D = fields.Integer(string='Shaft Dia D', store=True)
    x_shift_dia_d1 = fields.Integer(string='Shaft DIA d1', store=True)
    x_shift_dia_da = fields.Integer(string='Shift Dia da', store=True)
    x_shift_dia_db = fields.Integer(string='Shift Dia db', store=True)
    x_shift_dia_mm = fields.Integer(string='Shift Dia mm', store=True)
    x_width_across_seal_A2 = fields.Integer(string='A2 Width Across', store=True)
    x_width_across_seal_A3 = fields.Integer(string='A3 Width ACross', store=True)
    x_width_across_seal_Da = fields.Integer(string='Da', store=True)
    x_without_seal_housing = fields.Char(string='Housing Designation Without Seals', store=True)
    x_without_seal_housing_des = fields.Char(string='Designation Without Seals', store=True)

# class Product_product(models.Model):
#     _inherit = 'product.product'

class Product_public_category(models.Model):
    _inherit = 'product.public.category'

    x_category_description = fields.Html(string='Category Description', store=True)
    x_diagrams = fields.Many2many(
        'ir.attachment',  
        'category_attachment_rel',  # Relation table name
        'category_id',             # Column 1 for this record's id
        'attachment_id',           # Column 2 for the attachment id
        string='Diagrams',
        store=True
    )
    x_product_image = fields.Binary(string='Product Image', store=True)

class Account_analytic_line(models.Model):
    _inherit = 'account.analytic.line'

    x_plan2_id = fields.Many2one(string='Departments', store=True, relation='account.analytic.account')
    x_plan3_id = fields.Many2one(string='Internal', store=True, relation='account.analytic.account')
