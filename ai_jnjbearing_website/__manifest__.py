# -*- coding: utf-8 -*-
{
    'name': 'ai_jnjbearing_website',
    'version': '18.0.1.1',
    'author': 'Arihant AI',
    'depends': [
        'base',
        'website',
        'product',  # Added for product.template model
        'website_sale',  # Added for e-commerce features
        'analytic',  # Added for analytic line views
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/custom_views.xml',
        'views/webmenu.xml',
        'views/menu.xml',
        # 'views/website/menu.xml',
        # Temporarily comment out large files
        # 'views/website/pages_1.xml',
        # 'views/website/pages_2.xml',
        'views/pages_3.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            # Add your CSS/JS files here
        ],
        'web.assets_backend': [
            # Add your backend CSS/JS files here
        ],
    },
    'images': [
        'static/description/icon.png',
    ],
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
}