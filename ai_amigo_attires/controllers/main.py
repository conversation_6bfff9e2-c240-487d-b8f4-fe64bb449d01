from odoo import http
from odoo.http import request, Response
from odoo.addons.website.controllers.main import Website
# Removed import for slug as it's not available in Odoo 18
import json
import re

class AmigoAttiresWebsite(Website):
    def slug(self, record):
        """Custom implementation of slug function for Odoo 18
        Converts a record to a URL-friendly slug string
        """
        if not record:
            return ''
        # Get the name or display_name of the record
        name = getattr(record, 'name', False) or getattr(record, 'display_name', '')
        # Convert to string and clean it
        name = str(name).lower().strip()
        # Replace non-alphanumeric characters with hyphens
        name = re.sub(r'[^\w\s-]', '', name)
        # Replace whitespace with hyphens
        name = re.sub(r'[\s_]+', '-', name)
        # Add record ID for uniqueness
        return f"{name}-{record.id}"


    def _get_image_url(self, category):
        """Helper method to get category image URL"""
        # We can uncomment this if we need the full URL in the future
        # base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
        if category.image_1920:
            return f"web/image/product.public.category/{category.id}/image_1920"
            # return f"{base_url}/web/image/product.public.category/{category.id}/image_1920"
        return None

    def _prepare_category_data(self, category):
        """Helper method to prepare category data"""
        # base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
        category_url = f"shop/category/{self.slug(category)}"  # Using our custom slug method
        # category_url = f"{base_url}/shop/category/{self.slug(category)}"
        return {
            'id': category.id,
            'name': category.name,
            'parent_id': category.parent_id.id if category.parent_id else None,
            'image_url': self._get_image_url(category),
            'url': category_url  # Add the category URL
        }
    def _find_gender_categories(self):
        """Find Men and Women categories"""
        Category = request.env['product.public.category'].sudo()
        men_category = Category.search([('name', 'ilike', 'men'), ('parent_id', '=', False)], limit=1)
        women_category = Category.search([('name', 'ilike', 'women'), ('parent_id', '=', False)], limit=1)
        return men_category, women_category

    @http.route('/api/v3/categories', type='http', auth='public', methods=['GET'], csrf=False)
    def list_categories(self, **kwargs):
        try:
            Category = request.env['product.public.category'].sudo()

            parent_id = None
            if kwargs.get('parent_id'):
                parent_id = kwargs.get('parent_id')

            # Get main categories (no parent)
            main_categories = Category.search([('parent_id', '=', False)], limit=10)
            main_category_list = [self._prepare_category_data(category) for category in main_categories]

            # Find Men and Women categories
            men_category, women_category = self._find_gender_categories()

            # Get subcategories based on parent_id parameter
            if parent_id:
                sub_categories = Category.search([('parent_id', '=', int(parent_id))], limit=20)
                sub_category_list = [self._prepare_category_data(category) for category in sub_categories]

                return Response(
                    json.dumps({
                        'status': 'success',
                        'sub_categories': {
                            'count': len(sub_category_list),
                            'data': sub_category_list
                        },
                    }),
                    content_type='application/json',
                    status=200
                )
            else:
                # Get all Men subcategories
                men_subcategories = []
                if men_category:
                    men_subs = Category.search([('parent_id', '=', men_category.id)], limit=20)
                    men_subcategories = [self._prepare_category_data(category) for category in men_subs]

                # Get all Women subcategories
                women_subcategories = []
                if women_category:
                    women_subs = Category.search([('parent_id', '=', women_category.id)], limit=20)
                    women_subcategories = [self._prepare_category_data(category) for category in women_subs]

                return Response(
                    json.dumps({
                        'status': 'success',
                        'main_categories': {
                            'count': len(main_category_list),
                            'data': main_category_list
                        },
                        'men_subcategories': {
                            'count': len(men_subcategories),
                            'data': men_subcategories
                        },
                        'women_subcategories': {
                            'count': len(women_subcategories),
                            'data': women_subcategories
                        }
                    }),
                    content_type='application/json',
                    status=200
                )

        except Exception as e:
            return Response(
                json.dumps({
                    'status': 'error',
                    'message': str(e)
                }),
                content_type='application/json',
                status=500
            )
    # @http.route(['/'], type='http', auth="public", website=True, sitemap=True)
    # def homepage(self, **post):
    #     """Display the homepage"""
    #     # Get featured products (bestsellers)
    #     bestseller_products = request.website.get_bestsellers(limit=8)
    #     # Get new arrivals
    #     new_arrival_products = request.website.get_new_arrivals(limit=8)

    #     values = {
    #         'bestseller_products': bestseller_products,
    #         'new_arrival_products': new_arrival_products,
    #     }
    #     return request.render("ai_amigo_attires.homepage", values)

    # @http.route(['/shop/category/<string:category_name>'], type='http', auth="public", website=True, sitemap=True)
    # def shop_category(self, category_name, **post):
    #     """Display products by category"""
    #     # Find the category
    #     category = None
    #     if category_name.lower() == 'men':
    #         men_category = request.env['product.public.category'].sudo().search([('name', 'ilike', 'men')], limit=1)
    #         if not men_category:
    #             men_category = request.env['product.public.category'].sudo().create({
    #                 'name': 'Men',
    #                 'sequence': 1,
    #             })
    #         category = men_category
    #     elif category_name.lower() == 'women':
    #         women_category = request.env['product.public.category'].sudo().search([('name', 'ilike', 'women')], limit=1)
    #         if not women_category:
    #             women_category = request.env['product.public.category'].sudo().create({
    #                 'name': 'Women',
    #                 'sequence': 2,
    #             })
    #         category = women_category
    #     elif category_name.lower() == 'kids':
    #         kids_category = request.env['product.public.category'].sudo().search([('name', 'ilike', 'kids')], limit=1)
    #         if not kids_category:
    #             kids_category = request.env['product.public.category'].sudo().create({
    #                 'name': 'Kids',
    #                 'sequence': 3,
    #             })
    #         category = kids_category

    #     if not category:
    #         return request.redirect('/shop')

    #     # Get products for this category
    #     products = request.env['product.template'].sudo().search([
    #         ('website_published', '=', True),
    #         ('public_categ_ids', 'in', category.id),
    #     ])

    #     values = {
    #         'category': category,
    #         'products': products,
    #     }

    #     return request.redirect('/shop/category/%s' % category.id)
    # @http.route(['/shop/trending'], type='http', auth="public", website=True, sitemap=True)
    # def trending(self, **post):
    #     """Display trending products page"""
    #     trending_products = request.website.get_trending_products(limit=20)
    #     values = {
    #         'products': trending_products,
    #         'pager': {'page_count': 1},
    #         'page_name': 'trending',
    #         'page': 0,
    #         'ppg': 20,
    #         'ppr': 4,
    #     }
    #     return request.render("ai_amigo_attires.trending_products", values)

    # @http.route(['/shop/new-arrivals'], type='http', auth="public", website=True, sitemap=True)
    # def new_arrivals(self, **post):
    #     """Display new arrivals page"""
    #     new_products = request.website.get_new_arrivals(limit=20)
    #     values = {
    #         'products': new_products,
    #         'pager': {'page_count': 1},
    #         'page_name': 'new_arrivals',
    #         'page': 0,
    #         'ppg': 20,
    #         'ppr': 4,
    #     }
    #     return request.render("ai_amigo_attires.new_arrivals", values)

    # @http.route(['/shop/bestsellers'], type='http', auth="public", website=True, sitemap=True)
    # def bestsellers(self, **post):
    #     """Display bestsellers page"""
    #     bestseller_products = request.website.get_bestsellers(limit=20)
    #     values = {
    #         'products': bestseller_products,
    #         'pager': {'page_count': 1},
    #         'page_name': 'bestsellers',
    #         'page': 0,
    #         'ppg': 20,
    #         'ppr': 4,
    #     }
    #     return request.render("ai_amigo_attires.bestsellers", values)

    # @http.route(['/men'], type='http', auth="public", website=True, sitemap=True)
    # def men_products(self, **post):
    #     """Display men's products page"""
    #     men_products = request.website.get_men_products(limit=20)
    #     values = {
    #         'products': men_products,
    #         'pager': {'page_count': 1},
    #         'page_name': 'men',
    #         'page': 0,
    #         'ppg': 20,
    #         'ppr': 4,
    #     }
    #     return request.render("ai_amigo_attires.men_products", values)

    # @http.route(['/women'], type='http', auth="public", website=True, sitemap=True)
    # def women_products(self, **post):
    #     """Display women's products page"""
    #     women_products = request.website.get_women_products(limit=20)
    #     values = {
    #         'products': women_products,
    #         'pager': {'page_count': 1},
    #         'page_name': 'women',
    #         'page': 0,
    #         'ppg': 20,
    #         'ppr': 4,
    #     }
    #     return request.render("ai_amigo_attires.women_products", values)

    # @http.route(['/about-us'], type='http', auth="public", website=True, sitemap=True)
    # def about_us(self, **post):
    #     """Display about us page"""
    #     return request.render("ai_amigo_attires.about_us")

    # @http.route(['/contact-us'], type='http', auth="public", website=True, sitemap=True)
    # def contact_us(self, **post):
    #     """Display contact us page"""
    #     return request.render("ai_amigo_attires.contact_us")

    # @http.route(['/privacy-policy'], type='http', auth="public", website=True, sitemap=True)
    # def privacy_policy(self, **post):
    #     """Display privacy policy page"""
    #     return request.render("ai_amigo_attires.privacy_policy_base")

    # @http.route(['/terms-of-service'], type='http', auth="public", website=True, sitemap=True)
    # def terms_of_service(self, **post):
    #     """Display terms of service page"""
    #     return request.render("ai_amigo_attires.terms_of_service_base")

    # @http.route(['/shipping-policy'], type='http', auth="public", website=True, sitemap=True)
    # def shipping_policy(self, **post):
    #     """Display shipping policy page"""
    #     return request.render("ai_amigo_attires.shipping_policy_base")

    # @http.route(['/return-policy'], type='http', auth="public", website=True, sitemap=True)
    # def return_policy(self, **post):
    #     """Display return policy page"""
    #     return request.render("ai_amigo_attires.return_policy_base")
