odoo.define('ai_amigo_attires.animations', function (require) {
    'use strict';
    
    var publicWidget = require('web.public.widget');
    var core = require('web.core');
    var _t = core._t;
    
    publicWidget.registry.AmigoAnimations = publicWidget.Widget.extend({
        selector: '.oe_website_sale',
        events: {
            'mouseenter .product-card': '_onProductCardHover',
            'mouseleave .product-card': '_onProductCardLeave',
            'click .wishlist-btn': '_onWishlistClick',
            'click .quick-view-btn': '_onQuickViewClick',
        },
        
        /**
         * @override
         */
        start: function () {
            var self = this;
            return this._super.apply(this, arguments).then(function () {
                self._initScrollAnimations();
                self._initParallaxEffects();
                self._initProductImageZoom();
            });
        },
        
        //--------------------------------------------------------------------------
        // Private
        //--------------------------------------------------------------------------
        
        /**
         * Initialize scroll animations for elements with the 'animate-on-scroll' class
         * 
         * @private
         */
        _initScrollAnimations: function () {
            var self = this;
            var $animatedElements = $('.animate-on-scroll');
            
            if ($animatedElements.length) {
                // Initial check for elements in viewport
                self._checkElementsInViewport();
                
                // Check on scroll
                $(window).on('scroll', _.throttle(function () {
                    self._checkElementsInViewport();
                }, 200));
            }
            
            // Initialize staggered animations
            $('.stagger-children').addClass('animated');
        },
        
        /**
         * Check which elements are in the viewport and animate them
         * 
         * @private
         */
        _checkElementsInViewport: function () {
            var windowHeight = $(window).height();
            var windowTopPosition = $(window).scrollTop();
            var windowBottomPosition = windowTopPosition + windowHeight;
            
            $('.animate-on-scroll:not(.animated)').each(function () {
                var $element = $(this);
                var elementHeight = $element.outerHeight();
                var elementTopPosition = $element.offset().top;
                var elementBottomPosition = elementTopPosition + elementHeight;
                
                // Check if element is in viewport
                if ((elementBottomPosition >= windowTopPosition) &&
                    (elementTopPosition <= windowBottomPosition)) {
                    $element.addClass('animated');
                }
            });
        },
        
        /**
         * Initialize parallax effects for elements with the 'parallax' class
         * 
         * @private
         */
        _initParallaxEffects: function () {
            var self = this;
            var $parallaxElements = $('.parallax');
            
            if ($parallaxElements.length) {
                $(window).on('scroll', _.throttle(function () {
                    var scrollTop = $(window).scrollTop();
                    
                    $parallaxElements.each(function () {
                        var $element = $(this);
                        var speed = $element.data('parallax-speed') || 0.5;
                        var yPos = -(scrollTop * speed);
                        
                        $element.css('transform', 'translate3d(0, ' + yPos + 'px, 0)');
                    });
                }, 10));
            }
        },
        
        /**
         * Initialize product image zoom effect
         * 
         * @private
         */
        _initProductImageZoom: function () {
            $('.product-detail-img').each(function () {
                var $img = $(this);
                
                $img.on('mousemove', function (e) {
                    var zoomer = e.currentTarget;
                    var x = e.offsetX / zoomer.offsetWidth * 100;
                    var y = e.offsetY / zoomer.offsetHeight * 100;
                    
                    zoomer.style.backgroundPosition = x + '% ' + y + '%';
                });
            });
        },
        
        //--------------------------------------------------------------------------
        // Handlers
        //--------------------------------------------------------------------------
        
        /**
         * Handle product card hover animation
         * 
         * @private
         * @param {Event} ev
         */
        _onProductCardHover: function (ev) {
            var $card = $(ev.currentTarget);
            $card.addClass('hovered');
        },
        
        /**
         * Handle product card leave animation
         * 
         * @private
         * @param {Event} ev
         */
        _onProductCardLeave: function (ev) {
            var $card = $(ev.currentTarget);
            $card.removeClass('hovered');
        },
        
        /**
         * Handle wishlist button click
         * 
         * @private
         * @param {Event} ev
         */
        _onWishlistClick: function (ev) {
            ev.preventDefault();
            var $btn = $(ev.currentTarget);
            $btn.toggleClass('active');
            
            // Add heart animation
            $btn.append('<span class="heart-animation"></span>');
            setTimeout(function () {
                $btn.find('.heart-animation').remove();
            }, 1000);
        },
        
        /**
         * Handle quick view button click
         * 
         * @private
         * @param {Event} ev
         */
        _onQuickViewClick: function (ev) {
            ev.preventDefault();
            var $btn = $(ev.currentTarget);
            var productId = $btn.data('product-id');
            
            // Show loading spinner
            $btn.html('<i class="fa fa-spinner fa-spin"></i>');
            
            // Simulate loading
            setTimeout(function () {
                $btn.html('<i class="fa fa-eye"></i>');
                
                // Show quick view modal
                self._showQuickViewModal(productId);
            }, 800);
        },
        
        /**
         * Show quick view modal for a product
         * 
         * @private
         * @param {Integer} productId
         */
        _showQuickViewModal: function (productId) {
            // This is a placeholder for the actual implementation
            // In a real implementation, you would fetch the product data and show a modal
            console.log('Show quick view for product ID:', productId);
        },
    });
    
    return publicWidget.registry.AmigoAnimations;
});
