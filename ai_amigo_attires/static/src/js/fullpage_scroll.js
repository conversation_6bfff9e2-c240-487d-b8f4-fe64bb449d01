// Fullpage Scroll Implementation
odoo.define('ai_amigo_attires.fullpage_scroll', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');
    var core = require('web.core');

    publicWidget.registry.FullpageScroll = publicWidget.Widget.extend({
        selector: 'body',
        events: {
            'click .scroll-down-btn': '_onScrollDownClick',
            'click .nav-dot': '_onNavDotClick'
        },

        /**
         * @override
         */
        start: function () {
            var self = this;
            this.isAnimating = false;
            this.currentSection = 0;
            this.touchStartY = 0;
            this.touchEndY = 0;
            this.sections = [];
            this.navDots = [];
            this.totalSections = 0;
            
            return this._super.apply(this, arguments).then(function () {
                self._initFullpageScroll();
                self._setupScrollProgressBar();
                self._setupMandalaAnimations();
                self._setupSectionAnimations();
            });
        },

        //--------------------------------------------------------------------------
        // Private
        //--------------------------------------------------------------------------

        /**
         * Initialize fullpage scroll
         * 
         * @private
         */
        _initFullpageScroll: function () {
            var self = this;
            
            // Find all sections that should be treated as fullpage sections
            this.sections = Array.from(document.querySelectorAll('section.fullpage-section, section.fp-section, section[data-fullpage="true"]'));
            
            // If no sections are explicitly marked, use sections that are tall enough
            if (this.sections.length === 0) {
                this.sections = Array.from(document.querySelectorAll('section')).filter(function(section) {
                    return section.offsetHeight >= window.innerHeight * 0.75;
                });
            }
            
            this.totalSections = this.sections.length;
            
            if (this.totalSections <= 1) {
                return; // Not enough sections for fullpage scroll
            }
            
            // Add navigation dots
            this._addNavigationDots();
            
            // Add scroll down button to first section
            this._addScrollDownButton();
            
            // Set initial section
            this._goToSection(0, false);
            
            // Add wheel event listener
            window.addEventListener('wheel', function(e) {
                self._handleWheelEvent(e);
            }, { passive: false });
            
            // Add touch events for mobile
            window.addEventListener('touchstart', function(e) {
                self.touchStartY = e.touches[0].clientY;
            }, { passive: true });
            
            window.addEventListener('touchend', function(e) {
                self._handleTouchEnd(e);
            }, { passive: false });
            
            // Add keyboard navigation
            document.addEventListener('keydown', function(e) {
                self._handleKeyDown(e);
            });
            
            // Handle hash changes
            window.addEventListener('hashchange', function() {
                self._handleHashChange();
            });
            
            // Handle initial hash
            if (window.location.hash) {
                setTimeout(function() {
                    self._handleHashChange();
                }, 500);
            }
        },
        
        /**
         * Add navigation dots to the page
         * 
         * @private
         */
        _addNavigationDots: function () {
            var self = this;
            var navDotsContainer = document.createElement('div');
            navDotsContainer.className = 'fullpage-nav-dots';
            
            for (var i = 0; i < this.totalSections; i++) {
                var dot = document.createElement('div');
                dot.className = 'nav-dot';
                dot.setAttribute('data-index', i);
                
                // Add section name as tooltip if available
                var sectionName = this.sections[i].getAttribute('data-section-name') || '';
                if (sectionName) {
                    dot.setAttribute('title', sectionName);
                    var tooltip = document.createElement('span');
                    tooltip.className = 'nav-tooltip';
                    tooltip.textContent = sectionName;
                    dot.appendChild(tooltip);
                }
                
                navDotsContainer.appendChild(dot);
                this.navDots.push(dot);
            }
            
            document.body.appendChild(navDotsContainer);
            
            // Set first dot as active
            if (this.navDots.length > 0) {
                this.navDots[0].classList.add('active');
            }
        },
        
        /**
         * Add scroll down button to first section
         * 
         * @private
         */
        _addScrollDownButton: function () {
            if (this.sections.length > 0) {
                var scrollDownBtn = document.createElement('div');
                scrollDownBtn.className = 'scroll-down-btn';
                scrollDownBtn.innerHTML = '<div class="arrow"></div><div class="text">Scroll Down</div>';
                this.sections[0].appendChild(scrollDownBtn);
            }
        },
        
        /**
         * Handle wheel event
         * 
         * @private
         * @param {WheelEvent} e - Wheel event
         */
        _handleWheelEvent: function (e) {
            if (this.isAnimating) {
                e.preventDefault();
                return;
            }
            
            var delta = e.deltaY;
            
            if (Math.abs(delta) < 30) {
                return; // Ignore small movements
            }
            
            e.preventDefault();
            
            if (delta > 0) {
                this._goToNextSection();
            } else {
                this._goToPrevSection();
            }
        },
        
        /**
         * Handle touch end event
         * 
         * @private
         * @param {TouchEvent} e - Touch event
         */
        _handleTouchEnd: function (e) {
            if (this.isAnimating) {
                return;
            }
            
            this.touchEndY = e.changedTouches[0].clientY;
            var deltaY = this.touchStartY - this.touchEndY;
            
            if (Math.abs(deltaY) < 50) {
                return; // Ignore small swipes
            }
            
            if (deltaY > 0) {
                this._goToNextSection();
            } else {
                this._goToPrevSection();
            }
        },
        
        /**
         * Handle keyboard navigation
         * 
         * @private
         * @param {KeyboardEvent} e - Keyboard event
         */
        _handleKeyDown: function (e) {
            if (this.isAnimating) {
                return;
            }
            
            if (e.key === 'ArrowDown' || e.key === 'PageDown') {
                this._goToNextSection();
                e.preventDefault();
            } else if (e.key === 'ArrowUp' || e.key === 'PageUp') {
                this._goToPrevSection();
                e.preventDefault();
            }
        },
        
        /**
         * Handle hash change
         * 
         * @private
         */
        _handleHashChange: function () {
            var hash = window.location.hash;
            if (!hash) return;
            
            var targetSection = document.querySelector(hash);
            if (!targetSection) return;
            
            var sectionIndex = this.sections.indexOf(targetSection);
            if (sectionIndex !== -1) {
                this._goToSection(sectionIndex, true);
            }
        },
        
        /**
         * Go to next section
         * 
         * @private
         */
        _goToNextSection: function () {
            if (this.currentSection < this.totalSections - 1) {
                this._goToSection(this.currentSection + 1, true);
            }
        },
        
        /**
         * Go to previous section
         * 
         * @private
         */
        _goToPrevSection: function () {
            if (this.currentSection > 0) {
                this._goToSection(this.currentSection - 1, true);
            }
        },
        
        /**
         * Go to specific section
         * 
         * @private
         * @param {number} index - Section index
         * @param {boolean} animate - Whether to animate the scroll
         */
        _goToSection: function (index, animate) {
            var self = this;
            
            if (index < 0 || index >= this.totalSections || this.isAnimating) {
                return;
            }
            
            this.isAnimating = animate;
            this.currentSection = index;
            
            // Update navigation dots
            this.navDots.forEach(function(dot, i) {
                if (i === index) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
            
            // Scroll to section
            var targetSection = this.sections[index];
            var targetPosition = targetSection.offsetTop;
            
            if (animate) {
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
                
                // Reset isAnimating after animation completes
                setTimeout(function() {
                    self.isAnimating = false;
                    
                    // Update URL hash if section has an id
                    if (targetSection.id) {
                        history.replaceState(null, null, '#' + targetSection.id);
                    }
                    
                    // Trigger section enter event
                    self._triggerSectionEnter(targetSection);
                }, 1000);
            } else {
                window.scrollTo(0, targetPosition);
                this.isAnimating = false;
                
                // Trigger section enter event
                this._triggerSectionEnter(targetSection);
            }
            
            // Hide/show scroll down button
            var scrollDownBtn = document.querySelector('.scroll-down-btn');
            if (scrollDownBtn) {
                if (index === this.totalSections - 1) {
                    scrollDownBtn.style.display = 'none';
                } else {
                    scrollDownBtn.style.display = 'flex';
                }
            }
        },
        
        /**
         * Trigger section enter event
         * 
         * @private
         * @param {Element} section - Section element
         */
        _triggerSectionEnter: function (section) {
            // Add visible class to section
            section.classList.add('section-visible');
            
            // Handle staggered children
            var staggerContainers = section.querySelectorAll('.stagger-children');
            staggerContainers.forEach(function(container) {
                container.classList.add('visible');
            });
            
            // Trigger custom event
            var event = new CustomEvent('section:enter', {
                detail: { section: section }
            });
            section.dispatchEvent(event);
        },
        
        /**
         * Setup scroll progress bar
         * 
         * @private
         */
        _setupScrollProgressBar: function () {
            // Create progress bar if it doesn't exist
            if (!document.querySelector('.scroll-progress-bar')) {
                var progressBar = document.createElement('div');
                progressBar.className = 'scroll-progress-bar';
                document.body.appendChild(progressBar);
                
                // Update progress bar on scroll
                window.addEventListener('scroll', function() {
                    var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    var scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
                    var progress = (scrollTop / scrollHeight) * 100;
                    progressBar.style.width = progress + '%';
                });
            }
        },
        
        /**
         * Setup mandala animations
         * 
         * @private
         */
        _setupMandalaAnimations: function () {
            // Add mandala elements to sections
            this.sections.forEach(function(section, index) {
                // Only add mandala to certain sections (e.g., odd-numbered)
                if (index % 2 === 0) {
                    var mandala = document.createElement('div');
                    mandala.className = 'mandala-decoration';
                    mandala.innerHTML = '<div class="mandala-inner"></div><div class="mandala-middle"></div><div class="mandala-outer"></div>';
                    
                    // Position randomly within the section
                    var posX = Math.random() * 80 + 10; // 10% to 90%
                    var posY = Math.random() * 80 + 10; // 10% to 90%
                    
                    mandala.style.left = posX + '%';
                    mandala.style.top = posY + '%';
                    
                    section.appendChild(mandala);
                }
                
                // Add peacock decoration to some sections
                if (index % 3 === 1) {
                    var peacock = document.createElement('div');
                    peacock.className = 'peacock-decoration';
                    
                    // Position at the bottom right
                    peacock.style.right = '5%';
                    peacock.style.bottom = '5%';
                    
                    section.appendChild(peacock);
                }
            });
        },
        
        /**
         * Setup section animations
         * 
         * @private
         */
        _setupSectionAnimations: function () {
            // Add animation classes to sections
            this.sections.forEach(function(section) {
                if (!section.classList.contains('section-visible') && 
                    !section.classList.contains('section-hidden')) {
                    section.classList.add('section-hidden');
                }
            });
            
            // Setup intersection observer for sections not handled by fullpage scroll
            var nonFullpageSections = Array.from(document.querySelectorAll('section:not(.fullpage-section):not(.fp-section):not([data-fullpage="true"])'));
            
            if (nonFullpageSections.length > 0 && 'IntersectionObserver' in window) {
                var sectionObserver = new IntersectionObserver(function(entries) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('section-visible');
                            entry.target.classList.remove('section-hidden');
                            
                            // Handle staggered children
                            var staggerContainers = entry.target.querySelectorAll('.stagger-children');
                            staggerContainers.forEach(function(container) {
                                container.classList.add('visible');
                            });
                            
                            sectionObserver.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.2 });
                
                nonFullpageSections.forEach(function(section) {
                    section.classList.add('section-hidden');
                    sectionObserver.observe(section);
                });
            }
        },
        
        //--------------------------------------------------------------------------
        // Handlers
        //--------------------------------------------------------------------------
        
        /**
         * Handle scroll down button click
         * 
         * @private
         */
        _onScrollDownClick: function () {
            this._goToNextSection();
        },
        
        /**
         * Handle navigation dot click
         * 
         * @private
         * @param {Event} ev - Click event
         */
        _onNavDotClick: function (ev) {
            var index = parseInt(ev.currentTarget.getAttribute('data-index'), 10);
            if (!isNaN(index)) {
                this._goToSection(index, true);
            }
        }
    });

    return publicWidget.registry.FullpageScroll;
});
