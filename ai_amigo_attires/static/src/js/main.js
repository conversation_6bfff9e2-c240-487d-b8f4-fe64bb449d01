/**
 * Main JavaScript for Amigo Attires
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize animations
    initAnimations();
    
    // Mobile menu enhancements
    enhanceMobileMenu();
    
    // Add smooth scrolling
    initSmoothScroll();
});

/**
 * Initialize animations for elements
 */
function initAnimations() {
    // Add fade-in animation to sections
    const sections = document.querySelectorAll('section');
    
    if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });
        
        sections.forEach(section => {
            section.classList.add('opacity-0');
            observer.observe(section);
        });
    } else {
        // Fallback for browsers that don't support IntersectionObserver
        sections.forEach(section => {
            section.classList.add('fade-in');
        });
    }
    
    // Add slide-up animation to product cards
    const productCards = document.querySelectorAll('.oe_product');
    
    if ('IntersectionObserver' in window) {
        const cardObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('slide-up');
                    cardObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });
        
        productCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transitionDelay = (index % 4) * 0.1 + 's';
            cardObserver.observe(card);
        });
    } else {
        // Fallback for browsers that don't support IntersectionObserver
        productCards.forEach(card => {
            card.classList.add('slide-up');
        });
    }
}

/**
 * Enhance mobile menu functionality
 */
function enhanceMobileMenu() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    if (navbarToggler && navbarCollapse) {
        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            const isClickInside = navbarToggler.contains(event.target) || navbarCollapse.contains(event.target);
            
            if (!isClickInside && navbarCollapse.classList.contains('show')) {
                navbarToggler.click();
            }
        });
        
        // Close menu when clicking on a link (for single page navigation)
        const navLinks = navbarCollapse.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (navbarCollapse.classList.contains('show') && window.innerWidth < 992) {
                    navbarToggler.click();
                }
            });
        });
    }
}

/**
 * Initialize smooth scrolling for anchor links
 */
function initSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href');
            
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                e.preventDefault();
                
                window.scrollTo({
                    top: targetElement.offsetTop - 80, // Adjust for header height
                    behavior: 'smooth'
                });
            }
        });
    });
}
