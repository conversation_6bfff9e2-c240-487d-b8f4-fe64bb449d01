/**
 * Smooth Scroll implementation for Amigo Attires
 * Based on smooth-scroll library by <PERSON>
 * https://github.com/cferdinandi/smooth-scroll
 */
odoo.define('ai_amigo_attires.smooth_scroll', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');
    var core = require('web.core');

    // Smooth Scroll implementation
    publicWidget.registry.SmoothScroll = publicWidget.Widget.extend({
        selector: 'body',

        /**
         * @override
         */
        start: function () {
            var self = this;
            return this._super.apply(this, arguments).then(function () {
                self._initSmoothScroll();
                self._setupScrollProgressBar();
                self._setupScrollNavigation();
                self._setupSectionAnimations();
                self._setupMandalaDecorations();
                self._setupPeacockDecorations();
            });
        },

        //--------------------------------------------------------------------------
        // Private
        //--------------------------------------------------------------------------

        /**
         * Initialize smooth scroll behavior
         *
         * @private
         */
        _initSmoothScroll: function () {
            // Add smooth scroll behavior to the html element
            document.documentElement.style.scrollBehavior = 'smooth';

            // Initialize smooth anchor links
            this._initSmoothAnchorLinks();

            // Add scroll snap container class to body if it has fullpage sections
            var fullpageSections = document.querySelectorAll('.fullpage-section');
            if (fullpageSections.length > 1) {
                document.body.classList.add('scroll-snap-container');
                fullpageSections.forEach(function(section) {
                    section.classList.add('scroll-snap-section');
                });
            }
        },

        /**
         * Initialize smooth anchor links
         *
         * @private
         */
        _initSmoothAnchorLinks: function () {
            var self = this;

            // Get all anchor links
            var anchorLinks = document.querySelectorAll('a[href^="#"]');

            anchorLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    var targetId = this.getAttribute('href');

                    // Skip if it's just "#"
                    if (targetId === '#') return;

                    var targetElement = document.querySelector(targetId);

                    if (targetElement) {
                        e.preventDefault();

                        // Scroll to the target element
                        window.scrollTo({
                            top: targetElement.offsetTop,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        },

        /**
         * Setup scroll progress bar at the top of the page
         *
         * @private
         */
        _setupScrollProgressBar: function () {
            // Create progress bar element if it doesn't exist
            if (!document.querySelector('.scroll-progress-bar')) {
                var progressBar = document.createElement('div');
                progressBar.className = 'scroll-progress-bar';
                document.body.appendChild(progressBar);

                // Update progress bar width on scroll
                window.addEventListener('scroll', function() {
                    var winScroll = document.body.scrollTop || document.documentElement.scrollTop;
                    var height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
                    var scrolled = (winScroll / height) * 100;
                    progressBar.style.width = scrolled + '%';
                });
            }
        },

        /**
         * Setup scroll navigation dots
         *
         * @private
         */
        _setupScrollNavigation: function () {
            var self = this;
            var sections = document.querySelectorAll('.fullpage-section');
            if (sections.length < 2) return; // Only show for pages with multiple sections

            // Create scroll navigation container if it doesn't exist
            if (!document.querySelector('.scroll-nav')) {
                var scrollNav = document.createElement('div');
                scrollNav.className = 'scroll-nav';

                // Create dots for each section
                sections.forEach(function(section, index) {
                    var dot = document.createElement('div');
                    dot.className = 'scroll-nav-dot';
                    dot.setAttribute('data-index', index);

                    // Add tooltip with section name if available
                    var sectionName = section.getAttribute('data-section-name') || 'Section ' + (index + 1);
                    var tooltip = document.createElement('span');
                    tooltip.className = 'scroll-nav-tooltip';
                    tooltip.textContent = sectionName;
                    dot.appendChild(tooltip);

                    // Add click event to scroll to section
                    dot.addEventListener('click', function() {
                        window.scrollTo({
                            top: section.offsetTop,
                            behavior: 'smooth'
                        });
                    });

                    scrollNav.appendChild(dot);
                });

                document.body.appendChild(scrollNav);

                // Update active dot on scroll
                window.addEventListener('scroll', function() {
                    var current = '';
                    sections.forEach(function(section, index) {
                        var sectionTop = section.offsetTop - 100;
                        var sectionBottom = sectionTop + section.offsetHeight;
                        if (window.pageYOffset >= sectionTop && window.pageYOffset < sectionBottom) {
                            current = index;
                        }
                    });

                    var dots = document.querySelectorAll('.scroll-nav-dot');
                    dots.forEach(function(dot, index) {
                        dot.classList.remove('active');
                        if (index == current) {
                            dot.classList.add('active');
                        }
                    });
                });

                // Trigger scroll event to set initial active dot
                window.dispatchEvent(new Event('scroll'));
            }
        },

        /**
         * Setup animations for sections when they come into view
         *
         * @private
         */
        _setupSectionAnimations: function () {
            // Add observer for section animations
            var sections = document.querySelectorAll('section');

            if ('IntersectionObserver' in window) {
                var sectionObserver = new IntersectionObserver(function(entries) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('section-visible');
                            entry.target.classList.remove('section-hidden');

                            // Handle staggered children
                            var staggerContainers = entry.target.querySelectorAll('.stagger-children');
                            staggerContainers.forEach(function(container) {
                                container.classList.add('visible');
                            });

                            // Handle reveal elements
                            var revealElements = entry.target.querySelectorAll('.reveal');
                            revealElements.forEach(function(element) {
                                element.classList.add('active');
                            });

                            sectionObserver.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.1 });

                sections.forEach(function(section) {
                    if (!section.classList.contains('section-visible')) {
                        section.classList.add('section-hidden');
                        sectionObserver.observe(section);
                    }
                });
            }
        },

        /**
         * Setup mandala decorations
         *
         * @private
         */
        _setupMandalaDecorations: function() {
            var mandalaDecorations = document.querySelectorAll('.mandala-decoration');

            mandalaDecorations.forEach(function(mandala) {
                // Create mandala elements if they don't exist
                if (!mandala.querySelector('.mandala-inner')) {
                    var inner = document.createElement('div');
                    inner.className = 'mandala-inner';

                    var middle = document.createElement('div');
                    middle.className = 'mandala-middle';

                    var outer = document.createElement('div');
                    outer.className = 'mandala-outer';

                    mandala.appendChild(inner);
                    mandala.appendChild(middle);
                    mandala.appendChild(outer);
                }
            });
        },

        /**
         * Setup peacock decorations
         *
         * @private
         */
        _setupPeacockDecorations: function() {
            var peacockDecorations = document.querySelectorAll('.peacock-decoration');

            peacockDecorations.forEach(function(peacock) {
                // Create a simple peacock shape with divs if it doesn't exist
                if (!peacock.querySelector('.peacock-body')) {
                    var body = document.createElement('div');
                    body.className = 'peacock-body';
                    body.style.width = '50px';
                    body.style.height = '100px';
                    body.style.borderRadius = '50% 50% 50% 50% / 60% 60% 40% 40%';
                    body.style.backgroundColor = '#6a1b9a';
                    body.style.opacity = '0.2';
                    body.style.position = 'absolute';
                    body.style.bottom = '50px';
                    body.style.right = '50px';

                    var head = document.createElement('div');
                    head.className = 'peacock-head';
                    head.style.width = '30px';
                    head.style.height = '30px';
                    head.style.borderRadius = '50%';
                    head.style.backgroundColor = '#6a1b9a';
                    head.style.opacity = '0.2';
                    head.style.position = 'absolute';
                    head.style.bottom = '140px';
                    head.style.right = '60px';

                    var feathers = document.createElement('div');
                    feathers.className = 'peacock-feathers';
                    feathers.style.width = '150px';
                    feathers.style.height = '200px';
                    feathers.style.borderRadius = '50% 50% 0 0';
                    feathers.style.border = '1px solid #6a1b9a';
                    feathers.style.opacity = '0.1';
                    feathers.style.position = 'absolute';
                    feathers.style.bottom = '100px';
                    feathers.style.right = '0';

                    peacock.appendChild(feathers);
                    peacock.appendChild(body);
                    peacock.appendChild(head);
                }
            });
        }
    });

    return publicWidget.registry.SmoothScroll;
});
