/**
 * Mandala Animation
 * This script creates a rotating mandala background effect
 */
document.addEventListener('DOMContentLoaded', function() {
    // Create mandala elements
    createMandalaElements();
    
    // Start animation
    animateMandala();
});

/**
 * Create mandala elements in the background
 */
function createMandalaElements() {
    // Only add mandala to specific pages
    const validPages = ['/', '/shop', '/men', '/women', '/shop/new-arrivals', '/shop/trending', '/shop/bestsellers'];
    const currentPath = window.location.pathname;
    
    if (!validPages.includes(currentPath)) {
        return;
    }
    
    // Create container
    const container = document.createElement('div');
    container.className = 'mandala-container';
    container.style.position = 'fixed';
    container.style.top = '0';
    container.style.left = '0';
    container.style.width = '100%';
    container.style.height = '100%';
    container.style.pointerEvents = 'none';
    container.style.zIndex = '-1';
    container.style.opacity = '0.1';
    
    // Create mandala elements
    for (let i = 0; i < 3; i++) {
        const mandala = document.createElement('div');
        mandala.className = 'mandala mandala-' + i;
        mandala.style.position = 'absolute';
        mandala.style.backgroundImage = 'url("/ai_amigo_attires/static/src/img/mandala-' + (i + 1) + '.svg")';
        mandala.style.backgroundSize = 'contain';
        mandala.style.backgroundRepeat = 'no-repeat';
        mandala.style.backgroundPosition = 'center';
        mandala.style.width = (50 + i * 20) + 'vw';
        mandala.style.height = (50 + i * 20) + 'vw';
        
        // Position randomly
        mandala.style.top = Math.random() * 100 + 'vh';
        mandala.style.left = Math.random() * 100 + 'vw';
        
        // Add to container
        container.appendChild(mandala);
    }
    
    // Add container to body
    document.body.appendChild(container);
}

/**
 * Animate the mandala elements
 */
function animateMandala() {
    const mandalas = document.querySelectorAll('.mandala');
    
    mandalas.forEach((mandala, index) => {
        // Set initial rotation
        mandala.style.transform = 'rotate(0deg)';
        
        // Animate rotation
        const duration = 60 + index * 20; // seconds
        const direction = index % 2 === 0 ? 1 : -1; // alternate direction
        
        mandala.style.transition = 'transform ' + duration + 's linear infinite';
        
        // Start animation with a slight delay for each mandala
        setTimeout(() => {
            mandala.style.transform = 'rotate(' + (360 * direction) + 'deg)';
            
            // Reset rotation and continue animation
            setInterval(() => {
                mandala.style.transition = 'none';
                mandala.style.transform = 'rotate(0deg)';
                
                setTimeout(() => {
                    mandala.style.transition = 'transform ' + duration + 's linear infinite';
                    mandala.style.transform = 'rotate(' + (360 * direction) + 'deg)';
                }, 50);
            }, duration * 1000);
        }, index * 500);
    });
}
