/**
 * Product Card Interactions
 */
document.addEventListener('DOMContentLoaded', function() {
    // Quick view functionality
    const quickViewButtons = document.querySelectorAll('.quick-view-btn');
    
    quickViewButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = this.getAttribute('data-product-id');
            
            // Open quick view modal
            if (productId) {
                // Using Odoo's public widget system to open the quick view
                const publicWidget = odoo.__DEBUG__.services['web.public.widget'];
                if (publicWidget) {
                    publicWidget.registry.ProductQuickView.prototype._openQuickView({
                        currentTarget: this
                    });
                }
            }
        });
    });
    
    // Product image hover effect
    const productCards = document.querySelectorAll('.oe_product');
    
    productCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const img = this.querySelector('.oe_product_image img');
            if (img) {
                img.style.transform = 'scale(1.05)';
                img.style.transition = 'transform 0.3s ease';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            const img = this.querySelector('.oe_product_image img');
            if (img) {
                img.style.transform = 'scale(1)';
            }
        });
    });
});
