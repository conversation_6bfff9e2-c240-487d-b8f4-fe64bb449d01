/* Main SCSS file for Amigo Attires */

// Variables
$primary-color: #7C3AED;
$secondary-color: #FF6B6B;
$accent-color: #4CAF50;
$text-color: #333333;
$light-color: #F8F9FA;
$dark-color: #212529;

// Indian-inspired color palette
$saffron: #FF9933;
$white: #FFFFFF;
$green: #138808;
$navy: #000080;
$maroon: #800000;

// Typography
$font-family-base: 'Poppins', sans-serif;
$font-size-base: 1rem;
$line-height-base: 1.5;
$headings-font-family: 'Playfair Display', serif;

// Custom Styles
body {
    font-family: $font-family-base;
    color: $text-color;
    background-color: $light-color;
}

h1, h2, h3, h4, h5, h6 {
    font-family: $headings-font-family;
    color: $dark-color;
}

// Header Styling
header {
    background-color: $white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    
    .navbar-brand {
        font-family: $headings-font-family;
        font-weight: 700;
        color: $primary-color;
    }
    
    .nav-link {
        font-weight: 500;
        color: $dark-color;
        
        &:hover {
            color: $primary-color;
        }
    }
    
    .dropdown-menu {
        border-radius: 0.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
}

// Footer Styling
footer {
    background-color: $dark-color;
    color: $light-color;
    padding: 3rem 0;
    
    h5 {
        color: $white;
        font-weight: 600;
        margin-bottom: 1.5rem;
    }
    
    a {
        color: rgba(255, 255, 255, 0.7);
        
        &:hover {
            color: $white;
            text-decoration: none;
        }
    }
    
    .footer-bottom {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 1.5rem;
        margin-top: 2rem;
    }
}

// Button Styling
.btn-primary {
    background-color: $primary-color;
    border-color: $primary-color;
    
    &:hover {
        background-color: darken($primary-color, 10%);
        border-color: darken($primary-color, 10%);
    }
}

.btn-secondary {
    background-color: $secondary-color;
    border-color: $secondary-color;
    
    &:hover {
        background-color: darken($secondary-color, 10%);
        border-color: darken($secondary-color, 10%);
    }
}

.btn-accent {
    background-color: $accent-color;
    border-color: $accent-color;
    color: $white;
    
    &:hover {
        background-color: darken($accent-color, 10%);
        border-color: darken($accent-color, 10%);
        color: $white;
    }
}

// Home Page Styling
.hero-section {
    background: linear-gradient(135deg, $primary-color, $navy);
    color: $white;
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('/ai_amigo_attires/static/src/img/mandala-1.svg');
        background-size: cover;
        background-position: center;
        opacity: 0.1;
        z-index: 0;
    }
    
    .container {
        position: relative;
        z-index: 1;
    }
    
    h1 {
        font-size: 3rem;
        font-weight: 700;
        color: $white;
    }
    
    p {
        font-size: 1.2rem;
        max-width: 600px;
        margin: 1.5rem auto;
    }
}

// Section Styling
.section-title {
    position: relative;
    margin-bottom: 2.5rem;
    text-align: center;
    
    &::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background-color: $primary-color;
    }
}

// Product Card Styling (additional to product_card.css)
.oe_product {
    .product_price {
        color: $primary-color;
    }
    
    .oe_product_image {
        border: 1px solid rgba(0, 0, 0, 0.1);
    }
}

// Category Showcase
.category-showcase {
    .category-item {
        position: relative;
        overflow: hidden;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
        
        img {
            transition: transform 0.5s ease;
        }
        
        &:hover img {
            transform: scale(1.05);
        }
        
        .category-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            padding: 1rem;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
            color: $white;
            
            h3 {
                color: $white;
                margin-bottom: 0.5rem;
            }
        }
    }
}

// Testimonials
.testimonial-section {
    background-color: rgba($primary-color, 0.05);
    padding: 4rem 0;
    
    .testimonial-item {
        background-color: $white;
        border-radius: 0.5rem;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        
        .testimonial-content {
            font-style: italic;
            margin-bottom: 1.5rem;
        }
        
        .testimonial-author {
            display: flex;
            align-items: center;
            
            img {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                margin-right: 1rem;
            }
            
            .author-info {
                h5 {
                    margin-bottom: 0.25rem;
                }
                
                p {
                    color: $text-color;
                    opacity: 0.7;
                    margin-bottom: 0;
                }
            }
        }
    }
}

// Newsletter
.newsletter-section {
    background-color: $primary-color;
    color: $white;
    padding: 3rem 0;
    
    h3 {
        color: $white;
    }
    
    .form-control {
        border-radius: 2rem 0 0 2rem;
        border: none;
        height: 50px;
    }
    
    .btn {
        border-radius: 0 2rem 2rem 0;
        height: 50px;
        background-color: $saffron;
        border-color: $saffron;
        
        &:hover {
            background-color: darken($saffron, 10%);
            border-color: darken($saffron, 10%);
        }
    }
}

// About Us Page
.about-us-section {
    padding: 4rem 0;
    
    .about-image {
        position: relative;
        
        &::before {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            width: 100%;
            height: 100%;
            border: 5px solid $primary-color;
            z-index: -1;
        }
        
        img {
            border-radius: 0.5rem;
        }
    }
    
    .about-content {
        h2 {
            margin-bottom: 1.5rem;
        }
        
        p {
            margin-bottom: 1.5rem;
            line-height: 1.8;
        }
    }
}

// Contact Us Page
.contact-section {
    padding: 4rem 0;
    
    .contact-info {
        background-color: $primary-color;
        color: $white;
        padding: 2rem;
        border-radius: 0.5rem;
        height: 100%;
        
        h3 {
            color: $white;
            margin-bottom: 1.5rem;
        }
        
        .contact-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1.5rem;
            
            i {
                font-size: 1.5rem;
                margin-right: 1rem;
                color: $saffron;
            }
            
            .contact-text {
                h5 {
                    color: $white;
                    margin-bottom: 0.5rem;
                }
                
                p {
                    margin-bottom: 0;
                    opacity: 0.8;
                }
            }
        }
    }
    
    .contact-form {
        background-color: $white;
        padding: 2rem;
        border-radius: 0.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        
        h3 {
            margin-bottom: 1.5rem;
        }
        
        .form-control {
            border-radius: 0.25rem;
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0.75rem;
            margin-bottom: 1.5rem;
        }
        
        .btn-primary {
            padding: 0.75rem 2rem;
        }
    }
}

// Responsive adjustments
@media (max-width: 767.98px) {
    .hero-section {
        padding: 3rem 0;
        
        h1 {
            font-size: 2rem;
        }
        
        p {
            font-size: 1rem;
        }
    }
    
    .section-title {
        font-size: 1.5rem;
    }
    
    .about-us-section {
        .about-image {
            margin-bottom: 2rem;
        }
    }
    
    .contact-section {
        .contact-info {
            margin-bottom: 2rem;
        }
    }
}
