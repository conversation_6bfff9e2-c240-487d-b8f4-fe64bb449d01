<?xml version="1.0" encoding="UTF-8"?>
<svg width="300px" height="300px" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
  <g fill="none" stroke="#6a1b9a">
    <!-- Outer circles -->
    <circle cx="150" cy="150" r="145" stroke-width="1"/>
    <circle cx="150" cy="150" r="135" stroke-width="1"/>
    <circle cx="150" cy="150" r="125" stroke-width="1"/>
    <circle cx="150" cy="150" r="115" stroke-width="0.8"/>
    
    <!-- Decorative patterns -->
    <g stroke-width="0.8">
      <!-- 16 large decorative elements -->
      <g>
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(22.5, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(45, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(67.5, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(90, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(112.5, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(135, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(157.5, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(180, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(202.5, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(225, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(247.5, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(270, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(292.5, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(315, 150, 150)" />
        <path d="M150,15 C170,35 170,65 150,85 C130,65 130,35 150,15" transform="rotate(337.5, 150, 150)" />
      </g>
      
      <!-- Connecting arcs -->
      <path d="M150,25 C200,25 200,75 150,75" stroke-width="0.5" />
      <path d="M150,25 C200,25 200,75 150,75" stroke-width="0.5" transform="rotate(45, 150, 150)" />
      <path d="M150,25 C200,25 200,75 150,75" stroke-width="0.5" transform="rotate(90, 150, 150)" />
      <path d="M150,25 C200,25 200,75 150,75" stroke-width="0.5" transform="rotate(135, 150, 150)" />
      <path d="M150,25 C200,25 200,75 150,75" stroke-width="0.5" transform="rotate(180, 150, 150)" />
      <path d="M150,25 C200,25 200,75 150,75" stroke-width="0.5" transform="rotate(225, 150, 150)" />
      <path d="M150,25 C200,25 200,75 150,75" stroke-width="0.5" transform="rotate(270, 150, 150)" />
      <path d="M150,25 C200,25 200,75 150,75" stroke-width="0.5" transform="rotate(315, 150, 150)" />
      
      <!-- Radial lines -->
      <path d="M150,5 L150,295" stroke-width="0.5" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(11.25, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(22.5, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(33.75, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(45, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(56.25, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(67.5, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(78.75, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(90, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(101.25, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(112.5, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(123.75, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(135, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(146.25, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(157.5, 150, 150)" />
      <path d="M150,5 L150,295" stroke-width="0.5" transform="rotate(168.75, 150, 150)" />
    </g>
    
    <!-- Decorative dots -->
    <g fill="#6a1b9a" stroke="none">
      <circle cx="150" cy="15" r="2" />
      <circle cx="150" cy="15" r="2" transform="rotate(22.5, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(45, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(67.5, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(90, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(112.5, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(135, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(157.5, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(180, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(202.5, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(225, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(247.5, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(270, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(292.5, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(315, 150, 150)" />
      <circle cx="150" cy="15" r="2" transform="rotate(337.5, 150, 150)" />
      
      <!-- Inner decorative dots -->
      <circle cx="150" cy="125" r="1.5" />
      <circle cx="150" cy="125" r="1.5" transform="rotate(30, 150, 150)" />
      <circle cx="150" cy="125" r="1.5" transform="rotate(60, 150, 150)" />
      <circle cx="150" cy="125" r="1.5" transform="rotate(90, 150, 150)" />
      <circle cx="150" cy="125" r="1.5" transform="rotate(120, 150, 150)" />
      <circle cx="150" cy="125" r="1.5" transform="rotate(150, 150, 150)" />
      <circle cx="150" cy="125" r="1.5" transform="rotate(180, 150, 150)" />
      <circle cx="150" cy="125" r="1.5" transform="rotate(210, 150, 150)" />
      <circle cx="150" cy="125" r="1.5" transform="rotate(240, 150, 150)" />
      <circle cx="150" cy="125" r="1.5" transform="rotate(270, 150, 150)" />
      <circle cx="150" cy="125" r="1.5" transform="rotate(300, 150, 150)" />
      <circle cx="150" cy="125" r="1.5" transform="rotate(330, 150, 150)" />
    </g>
  </g>
</svg>
