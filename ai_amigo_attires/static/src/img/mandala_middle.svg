<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <g fill="none" stroke="#6a1b9a">
    <circle cx="100" cy="100" r="80" stroke-width="1"/>
    <circle cx="100" cy="100" r="70" stroke-width="1"/>
    <circle cx="100" cy="100" r="60" stroke-width="1"/>
    
    <!-- Radial lines -->
    <g stroke-width="1">
      <!-- 24 lines at 15-degree intervals -->
      <path d="M100,30 L100,170"/>
      <path d="M135,35 L65,165" transform="rotate(15, 100, 100)"/>
      <path d="M165,65 L35,135" transform="rotate(30, 100, 100)"/>
      <path d="M170,100 L30,100" transform="rotate(45, 100, 100)"/>
      <path d="M165,135 L35,65" transform="rotate(60, 100, 100)"/>
      <path d="M135,165 L65,35" transform="rotate(75, 100, 100)"/>
      <path d="M100,170 L100,30" transform="rotate(90, 100, 100)"/>
      <path d="M65,165 L135,35" transform="rotate(105, 100, 100)"/>
      <path d="M35,135 L165,65" transform="rotate(120, 100, 100)"/>
      <path d="M30,100 L170,100" transform="rotate(135, 100, 100)"/>
      <path d="M35,65 L165,135" transform="rotate(150, 100, 100)"/>
      <path d="M65,35 L135,165" transform="rotate(165, 100, 100)"/>
    </g>
    
    <!-- Decorative elements -->
    <g stroke-width="1.5">
      <!-- Lotus petals -->
      <path d="M100,40 C110,50 110,70 100,80 C90,70 90,50 100,40" />
      <path d="M100,40 C110,50 110,70 100,80 C90,70 90,50 100,40" transform="rotate(30, 100, 100)" />
      <path d="M100,40 C110,50 110,70 100,80 C90,70 90,50 100,40" transform="rotate(60, 100, 100)" />
      <path d="M100,40 C110,50 110,70 100,80 C90,70 90,50 100,40" transform="rotate(90, 100, 100)" />
      <path d="M100,40 C110,50 110,70 100,80 C90,70 90,50 100,40" transform="rotate(120, 100, 100)" />
      <path d="M100,40 C110,50 110,70 100,80 C90,70 90,50 100,40" transform="rotate(150, 100, 100)" />
      <path d="M100,40 C110,50 110,70 100,80 C90,70 90,50 100,40" transform="rotate(180, 100, 100)" />
      <path d="M100,40 C110,50 110,70 100,80 C90,70 90,50 100,40" transform="rotate(210, 100, 100)" />
      <path d="M100,40 C110,50 110,70 100,80 C90,70 90,50 100,40" transform="rotate(240, 100, 100)" />
      <path d="M100,40 C110,50 110,70 100,80 C90,70 90,50 100,40" transform="rotate(270, 100, 100)" />
      <path d="M100,40 C110,50 110,70 100,80 C90,70 90,50 100,40" transform="rotate(300, 100, 100)" />
      <path d="M100,40 C110,50 110,70 100,80 C90,70 90,50 100,40" transform="rotate(330, 100, 100)" />
      
      <!-- Outer lotus petals -->
      <path d="M100,20 C115,35 115,65 100,80 C85,65 85,35 100,20" />
      <path d="M100,20 C115,35 115,65 100,80 C85,65 85,35 100,20" transform="rotate(45, 100, 100)" />
      <path d="M100,20 C115,35 115,65 100,80 C85,65 85,35 100,20" transform="rotate(90, 100, 100)" />
      <path d="M100,20 C115,35 115,65 100,80 C85,65 85,35 100,20" transform="rotate(135, 100, 100)" />
      <path d="M100,20 C115,35 115,65 100,80 C85,65 85,35 100,20" transform="rotate(180, 100, 100)" />
      <path d="M100,20 C115,35 115,65 100,80 C85,65 85,35 100,20" transform="rotate(225, 100, 100)" />
      <path d="M100,20 C115,35 115,65 100,80 C85,65 85,35 100,20" transform="rotate(270, 100, 100)" />
      <path d="M100,20 C115,35 115,65 100,80 C85,65 85,35 100,20" transform="rotate(315, 100, 100)" />
    </g>
  </g>
</svg>
