/* Fullpage Scroll Styles */

/* Basic Styles */
html {
    scroll-behavior: smooth;
}

body {
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

/* Fullpage Sections */
section.fullpage-section,
section.fp-section,
section[data-fullpage="true"] {
    min-height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
}

/* Navigation Dots */
.fullpage-nav-dots {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(106, 27, 154, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.nav-dot:hover {
    transform: scale(1.2);
    background-color: rgba(255, 255, 255, 0.8);
}

.nav-dot.active {
    background-color: #6a1b9a;
    transform: scale(1.3);
}

.nav-tooltip {
    position: absolute;
    right: 25px;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(106, 27, 154, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.nav-dot:hover .nav-tooltip {
    opacity: 1;
    visibility: visible;
    right: 30px;
}

/* Scroll Down Button */
.scroll-down-btn {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    z-index: 10;
    animation: bounce 2s infinite;
}

.scroll-down-btn .arrow {
    width: 20px;
    height: 20px;
    border-right: 3px solid #6a1b9a;
    border-bottom: 3px solid #6a1b9a;
    transform: rotate(45deg);
    margin-bottom: 5px;
}

.scroll-down-btn .text {
    font-size: 12px;
    color: #6a1b9a;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Progress Bar */
.scroll-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(to right, #6a1b9a, #ff9800);
    z-index: 9999;
    width: 0;
    transition: width 0.1s ease;
}

/* Section Animations */
.section-hidden {
    opacity: 0;
    transform: translateY(50px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.section-visible {
    opacity: 1;
    transform: translateY(0);
}

/* Staggered Children Animation */
.stagger-children > * {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.stagger-children.visible > *:nth-child(1) { transition-delay: 0.1s; }
.stagger-children.visible > *:nth-child(2) { transition-delay: 0.2s; }
.stagger-children.visible > *:nth-child(3) { transition-delay: 0.3s; }
.stagger-children.visible > *:nth-child(4) { transition-delay: 0.4s; }
.stagger-children.visible > *:nth-child(5) { transition-delay: 0.5s; }
.stagger-children.visible > *:nth-child(6) { transition-delay: 0.6s; }
.stagger-children.visible > *:nth-child(7) { transition-delay: 0.7s; }
.stagger-children.visible > *:nth-child(8) { transition-delay: 0.8s; }

.stagger-children.visible > * {
    opacity: 1;
    transform: translateY(0);
}

/* Mandala Decorations */
.mandala-decoration {
    position: absolute;
    width: 300px;
    height: 300px;
    opacity: 0.15;
    pointer-events: none;
    z-index: 1;
}

.mandala-inner,
.mandala-middle,
.mandala-outer {
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.mandala-inner {
    width: 100px;
    height: 100px;
    transform: translate(-50%, -50%);
    background-image: url('/ai_amigo_attires/static/src/img/mandala_inner.svg');
    animation: rotate 20s linear infinite;
}

.mandala-middle {
    width: 200px;
    height: 200px;
    transform: translate(-50%, -50%);
    background-image: url('/ai_amigo_attires/static/src/img/mandala_middle.svg');
    animation: rotate 30s linear infinite reverse;
}

.mandala-outer {
    width: 300px;
    height: 300px;
    transform: translate(-50%, -50%);
    background-image: url('/ai_amigo_attires/static/src/img/mandala_outer.svg');
    animation: rotate 40s linear infinite;
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Peacock Decoration */
.peacock-decoration {
    position: absolute;
    width: 200px;
    height: 300px;
    background-image: url('/ai_amigo_attires/static/src/img/peacock.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: bottom right;
    z-index: 1;
    opacity: 0.2;
    transform-origin: bottom right;
    animation: peacockSway 10s ease-in-out infinite alternate;
}

@keyframes peacockSway {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(5deg); }
}

/* Indian Theme Colors */
.indian-theme-bg-primary {
    background-color: #6a1b9a; /* Deep Purple */
}

.indian-theme-bg-secondary {
    background-color: #ff9800; /* Orange */
}

.indian-theme-bg-accent {
    background-color: #00bcd4; /* Cyan */
}

.indian-theme-text-primary {
    color: #6a1b9a; /* Deep Purple */
}

.indian-theme-text-secondary {
    color: #ff9800; /* Orange */
}

.indian-theme-text-accent {
    color: #00bcd4; /* Cyan */
}

.indian-theme-border {
    border-color: #6a1b9a; /* Deep Purple */
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .fullpage-nav-dots {
        right: 10px;
    }
    
    .nav-dot {
        width: 10px;
        height: 10px;
    }
    
    .mandala-decoration {
        width: 200px;
        height: 200px;
    }
    
    .mandala-inner {
        width: 70px;
        height: 70px;
    }
    
    .mandala-middle {
        width: 140px;
        height: 140px;
    }
    
    .mandala-outer {
        width: 200px;
        height: 200px;
    }
    
    .peacock-decoration {
        width: 150px;
        height: 200px;
    }
    
    /* Reduce transition delays on mobile */
    .stagger-children.visible > * {
        transition-delay: 0.1s;
    }
}
