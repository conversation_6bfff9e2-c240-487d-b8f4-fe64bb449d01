/* Smooth Scroll Styles */
html {
    scroll-behavior: smooth;
}

body {
    overflow-x: hidden;
    position: relative;
}

/* Fullpage-like Sections */
.fullpage-section {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
    padding: 80px 0;
    transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fullpage-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(106, 27, 154, 0.05) 0%, rgba(255, 109, 0, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
    pointer-events: none;
    z-index: -1;
}

.fullpage-section:hover::before {
    opacity: 1;
}

/* Scroll Navigation */
.scroll-nav {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.scroll-nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(106, 27, 154, 0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    position: relative;
}

.scroll-nav-dot::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid rgba(106, 27, 154, 0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-nav-dot:hover,
.scroll-nav-dot.active {
    background-color: #6a1b9a;
    transform: scale(1.2);
}

.scroll-nav-dot:hover::before,
.scroll-nav-dot.active::before {
    transform: translate(-50%, -50%) scale(1);
}

.scroll-nav-tooltip {
    position: absolute;
    right: 30px;
    background-color: #6a1b9a;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    transform: translateX(10px);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    pointer-events: none;
}

.scroll-nav-dot:hover .scroll-nav-tooltip {
    opacity: 1;
    transform: translateX(0);
}

/* Scroll Down Button */
.scroll-down-btn {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-down-btn .arrow {
    width: 20px;
    height: 20px;
    border-right: 2px solid #6a1b9a;
    border-bottom: 2px solid #6a1b9a;
    transform: rotate(45deg);
    margin-bottom: 10px;
    animation: scrollDownArrow 2s infinite;
}

@keyframes scrollDownArrow {
    0% { opacity: 0; transform: rotate(45deg) translate(-10px, -10px); }
    50% { opacity: 1; }
    100% { opacity: 0; transform: rotate(45deg) translate(10px, 10px); }
}

.scroll-down-btn .text {
    font-size: 12px;
    color: #6a1b9a;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.scroll-down-btn:hover .text {
    opacity: 1;
    transform: translateY(-3px);
}

/* Scroll Progress Bar */
.scroll-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(to right, #6a1b9a, #ff6d00);
    z-index: 9999;
    transition: width 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Section Transitions */
.section-hidden {
    opacity: 0;
    transform: translateY(50px);
}

.section-visible {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Section Background Patterns */
.section-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: 20px 20px;
    background-image: radial-gradient(circle, #6a1b9a 1px, transparent 1px);
    opacity: 0.05;
    z-index: -1;
    pointer-events: none;
}

/* Scroll Snap */
.scroll-snap-container {
    scroll-snap-type: y mandatory;
    overflow-y: scroll;
    height: 100vh;
    scroll-behavior: smooth;
}

.scroll-snap-section {
    scroll-snap-align: start;
    min-height: 100vh;
    position: relative;
}

/* Parallax Effect */
.parallax-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 120%;
    background-size: cover;
    background-position: center;
    transform: translateY(0);
    z-index: -1;
    will-change: transform;
}

/* Scroll Reveal */
.reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.reveal.active {
    opacity: 1;
    transform: translateY(0);
}

/* Scroll Direction Indicator */
.scroll-direction {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(106, 27, 154, 0.8);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.scroll-direction i {
    font-size: 20px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-direction.up i {
    transform: rotate(180deg);
}

/* Fullpage Pagination */
.fullpage-pagination {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.fullpage-pagination-bullet {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(106, 27, 154, 0.3);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.fullpage-pagination-bullet::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid rgba(106, 27, 154, 0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fullpage-pagination-bullet:hover,
.fullpage-pagination-bullet.active {
    background-color: #6a1b9a;
    transform: scale(1.2);
}

.fullpage-pagination-bullet:hover::after,
.fullpage-pagination-bullet.active::after {
    transform: translate(-50%, -50%) scale(1);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .scroll-nav,
    .fullpage-pagination {
        display: none;
    }

    .scroll-down-btn {
        bottom: 20px;
    }

    .scroll-down-btn .arrow {
        width: 15px;
        height: 15px;
    }

    .scroll-down-btn .text {
        font-size: 10px;
    }

    .fullpage-section {
        padding: 60px 0;
    }

    .section-pattern {
        background-size: 15px 15px;
    }
}
