/* Animation Styles for Amigo Attires */

/* Fade In Animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Fade In Up Animation */
@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in-up {
    animation: fadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Fade In Down Animation */
@keyframes fadeInDown {
    from { opacity: 0; transform: translateY(-30px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in-down {
    animation: fadeInDown 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Fade In Left Animation */
@keyframes fadeInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

.fade-in-left {
    animation: fadeInLeft 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Fade In Right Animation */
@keyframes fadeInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

.fade-in-right {
    animation: fadeInRight 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Zoom In Animation */
@keyframes zoomIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

.zoom-in {
    animation: zoomIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Pulse Animation */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s ease infinite;
}

/* Rotate Animation */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.rotate {
    animation: rotate 20s linear infinite;
}

.rotate-reverse {
    animation: rotate 20s linear infinite reverse;
}

/* Shimmer Effect */
@keyframes shimmer {
    0% { background-position: -100% 0; }
    100% { background-position: 100% 0; }
}

.shimmer {
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

/* Badge Animation */
@keyframes badgePulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.badge {
    animation: badgePulse 2s infinite;
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    60% { transform: translateY(-10px); }
}

.bounce {
    animation: bounce 2s ease infinite;
}

/* Float Animation */
@keyframes float {
    0% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0); }
}

.float {
    animation: float 3s ease-in-out infinite;
}

/* Apply animations to elements */
.product-badges .badge {
    animation: badgePulse 2s infinite;
}

.oe_product:hover .product_name {
    color: #6a1b9a;
    transition: color 0.3s ease;
}

/* New Arrival Badge Animation */
.badge.bg-danger {
    animation: pulse 1.5s infinite;
}

/* Trending Badge Animation */
.badge.bg-warning {
    animation: pulse 2s infinite;
}

/* Bestseller Badge Animation */
.badge.bg-success {
    animation: pulse 2.5s infinite;
}

/* Section Animations */
.section-hidden {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.section-visible {
    opacity: 1;
    transform: translateY(0);
}

/* Staggered Children Animation */
.stagger-children > * {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.stagger-children.visible > *:nth-child(1) { transition-delay: 0.1s; }
.stagger-children.visible > *:nth-child(2) { transition-delay: 0.2s; }
.stagger-children.visible > *:nth-child(3) { transition-delay: 0.3s; }
.stagger-children.visible > *:nth-child(4) { transition-delay: 0.4s; }
.stagger-children.visible > *:nth-child(5) { transition-delay: 0.5s; }
.stagger-children.visible > *:nth-child(6) { transition-delay: 0.6s; }
.stagger-children.visible > *:nth-child(7) { transition-delay: 0.7s; }
.stagger-children.visible > *:nth-child(8) { transition-delay: 0.8s; }

.stagger-children.visible > * {
    opacity: 1;
    transform: translateY(0);
}

/* Icon Circle Animation */
.icon-circle {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    background-color: rgba(106, 27, 154, 0.1);
    border: 1px solid rgba(106, 27, 154, 0.2);
    transition: all 0.3s ease;
}

.icon-circle:hover {
    background-color: rgba(106, 27, 154, 0.2);
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(106, 27, 154, 0.1);
}

.icon-circle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: iconShine 3s infinite;
}

@keyframes iconShine {
    0% { left: -100%; }
    20% { left: 100%; }
    100% { left: 100%; }
}

.icon-circle i {
    color: #6a1b9a;
    font-size: 1.8rem;
    transition: all 0.3s ease;
}

.icon-circle:hover i {
    transform: scale(1.2);
}

/* Hover Card Animation */
.hover-card {
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 0;
    overflow: hidden;
    position: relative;
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.hover-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(106, 27, 154, 0), rgba(106, 27, 154, 0.1));
    opacity: 0;
    transition: all 0.5s ease;
    z-index: 1;
    pointer-events: none;
}

.hover-card::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 1px solid #6a1b9a;
    opacity: 0;
    transition: all 0.5s ease;
    pointer-events: none;
    z-index: 1;
}

.hover-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.hover-card:hover::before {
    opacity: 1;
}

.hover-card:hover::after {
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    opacity: 1;
}

/* Category Card Styles */
.category-image-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 0;
}

.category-image-wrapper img {
    transition: all 0.5s ease;
    transform: scale(1);
}

.hover-card:hover .category-image-wrapper img {
    transform: scale(1.05);
}

.category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(106, 27, 154, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.5s ease;
}

.hover-card:hover .category-overlay {
    opacity: 1;
}

.overlay-content {
    transform: translateY(20px);
    transition: all 0.5s ease;
}

.hover-card:hover .overlay-content {
    transform: translateY(0);
}

/* Mandala Animations */
@keyframes mandalaRotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

.mandala-decoration {
    position: absolute;
    width: 300px;
    height: 300px;
    opacity: 0.15;
    pointer-events: none;
    z-index: 1;
}

.mandala-inner {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    border: 2px solid #6a1b9a;
    animation: mandalaRotate 20s linear infinite;
}

.mandala-middle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    border: 1px solid #6a1b9a;
    animation: mandalaRotate 30s linear infinite reverse;
}

.mandala-outer {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    border: 1px solid #6a1b9a;
    animation: mandalaRotate 40s linear infinite;
}

/* Peacock Animation */
@keyframes peacockSway {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(5deg); }
}

.peacock-decoration {
    position: absolute;
    width: 200px;
    height: 300px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: bottom right;
    z-index: 1;
    opacity: 0.2;
    transform-origin: bottom right;
    animation: peacockSway 10s ease-in-out infinite alternate;
}

/* Button Hover Animation */
.btn-hover-effect {
    position: relative;
    overflow: hidden;
    z-index: 1;
    transition: all 0.3s ease;
}

.btn-hover-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    z-index: -1;
}

.btn-hover-effect:hover::before {
    left: 0;
}

/* Hover Underline Animation */
.hover-underline {
    position: relative;
    display: inline-block;
}

.hover-underline::after {
    content: '';
    position: absolute;
    width: 100%;
    transform: scaleX(0);
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #6a1b9a;
    transform-origin: bottom right;
    transition: transform 0.3s ease-out;
}

.hover-underline:hover::after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

/* Scroll Progress Bar */
.scroll-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    height: 3px;
    background-color: #6a1b9a;
    z-index: 9999;
    width: 0%;
    transition: width 0.2s ease;
}

/* Scroll Navigation Dots */
.scroll-nav-dots {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.scroll-nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(106, 27, 154, 0.3);
    margin: 8px 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.scroll-nav-dot:hover,
.scroll-nav-dot.active {
    background-color: #6a1b9a;
    transform: scale(1.3);
}

/* Scroll Down Indicator */
.scroll-down-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #6a1b9a;
    font-size: 0.9rem;
    letter-spacing: 1px;
    text-transform: uppercase;
    opacity: 0.8;
    transition: all 0.3s ease;
    cursor: pointer;
}

.scroll-down-indicator:hover {
    opacity: 1;
}

.scroll-down-indicator .arrow {
    width: 20px;
    height: 20px;
    border-right: 2px solid #6a1b9a;
    border-bottom: 2px solid #6a1b9a;
    transform: rotate(45deg);
    margin-top: 10px;
    animation: scrollDownArrow 2s infinite;
}

@keyframes scrollDownArrow {
    0% { opacity: 0; transform: rotate(45deg) translate(-10px, -10px); }
    50% { opacity: 1; }
    100% { opacity: 0; transform: rotate(45deg) translate(10px, 10px); }
}

/* Image Hover Zoom */
.img-hover-zoom {
    overflow: hidden;
}

.img-hover-zoom img {
    transition: transform 0.5s ease;
}

.img-hover-zoom:hover img {
    transform: scale(1.1);
}

/* Gradient Text Animation */
@keyframes gradientText {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.gradient-text {
    background: linear-gradient(45deg, #6a1b9a, #ff6d00, #6a1b9a);
    background-size: 200% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientText 3s linear infinite;
}

/* Mobile Optimizations */
@media (max-width: 767.98px) {
    /* Reduce animation intensity on mobile */
    .pulse, .rotate, .badge {
        animation-duration: 3s;
    }

    /* Reduce transition delays on mobile */
    .stagger-children.visible > * {
        transition-delay: 0.1s;
    }

    /* Adjust icon circle size on mobile */
    .icon-circle {
        width: 50px;
        height: 50px;
    }

    .icon-circle i {
        font-size: 1.5rem !important;
    }

    /* Adjust mandala size on mobile */
    .mandala-decoration {
        width: 200px;
        height: 200px;
    }

    .mandala-inner {
        width: 70px;
        height: 70px;
    }

    .mandala-middle {
        width: 140px;
        height: 140px;
    }

    .mandala-outer {
        width: 200px;
        height: 200px;
    }

    /* Adjust peacock size on mobile */
    .peacock-decoration {
        width: 150px;
        height: 200px;
    }

    /* Hide scroll nav dots on mobile */
    .scroll-nav-dots {
        display: none;
    }
}
