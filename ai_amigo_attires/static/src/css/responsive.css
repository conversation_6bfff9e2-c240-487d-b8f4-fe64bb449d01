/* Responsive Styles for Amigo Attires */

/* Base Mobile First Approach */
body {
    font-size: 14px;
    line-height: 1.5;
}

.container {
    padding-left: 15px;
    padding-right: 15px;
}

/* Product Grid */
.oe_product {
    margin-bottom: 20px;
}

.oe_product .product_name {
    font-size: 0.9rem;
    line-height: 1.3;
    height: 2.6rem;
    overflow: hidden;
}

.oe_product .product_price {
    font-size: 0.95rem;
}

/* Product Detail Page */
.product_detail {
    padding: 10px;
}

.product_detail h1 {
    font-size: 1.5rem;
    line-height: 1.3;
}

.product_detail .product_price {
    font-size: 1.2rem;
}

.amigo-product-description {
    font-size: 0.9rem;
}

/* Mandala Animation - reduce size on mobile */
.mandala {
    opacity: 0.05;
}

/* Tablet Styles */
@media (min-width: 768px) {
    body {
        font-size: 15px;
    }
    
    .oe_product .product_name {
        font-size: 1rem;
        height: 2.8rem;
    }
    
    .oe_product .product_price {
        font-size: 1.1rem;
    }
    
    .product_detail h1 {
        font-size: 1.8rem;
    }
    
    .product_detail .product_price {
        font-size: 1.4rem;
    }
    
    .amigo-product-description {
        font-size: 1rem;
    }
    
    .mandala {
        opacity: 0.08;
    }
}

/* Desktop Styles */
@media (min-width: 992px) {
    body {
        font-size: 16px;
    }
    
    .oe_product .product_name {
        font-size: 1.1rem;
        height: 3rem;
    }
    
    .oe_product .product_price {
        font-size: 1.2rem;
    }
    
    .product_detail h1 {
        font-size: 2rem;
    }
    
    .product_detail .product_price {
        font-size: 1.6rem;
    }
    
    .amigo-product-description {
        font-size: 1.1rem;
    }
    
    .mandala {
        opacity: 0.1;
    }
}

/* Large Desktop Styles */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
    
    .oe_product .product_name {
        font-size: 1.2rem;
    }
    
    .product_detail h1 {
        font-size: 2.2rem;
    }
}

/* Fix for mobile navigation */
@media (max-width: 767.98px) {
    .navbar-collapse {
        max-height: 80vh;
        overflow-y: auto;
    }
    
    .navbar-nav .dropdown-menu {
        position: static;
        float: none;
    }
}
