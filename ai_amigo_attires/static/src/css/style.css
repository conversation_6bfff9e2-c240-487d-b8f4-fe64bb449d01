/* Main Styles for Amigo Attires */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

/* Color Variables */
:root {
    --primary-color: #6a1b9a;
    --primary-light: #9c4dcc;
    --primary-dark: #38006b;
    --secondary-color: #ff6d00;
    --secondary-light: #ff9e40;
    --secondary-dark: #c43c00;
    --text-dark: #333333;
    --text-light: #ffffff;
    --background-light: #f8f9fa;
    --background-dark: #212529;
    --border-color: #e0e0e0;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

/* Base Styles */
body {
    font-family: 'Poppins', sans-serif;
    color: var(--text-dark);
    line-height: 1.6;
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--primary-light);
    text-decoration: none;
}

.btn {
    border-radius: 0;
    padding: 10px 25px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(106, 27, 154, 0.3);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 109, 0, 0.3);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--text-light);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(106, 27, 154, 0.3);
}

/* Indian Theme Specific Styles */
.indian-theme-bg-primary {
    background-color: var(--primary-color);
    color: var(--text-light);
}

.indian-theme-bg-secondary {
    background-color: var(--secondary-color);
    color: var(--text-light);
}

.indian-theme-text-primary {
    color: var(--primary-color);
}

.indian-theme-text-secondary {
    color: var(--secondary-color);
}

.indian-border {
    border: 2px solid var(--primary-color);
    border-radius: 0;
    position: relative;
}

.indian-border::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: 10px;
    bottom: 10px;
    border: 1px solid var(--primary-light);
    z-index: -1;
    transition: all 0.3s ease;
}

.indian-border:hover::before {
    top: -5px;
    left: -5px;
    right: 5px;
    bottom: 5px;
}

/* Section Styles */
.section-title {
    position: relative;
    margin-bottom: 50px;
    padding-bottom: 20px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
}

.section-title.text-left::after {
    left: 0;
    transform: none;
}

/* Navbar Styles */
.navbar {
    padding: 15px 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    font-size: 1.8rem;
    color: var(--primary-color);
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 10px 15px;
    position: relative;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 15px;
    right: 15px;
    height: 2px;
    background-color: var(--primary-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-item.active .nav-link::after {
    transform: scaleX(1);
}

.navbar-nav .nav-item.active .nav-link {
    color: var(--primary-color);
}

/* Scrolled Navbar */
.navbar.scrolled {
    background-color: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 10px 0;
}

/* Footer Styles */
footer {
    background-color: var(--background-dark);
    color: var(--text-light);
    padding: 60px 0 30px;
}

footer h5 {
    color: var(--text-light);
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 10px;
}

footer h5::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--primary-light);
}

footer ul {
    padding-left: 0;
    list-style: none;
}

footer ul li {
    margin-bottom: 10px;
}

footer ul li a {
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
}

footer ul li a:hover {
    color: var(--text-light);
    padding-left: 5px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    margin-top: 40px;
}

/* Social Icons */
.social-icons a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-right: 10px;
    color: var(--text-light);
    transition: all 0.3s ease;
}

.social-icons a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

/* Breadcrumb Styles */
.breadcrumb-section {
    background-color: var(--background-light);
    padding: 30px 0;
    border-bottom: 1px solid var(--border-color);
}

.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: '>';
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--primary-color);
}

/* Page Header */
.page-header {
    padding: 80px 0;
    background-color: var(--background-light);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(106, 27, 154, 0.8), rgba(255, 109, 0, 0.8));
    opacity: 0.1;
}

.page-header h1 {
    position: relative;
    z-index: 1;
    margin-bottom: 0;
}

/* Mandala Decorations */
.mandala-decoration {
    position: absolute;
    width: 300px;
    height: 300px;
    opacity: 0.15;
    pointer-events: none;
    z-index: 1;
}

/* Peacock Decorations */
.peacock-decoration {
    position: absolute;
    width: 200px;
    height: 300px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: bottom right;
    z-index: 1;
    opacity: 0.2;
    transform-origin: bottom right;
}

/* Fullpage Sections */
.fullpage-section {
    min-height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
}

/* Staggered Children Animation Base */
.stagger-children > * {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--background-light);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-light);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}
