/* Product Card Styling */
.oe_product {
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 0;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    position: relative;
    margin-bottom: 30px;
    border: none;
}

.oe_product::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(106, 27, 154, 0), rgba(106, 27, 154, 0.1));
    opacity: 0;
    transition: all 0.5s ease;
    z-index: 1;
    pointer-events: none;
}

.oe_product::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 1px solid #6a1b9a;
    opacity: 0;
    transition: all 0.5s ease;
    pointer-events: none;
    z-index: 1;
}

.oe_product:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.oe_product:hover::before {
    opacity: 1;
}

.oe_product:hover::after {
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    opacity: 1;
}

.oe_product .oe_product_image {
    border-radius: 0;
    transition: all 0.5s ease;
    transform: scale(1);
}

.oe_product:hover .oe_product_image {
    transform: scale(1.05);
}

.oe_product .product-overlay {
    background-color: rgba(106, 27, 154, 0.3);
    opacity: 0;
    transition: all 0.5s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.oe_product:hover .product-overlay {
    opacity: 1;
}

.oe_product .product_name {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    margin-top: 15px;
    font-size: 1.1rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: #333;
    transition: all 0.3s ease;
}

.oe_product:hover .product_name {
    color: #6a1b9a;
}

.oe_product .product_price {
    font-weight: bold;
    color: #6a1b9a;
    font-size: 1.2rem;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.oe_product:hover .product_price {
    color: #9c4dcc;
}

/* Quick Shop Button */
.oe_product .quick-shop {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #6a1b9a;
    color: #fff;
    text-align: center;
    padding: 12px;
    transform: translateY(100%);
    transition: all 0.5s ease;
    z-index: 2;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    font-size: 0.9rem;
    cursor: pointer;
}

.oe_product:hover .quick-shop {
    transform: translateY(0);
}

.oe_product .quick-shop:hover {
    background-color: #38006b;
}

/* Wishlist Button */
.oe_product .wishlist-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: rgba(255, 255, 255, 0.9);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.oe_product:hover .wishlist-btn {
    opacity: 1;
    transform: translateY(0);
}

.oe_product .wishlist-btn i {
    color: #999;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.oe_product .wishlist-btn:hover i {
    color: #dc3545;
}

.oe_product .wishlist-btn.active i {
    color: #dc3545;
}

/* Product Badge */
.oe_product .product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 3;
    padding: 5px 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.oe_product .product-badge.new {
    background-color: #28a745;
    color: #fff;
}

.oe_product .product-badge.sale {
    background-color: #dc3545;
    color: #fff;
}

.oe_product .product-badge.out-of-stock {
    background-color: #333;
    color: #fff;
}

/* Color Options */
.oe_product .color-options {
    display: flex;
    margin: 15px 0;
}

.oe_product .color-option {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 8px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.oe_product .color-option:hover,
.oe_product .color-option.active {
    transform: scale(1.2);
    border-color: #fff;
    box-shadow: 0 0 0 1px #6a1b9a;
}

/* Size Options */
.oe_product .size-options {
    display: flex;
    margin: 15px 0;
}

.oe_product .size-option {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e0e0e0;
    margin-right: 8px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.oe_product .size-option:hover,
.oe_product .size-option.active {
    background-color: #6a1b9a;
    color: #fff;
    border-color: #6a1b9a;
}

/* Add to Cart Button */
.oe_product .btn-add-to-cart {
    width: 100%;
    background-color: transparent;
    border: 1px solid #6a1b9a;
    color: #6a1b9a;
    padding: 10px 20px;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
    margin-top: 15px;
}

.oe_product .btn-add-to-cart::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: #6a1b9a;
    transition: all 0.3s ease;
    z-index: -1;
}

.oe_product .btn-add-to-cart:hover {
    color: #fff;
}

.oe_product .btn-add-to-cart:hover::before {
    left: 0;
}

/* Formatted Description Styling */
.formatted-description {
    font-family: 'Poppins', sans-serif;
    line-height: 1.8;
    color: #555;
}

.formatted-description h3 {
    font-family: 'Playfair Display', serif;
    color: #6a1b9a;
    margin-bottom: 15px;
    font-weight: 600;
}

.formatted-description ul {
    padding-left: 20px;
    margin-bottom: 20px;
}

.formatted-description ul li {
    margin-bottom: 10px;
    position: relative;
}

.formatted-description ul li::before {
    content: '•';
    color: #6a1b9a;
    font-weight: bold;
    display: inline-block;
    width: 1em;
    margin-left: -1em;
}

.formatted-description .emoji {
    font-size: 1.2em;
    margin-right: 5px;
}

.formatted-description .highlight {
    background-color: rgba(106, 27, 154, 0.1);
    padding: 2px 5px;
    border-radius: 3px;
}

/* Mobile Optimization */
@media (max-width: 767.98px) {
    .oe_product {
        margin-bottom: 20px;
    }

    .oe_product .product_name {
        font-size: 1rem;
    }

    .oe_product .product_price {
        font-size: 1.1rem;
    }

    .oe_product .quick-shop {
        padding: 10px;
        font-size: 0.8rem;
    }

    .oe_product .wishlist-btn {
        width: 35px;
        height: 35px;
    }

    .oe_product .color-option {
        width: 18px;
        height: 18px;
        margin-right: 6px;
    }

    .oe_product .size-option {
        width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }
}
