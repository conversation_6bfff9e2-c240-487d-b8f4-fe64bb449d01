<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Hero Section -->
    <template id="homepage_hero" inherit_id="ai_amigo_attires.homepage">
        <xpath expr="//div[@id='wrap']" position="inside">
            <!-- Hero Section -->
            <section class="hero-section position-relative fullpage-section" data-section-name="Home" id="home">
                <style>
                    .mandala-decoration {
                        position: absolute;
                        width: 300px;
                        height: 300px;
                        opacity: 0.15;
                        pointer-events: none;
                        z-index: 1;
                    }
                    .mandala-inner,
                    .mandala-middle,
                    .mandala-outer {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        border-radius: 50%;
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                    }
                    .mandala-inner {
                        width: 100px;
                        height: 100px;
                        transform: translate(-50%, -50%);
                        border: 2px solid #6a1b9a;
                        animation: rotate 20s linear infinite;
                    }
                    .mandala-middle {
                        width: 200px;
                        height: 200px;
                        transform: translate(-50%, -50%);
                        border: 1px solid #6a1b9a;
                        animation: rotate 30s linear infinite reverse;
                    }
                    .mandala-outer {
                        width: 300px;
                        height: 300px;
                        transform: translate(-50%, -50%);
                        border: 1px solid #6a1b9a;
                        animation: rotate 40s linear infinite;
                    }
                    .peacock-decoration {
                        position: absolute;
                        width: 200px;
                        height: 300px;
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: bottom right;
                        z-index: 1;
                        opacity: 0.2;
                        transform-origin: bottom right;
                        animation: peacockSway 10s ease-in-out infinite alternate;
                    }
                    @keyframes peacockSway {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(5deg); }
                    }
                    @keyframes rotate {
                        from { transform: translate(-50%, -50%) rotate(0deg); }
                        to { transform: translate(-50%, -50%) rotate(360deg); }
                    }
                    .fullpage-section {
                        min-height: 100vh;
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        overflow: hidden;
                    }
                    .indian-theme-text-primary {
                        color: #6a1b9a;
                    }
                    .stagger-children > * {
                        opacity: 0;
                        transform: translateY(20px);
                        transition: opacity 0.5s ease, transform 0.5s ease;
                    }
                    .stagger-children.visible > * {
                        opacity: 1;
                        transform: translateY(0);
                    }
                    .stagger-children.visible > *:nth-child(1) { transition-delay: 0.1s; }
                    .stagger-children.visible > *:nth-child(2) { transition-delay: 0.2s; }
                    .stagger-children.visible > *:nth-child(3) { transition-delay: 0.3s; }
                </style>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Add visible class to stagger children
                        var staggerContainers = document.querySelectorAll('.stagger-children');
                        staggerContainers.forEach(function(container) {
                            container.classList.add('visible');
                        });

                        // Create mandala elements
                        var mandalaDecorations = document.querySelectorAll('.mandala-decoration');
                        mandalaDecorations.forEach(function(mandala) {
                            var inner = document.createElement('div');
                            inner.className = 'mandala-inner';

                            var middle = document.createElement('div');
                            middle.className = 'mandala-middle';

                            var outer = document.createElement('div');
                            outer.className = 'mandala-outer';

                            mandala.appendChild(inner);
                            mandala.appendChild(middle);
                            mandala.appendChild(outer);
                        });

                        // Create peacock elements
                        var peacockDecorations = document.querySelectorAll('.peacock-decoration');
                        peacockDecorations.forEach(function(peacock) {
                            // Create a simple peacock shape with divs
                            var body = document.createElement('div');
                            body.style.width = '50px';
                            body.style.height = '100px';
                            body.style.borderRadius = '50% 50% 50% 50% / 60% 60% 40% 40%';
                            body.style.backgroundColor = '#6a1b9a';
                            body.style.opacity = '0.2';
                            body.style.position = 'absolute';
                            body.style.bottom = '50px';
                            body.style.right = '50px';

                            var head = document.createElement('div');
                            head.style.width = '30px';
                            head.style.height = '30px';
                            head.style.borderRadius = '50%';
                            head.style.backgroundColor = '#6a1b9a';
                            head.style.opacity = '0.2';
                            head.style.position = 'absolute';
                            head.style.bottom = '140px';
                            head.style.right = '60px';

                            var feathers = document.createElement('div');
                            feathers.style.width = '150px';
                            feathers.style.height = '200px';
                            feathers.style.borderRadius = '50% 50% 0 0';
                            feathers.style.border = '1px solid #6a1b9a';
                            feathers.style.opacity = '0.1';
                            feathers.style.position = 'absolute';
                            feathers.style.bottom = '100px';
                            feathers.style.right = '0';

                            peacock.appendChild(feathers);
                            peacock.appendChild(body);
                            peacock.appendChild(head);
                        });
                    });
                </script>
                <div class="mandala-decoration" style="top: 10%; right: 10%;"></div>
                <div class="peacock-decoration" style="bottom: 5%; right: 5%;"></div>
                <div class="container py-5">
                    <div class="row align-items-center">
                        <div class="col-lg-6 py-5 stagger-children">
                            <h1 class="display-4 fw-bold mb-4 indian-theme-text-primary">Discover Authentic Indian Ethnic Wear</h1>
                            <p class="lead mb-4">Exquisite collection of traditional and contemporary Indian clothing for every occasion.</p>
                            <div class="d-flex gap-3">
                                <a href="/shop" class="btn btn-primary btn-lg">Shop Now</a>
                                <a href="/about-us" class="btn btn-outline-secondary btn-lg">Learn More</a>
                            </div>
                        </div>
                        <div class="col-lg-6 py-3">
                            <div class="position-relative">
                                <img src="/web/image/website.s_banner_default_image" class="img-fluid rounded shadow-lg" alt="Indian Ethnic Wear"/>
                                <div class="position-absolute" style="bottom: -20px; right: -20px; z-index: -1; width: 100%; height: 100%; border: 2px solid #6a1b9a; border-radius: 0.5rem;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Categories Section -->
            <section class="categories-section py-5 bg-light fullpage-section" data-section-name="Categories" id="categories">
                <div class="peacock-decoration" style="bottom: 0; right: 0;"></div>
                <div class="container py-4">
                    <div class="text-center mb-5 stagger-children">
                        <h2 class="display-5 fw-bold mb-3 indian-theme-text-primary">Shop By Category</h2>
                        <p class="lead">Explore our wide range of ethnic wear categories</p>
                    </div>
                    <div class="row g-4 stagger-children">
                        <div class="col-md-4 col-6">
                            <a href="/shop/category/men" class="text-decoration-none">
                                <div class="category-card shadow hover-card">
                                    <div class="category-image-wrapper">
                                        <img src="/web/image/website.s_product_catalog_default_image" class="img-fluid w-100" alt="Men's Collection"/>
                                        <div class="category-overlay">
                                            <div class="overlay-content">
                                                <span class="btn btn-sm btn-light">Explore</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="category-content">
                                        <h3 class="mb-2 indian-theme-text-primary">Men's Collection</h3>
                                        <p class="mb-0">Kurtas, Sherwanis &amp; more</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4 col-6">
                            <a href="/shop/category/women" class="text-decoration-none">
                                <div class="category-card shadow hover-card">
                                    <div class="category-image-wrapper">
                                        <img src="/web/image/website.s_product_catalog_default_image" class="img-fluid w-100" alt="Women's Collection"/>
                                        <div class="category-overlay">
                                            <div class="overlay-content">
                                                <span class="btn btn-sm btn-light">Explore</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="category-content">
                                        <h3 class="mb-2 indian-theme-text-primary">Women's Collection</h3>
                                        <p class="mb-0">Sarees, Lehengas &amp; more</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4 col-6">
                            <a href="/shop/category/kids" class="text-decoration-none">
                                <div class="category-card shadow hover-card">
                                    <div class="category-image-wrapper">
                                        <img src="/web/image/website.s_product_catalog_default_image" class="img-fluid w-100" alt="Kids Collection"/>
                                        <div class="category-overlay">
                                            <div class="overlay-content">
                                                <span class="btn btn-sm btn-light">Explore</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="category-content">
                                        <h3 class="mb-2 indian-theme-text-primary">Kids Collection</h3>
                                        <p class="mb-0">Adorable ethnic wear for kids</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </section>
        </xpath>
    </template>
</odoo>
