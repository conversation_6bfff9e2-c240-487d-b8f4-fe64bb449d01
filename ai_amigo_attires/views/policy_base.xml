<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Base Privacy Policy Template -->
    <template id="privacy_policy_base" name="Privacy Policy Base">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <section class="policy-header py-5 bg-light">
                    <div class="container">
                        <h1 class="text-center mb-4">Privacy Policy</h1>
                        <p class="text-center">Last Updated: <span t-field="res_company.write_date" t-options="{'widget': 'date'}"/></p>
                    </div>
                </section>
                <section class="policy-content py-5">
                    <div class="container">
                        <div id="policy-container" class="policy-container bg-white p-4 p-md-5 rounded shadow-sm">
                            <!-- Content will be added through inheritance -->
                        </div>
                    </div>
                </section>
            </div>
        </t>
    </template>

    <!-- Base Terms of Service Template -->
    <template id="terms_of_service_base" name="Terms of Service Base">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <section class="policy-header py-5 bg-light">
                    <div class="container">
                        <h1 class="text-center mb-4">Terms of Service</h1>
                        <p class="text-center">Last Updated: <span t-field="res_company.write_date" t-options="{'widget': 'date'}"/></p>
                    </div>
                </section>
                <section class="policy-content py-5">
                    <div class="container">
                        <div id="policy-container" class="policy-container bg-white p-4 p-md-5 rounded shadow-sm">
                            <!-- Content will be added through inheritance -->
                        </div>
                    </div>
                </section>
            </div>
        </t>
    </template>

    <!-- Base Shipping Policy Template -->
    <template id="shipping_policy_base" name="Shipping Policy Base">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <section class="policy-header py-5 bg-light">
                    <div class="container">
                        <h1 class="text-center mb-4">Shipping Policy</h1>
                        <p class="text-center">Last Updated: <span t-field="res_company.write_date" t-options="{'widget': 'date'}"/></p>
                    </div>
                </section>
                <section class="policy-content py-5">
                    <div class="container">
                        <div id="policy-container" class="policy-container bg-white p-4 p-md-5 rounded shadow-sm">
                            <!-- Content will be added through inheritance -->
                        </div>
                    </div>
                </section>
            </div>
        </t>
    </template>

    <!-- Base Return Policy Template -->
    <template id="return_policy_base" name="Return Policy Base">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <section class="policy-header py-5 bg-light">
                    <div class="container">
                        <h1 class="text-center mb-4">Return &amp; Refund Policy</h1>
                        <p class="text-center">Last Updated: <span t-field="res_company.write_date" t-options="{'widget': 'date'}"/></p>
                    </div>
                </section>
                <section class="policy-content py-5">
                    <div class="container">
                        <div id="policy-container" class="policy-container bg-white p-4 p-md-5 rounded shadow-sm">
                            <!-- Content will be added through inheritance -->
                        </div>
                    </div>
                </section>
            </div>
        </t>
    </template>
</odoo>
