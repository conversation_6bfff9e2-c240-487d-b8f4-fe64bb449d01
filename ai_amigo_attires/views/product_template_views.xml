<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Product Template Form View -->
    <record id="product_template_form_view_inherit" model="ir.ui.view">
        <field name="name">product.template.form.inherit.amigo.attires</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='general_information']" position="after">
                <page string="Amigo Attires Details" name="amigo_attires_details">
                    <group>
                        <group string="Product Identification">
                            <field name="sku"/>
                        </group>
                        <group string="Product Status">
                            <field name="is_new_arrival"/>
                            <field name="is_trending"/>
                            <field name="is_bestseller"/>
                        </group>
                    </group>
                    <group>
                        <group string="Main Product Details">
                            <field name="is_stitched"/>
                            <field name="fabric_material"/>
                            <field name="work_type"/>
                        </group>
                        <group string="Bottom Details">
                            <field name="has_bottom"/>
                            <field name="bottom_material"/>
                            <field name="bottom_is_stitched"/>
                        </group>
                        <group string="Dupatta Details">
                            <field name="has_dupatta"/>
                            <field name="dupatta_material"/>
                            <field name="dupatta_is_stitched"/>
                            <field name="dupatta_length"/>
                        </group>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
