<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Main Menu Structure -->
        <record id="menu_amigo_attires_home" model="website.menu">
            <field name="name">Home</field>
            <field name="url">/</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">10</field>
        </record>

        <record id="menu_amigo_attires_men" model="website.menu">
            <field name="name">Men</field>
            <field name="url">/men</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">20</field>
        </record>

        <record id="menu_amigo_attires_women" model="website.menu">
            <field name="name">Women</field>
            <field name="url">/women</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">30</field>
        </record>

        <record id="menu_amigo_attires_new_arrivals" model="website.menu">
            <field name="name">New Arrivals</field>
            <field name="url">/shop/new-arrivals</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">40</field>
        </record>

        <record id="menu_amigo_attires_trending" model="website.menu">
            <field name="name">Trending</field>
            <field name="url">/shop/trending</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">50</field>
        </record>

        <record id="menu_amigo_attires_bestsellers" model="website.menu">
            <field name="name">Bestsellers</field>
            <field name="url">/shop/bestsellers</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">60</field>
        </record>

        <record id="menu_amigo_attires_about_us" model="website.menu">
            <field name="name">About Us</field>
            <field name="url">/about-us</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">70</field>
        </record>

        <record id="menu_amigo_attires_contact_us" model="website.menu">
            <field name="name">Contact Us</field>
            <field name="url">/contact-us</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">80</field>
        </record>

        <!-- Men's Submenu -->
        <record id="menu_amigo_attires_men_kurta" model="website.menu">
            <field name="name">Kurta</field>
            <field name="url">/shop/category/men-kurta</field>
            <field name="parent_id" ref="menu_amigo_attires_men"/>
            <field name="sequence" type="int">10</field>
        </record>

        <record id="menu_amigo_attires_men_kurta_pajama" model="website.menu">
            <field name="name">Kurta Pajama</field>
            <field name="url">/shop/category/men-kurta-pajama</field>
            <field name="parent_id" ref="menu_amigo_attires_men"/>
            <field name="sequence" type="int">20</field>
        </record>

        <record id="menu_amigo_attires_men_sherwani" model="website.menu">
            <field name="name">Sherwani</field>
            <field name="url">/shop/category/men-sherwani</field>
            <field name="parent_id" ref="menu_amigo_attires_men"/>
            <field name="sequence" type="int">30</field>
        </record>

        <!-- Women's Submenu -->
        <record id="menu_amigo_attires_women_saree" model="website.menu">
            <field name="name">Saree</field>
            <field name="url">/shop/category/women-saree</field>
            <field name="parent_id" ref="menu_amigo_attires_women"/>
            <field name="sequence" type="int">10</field>
        </record>

        <record id="menu_amigo_attires_women_kurta" model="website.menu">
            <field name="name">Kurta</field>
            <field name="url">/shop/category/women-kurta</field>
            <field name="parent_id" ref="menu_amigo_attires_women"/>
            <field name="sequence" type="int">20</field>
        </record>

        <record id="menu_amigo_attires_women_kurta_sets" model="website.menu">
            <field name="name">Kurta Sets</field>
            <field name="url">/shop/category/women-kurta-sets</field>
            <field name="parent_id" ref="menu_amigo_attires_women"/>
            <field name="sequence" type="int">30</field>
        </record>

        <record id="menu_amigo_attires_women_lehenga" model="website.menu">
            <field name="name">Lehenga</field>
            <field name="url">/shop/category/women-lehenga</field>
            <field name="parent_id" ref="menu_amigo_attires_women"/>
            <field name="sequence" type="int">40</field>
        </record>
        <!-- Footer Menu Structure -->
        <record id="menu_amigo_attires_privacy_policy" model="website.menu">
            <field name="name">Privacy Policy</field>
            <field name="url">/privacy-policy</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">90</field>
        </record>

        <record id="menu_amigo_attires_terms_of_service" model="website.menu">
            <field name="name">Terms of Service</field>
            <field name="url">/terms-of-service</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">91</field>
        </record>

        <record id="menu_amigo_attires_shipping_policy" model="website.menu">
            <field name="name">Shipping Policy</field>
            <field name="url">/shipping-policy</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">92</field>
        </record>

        <record id="menu_amigo_attires_return_policy" model="website.menu">
            <field name="name">Return Policy</field>
            <field name="url">/return-policy</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">93</field>
        </record>
    </data>
</odoo>
