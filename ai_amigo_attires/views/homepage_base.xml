<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Base Homepage Template -->
    <template id="homepage" name="Amigo Attires Homepage">
        <t t-call="website.layout">
            <!-- Inline CSS for Smooth Scroll and Animations -->
            <style>
                /* Smooth Scroll Styles */
                html {
                    scroll-behavior: smooth;
                }

                body {
                    overflow-x: hidden;
                    position: relative;
                }

                /* Fullpage-like Sections */
                .fullpage-section {
                    min-height: 100vh;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    position: relative;
                    overflow: hidden;
                    padding: 80px 0;
                    transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                /* Scroll Progress Bar */
                .scroll-progress-bar {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 0;
                    height: 3px;
                    background: linear-gradient(to right, #6a1b9a, #ff6d00);
                    z-index: 9999;
                    transition: width 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                /* Scroll Navigation */
                .scroll-nav {
                    position: fixed;
                    top: 50%;
                    right: 20px;
                    transform: translateY(-50%);
                    z-index: 1000;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 15px;
                }

                .scroll-nav-dot {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    background-color: rgba(106, 27, 154, 0.3);
                    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                    cursor: pointer;
                    position: relative;
                }

                .scroll-nav-dot::before {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%) scale(0);
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    border: 1px solid rgba(106, 27, 154, 0.3);
                    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                .scroll-nav-dot:hover,
                .scroll-nav-dot.active {
                    background-color: #6a1b9a;
                    transform: scale(1.2);
                }

                .scroll-nav-dot:hover::before,
                .scroll-nav-dot.active::before {
                    transform: translate(-50%, -50%) scale(1);
                }

                .scroll-nav-tooltip {
                    position: absolute;
                    right: 30px;
                    background-color: #6a1b9a;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-size: 12px;
                    white-space: nowrap;
                    opacity: 0;
                    transform: translateX(10px);
                    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                    pointer-events: none;
                }

                .scroll-nav-dot:hover .scroll-nav-tooltip {
                    opacity: 1;
                    transform: translateX(0);
                }

                /* Mandala Animations */
                @keyframes mandalaRotate {
                    from { transform: translate(-50%, -50%) rotate(0deg); }
                    to { transform: translate(-50%, -50%) rotate(360deg); }
                }

                .mandala-decoration {
                    position: absolute;
                    width: 300px;
                    height: 300px;
                    opacity: 0.15;
                    pointer-events: none;
                    z-index: 1;
                }

                .mandala-inner {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    width: 100px;
                    height: 100px;
                    transform: translate(-50%, -50%);
                    border-radius: 50%;
                    border: 2px solid #6a1b9a;
                    animation: mandalaRotate 20s linear infinite;
                }

                .mandala-middle {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    width: 200px;
                    height: 200px;
                    transform: translate(-50%, -50%);
                    border-radius: 50%;
                    border: 1px solid #6a1b9a;
                    animation: mandalaRotate 30s linear infinite reverse;
                }

                .mandala-outer {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    width: 300px;
                    height: 300px;
                    transform: translate(-50%, -50%);
                    border-radius: 50%;
                    border: 1px solid #6a1b9a;
                    animation: mandalaRotate 40s linear infinite;
                }

                /* Peacock Animation */
                @keyframes peacockSway {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(5deg); }
                }

                .peacock-decoration {
                    position: absolute;
                    width: 200px;
                    height: 300px;
                    background-size: contain;
                    background-repeat: no-repeat;
                    background-position: bottom right;
                    z-index: 1;
                    opacity: 0.2;
                    transform-origin: bottom right;
                    animation: peacockSway 10s ease-in-out infinite alternate;
                }

                /* Staggered Children Animation */
                .stagger-children > * {
                    opacity: 0;
                    transform: translateY(20px);
                    transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                .stagger-children.visible > * {
                    opacity: 1;
                    transform: translateY(0);
                }

                .stagger-children.visible > *:nth-child(1) { transition-delay: 0.1s; }
                .stagger-children.visible > *:nth-child(2) { transition-delay: 0.2s; }
                .stagger-children.visible > *:nth-child(3) { transition-delay: 0.3s; }
                .stagger-children.visible > *:nth-child(4) { transition-delay: 0.4s; }
                .stagger-children.visible > *:nth-child(5) { transition-delay: 0.5s; }
                .stagger-children.visible > *:nth-child(6) { transition-delay: 0.6s; }

                /* Reveal Animation */
                .reveal {
                    opacity: 0;
                    transform: translateY(30px);
                    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                .reveal.active {
                    opacity: 1;
                    transform: translateY(0);
                }

                /* Product Card Styling */
                .oe_product {
                    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                    border-radius: 0;
                    overflow: hidden;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
                    position: relative;
                    margin-bottom: 30px;
                    border: none;
                }

                .oe_product::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(to bottom, rgba(106, 27, 154, 0), rgba(106, 27, 154, 0.1));
                    opacity: 0;
                    transition: all 0.5s ease;
                    z-index: 1;
                    pointer-events: none;
                }

                .oe_product::after {
                    content: '';
                    position: absolute;
                    top: 10px;
                    left: 10px;
                    right: 10px;
                    bottom: 10px;
                    border: 1px solid #6a1b9a;
                    opacity: 0;
                    transition: all 0.5s ease;
                    pointer-events: none;
                    z-index: 1;
                }

                .oe_product:hover {
                    transform: translateY(-10px);
                    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
                }

                .oe_product:hover::before {
                    opacity: 1;
                }

                .oe_product:hover::after {
                    top: 20px;
                    left: 20px;
                    right: 20px;
                    bottom: 20px;
                    opacity: 1;
                }

                .oe_product .oe_product_image {
                    border-radius: 0;
                    transition: all 0.5s ease;
                    transform: scale(1);
                }

                .oe_product:hover .oe_product_image {
                    transform: scale(1.05);
                }

                .oe_product .product_name {
                    font-family: 'Playfair Display', serif;
                    font-weight: 600;
                    margin-top: 15px;
                    font-size: 1.1rem;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    color: #333;
                    transition: all 0.3s ease;
                }

                .oe_product:hover .product_name {
                    color: #6a1b9a;
                }

                .oe_product .product_price {
                    font-weight: bold;
                    color: #6a1b9a;
                    font-size: 1.2rem;
                    margin-top: 10px;
                    transition: all 0.3s ease;
                }

                /* Responsive Adjustments */
                @media (max-width: 768px) {
                    .scroll-nav {
                        display: none;
                    }

                    .fullpage-section {
                        padding: 60px 0;
                    }

                    .mandala-decoration {
                        width: 200px;
                        height: 200px;
                    }

                    .mandala-inner {
                        width: 70px;
                        height: 70px;
                    }

                    .mandala-middle {
                        width: 140px;
                        height: 140px;
                    }

                    .mandala-outer {
                        width: 200px;
                        height: 200px;
                    }

                    .peacock-decoration {
                        width: 150px;
                        height: 200px;
                    }
                }
            </style>

            <div id="wrap" class="oe_structure oe_empty scroll-snap-container">
                <!-- Content will be added through inheritance -->
            </div>

            <!-- Scroll Progress Bar -->
            <div class="scroll-progress-bar"></div>

            <!-- Scroll Navigation Dots -->
            <div class="scroll-nav"></div>

            <!-- Inline JavaScript for Smooth Scroll -->
            <script type="text/javascript">
                document.addEventListener('DOMContentLoaded', function() {
                    // Setup scroll progress bar
                    var progressBar = document.querySelector('.scroll-progress-bar');
                    window.addEventListener('scroll', function() {
                        var winScroll = document.body.scrollTop || document.documentElement.scrollTop;
                        var height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
                        var scrolled = (winScroll / height) * 100;
                        progressBar.style.width = scrolled + '%';
                    });

                    // Setup scroll navigation dots
                    var sections = document.querySelectorAll('.fullpage-section');
                    var scrollNav = document.querySelector('.scroll-nav');

                    if (sections.length > 1) {
                        // Create dots for each section
                        sections.forEach(function(section, index) {
                            var dot = document.createElement('div');
                            dot.className = 'scroll-nav-dot';
                            dot.setAttribute('data-index', index);

                            // Add tooltip with section name if available
                            var sectionName = section.getAttribute('data-section-name') || 'Section ' + (index + 1);
                            var tooltip = document.createElement('span');
                            tooltip.className = 'scroll-nav-tooltip';
                            tooltip.textContent = sectionName;
                            dot.appendChild(tooltip);

                            // Add click event to scroll to section
                            dot.addEventListener('click', function() {
                                window.scrollTo({
                                    top: section.offsetTop,
                                    behavior: 'smooth'
                                });
                            });

                            scrollNav.appendChild(dot);
                        });

                        // Update active dot on scroll
                        window.addEventListener('scroll', function() {
                            var current = '';
                            sections.forEach(function(section, index) {
                                var sectionTop = section.offsetTop - 100;
                                var sectionBottom = sectionTop + section.offsetHeight;
                                if (window.pageYOffset >= sectionTop && window.pageYOffset < sectionBottom) {
                                    current = index;
                                }
                            });

                            var dots = document.querySelectorAll('.scroll-nav-dot');
                            dots.forEach(function(dot, index) {
                                dot.classList.remove('active');
                                if (index == current) {
                                    dot.classList.add('active');
                                }
                            });
                        });
                    }

                    // Setup mandala decorations
                    var mandalaDecorations = document.querySelectorAll('.mandala-decoration');
                    mandalaDecorations.forEach(function(mandala) {
                        // Create mandala elements if they don't exist
                        if (!mandala.querySelector('.mandala-inner')) {
                            var inner = document.createElement('div');
                            inner.className = 'mandala-inner';

                            var middle = document.createElement('div');
                            middle.className = 'mandala-middle';

                            var outer = document.createElement('div');
                            outer.className = 'mandala-outer';

                            mandala.appendChild(inner);
                            mandala.appendChild(middle);
                            mandala.appendChild(outer);
                        }
                    });

                    // Setup peacock decorations
                    var peacockDecorations = document.querySelectorAll('.peacock-decoration');
                    peacockDecorations.forEach(function(peacock) {
                        // Create a simple peacock shape with divs if it doesn't exist
                        if (!peacock.querySelector('.peacock-body')) {
                            var body = document.createElement('div');
                            body.className = 'peacock-body';
                            body.style.width = '50px';
                            body.style.height = '100px';
                            body.style.borderRadius = '50% 50% 50% 50% / 60% 60% 40% 40%';
                            body.style.backgroundColor = '#6a1b9a';
                            body.style.opacity = '0.2';
                            body.style.position = 'absolute';
                            body.style.bottom = '50px';
                            body.style.right = '50px';

                            var head = document.createElement('div');
                            head.className = 'peacock-head';
                            head.style.width = '30px';
                            head.style.height = '30px';
                            head.style.borderRadius = '50%';
                            head.style.backgroundColor = '#6a1b9a';
                            head.style.opacity = '0.2';
                            head.style.position = 'absolute';
                            head.style.bottom = '140px';
                            head.style.right = '60px';

                            var feathers = document.createElement('div');
                            feathers.className = 'peacock-feathers';
                            feathers.style.width = '150px';
                            feathers.style.height = '200px';
                            feathers.style.borderRadius = '50% 50% 0 0';
                            feathers.style.border = '1px solid #6a1b9a';
                            feathers.style.opacity = '0.1';
                            feathers.style.position = 'absolute';
                            feathers.style.bottom = '100px';
                            feathers.style.right = '0';

                            peacock.appendChild(feathers);
                            peacock.appendChild(body);
                            peacock.appendChild(head);
                        }
                    });

                    // Setup staggered animations
                    var staggerContainers = document.querySelectorAll('.stagger-children');
                    staggerContainers.forEach(function(container) {
                        container.classList.add('visible');
                    });

                    // Setup reveal animations
                    if ('IntersectionObserver' in window) {
                        var revealObserver = new IntersectionObserver(function(entries) {
                            entries.forEach(function(entry) {
                                if (entry.isIntersecting) {
                                    entry.target.classList.add('active');
                                    revealObserver.unobserve(entry.target);
                                }
                            });
                        }, { threshold: 0.1 });

                        var revealElements = document.querySelectorAll('.reveal');
                        revealElements.forEach(function(element) {
                            revealObserver.observe(element);
                        });
                    }

                    // Setup smooth anchor links
                    var anchorLinks = document.querySelectorAll('a[href^="#"]');
                    anchorLinks.forEach(function(link) {
                        link.addEventListener('click', function(e) {
                            var targetId = this.getAttribute('href');

                            // Skip if it's just "#"
                            if (targetId === '#') return;

                            var targetElement = document.querySelector(targetId);

                            if (targetElement) {
                                e.preventDefault();

                                // Scroll to the target element
                                window.scrollTo({
                                    top: targetElement.offsetTop,
                                    behavior: 'smooth'
                                });
                            }
                        });
                    });
                });
            </script>
        </t>
    </template>
</odoo>
