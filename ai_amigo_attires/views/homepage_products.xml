<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Featured Products and New Arrivals Sections -->
    <template id="homepage_products" inherit_id="ai_amigo_attires.homepage">
        <xpath expr="//div[@id='wrap']" position="inside">
            <!-- Featured Products Section -->
            <section class="featured-products-section py-5 fullpage-section" data-section-name="Featured Products" id="featured-products">
                <div class="mandala-decoration" style="top: 15%; left: 5%;"></div>
                <div class="container py-4">
                    <div class="text-center mb-5 stagger-children">
                        <h2 class="display-5 fw-bold mb-3 indian-theme-text-primary">Featured Products</h2>
                        <p class="lead">Handpicked selection of our finest ethnic wear</p>
                    </div>
                    <div class="row g-4 stagger-children">
                        <t t-foreach="bestseller_products" t-as="product">
                            <div class="col-lg-3 col-md-4 col-6">
                                <div class="product-card">
                                    <div class="product-image">
                                        <a t-att-href="'/shop/product/%s' % product.id">
                                            <img t-att-src="'/web/image/product.template/%s/image_1024' % product.id" t-att-alt="product.name"/>
                                        </a>
                                        <div class="product-badges">
                                            <span t-if="product.is_new_arrival" class="badge bg-danger">New</span>
                                            <span t-if="product.is_trending" class="badge bg-warning">Trending</span>
                                            <span t-if="product.is_bestseller" class="badge bg-success">Bestseller</span>
                                        </div>
                                        <div class="product-overlay">
                                            <div class="product-actions">
                                                <a href="#" class="btn quick-view-btn" t-att-data-product-id="product.id">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <a t-att-href="'/shop/product/%s' % product.id" class="btn">
                                                    <i class="fa fa-shopping-cart"></i>
                                                </a>
                                                <button class="btn wishlist-btn">
                                                    <i class="fa fa-heart"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="product-content">
                                        <div class="product-category" t-if="product.public_categ_ids">
                                            <t t-esc="', '.join(product.public_categ_ids.mapped('name'))"/>
                                        </div>
                                        <h3 class="product-title">
                                            <a t-att-href="'/shop/product/%s' % product.id" t-field="product.name"/>
                                        </h3>
                                        <div class="product-price">
                                            <span class="current-price" t-field="product.list_price" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                        </div>
                                        <a t-att-href="'/shop/product/%s' % product.id" class="btn btn-primary w-100 mt-3">View Details</a>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                    <div class="text-center mt-5">
                        <a href="/shop" class="btn btn-lg btn-outline-primary">View All Products</a>
                    </div>
                </div>
            </section>

            <!-- New Arrivals Section -->
            <section class="new-arrivals-section py-5 bg-light fullpage-section" data-section-name="New Arrivals" id="new-arrivals">
                <div class="peacock-decoration" style="bottom: 5%; right: 5%;"></div>
                <div class="container py-4">
                    <div class="text-center mb-5 stagger-children">
                        <h2 class="display-5 fw-bold mb-3 indian-theme-text-primary">New Arrivals</h2>
                        <p class="lead">Latest additions to our collection</p>
                    </div>
                    <div class="row g-4 stagger-children">
                        <t t-foreach="new_arrival_products" t-as="product">
                            <div class="col-lg-3 col-md-4 col-6">
                                <div class="product-card">
                                    <div class="product-image">
                                        <a t-att-href="'/shop/product/%s' % product.id">
                                            <img t-att-src="'/web/image/product.template/%s/image_1024' % product.id" t-att-alt="product.name"/>
                                        </a>
                                        <div class="product-badges">
                                            <span class="badge bg-danger">New</span>
                                        </div>
                                        <div class="product-overlay">
                                            <div class="product-actions">
                                                <a href="#" class="btn quick-view-btn" t-att-data-product-id="product.id">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <a t-att-href="'/shop/product/%s' % product.id" class="btn">
                                                    <i class="fa fa-shopping-cart"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="product-content">
                                        <h3 class="product-title">
                                            <a t-att-href="'/shop/product/%s' % product.id" t-field="product.name"/>
                                        </h3>
                                        <div class="product-price">
                                            <span class="current-price" t-field="product.list_price" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                    <div class="text-center mt-5">
                        <a href="/shop/new-arrivals" class="btn btn-lg btn-outline-primary">View All New Arrivals</a>
                    </div>
                </div>
            </section>
        </xpath>
    </template>
</odoo>
