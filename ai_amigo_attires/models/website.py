from odoo import api, fields, models, _


class Website(models.Model):
    _inherit = 'website'
    
    def get_trending_products(self, limit=10):
        """Get trending products for the website"""
        return self.env['product.template'].search([
            ('website_published', '=', True),
            ('is_trending', '=', True),
        ], limit=limit)
    
    def get_new_arrivals(self, limit=10):
        """Get new arrival products for the website"""
        return self.env['product.template'].search([
            ('website_published', '=', True),
            ('is_new_arrival', '=', True),
        ], limit=limit)
    
    def get_bestsellers(self, limit=10):
        """Get bestseller products for the website"""
        return self.env['product.template'].search([
            ('website_published', '=', True),
            ('is_bestseller', '=', True),
        ], limit=limit)
    
    def get_men_products(self, limit=10):
        """Get men's products for the website"""
        men_category = self.env['product.public.category'].search([('name', 'ilike', 'men')], limit=1)
        if men_category:
            return self.env['product.template'].search([
                ('website_published', '=', True),
                ('public_categ_ids', 'in', men_category.id),
            ], limit=limit)
        return self.env['product.template'].browse()
    
    def get_women_products(self, limit=10):
        """Get women's products for the website"""
        women_category = self.env['product.public.category'].search([('name', 'ilike', 'women')], limit=1)
        if women_category:
            return self.env['product.template'].search([
                ('website_published', '=', True),
                ('public_categ_ids', 'in', women_category.id),
            ], limit=limit)
        return self.env['product.template'].browse()
