from odoo import api, fields, models, _
from odoo.tools.safe_eval import safe_eval


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    # Additional fields for Indian clothing products
    fabric_material = fields.Char(string="Fabric Material")
    work_type = fields.Char(string="Work Type")
    is_stitched = fields.Boolean(string="Is Stitched")

    # Bottom details
    has_bottom = fields.<PERSON>olean(string="Has Bottom")
    bottom_material = fields.Char(string="Bottom Material")
    bottom_is_stitched = fields.<PERSON>olean(string="Bottom Is Stitched")

    # Dupatta details
    has_dupatta = fields.<PERSON><PERSON>an(string="Has Dupatta")
    dupatta_material = fields.Char(string="Dupatta Material")
    dupatta_is_stitched = fields.<PERSON><PERSON>an(string="Dupatta Is Stitched")
    dupatta_length = fields.Float(string="Dupatta Length (meters)")

    # Additional product details
    sku = fields.Char(string="SKU")

    # For trending/new products
    is_new_arrival = fields.<PERSON><PERSON>an(string="Is New Arrival")
    is_trending = fields.<PERSON><PERSON><PERSON>(string="Is Trending")
    is_bestseller = fields.<PERSON><PERSON><PERSON>(string="Is Bestseller")

    def _get_combination_info(self, combination=False, product_id=False, add_qty=1, pricelist=False, parent_combination=False, only_template=False, **kwargs):
        """Override to add custom fields to the combination info"""
        combination_info = super()._get_combination_info(
            combination=combination, product_id=product_id, add_qty=add_qty, pricelist=pricelist,
            parent_combination=parent_combination, only_template=only_template, **kwargs
        )

        if not self or not combination_info:
            return combination_info

        product = self.env['product.product'].browse(combination_info['product_id']) or self

        combination_info.update({
            'fabric_material': product.fabric_material or self.fabric_material or '',
            'work_type': product.work_type or self.work_type or '',
            'is_stitched': product.is_stitched or self.is_stitched or False,
            'has_bottom': product.has_bottom or self.has_bottom or False,
            'bottom_material': product.bottom_material or self.bottom_material or '',
            'bottom_is_stitched': product.bottom_is_stitched or self.bottom_is_stitched or False,
            'has_dupatta': product.has_dupatta or self.has_dupatta or False,
            'dupatta_material': product.dupatta_material or self.dupatta_material or '',
            'dupatta_is_stitched': product.dupatta_is_stitched or self.dupatta_is_stitched or False,
            'dupatta_length': product.dupatta_length or self.dupatta_length or 0.0,
            'sku': product.sku or self.sku or '',
            'is_new_arrival': product.is_new_arrival or self.is_new_arrival or False,
            'is_trending': product.is_trending or self.is_trending or False,
            'is_bestseller': product.is_bestseller or self.is_bestseller or False,
            'formatted_description': self.get_formatted_description(),
        })

        return combination_info

    def get_formatted_description(self):
        """Generate a formatted product description with emojis based on product attributes"""
        self.ensure_one()

        description = []

        # Add new catalog header if it's a new arrival
        if self.is_new_arrival:
            description.append("🔥 New Catalog Launched 🔥\n")

        # Add product name
        description.append(f"{self.name}\n")

        # Add SKU
        if self.sku:
            description.append(f"SKU = {self.sku}")

        # Add color variants
        color_attribute = self.env['product.attribute'].search([('name', 'ilike', 'color')], limit=1)
        if color_attribute:
            color_values = self.attribute_line_ids.filtered(lambda l: l.attribute_id.id == color_attribute.id).value_ids
            if color_values:
                description.append(f"Color = {', '.join(color_values.mapped('name'))}")

        # Add price
        description.append(f"\nPrice = {self.list_price}")

        # Add sizes
        size_attribute = self.env['product.attribute'].search([('name', 'ilike', 'size')], limit=1)
        if size_attribute:
            size_values = self.attribute_line_ids.filtered(lambda l: l.attribute_id.id == size_attribute.id).value_ids
            if size_values:
                description.append(f"\nSize = {', '.join(size_values.mapped('name'))}")

        # Add product details section
        description.append("\n👗 Product Detail 👗\n")

        # Main product details
        description.append(f"➡️ {self.name.split()[0]} = {'Stitched' if self.is_stitched else 'Unstitched'}")
        if self.fabric_material:
            description.append(f"➡️ {self.name.split()[0]} Material = {self.fabric_material}")
        if self.work_type:
            description.append(f"➡️ Work = {self.work_type}")

        # Bottom details
        if self.has_bottom:
            description.append(f"\n➡️ Bottom = {'Stitched' if self.bottom_is_stitched else 'Unstitched'}")
            if self.bottom_material:
                description.append(f"➡️ Bottom Material = {self.bottom_material}")

        # Dupatta details
        if self.has_dupatta:
            description.append(f"\n➡️ Dupatta = {'Stitched' if self.dupatta_is_stitched else 'Unstitched'}")
            if self.dupatta_material:
                description.append(f"➡️ Dupatta Material = {self.dupatta_material}")
            if self.dupatta_length:
                description.append(f"➡️ Dupatta Length = {self.dupatta_length} Meter")

        return "\n".join(description)