{
    'name': 'Amigo Attires Website',
    'version': '1.0',
    'category': 'Website/eCommerce',
    'summary': 'Custom eCommerce website for Amigo Attires',
    'description': """
        Amigo Attires eCommerce Website
        ===============================

        This module provides a stunning, modern, and visually appealing eCommerce website
        for Amigo Attires, featuring Indian themes with Mandala and Peacock designs.

        The website is designed to cater to an international audience with a focus on
        selling men's and women's clothing.
    """,
    'author': 'Arihant AI',
    'website': 'https://arihantai.com',
    'depends': [
        # 'base',
        'website',
        'website_sale',
        # 'website_sale_wishlist',
        # 'website_sale_comparison',
        # 'website_sale_stock',
        # 'delivery',
        # 'website_blog',
        # 'website_payment',
        # 'payment',
    ],
    'data': [
    #     # Security
    #     'security/ir.model.access.csv',

    #     # Data
    #     'data/website_data.xml',
    #     'data/product_data.xml',
    #     'data/product_category_data.xml',

    #     # Views
    #     'views/assets.xml',
    #     'views/templates.xml',
    #     'views/product_template_views.xml',
    #     'views/website_menu.xml',
    #     'views/homepage_base.xml',
    #     'views/homepage_hero.xml',
    #     'views/homepage_products.xml',
    #     'views/homepage_collections.xml',
    #     'views/homepage_features.xml',
    #     'views/policy_base.xml',
    #     'views/privacy_policy_content.xml',
    #     'views/terms_service_content.xml',
    #     'views/shipping_policy_content.xml',
    #     'views/return_policy_content.xml',
    ],
    'assets': {
        # 'website.assets_frontend': [
        #     # External CSS
        #     ('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap', {'type': 'url'}),
        #     ('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css', {'type': 'url'}),

        #     # CSS
        #     'ai_amigo_attires/static/src/scss/style.scss',
        #     'ai_amigo_attires/static/src/css/product_card.css',
        #     'ai_amigo_attires/static/src/css/animations.css',
        #     'ai_amigo_attires/static/src/css/smooth_scroll.css',
        #     'ai_amigo_attires/static/src/css/fullpage_scroll.css',
        #     'ai_amigo_attires/static/src/css/responsive.css',

        #     # JS
        #     'ai_amigo_attires/static/src/js/mandala_animation.js',
        #     'ai_amigo_attires/static/src/js/product_card.js',
        #     'ai_amigo_attires/static/src/js/smooth_scroll.js',
        #     'ai_amigo_attires/static/src/js/fullpage_scroll.js',
        #     'ai_amigo_attires/static/src/js/animations.js',
        #     'ai_amigo_attires/static/src/js/main.js',
        # ],
    },
    'demo': [
        # 'data/demo_data.xml',
    ],
    'images': [
        # 'static/description/banner.png',
    ],
    'installable': True,
    'application': True,
    'license': 'LGPL-3',
}
