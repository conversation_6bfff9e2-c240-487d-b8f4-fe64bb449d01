<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Main Categories -->
    <record id="category_men" model="product.public.category">
        <field name="name">Men</field>
        <field name="sequence">10</field>
    </record>
    
    <record id="category_women" model="product.public.category">
        <field name="name">Women</field>
        <field name="sequence">20</field>
    </record>
    
    <record id="category_kids" model="product.public.category">
        <field name="name">Kids</field>
        <field name="sequence">30</field>
    </record>
    
    <record id="category_accessories" model="product.public.category">
        <field name="name">Accessories</field>
        <field name="sequence">40</field>
    </record>
    
    <!-- Men's Subcategories -->
    <record id="category_men_kurta" model="product.public.category">
        <field name="name">Kurta</field>
        <field name="parent_id" ref="category_men"/>
        <field name="sequence">10</field>
    </record>
    
    <record id="category_men_kurta_pajama" model="product.public.category">
        <field name="name"><PERSON><PERSON></field>
        <field name="parent_id" ref="category_men"/>
        <field name="sequence">20</field>
    </record>
    
    <record id="category_men_sherwani" model="product.public.category">
        <field name="name">Sherwani</field>
        <field name="parent_id" ref="category_men"/>
        <field name="sequence">30</field>
    </record>
    
    <record id="category_men_nehru_jacket" model="product.public.category">
        <field name="name">Nehru Jacket</field>
        <field name="parent_id" ref="category_men"/>
        <field name="sequence">40</field>
    </record>
    
    <record id="category_men_dhoti" model="product.public.category">
        <field name="name">Dhoti</field>
        <field name="parent_id" ref="category_men"/>
        <field name="sequence">50</field>
    </record>
    
    <!-- Women's Subcategories -->
    <record id="category_women_saree" model="product.public.category">
        <field name="name">Saree</field>
        <field name="parent_id" ref="category_women"/>
        <field name="sequence">10</field>
    </record>
    
    <record id="category_women_kurta" model="product.public.category">
        <field name="name">Kurta</field>
        <field name="parent_id" ref="category_women"/>
        <field name="sequence">20</field>
    </record>
    
    <record id="category_women_kurta_sets" model="product.public.category">
        <field name="name">Kurta Sets</field>
        <field name="parent_id" ref="category_women"/>
        <field name="sequence">30</field>
    </record>
    
    <record id="category_women_lehenga" model="product.public.category">
        <field name="name">Lehenga</field>
        <field name="parent_id" ref="category_women"/>
        <field name="sequence">40</field>
    </record>
    
    <record id="category_women_salwar_kameez" model="product.public.category">
        <field name="name">Salwar Kameez</field>
        <field name="parent_id" ref="category_women"/>
        <field name="sequence">50</field>
    </record>
    
    <record id="category_women_gown" model="product.public.category">
        <field name="name">Gown</field>
        <field name="parent_id" ref="category_women"/>
        <field name="sequence">60</field>
    </record>
    
    <!-- Kids Subcategories -->
    <record id="category_kids_boys" model="product.public.category">
        <field name="name">Boys</field>
        <field name="parent_id" ref="category_kids"/>
        <field name="sequence">10</field>
    </record>
    
    <record id="category_kids_girls" model="product.public.category">
        <field name="name">Girls</field>
        <field name="parent_id" ref="category_kids"/>
        <field name="sequence">20</field>
    </record>
    
    <!-- Boys Subcategories -->
    <record id="category_kids_boys_kurta" model="product.public.category">
        <field name="name">Kurta</field>
        <field name="parent_id" ref="category_kids_boys"/>
        <field name="sequence">10</field>
    </record>
    
    <record id="category_kids_boys_kurta_pajama" model="product.public.category">
        <field name="name">Kurta Pajama</field>
        <field name="parent_id" ref="category_kids_boys"/>
        <field name="sequence">20</field>
    </record>
    
    <record id="category_kids_boys_dhoti" model="product.public.category">
        <field name="name">Dhoti</field>
        <field name="parent_id" ref="category_kids_boys"/>
        <field name="sequence">30</field>
    </record>
    
    <!-- Girls Subcategories -->
    <record id="category_kids_girls_lehenga" model="product.public.category">
        <field name="name">Lehenga</field>
        <field name="parent_id" ref="category_kids_girls"/>
        <field name="sequence">10</field>
    </record>
    
    <record id="category_kids_girls_gown" model="product.public.category">
        <field name="name">Gown</field>
        <field name="parent_id" ref="category_kids_girls"/>
        <field name="sequence">20</field>
    </record>
    
    <record id="category_kids_girls_kurta_sets" model="product.public.category">
        <field name="name">Kurta Sets</field>
        <field name="parent_id" ref="category_kids_girls"/>
        <field name="sequence">30</field>
    </record>
    
    <!-- Accessories Subcategories -->
    <record id="category_accessories_jewelry" model="product.public.category">
        <field name="name">Jewelry</field>
        <field name="parent_id" ref="category_accessories"/>
        <field name="sequence">10</field>
    </record>
    
    <record id="category_accessories_footwear" model="product.public.category">
        <field name="name">Footwear</field>
        <field name="parent_id" ref="category_accessories"/>
        <field name="sequence">20</field>
    </record>
    
    <record id="category_accessories_bags" model="product.public.category">
        <field name="name">Bags</field>
        <field name="parent_id" ref="category_accessories"/>
        <field name="sequence">30</field>
    </record>
    
    <record id="category_accessories_stoles" model="product.public.category">
        <field name="name">Stoles</field>
        <field name="parent_id" ref="category_accessories"/>
        <field name="sequence">40</field>
    </record>
</odoo>
