<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Website Settings -->
        <record id="website_amigo_attires" model="website">
            <field name="name">Amigo Attires</field>
            <field name="domain">amigoattires.com</field>
            <field name="company_id" ref="base.main_company"/>
            <field name="favicon" type="base64" file="ai_amigo_attires/static/src/img/favicon.ico"/>
            <field name="user_id" ref="base.public_user"/>
            <field name="language_ids" eval="[(6, 0, [ref('base.lang_en')])]"/>
            <field name="default_lang_id" ref="base.lang_en"/>
        </record>
        
        <!-- Website Menu (Additional to website_menu.xml) -->
        <record id="menu_amigo_attires_shop" model="website.menu">
            <field name="name">Shop</field>
            <field name="url">/shop</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">35</field>
            <field name="website_id" ref="website_amigo_attires"/>
        </record>
    </data>
</odoo>
