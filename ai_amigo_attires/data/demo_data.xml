<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Product Categories -->
        <record id="product_category_men" model="product.public.category">
            <field name="name">Men</field>
            <field name="sequence">10</field>
        </record>
        
        <record id="product_category_men_kurta" model="product.public.category">
            <field name="name">Men's Kurta</field>
            <field name="sequence">10</field>
            <field name="parent_id" ref="product_category_men"/>
        </record>
        
        <record id="product_category_men_kurta_pajama" model="product.public.category">
            <field name="name">Men's Kurt<PERSON> Pajama</field>
            <field name="sequence">20</field>
            <field name="parent_id" ref="product_category_men"/>
        </record>
        
        <record id="product_category_men_sherwani" model="product.public.category">
            <field name="name">Men's Sherwani</field>
            <field name="sequence">30</field>
            <field name="parent_id" ref="product_category_men"/>
        </record>
        
        <record id="product_category_women" model="product.public.category">
            <field name="name">Women</field>
            <field name="sequence">20</field>
        </record>
        
        <record id="product_category_women_saree" model="product.public.category">
            <field name="name">Women's Saree</field>
            <field name="sequence">10</field>
            <field name="parent_id" ref="product_category_women"/>
        </record>
        
        <record id="product_category_women_kurta" model="product.public.category">
            <field name="name">Women's Kurta</field>
            <field name="sequence">20</field>
            <field name="parent_id" ref="product_category_women"/>
        </record>
        
        <record id="product_category_women_kurta_sets" model="product.public.category">
            <field name="name">Women's Kurta Sets</field>
            <field name="sequence">30</field>
            <field name="parent_id" ref="product_category_women"/>
        </record>
        
        <record id="product_category_women_lehenga" model="product.public.category">
            <field name="name">Women's Lehenga</field>
            <field name="sequence">40</field>
            <field name="parent_id" ref="product_category_women"/>
        </record>
        
        <!-- Demo Product: Women's Kurta Set -->
        <record id="product_template_women_kurta_set_1" model="product.template">
            <field name="name">Women's Vichitra Silk Embroidered Kurta Set</field>
            <field name="description_sale">Beautiful Women's Vichitra Silk with Cotton Lining Embroidered and Printed Straight Kurta with Rayon Pant And Taby Laced Dupatta Sets</field>
            <field name="list_price">850</field>
            <field name="standard_price">600</field>
            <field name="type">product</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">WKSET-001</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="public_categ_ids" eval="[(6, 0, [ref('product_category_women_kurta_sets')])]"/>
            <field name="website_published" eval="True"/>
            <field name="website_sequence">10</field>
            <field name="is_published" eval="True"/>
            
            <!-- Custom fields -->
            <field name="sku">648</field>
            <field name="fabric_material">Vichitra Silk with Cotton Lining</field>
            <field name="work_type">Embroidered and Printed</field>
            <field name="is_stitched" eval="True"/>
            <field name="has_bottom" eval="True"/>
            <field name="bottom_material">Rayon</field>
            <field name="bottom_is_stitched" eval="True"/>
            <field name="has_dupatta" eval="True"/>
            <field name="dupatta_material">Taby</field>
            <field name="dupatta_is_stitched" eval="True"/>
            <field name="dupatta_length">2.10</field>
            <field name="is_new_arrival" eval="True"/>
            <field name="is_trending" eval="True"/>
        </record>
        
        <!-- Product Attribute Lines for Women's Kurta Set -->
        <record id="product_template_attribute_line_color_women_kurta_set_1" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="product_template_women_kurta_set_1"/>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_red'), ref('product_attribute_value_rani')])]"/>
        </record>
        
        <record id="product_template_attribute_line_size_women_kurta_set_1" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="product_template_women_kurta_set_1"/>
            <field name="attribute_id" ref="product_attribute_size"/>
            <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_s'), ref('product_attribute_value_m'), ref('product_attribute_value_l'), ref('product_attribute_value_xl'), ref('product_attribute_value_xxl'), ref('product_attribute_value_3xl')])]"/>
        </record>
    </data>
</odoo>
