<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Product Attributes -->
        <record id="product_attribute_color" model="product.attribute">
            <field name="name">Color</field>
            <field name="sequence">1</field>
            <field name="create_variant">always</field>
            <field name="display_type">color</field>
        </record>
        
        <!-- Color Attribute Values -->
        <record id="product_attribute_value_red" model="product.attribute.value">
            <field name="name">Red</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">1</field>
            <field name="html_color">#FF0000</field>
        </record>
        
        <record id="product_attribute_value_rani" model="product.attribute.value">
            <field name="name">Rani</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">2</field>
            <field name="html_color">#FF1493</field>
        </record>
        
        <record id="product_attribute_value_blue" model="product.attribute.value">
            <field name="name">Blue</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">3</field>
            <field name="html_color">#0000FF</field>
        </record>
        
        <record id="product_attribute_value_green" model="product.attribute.value">
            <field name="name">Green</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">4</field>
            <field name="html_color">#008000</field>
        </record>
        
        <record id="product_attribute_value_yellow" model="product.attribute.value">
            <field name="name">Yellow</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">5</field>
            <field name="html_color">#FFFF00</field>
        </record>
        
        <record id="product_attribute_value_black" model="product.attribute.value">
            <field name="name">Black</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">6</field>
            <field name="html_color">#000000</field>
        </record>
        
        <record id="product_attribute_value_white" model="product.attribute.value">
            <field name="name">White</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">7</field>
            <field name="html_color">#FFFFFF</field>
        </record>
        
        <record id="product_attribute_value_maroon" model="product.attribute.value">
            <field name="name">Maroon</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">8</field>
            <field name="html_color">#800000</field>
        </record>
        
        <record id="product_attribute_value_navy" model="product.attribute.value">
            <field name="name">Navy</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">9</field>
            <field name="html_color">#000080</field>
        </record>
        
        <record id="product_attribute_value_pink" model="product.attribute.value">
            <field name="name">Pink</field>
            <field name="attribute_id" ref="product_attribute_color"/>
            <field name="sequence">10</field>
            <field name="html_color">#FFC0CB</field>
        </record>
        
        <!-- Size Attribute -->
        <record id="product_attribute_size" model="product.attribute">
            <field name="name">Size</field>
            <field name="sequence">2</field>
            <field name="create_variant">always</field>
            <field name="display_type">select</field>
        </record>
        
        <!-- Size Attribute Values -->
        <record id="product_attribute_value_s" model="product.attribute.value">
            <field name="name">S</field>
            <field name="attribute_id" ref="product_attribute_size"/>
            <field name="sequence">1</field>
        </record>
        
        <record id="product_attribute_value_m" model="product.attribute.value">
            <field name="name">M</field>
            <field name="attribute_id" ref="product_attribute_size"/>
            <field name="sequence">2</field>
        </record>
        
        <record id="product_attribute_value_l" model="product.attribute.value">
            <field name="name">L</field>
            <field name="attribute_id" ref="product_attribute_size"/>
            <field name="sequence">3</field>
        </record>
        
        <record id="product_attribute_value_xl" model="product.attribute.value">
            <field name="name">XL</field>
            <field name="attribute_id" ref="product_attribute_size"/>
            <field name="sequence">4</field>
        </record>
        
        <record id="product_attribute_value_xxl" model="product.attribute.value">
            <field name="name">XXL</field>
            <field name="attribute_id" ref="product_attribute_size"/>
            <field name="sequence">5</field>
        </record>
        
        <record id="product_attribute_value_3xl" model="product.attribute.value">
            <field name="name">3XL</field>
            <field name="attribute_id" ref="product_attribute_size"/>
            <field name="sequence">6</field>
        </record>
    </data>
</odoo>
