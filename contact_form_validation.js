
// Enhanced form validation for profectusaccounts.com contact forms
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactus_form');
    
    if (contactForm) {
        // Add real-time validation
        const requiredFields = contactForm.querySelectorAll('input[required], textarea[required]');
        
        requiredFields.forEach(field => {
            field.addEventListener('blur', function() {
                validateField(this);
            });
            
            field.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    validateField(this);
                }
            });
        });
        
        // Email validation
        const emailField = contactForm.querySelector('input[type="email"]');
        if (emailField) {
            emailField.addEventListener('blur', function() {
                validateEmail(this);
            });
        }
        
        // Phone validation
        const phoneField = contactForm.querySelector('input[type="tel"]');
        if (phoneField) {
            phoneField.addEventListener('blur', function() {
                validatePhone(this);
            });
        }
        
        // Form submission validation
        contactForm.addEventListener('submit', function(e) {
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!validateField(field)) {
                    isValid = false;
                }
            });
            
            if (emailField && !validateEmail(emailField)) {
                isValid = false;
            }
            
            if (!isValid) {
                e.preventDefault();
                showNotification('Please correct the errors in the form.', 'error');
            } else {
                showNotification('Submitting your message...', 'info');
            }
        });
    }
    
    function validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');
        
        if (isRequired && !value) {
            setFieldError(field, 'This field is required.');
            return false;
        } else {
            clearFieldError(field);
            return true;
        }
    }
    
    function validateEmail(field) {
        const email = field.value.trim();
        const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        
        if (email && !emailPattern.test(email)) {
            setFieldError(field, 'Please enter a valid email address.');
            return false;
        } else {
            clearFieldError(field);
            return true;
        }
    }
    
    function validatePhone(field) {
        const phone = field.value.trim();
        const phonePattern = /^[\+]?[\d\s\-\(\)]{7,15}$/;
        
        if (phone && !phonePattern.test(phone)) {
            setFieldError(field, 'Please enter a valid phone number.');
            return false;
        } else {
            clearFieldError(field);
            return true;
        }
    }
    
    function setFieldError(field, message) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        
        let feedback = field.parentNode.querySelector('.invalid-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            field.parentNode.appendChild(feedback);
        }
        feedback.textContent = message;
    }
    
    function clearFieldError(field) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = '';
        }
    }
    
    function showNotification(message, type) {
        const alertClass = type === 'error' ? 'alert-danger' : type === 'info' ? 'alert-info' : 'alert-success';
        const notification = document.createElement('div');
        notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
});
        