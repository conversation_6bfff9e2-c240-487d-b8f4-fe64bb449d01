#!/usr/bin/env python3
"""
Create Partner View Extensions Script
This script manually creates the partner form view extensions in the database
"""

import xmlrpc.client
import time
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

def connect_to_odoo():
    """Connect to Odoo"""
    try:
        logger.info(f"Connecting to Odoo at {URL}")
        common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
        models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
        
        uid = common.authenticate(DB, USERNAME, PASSWORD, {})
        if not uid:
            raise Exception("Authentication failed")
            
        logger.info(f"Successfully connected as user ID: {uid}")
        return common, models, uid
        
    except Exception as e:
        logger.error(f"Connection failed: {e}")
        return None, None, None

def get_base_partner_view_id(models, uid):
    """Get the ID of the base partner form view"""
    try:
        # Search for the base partner form view by XML ID
        views = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.model.data', 'search_read',
            [[('name', '=', 'view_partner_form'), ('module', '=', 'base')]],
            {'fields': ['res_id']}
        )

        if views:
            return views[0]['res_id']

        # If not found by XML ID, try searching by name
        views = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.ui.view', 'search_read',
            [[('name', 'like', 'partner_form'), ('model', '=', 'res.partner')]],
            {'fields': ['id', 'name', 'model']}
        )

        if views:
            logger.info(f"Found partner form view: {views[0]['name']}")
            return views[0]['id']
        else:
            logger.error("Base partner form view not found")
            return None

    except Exception as e:
        logger.error(f"Error getting base partner view ID: {e}")
        return None

def create_view_extension(models, uid, view_info):
    """Create a view extension"""
    try:
        # Check if view already exists
        existing_views = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.ui.view', 'search',
            [[('name', '=', view_info['name'])]]
        )
        
        if existing_views:
            logger.info(f"View {view_info['name']} already exists")
            return True
        
        # Create the view
        view_id = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.ui.view', 'create',
            [view_info]
        )
        
        logger.info(f"✅ Created view {view_info['name']} with ID {view_id}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating view {view_info['name']}: {e}")
        return False

def main():
    """Main execution function"""
    logger.info("=" * 60)
    logger.info("CREATE PARTNER VIEW EXTENSIONS SCRIPT")
    logger.info("=" * 60)
    
    # Connect to Odoo
    common, models, uid = connect_to_odoo()
    if not models:
        logger.error("Failed to connect to Odoo. Exiting.")
        sys.exit(1)
    
    # Get base partner view ID
    base_view_id = get_base_partner_view_id(models, uid)
    if not base_view_id:
        logger.error("Failed to get base partner view ID. Exiting.")
        sys.exit(1)
    
    logger.info(f"Base partner form view ID: {base_view_id}")
    
    # Define view extensions
    view_extensions = [
        {
            'name': 'res.partner.form.client.documents',
            'model': 'res.partner',
            'inherit_id': base_view_id,
            'arch': '''
<xpath expr="//notebook" position="inside">
    <page string="Documents" name="documents_info">
        <field name="x_client_document_ids" nolabel="1">
            <list>
                <field name="name"/>
                <field name="x_task_ref"/>
                <field name="x_document_folder"/>
            </list>
        </field>
    </page>
</xpath>''',
            'active': True,
            'type': 'form'
        },
        {
            'name': 'res.partner.form.credentials',
            'model': 'res.partner',
            'inherit_id': base_view_id,
            'arch': '''
<xpath expr="//notebook" position="inside">
    <page string="Credentials" name="credentials_info">
        <field name="x_credential_ids" nolabel="1">
            <list editable="bottom">
                <field name="name"/>
                <field name="x_type"/>
                <field name="x_url" widget="url"/>
                <field name="x_username"/>
            </list>
        </field>
    </page>
</xpath>''',
            'active': True,
            'type': 'form'
        },
        {
            'name': 'res.partner.form.dsc',
            'model': 'res.partner',
            'inherit_id': base_view_id,
            'arch': '''
<xpath expr="//notebook" position="inside">
    <page string="DSC Management" name="dsc_info">
        <field name="x_dsc_ids" nolabel="1">
            <list editable="bottom">
                <field name="name"/>
                <field name="x_expiry_date"/>
                <field name="x_location"/>
            </list>
        </field>
    </page>
</xpath>''',
            'active': True,
            'type': 'form'
        }
    ]
    
    # Create view extensions
    logger.info("\n" + "=" * 40)
    logger.info("CREATING PARTNER VIEW EXTENSIONS")
    logger.info("=" * 40)
    
    creation_results = {}
    for view_ext in view_extensions:
        creation_results[view_ext['name']] = create_view_extension(models, uid, view_ext)
    
    # Test if views were created successfully
    logger.info("\n" + "=" * 40)
    logger.info("TESTING VIEW CREATION")
    logger.info("=" * 40)
    
    view_names_to_check = [
        'res.partner.form.client.documents',
        'res.partner.form.credentials',
        'res.partner.form.dsc'
    ]
    
    verification_results = {}
    for view_name in view_names_to_check:
        try:
            views = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.ui.view', 'search_read',
                [[('name', '=', view_name)]],
                {'fields': ['name', 'model', 'active', 'inherit_id']}
            )
            
            if views:
                view = views[0]
                logger.info(f"✅ {view_name}: Found - Model: {view['model']}, Active: {view['active']}")
                verification_results[view_name] = True
            else:
                logger.warning(f"❌ {view_name}: Not found in database")
                verification_results[view_name] = False
                
        except Exception as e:
            logger.error(f"❌ Error checking {view_name}: {e}")
            verification_results[view_name] = False
    
    # Final summary
    logger.info("\n" + "=" * 60)
    logger.info("FINAL SUMMARY")
    logger.info("=" * 60)
    
    logger.info("View Creation Results:")
    for view_name, success in creation_results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        logger.info(f"  {view_name}: {status}")
    
    logger.info("\nView Verification Results:")
    for view_name, exists in verification_results.items():
        status = "✅ EXISTS" if exists else "❌ MISSING"
        logger.info(f"  {view_name}: {status}")
    
    # Calculate success rates
    successful_creations = sum(creation_results.values())
    successful_verifications = sum(verification_results.values())
    
    logger.info(f"\nOverall Results:")
    logger.info(f"  View creations: {successful_creations}/{len(creation_results)}")
    logger.info(f"  View verifications: {successful_verifications}/{len(verification_results)}")
    
    if successful_verifications == len(verification_results):
        logger.info("\n🎉 SUCCESS! All partner view extensions have been created!")
        logger.info("\n📋 Next Steps:")
        logger.info("1. Open a partner record in Odoo")
        logger.info("2. You should now see new tabs: Documents, Credentials, DSC Management")
        logger.info("3. The Groups and Services tabs should already be visible")
        logger.info("4. Test the functionality of each tab")
    else:
        logger.warning(f"\n⚠️ Partial success: {successful_verifications}/{len(verification_results)} views verified")
    
    logger.info("\nScript completed!")

if __name__ == "__main__":
    main()
