2025-07-15 08:36:54,363 - INFO - ============================================================
2025-07-15 08:36:54,364 - INFO - <PERSON><PERSON> Modules Upgrade Script Started
2025-07-15 08:36:54,364 - INFO - ============================================================
2025-07-15 08:36:54,364 - INFO - Connecting to Odoo at https://sdpm.arihantai.com
2025-07-15 08:36:54,878 - INFO - Successfully connected as user ID: 6
2025-07-15 08:36:54,878 - INFO - 
========================================
2025-07-15 08:36:54,878 - INFO - CHECKING INITIAL LOGS
2025-07-15 08:36:54,878 - INFO - ========================================
2025-07-15 08:36:54,878 - INFO - Checking recent logs for errors...
2025-07-15 08:36:55,418 - ERROR - Error executing ir.logging.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.logging\'\n'>
2025-07-15 08:36:55,418 - ERROR - Error checking logs: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.logging\'\n'>
2025-07-15 08:36:55,418 - INFO - 
========================================
2025-07-15 08:36:55,418 - INFO - UPGRADING IMCA MODULES
2025-07-15 08:36:55,418 - INFO - ========================================
2025-07-15 08:36:55,418 - INFO - Starting upgrade for module: imca_groups
2025-07-15 08:36:55,436 - ERROR - Error executing ir.module.module.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:36:55,437 - ERROR - Error getting module info for imca_groups: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:36:55,437 - ERROR - Module imca_groups not found
2025-07-15 08:36:56,438 - INFO - Starting upgrade for module: imca_services
2025-07-15 08:36:56,464 - ERROR - Error executing ir.module.module.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:36:56,465 - ERROR - Error getting module info for imca_services: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:36:56,465 - ERROR - Module imca_services not found
2025-07-15 08:36:57,466 - INFO - Starting upgrade for module: imca_client_documents
2025-07-15 08:36:57,484 - ERROR - Error executing ir.module.module.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:36:57,484 - ERROR - Error getting module info for imca_client_documents: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:36:57,484 - ERROR - Module imca_client_documents not found
2025-07-15 08:36:58,485 - INFO - Starting upgrade for module: imca_crednetials_manager
2025-07-15 08:36:58,498 - ERROR - Error executing ir.module.module.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:36:58,498 - ERROR - Error getting module info for imca_crednetials_manager: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:36:58,498 - ERROR - Module imca_crednetials_manager not found
2025-07-15 08:36:59,499 - INFO - Starting upgrade for module: imca_dsc_management
2025-07-15 08:36:59,519 - ERROR - Error executing ir.module.module.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:36:59,519 - ERROR - Error getting module info for imca_dsc_management: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:36:59,519 - ERROR - Module imca_dsc_management not found
2025-07-15 08:37:00,520 - INFO - Waiting 10 seconds for upgrades to complete...
2025-07-15 08:37:10,531 - INFO - 
========================================
2025-07-15 08:37:10,531 - INFO - CHECKING LOGS AFTER UPGRADE
2025-07-15 08:37:10,531 - INFO - ========================================
2025-07-15 08:37:10,531 - INFO - Checking recent logs for errors...
2025-07-15 08:37:10,547 - ERROR - Error executing ir.logging.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.logging\'\n'>
2025-07-15 08:37:10,547 - ERROR - Error checking logs: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.logging\'\n'>
2025-07-15 08:37:10,547 - INFO - 
========================================
2025-07-15 08:37:10,548 - INFO - TESTING MODEL ACCESS
2025-07-15 08:37:10,548 - INFO - ========================================
2025-07-15 08:37:10,548 - INFO - Testing partner model access...
2025-07-15 08:37:10,571 - ERROR - Error executing res.partner.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'res.partner\'\n'>
2025-07-15 08:37:10,571 - ERROR - Error testing partner access: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'res.partner\'\n'>
2025-07-15 08:37:10,571 - INFO - Testing Groups model...
2025-07-15 08:37:10,588 - ERROR - Error executing x_groups.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_groups\'\n'>
2025-07-15 08:37:10,588 - ERROR - ✗ Groups model failed: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_groups\'\n'>
2025-07-15 08:37:10,588 - INFO - Testing Services model...
2025-07-15 08:37:10,602 - ERROR - Error executing x_services.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_services\'\n'>
2025-07-15 08:37:10,602 - ERROR - ✗ Services model failed: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_services\'\n'>
2025-07-15 08:37:10,602 - INFO - Testing Client Documents model...
2025-07-15 08:37:10,621 - ERROR - Error executing x_client_documents.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_client_documents\'\n'>
2025-07-15 08:37:10,621 - ERROR - ✗ Client Documents model failed: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_client_documents\'\n'>
2025-07-15 08:37:10,621 - INFO - Testing Credentials model...
2025-07-15 08:37:10,642 - ERROR - Error executing x_credentials.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_credentials\'\n'>
2025-07-15 08:37:10,643 - ERROR - ✗ Credentials model failed: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_credentials\'\n'>
2025-07-15 08:37:10,643 - INFO - Testing DSC Management model...
2025-07-15 08:37:10,663 - ERROR - Error executing x_dsc.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_dsc\'\n'>
2025-07-15 08:37:10,663 - ERROR - ✗ DSC Management model failed: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_dsc\'\n'>
2025-07-15 08:37:10,663 - INFO - 
============================================================
2025-07-15 08:37:10,663 - INFO - UPGRADE SUMMARY
2025-07-15 08:37:10,664 - INFO - ============================================================
2025-07-15 08:37:10,664 - INFO - Module Upgrade Results:
2025-07-15 08:37:10,664 - INFO -   imca_groups: ✗ FAILED
2025-07-15 08:37:10,664 - INFO -   imca_services: ✗ FAILED
2025-07-15 08:37:10,664 - INFO -   imca_client_documents: ✗ FAILED
2025-07-15 08:37:10,664 - INFO -   imca_crednetials_manager: ✗ FAILED
2025-07-15 08:37:10,664 - INFO -   imca_dsc_management: ✗ FAILED
2025-07-15 08:37:10,664 - INFO - 
Partner Model Test: ✗ FAILED
2025-07-15 08:37:10,664 - INFO - 
IMCA Model Tests:
2025-07-15 08:37:10,664 - INFO -   x_groups: ✗ FAILED
2025-07-15 08:37:10,664 - INFO -   x_services: ✗ FAILED
2025-07-15 08:37:10,664 - INFO -   x_client_documents: ✗ FAILED
2025-07-15 08:37:10,664 - INFO -   x_credentials: ✗ FAILED
2025-07-15 08:37:10,664 - INFO -   x_dsc: ✗ FAILED
2025-07-15 08:37:10,664 - INFO - 
Total Error Logs Found: 0
2025-07-15 08:37:10,664 - WARNING - 
⚠️  Some issues detected. Please review the logs above.
2025-07-15 08:37:10,664 - INFO - 
Upgrade script completed.
2025-07-15 08:37:21,123 - INFO - ============================================================
2025-07-15 08:37:21,124 - INFO - IMCA Modules Upgrade Script Started
2025-07-15 08:37:21,124 - INFO - ============================================================
2025-07-15 08:37:21,124 - INFO - Connecting to Odoo at https://sdpm.arihantai.com
2025-07-15 08:37:21,758 - INFO - Successfully connected as user ID: 6
2025-07-15 08:37:21,758 - INFO - 
========================================
2025-07-15 08:37:21,758 - INFO - CHECKING INITIAL LOGS
2025-07-15 08:37:21,758 - INFO - ========================================
2025-07-15 08:37:21,758 - INFO - Checking recent logs for errors...
2025-07-15 08:37:21,817 - ERROR - Error executing ir.logging.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.logging\'\n'>
2025-07-15 08:37:21,818 - ERROR - Error checking logs: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.logging\'\n'>
2025-07-15 08:37:21,818 - INFO - 
========================================
2025-07-15 08:37:21,818 - INFO - UPGRADING IMCA MODULES
2025-07-15 08:37:21,818 - INFO - ========================================
2025-07-15 08:37:21,818 - INFO - Starting upgrade for module: imca_groups
2025-07-15 08:37:21,839 - ERROR - Error executing ir.module.module.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:37:21,839 - ERROR - Error getting module info for imca_groups: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:37:21,839 - ERROR - Module imca_groups not found
2025-07-15 08:37:22,841 - INFO - Starting upgrade for module: imca_services
2025-07-15 08:37:22,858 - ERROR - Error executing ir.module.module.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:37:22,858 - ERROR - Error getting module info for imca_services: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:37:22,858 - ERROR - Module imca_services not found
2025-07-15 08:37:23,860 - INFO - Starting upgrade for module: imca_client_documents
2025-07-15 08:37:23,886 - ERROR - Error executing ir.module.module.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:37:23,886 - ERROR - Error getting module info for imca_client_documents: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:37:23,886 - ERROR - Module imca_client_documents not found
2025-07-15 08:37:24,887 - INFO - Starting upgrade for module: imca_crednetials_manager
2025-07-15 08:37:24,902 - ERROR - Error executing ir.module.module.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:37:24,902 - ERROR - Error getting module info for imca_crednetials_manager: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:37:24,902 - ERROR - Module imca_crednetials_manager not found
2025-07-15 08:37:25,903 - INFO - Starting upgrade for module: imca_dsc_management
2025-07-15 08:37:25,922 - ERROR - Error executing ir.module.module.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:37:25,922 - ERROR - Error getting module info for imca_dsc_management: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.module.module\'\n'>
2025-07-15 08:37:25,922 - ERROR - Module imca_dsc_management not found
2025-07-15 08:37:26,923 - INFO - Waiting 10 seconds for upgrades to complete...
2025-07-15 08:37:36,934 - INFO - 
========================================
2025-07-15 08:37:36,934 - INFO - CHECKING LOGS AFTER UPGRADE
2025-07-15 08:37:36,934 - INFO - ========================================
2025-07-15 08:37:36,934 - INFO - Checking recent logs for errors...
2025-07-15 08:37:36,978 - ERROR - Error executing ir.logging.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.logging\'\n'>
2025-07-15 08:37:36,979 - ERROR - Error checking logs: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'ir.logging\'\n'>
2025-07-15 08:37:36,979 - INFO - 
========================================
2025-07-15 08:37:36,979 - INFO - TESTING MODEL ACCESS
2025-07-15 08:37:36,979 - INFO - ========================================
2025-07-15 08:37:36,979 - INFO - Testing partner model access...
2025-07-15 08:37:37,012 - ERROR - Error executing res.partner.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'res.partner\'\n'>
2025-07-15 08:37:37,012 - ERROR - Error testing partner access: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'fields\' on model \'res.partner\'\n'>
2025-07-15 08:37:37,012 - INFO - Testing Groups model...
2025-07-15 08:37:37,038 - ERROR - Error executing x_groups.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_groups\'\n'>
2025-07-15 08:37:37,038 - ERROR - ✗ Groups model failed: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_groups\'\n'>
2025-07-15 08:37:37,038 - INFO - Testing Services model...
2025-07-15 08:37:37,069 - ERROR - Error executing x_services.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_services\'\n'>
2025-07-15 08:37:37,070 - ERROR - ✗ Services model failed: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_services\'\n'>
2025-07-15 08:37:37,070 - INFO - Testing Client Documents model...
2025-07-15 08:37:37,096 - ERROR - Error executing x_client_documents.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_client_documents\'\n'>
2025-07-15 08:37:37,096 - ERROR - ✗ Client Documents model failed: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_client_documents\'\n'>
2025-07-15 08:37:37,096 - INFO - Testing Credentials model...
2025-07-15 08:37:37,119 - ERROR - Error executing x_credentials.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_credentials\'\n'>
2025-07-15 08:37:37,119 - ERROR - ✗ Credentials model failed: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_credentials\'\n'>
2025-07-15 08:37:37,119 - INFO - Testing DSC Management model...
2025-07-15 08:37:37,136 - ERROR - Error executing x_dsc.search_read: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_dsc\'\n'>
2025-07-15 08:37:37,136 - ERROR - ✗ DSC Management model failed: <Fault 1: 'Traceback (most recent call last):\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 161, in xmlrpc_2\n    response = self._xmlrpc(service)\n  File "/usr/lib/python3/dist-packages/odoo/addons/base/controllers/rpc.py", line 139, in _xmlrpc\n    result = dispatch_rpc(service, method, params)\n  File "/usr/lib/python3/dist-packages/odoo/http.py", line 398, in dispatch_rpc\n    return dispatch(method, params)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 39, in dispatch\n    res = execute_kw(db, uid, *params[3:])\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 61, in execute_kw\n    return execute(db, uid, obj, method, *args, **kw or {})\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 68, in execute\n    res = execute_cr(cr, uid, obj, method, *args, **kw)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 52, in execute_cr\n    result = retrying(partial(odoo.api.call_kw, recs, method, args, kw), env)\n  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 137, in retrying\n    result = func()\n  File "/usr/lib/python3/dist-packages/odoo/api.py", line 517, in call_kw\n    result = getattr(recs, name)(*args, **kwargs)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 6061, in search_read\n    records = self.search_fetch(domain or [], fields, offset=offset, limit=limit, order=order)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 1747, in search_fetch\n    fields_to_fetch = self._determine_fields_to_fetch(field_names)\n  File "/usr/lib/python3/dist-packages/odoo/models.py", line 4111, in _determine_fields_to_fetch\n    raise ValueError(f"Invalid field {field_name!r} on model {self._name!r}")\nValueError: Invalid field \'limit\' on model \'x_dsc\'\n'>
2025-07-15 08:37:37,136 - INFO - 
============================================================
2025-07-15 08:37:37,136 - INFO - UPGRADE SUMMARY
2025-07-15 08:37:37,136 - INFO - ============================================================
2025-07-15 08:37:37,136 - INFO - Module Upgrade Results:
2025-07-15 08:37:37,136 - INFO -   imca_groups: ✗ FAILED
2025-07-15 08:37:37,136 - INFO -   imca_services: ✗ FAILED
2025-07-15 08:37:37,136 - INFO -   imca_client_documents: ✗ FAILED
2025-07-15 08:37:37,136 - INFO -   imca_crednetials_manager: ✗ FAILED
2025-07-15 08:37:37,136 - INFO -   imca_dsc_management: ✗ FAILED
2025-07-15 08:37:37,137 - INFO - 
Partner Model Test: ✗ FAILED
2025-07-15 08:37:37,137 - INFO - 
IMCA Model Tests:
2025-07-15 08:37:37,137 - INFO -   x_groups: ✗ FAILED
2025-07-15 08:37:37,137 - INFO -   x_services: ✗ FAILED
2025-07-15 08:37:37,137 - INFO -   x_client_documents: ✗ FAILED
2025-07-15 08:37:37,137 - INFO -   x_credentials: ✗ FAILED
2025-07-15 08:37:37,137 - INFO -   x_dsc: ✗ FAILED
2025-07-15 08:37:37,137 - INFO - 
Total Error Logs Found: 0
2025-07-15 08:37:37,137 - WARNING - 
⚠️  Some issues detected. Please review the logs above.
2025-07-15 08:37:37,137 - INFO - 
Upgrade script completed.
