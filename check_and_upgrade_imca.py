#!/usr/bin/env python3
"""
Check and upgrade IMCA modules
"""

import xmlrpc.client
import time
import sys

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

# IMCA modules to check
IMCA_MODULES = [
    'imca_groups',
    'imca_services', 
    'imca_client_documents',
    'imca_crednetials_manager',
    'imca_dsc_management'
]

def connect_odoo():
    """Connect to Odoo and return common, models, uid"""
    try:
        common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
        models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
        uid = common.authenticate(DB, USERNAME, PASSWORD, {})
        
        if not uid:
            raise Exception("Authentication failed")
            
        return common, models, uid
    except Exception as e:
        print(f"Connection error: {e}")
        return None, None, None

def check_module_status(models, uid):
    """Check status of IMCA modules"""
    print("\n" + "="*50)
    print("CHECKING IMCA MODULE STATUS")
    print("="*50)
    
    for module_name in IMCA_MODULES:
        try:
            modules = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.module.module', 'search_read',
                [('name', '=', module_name)],
                {'fields': ['name', 'state', 'installed_version', 'latest_version']}
            )
            
            if modules:
                module = modules[0]
                print(f"📦 {module_name}:")
                print(f"   State: {module['state']}")
                print(f"   Installed Version: {module.get('installed_version', 'N/A')}")
                print(f"   Latest Version: {module.get('latest_version', 'N/A')}")
            else:
                print(f"❌ {module_name}: Module not found")
                
        except Exception as e:
            print(f"❌ {module_name}: Error checking status - {e}")

def upgrade_modules(models, uid):
    """Upgrade IMCA modules"""
    print("\n" + "="*50)
    print("UPGRADING IMCA MODULES")
    print("="*50)
    
    upgrade_results = {}
    
    for module_name in IMCA_MODULES:
        try:
            print(f"\n🔄 Processing {module_name}...")
            
            # Find the module
            module_ids = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.module.module', 'search',
                [('name', '=', module_name)]
            )
            
            if not module_ids:
                print(f"   ❌ Module {module_name} not found")
                upgrade_results[module_name] = False
                continue
            
            module_id = module_ids[0]
            
            # Get module info
            module_info = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.module.module', 'read',
                [module_id], {'fields': ['state']}
            )[0]
            
            print(f"   Current state: {module_info['state']}")
            
            if module_info['state'] == 'installed':
                # Upgrade the module
                models.execute_kw(
                    DB, uid, PASSWORD,
                    'ir.module.module', 'button_immediate_upgrade',
                    [module_id]
                )
                print(f"   ✅ Upgrade initiated for {module_name}")
                upgrade_results[module_name] = True
                
            elif module_info['state'] == 'uninstalled':
                # Install the module
                models.execute_kw(
                    DB, uid, PASSWORD,
                    'ir.module.module', 'button_immediate_install',
                    [module_id]
                )
                print(f"   ✅ Installation initiated for {module_name}")
                upgrade_results[module_name] = True
                
            else:
                print(f"   ⚠️  Module {module_name} in state: {module_info['state']}")
                upgrade_results[module_name] = False
                
        except Exception as e:
            print(f"   ❌ Error processing {module_name}: {e}")
            upgrade_results[module_name] = False
    
    return upgrade_results

def test_new_fields(models, uid):
    """Test if new fields are accessible"""
    print("\n" + "="*50)
    print("TESTING NEW FIELDS")
    print("="*50)
    
    tests = []
    
    # Test partner fields
    try:
        print("🧪 Testing res.partner new fields...")
        partners = models.execute_kw(
            DB, uid, PASSWORD,
            'res.partner', 'search_read',
            [('is_company', '=', False)],
            {'fields': ['name', 'x_group_id'], 'limit': 1}
        )
        
        if partners:
            partner = partners[0]
            print(f"   ✅ Partner: {partner['name']}")
            print(f"   ✅ x_group_id field accessible: {partner.get('x_group_id', 'None')}")
            tests.append(True)
        else:
            print("   ⚠️  No partners found to test")
            tests.append(False)
            
    except Exception as e:
        print(f"   ❌ Error testing partner fields: {e}")
        tests.append(False)
    
    # Test IMCA models
    model_tests = {
        'x_groups': 'Groups',
        'x_services': 'Services',
        'x_client_documents': 'Client Documents',
        'x_credentials': 'Credentials',
        'x_dsc': 'DSC Management'
    }
    
    for model, name in model_tests.items():
        try:
            print(f"🧪 Testing {name} model ({model})...")
            count = models.execute_kw(
                DB, uid, PASSWORD,
                model, 'search_count', [[]]
            )
            print(f"   ✅ {name} model accessible, {count} records found")
            tests.append(True)
            
        except Exception as e:
            print(f"   ❌ Error accessing {name} model: {e}")
            tests.append(False)
    
    return tests

def check_logs(models, uid):
    """Check for recent errors in logs"""
    print("\n" + "="*50)
    print("CHECKING RECENT ERROR LOGS")
    print("="*50)
    
    try:
        # Check if ir.logging model exists and is accessible
        logs = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.logging', 'search_read',
            [('level', 'in', ['ERROR', 'CRITICAL'])],
            {'fields': ['create_date', 'name', 'level', 'message'],
             'order': 'create_date desc',
             'limit': 10}
        )
        
        if logs:
            print(f"⚠️  Found {len(logs)} recent error logs:")
            for log in logs:
                print(f"   [{log['create_date']}] {log['level']}: {log['message'][:100]}...")
        else:
            print("✅ No recent error logs found")
            
    except Exception as e:
        print(f"❌ Could not check logs: {e}")

def main():
    print("="*60)
    print("IMCA MODULES CHECK AND UPGRADE")
    print("="*60)
    
    # Connect to Odoo
    common, models, uid = connect_odoo()
    if not models:
        print("Failed to connect to Odoo")
        sys.exit(1)
    
    print(f"✅ Connected to Odoo as user ID: {uid}")
    
    # Check current module status
    check_module_status(models, uid)
    
    # Upgrade modules
    upgrade_results = upgrade_modules(models, uid)
    
    # Wait for upgrades to complete
    print("\n⏳ Waiting 15 seconds for upgrades to complete...")
    time.sleep(15)
    
    # Check status after upgrade
    print("\n" + "="*50)
    print("MODULE STATUS AFTER UPGRADE")
    print("="*50)
    check_module_status(models, uid)
    
    # Test new fields
    test_results = test_new_fields(models, uid)
    
    # Check logs
    check_logs(models, uid)
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    print("Module Upgrade Results:")
    for module, success in upgrade_results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"  {module}: {status}")
    
    successful_tests = sum(test_results)
    total_tests = len(test_results)
    print(f"\nField Tests: {successful_tests}/{total_tests} passed")
    
    if all(upgrade_results.values()) and successful_tests == total_tests:
        print("\n🎉 ALL UPGRADES AND TESTS SUCCESSFUL!")
    else:
        print("\n⚠️  Some issues detected. Please review the output above.")

if __name__ == "__main__":
    main()
