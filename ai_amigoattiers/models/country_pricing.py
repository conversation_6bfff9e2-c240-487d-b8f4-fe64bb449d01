# -*- coding: utf-8 -*-
# DISABLED - Country pricing functionality removed

"""
from odoo import models, fields, api


class WebsiteCountryPricing(models.Model):
    _name = 'website.country.pricing'
    _description = 'Website Country Pricing Configuration'
    _order = 'sequence, country_id'

    name = fields.Char(string='Name', compute='_compute_name', store=True)
    sequence = fields.Integer(string='Sequence', default=10)
    active = fields.Boolean(string='Active', default=True)
    country_id = fields.Many2one('res.country', string='Country', required=True)
    currency_id = fields.Many2one('res.currency', string='Currency', required=True)
    pricing_method = fields.Selection([
        ('multiplier', 'Multiplier'),
        ('fixed', 'Fixed Rate'),
        ('auto', 'Auto (from currency rates)')
    ], string='Pricing Method', default='multiplier', required=True)
    multiplier = fields.Float(string='Price Multiplier', default=1.0, digits=(12, 6))
    show_in_popup = fields.Boolean(string='Show in Country Selection Popup', default=True)

    @api.depends('country_id', 'currency_id')
    def _compute_name(self):
        for record in self:
            if record.country_id and record.currency_id:
                record.name = f"{record.country_id.name} ({record.currency_id.name})"
            else:
                record.name = "Country Pricing Configuration"

    @api.model
    def get_available_countries(self):
        """Get list of ALL countries with appropriate currency assignment"""
        # Get all countries
        all_countries = self.env['res.country'].search([])

        # Get USD currency as fallback
        usd_currency = self.env['res.currency'].search([('name', '=', 'USD')], limit=1)
        if not usd_currency:
            # If USD not found, get the first active currency
            usd_currency = self.env['res.currency'].search([('active', '=', True)], limit=1)

        countries = []
        for country in all_countries:
            # Determine currency for this country
            currency = None

            # First, check if country has an active currency_id
            if country.currency_id and country.currency_id.active:
                currency = country.currency_id
            else:
                # Fallback to USD
                currency = usd_currency

            if currency:  # Only add if we have a valid currency
                countries.append({
                    'id': country.id,
                    'name': country.name,
                    'code': country.code,
                    'currency': currency.name,
                    'currency_symbol': currency.symbol,
                    'flag_url': '',  # We'll use placeholder for now
                })

        # Sort countries by name
        countries.sort(key=lambda x: x['name'])
        return countries


class Website(models.Model):
    _inherit = 'website'

    @api.model
    def set_country_preference(self, country_code):
        """Set country preference in session"""
        if hasattr(self.env, 'request'):
            self.env.request.session['selected_country'] = country_code

            # Get country and its currency
            country = self.env['res.country'].search([('code', '=', country_code)], limit=1)
            if country and country.currency_id:
                self.env.request.session['currency_id'] = country.currency_id.id

        return True

    def get_selected_country(self):
        """Get currently selected country from session"""
        if hasattr(self.env, 'request'):
            country_code = self.env.request.session.get('selected_country')
            if country_code:
                return self.env['res.country'].search([('code', '=', country_code)], limit=1)
        return False
"""
