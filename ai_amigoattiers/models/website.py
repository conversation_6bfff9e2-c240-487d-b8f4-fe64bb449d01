# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.http import request


class Website(models.Model):
    _inherit = 'website'

    # DISABLED - Custom currency/pricelist overrides removed to allow standard Odoo currency switching
    # @property
    # def currency_id(self):
    #     """Override currency_id to use selected currency"""
    #     currency = self.get_current_currency()
    #     # Ensure we always return a valid currency (never empty recordset)
    #     if not currency or not currency.exists():
    #         currency = self._ensure_inr_currency()
    #     return currency

    # @property
    # def pricelist_id(self):
    #     """Override pricelist_id to use currency-matched pricelist"""
    #     return self.get_current_pricelist()

    def get_current_currency(self):
        """Get current currency based on user selection"""
        try:
            # Check session first (standard Odoo approach)
            if request and hasattr(request, 'session'):
                currency_id = request.session.get('website_sale_current_currency')
                if currency_id:
                    currency = self.env['res.currency'].browse(currency_id)
                    if currency.exists() and currency.active:
                        return currency

            # Check for currency in cookies as fallback
            if request and hasattr(request, 'httprequest'):
                selected_currency = request.httprequest.cookies.get('selected_currency')
                if selected_currency and selected_currency != 'INR':
                    currency = self.env['res.currency'].search([('name', '=', selected_currency)], limit=1)
                    if currency and currency.active:
                        # Update session for consistency
                        if request and hasattr(request, 'session'):
                            request.session['website_sale_current_currency'] = currency.id
                        return currency

            # Ensure INR exists and use it as fallback
            inr_currency = self._ensure_inr_currency()
            if inr_currency:
                return inr_currency

            # Fallback to website default currency
            website_currency = super().currency_id
            if website_currency and website_currency.exists():
                return website_currency

            # Final fallback - get any active currency
            any_currency = self.env['res.currency'].search([('active', '=', True)], limit=1)
            return any_currency
        except Exception:
            # Emergency fallback - try to get any currency
            try:
                return self.env['res.currency'].search([('active', '=', True)], limit=1)
            except:
                return self.env['res.currency']

    def _ensure_inr_currency(self):
        """Ensure INR currency exists and return it"""
        try:
            # Try to find existing INR currency
            inr_currency = self.env['res.currency'].search([('name', '=', 'INR')], limit=1)

            if not inr_currency:
                # Create INR currency if it doesn't exist
                inr_currency = self.env['res.currency'].create({
                    'name': 'INR',
                    'symbol': '₹',
                    'position': 'before',
                    'active': True,
                    'decimal_places': 2,
                })

            # Ensure it's active
            if not inr_currency.active:
                inr_currency.active = True

            return inr_currency
        except Exception:
            return False

    def get_current_pricelist(self):
        """Get pricelist that matches selected currency"""
        try:
            # Get the current currency
            current_currency = self.get_current_currency()

            # Look for a pricelist with the correct currency
            matching_pricelist = self.env['product.pricelist'].search([
                ('currency_id', '=', current_currency.id),
                ('website_id', 'in', [False, self.id]),
                ('active', '=', True)
            ], limit=1)

            if matching_pricelist:
                return matching_pricelist

            # If no matching pricelist found, get the default company pricelist
            default_pricelist = self.env['product.pricelist'].search([
                ('company_id', '=', self.company_id.id),
                ('active', '=', True)
            ], limit=1)

            return default_pricelist or self.env['product.pricelist'].search([('active', '=', True)], limit=1)

        except Exception:
            # If anything goes wrong, fallback to any active pricelist
            return self.env['product.pricelist'].search([('active', '=', True)], limit=1)

    def get_product_price(self, product):
        """Get product price in current currency using pricelist"""
        try:
            current_pricelist = self.get_current_pricelist()
            current_currency = self.get_current_currency()

            if current_pricelist and current_pricelist.currency_id == current_currency:
                # Use pricelist to get the price
                price = current_pricelist._get_product_price(product, 1.0)
                return price
            else:
                # Fallback to currency conversion
                company_currency = self.company_id.currency_id
                if company_currency != current_currency:
                    converted_price = company_currency._convert(
                        product.list_price,
                        current_currency,
                        self.company_id,
                        fields.Date.today()
                    )
                    return converted_price
                else:
                    return product.list_price
        except Exception:
            return product.list_price


# DISABLED - Custom product pricing methods removed to allow standard Odoo currency/pricelist handling
# class ProductTemplate(models.Model):
#     _inherit = 'product.template'

#     def get_website_price(self):
#         """Get price in current website currency"""
#         if hasattr(request, 'website') and request.website:
#             return request.website.get_product_price(self)
#         return self.list_price

#     def get_website_currency_price(self):
#         """Get formatted price in current website currency"""
#         try:
#             if hasattr(request, 'website') and request.website:
#                 price = request.website.get_product_price(self)
#                 currency = request.website.get_current_currency()
#                 return currency._format(price)
#             return self.currency_id._format(self.list_price)
#         except Exception:
#             return self.currency_id._format(self.list_price)


