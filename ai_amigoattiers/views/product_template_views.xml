<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Product Template Form View Extension -->
    <record id="product_template_form_view_inherit" model="ir.ui.view">
        <field name="name">product.template.form.inherit.size.chart</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='categ_id']" position="after">
                <field name="brand_id"/>
                <field name="gender"/>
                <field name="size_chart_id" domain="[('gender', '=', gender)]"/>
            </xpath>
        </field>
    </record>

    <!-- Size Chart Search View -->
    <record id="size_chart_search_view" model="ir.ui.view">
        <field name="name">website.size.chart.search</field>
        <field name="model">website.size.chart</field>
        <field name="arch" type="xml">
            <search string="Size Charts">
                <field name="name" string="Chart Name"/>
                <field name="gender"/>
                <field name="brand_id"/>
                <field name="category_id"/>
                <field name="sizing_standard"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Male" name="male" domain="[('gender', '=', 'male')]"/>
                <filter string="Female" name="female" domain="[('gender', '=', 'female')]"/>
                <filter string="Unisex" name="unisex" domain="[('gender', '=', 'unisex')]"/>
                <separator/>
                <filter string="Indian Standard" name="indian" domain="[('sizing_standard', '=', 'indian')]"/>
                <filter string="International" name="international" domain="[('sizing_standard', '=', 'international')]"/>
                <filter string="Brand Specific" name="brand_specific" domain="[('sizing_standard', '=', 'brand_specific')]"/>
                <group expand="0" string="Group By">
                    <filter string="Gender" name="group_gender" context="{'group_by': 'gender'}"/>
                    <filter string="Brand" name="group_brand" context="{'group_by': 'brand_id'}"/>
                    <filter string="Category" name="group_category" context="{'group_by': 'category_id'}"/>
                    <filter string="Sizing Standard" name="group_sizing" context="{'group_by': 'sizing_standard'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Size Chart Line Search View -->
    <record id="size_chart_line_search_view" model="ir.ui.view">
        <field name="name">website.size.chart.line.search</field>
        <field name="model">website.size.chart.line</field>
        <field name="arch" type="xml">
            <search string="Size Chart Lines">
                <field name="size_name" string="Size"/>
                <field name="size_number" string="Size Number"/>
                <field name="chart_id" string="Size Chart"/>
                <filter string="Has Size Number" name="has_size_number" domain="[('size_number', '!=', False)]"/>
                <filter string="Has Chest Measurement" name="has_chest" domain="[('chest_cm', '>', 0)]"/>
                <filter string="Has Waist Measurement" name="has_waist" domain="[('waist_cm', '>', 0)]"/>
                <group expand="0" string="Group By">
                    <filter string="Size Chart" name="group_chart" context="{'group_by': 'chart_id'}"/>
                    <filter string="Size Name" name="group_size" context="{'group_by': 'size_name'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Size Chart Form View with Instructions -->
    <record id="size_chart_form_view" model="ir.ui.view">
        <field name="name">website.size.chart.form</field>
        <field name="model">website.size.chart</field>
        <field name="arch" type="xml">
            <form string="Size Chart">
                <sheet>
                    <!-- Instructions Header -->
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="e.g., Men's Kurta Size Chart"/>
                        </h1>
                    </div>

                    <!-- Instructions Panel -->
                    <div class="alert alert-info" role="alert">
                        <h4><i class="fa fa-info-circle"></i> How to Create Size Charts</h4>
                        <p><strong>Chart Name:</strong> Give your size chart a descriptive name (e.g., "Men's Kurta Size Chart", "Women's Saree Blouse Chart")</p>
                        <p><strong>Gender:</strong> Select the target gender for this chart. Use "Unisex" for products suitable for all genders.</p>
                        <p><strong>Brand:</strong> If this chart is specific to a brand, select it. Leave blank for general charts.</p>
                        <p><strong>Sizing Standard:</strong> Choose the measurement system:</p>
                        <ul>
                            <li><strong>Indian Standard:</strong> Based on Indian body measurements and sizing conventions</li>
                            <li><strong>International:</strong> Based on international sizing standards</li>
                            <li><strong>Brand Specific:</strong> Custom sizing unique to a particular brand</li>
                        </ul>
                        <p><em>💡 Tip: Create separate charts for different product types (e.g., tops, bottoms, dresses) as they have different measurement requirements.</em></p>
                    </div>

                    <group>
                        <group string="Chart Information">
                            <field name="gender"/>
                            <field name="brand_id" placeholder="Select brand (optional)"/>
                            <field name="category_id" placeholder="Select category (optional)"/>
                        </group>
                        <group string="Settings">
                            <field name="sizing_standard"/>
                            <field name="sequence" help="Controls the order of this chart in lists"/>
                            <field name="active"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Chart Headers">
                            <div class="alert alert-info" role="alert">
                                <strong>Chart Headers:</strong> These define what measurements will be shown in your size chart.
                                Headers are automatically created based on your selected product category, but you can customize them here.
                                <br/><strong>Tip:</strong> Different product types need different measurements (e.g., tops need chest/shoulder, bottoms need waist/inseam).
                            </div>
                            <field name="header_ids">
                                <list editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="header_name"/>
                                    <field name="header_key"/>
                                    <field name="unit"/>
                                    <field name="is_required"/>
                                    <field name="active"/>
                                </list>
                            </field>
                        </page>
                        <page string="Size Lines">
                            <div class="alert alert-warning" role="alert">
                                <strong>Adding Size Lines:</strong> Click "Add a line" below to add individual sizes.
                                For each size, enter the size name (S, M, L, etc.) and all relevant measurements in centimeters.
                                The system will automatically convert to inches for customer display.
                            </div>
                            <field name="size_line_ids">
                                <list editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="size_name"/>
                                    <field name="size_number"/>
                                    <field name="chest_cm"/>
                                    <field name="waist_cm"/>
                                    <field name="hip_cm"/>
                                    <field name="shoulder_cm"/>
                                    <field name="sleeve_cm"/>
                                    <field name="length_cm"/>
                                </list>
                            </field>
                        </page>
                        <page string="Description &amp; Notes">
                            <div class="alert alert-success" role="alert">
                                <strong>Description:</strong> Add any additional information about this size chart,
                                fitting notes, or special instructions for customers.
                            </div>
                            <field name="description" placeholder="Add fitting notes, measurement instructions, or any special information about this size chart..."/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Size Chart Line List View -->
    <record id="size_chart_line_list_view" model="ir.ui.view">
        <field name="name">website.size.chart.line.list</field>
        <field name="model">website.size.chart.line</field>
        <field name="arch" type="xml">
            <list string="Size Chart Lines" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="size_name"/>
                <field name="size_number"/>
                <field name="chest_cm"/>
                <field name="waist_cm"/>
                <field name="hip_cm"/>
                <field name="shoulder_cm"/>
                <field name="sleeve_cm"/>
                <field name="length_cm"/>
                <field name="neck_cm"/>
                <field name="inseam_cm"/>
            </list>
        </field>
    </record>

    <!-- Size Chart Line Form View with Instructions -->
    <record id="size_chart_line_form_view" model="ir.ui.view">
        <field name="name">website.size.chart.line.form</field>
        <field name="model">website.size.chart.line</field>
        <field name="arch" type="xml">
            <form string="Size Chart Line">
                <sheet>
                    <!-- Instructions Header -->
                    <div class="oe_title">
                        <h1>
                            <field name="size_name" placeholder="e.g., S, M, L, XL"/>
                        </h1>
                        <div class="o_row">
                            <field name="chart_id" options="{'no_create': True}"/>
                        </div>
                    </div>

                    <!-- Instructions Panel -->
                    <div class="alert alert-info" role="alert">
                        <h4><i class="fa fa-info-circle"></i> How to Add Size Chart Lines</h4>
                        <p><strong>Size Name:</strong> Enter the size label (e.g., XS, S, M, L, XL, XXL, or 28, 30, 32, 34)</p>
                        <p><strong>Size Number:</strong> Optional numeric size (e.g., 28, 30, 32 for waist sizes)</p>
                        <p><strong>Measurements:</strong> Enter all measurements in centimeters. The system will automatically convert to inches for display.</p>
                        <ul>
                            <li><strong>Chest/Bust:</strong> Measurement around the fullest part of the chest</li>
                            <li><strong>Waist:</strong> Measurement around the natural waistline</li>
                            <li><strong>Hip:</strong> Measurement around the fullest part of the hips</li>
                            <li><strong>Shoulder:</strong> Measurement from shoulder point to shoulder point</li>
                            <li><strong>Sleeve:</strong> Measurement from shoulder to wrist</li>
                            <li><strong>Length:</strong> Total garment length</li>
                        </ul>
                        <p><em>💡 Tip: Use the sequence field to control the order of sizes in the chart.</em></p>
                    </div>

                    <group>
                        <group string="Size Information">
                            <field name="size_number" placeholder="e.g., 28, 30, 32"/>
                            <field name="sequence" help="Controls the order of this size in the chart"/>
                        </group>
                    </group>

                    <group string="Measurements (in Centimeters)">
                        <group string="Body Measurements">
                            <field name="chest_cm" placeholder="e.g., 86"/>
                            <field name="waist_cm" placeholder="e.g., 76"/>
                            <field name="hip_cm" placeholder="e.g., 96"/>
                        </group>
                        <group string="Garment Measurements">
                            <field name="shoulder_cm" placeholder="e.g., 42"/>
                            <field name="sleeve_cm" placeholder="e.g., 58"/>
                            <field name="length_cm" placeholder="e.g., 68"/>
                        </group>
                    </group>

                    <group string="Additional Measurements" groups="base.group_no_one">
                        <group>
                            <field name="neck_cm"/>
                            <field name="inseam_cm"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Size Chart List View -->
    <record id="size_chart_list_view" model="ir.ui.view">
        <field name="name">website.size.chart.list</field>
        <field name="model">website.size.chart</field>
        <field name="arch" type="xml">
            <list string="Size Charts">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="gender"/>
                <field name="brand_id"/>
                <field name="sizing_standard"/>
                <field name="active"/>
            </list>
        </field>
    </record>



    <!-- Product Brand Form View -->
    <record id="product_brand_form_view" model="ir.ui.view">
        <field name="name">product.brand.form</field>
        <field name="model">product.brand</field>
        <field name="arch" type="xml">
            <form string="Product Brand">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="logo" widget="image" class="oe_avatar"/>
                        </group>
                    </group>
                    <group>
                        <field name="description"/>
                    </group>
                    <group string="Fitting Notes">
                        <field name="fitting_notes"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Product Brand List View -->
    <record id="product_brand_list_view" model="ir.ui.view">
        <field name="name">product.brand.list</field>
        <field name="model">product.brand</field>
        <field name="arch" type="xml">
            <list string="Product Brands">
                <field name="name"/>
                <field name="description"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_size_charts" model="ir.actions.act_window">
        <field name="name">Size Charts</field>
        <field name="res_model">website.size.chart</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="size_chart_list_view"/>
        <field name="search_view_id" ref="size_chart_search_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first size chart!
            </p>
            <div class="alert alert-info">
                <h4><i class="fa fa-lightbulb-o"></i> Getting Started with Size Charts</h4>
                <p><strong>Size charts help customers choose the right size for your products.</strong></p>
                <ol>
                    <li><strong>Create a Chart:</strong> Click "New" to create a size chart for a specific product type</li>
                    <li><strong>Choose Gender &amp; Brand:</strong> Select the target audience and brand (if applicable)</li>
                    <li><strong>Add Size Lines:</strong> Add individual sizes (S, M, L, etc.) with measurements</li>
                    <li><strong>Assign to Products:</strong> Link the chart to your products in the product form</li>
                </ol>
                <p><em>💡 Pro Tip: Create separate charts for different product categories (tops, bottoms, dresses) as they have different measurement requirements.</em></p>
            </div>
        </field>
    </record>

    <!-- Size Chart Lines Action -->
    <record id="action_size_chart_lines" model="ir.actions.act_window">
        <field name="name">Size Chart Lines</field>
        <field name="res_model">website.size.chart.line</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="size_chart_line_list_view"/>
        <field name="search_view_id" ref="size_chart_line_search_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first size chart line!
            </p>
            <div class="alert alert-info">
                <h4><i class="fa fa-ruler"></i> Managing Size Chart Lines</h4>
                <p><strong>Size chart lines represent individual sizes within a size chart.</strong></p>
                <ol>
                    <li><strong>Size Name:</strong> Enter the size label (XS, S, M, L, XL, XXL, or numeric sizes like 28, 30, 32)</li>
                    <li><strong>Measurements:</strong> Add all measurements in centimeters (automatically converts to inches)</li>
                    <li><strong>Sequence:</strong> Control the order of sizes in the chart</li>
                </ol>
                <div class="row">
                    <div class="col-md-6">
                        <h5>Common Measurements:</h5>
                        <ul>
                            <li><strong>Chest/Bust:</strong> Around fullest part</li>
                            <li><strong>Waist:</strong> Natural waistline</li>
                            <li><strong>Hip:</strong> Fullest part of hips</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>Garment Measurements:</h5>
                        <ul>
                            <li><strong>Shoulder:</strong> Point to point</li>
                            <li><strong>Sleeve:</strong> Shoulder to wrist</li>
                            <li><strong>Length:</strong> Total garment length</li>
                        </ul>
                    </div>
                </div>
                <p><em>💡 Tip: Consistent measurements across all sizes ensure accurate size charts for customers.</em></p>
            </div>
        </field>
    </record>



    <!-- Size Chart Header Views -->
    <record id="size_chart_header_list_view" model="ir.ui.view">
        <field name="name">website.size.chart.header.list</field>
        <field name="model">website.size.chart.header</field>
        <field name="arch" type="xml">
            <list string="Size Chart Headers" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="chart_id"/>
                <field name="header_name"/>
                <field name="header_key"/>
                <field name="unit"/>
                <field name="is_required"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <record id="size_chart_header_form_view" model="ir.ui.view">
        <field name="name">website.size.chart.header.form</field>
        <field name="model">website.size.chart.header</field>
        <field name="arch" type="xml">
            <form string="Size Chart Header">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="header_name" placeholder="e.g., Chest, Waist, Hip"/>
                        </h1>
                    </div>

                    <div class="alert alert-info" role="alert">
                        <h4><i class="fa fa-info-circle"></i> Size Chart Header Configuration</h4>
                        <p><strong>Header Name:</strong> The display name shown to customers (e.g., "Chest", "Waist", "Hip")</p>
                        <p><strong>Header Key:</strong> Internal key used for data mapping (e.g., "chest", "waist", "hip")</p>
                        <p><strong>Unit Type:</strong> How this measurement should be displayed</p>
                        <ul>
                            <li><strong>Centimeters:</strong> For body measurements (will auto-convert to inches)</li>
                            <li><strong>Size Only:</strong> For size names like S, M, L</li>
                            <li><strong>Text:</strong> For size numbers like 28, 30, 32</li>
                        </ul>
                    </div>

                    <group>
                        <group string="Header Information">
                            <field name="chart_id"/>
                            <field name="header_key" placeholder="e.g., chest, waist, hip"/>
                            <field name="sequence"/>
                        </group>
                        <group string="Display Settings">
                            <field name="unit"/>
                            <field name="is_required"/>
                            <field name="active"/>
                        </group>
                    </group>

                    <group string="Help Text">
                        <field name="help_text" placeholder="Optional help text for customers"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="size_chart_header_search_view" model="ir.ui.view">
        <field name="name">website.size.chart.header.search</field>
        <field name="model">website.size.chart.header</field>
        <field name="arch" type="xml">
            <search string="Size Chart Headers">
                <field name="header_name"/>
                <field name="header_key"/>
                <field name="chart_id"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Required" name="required" domain="[('is_required', '=', True)]"/>
                <separator/>
                <filter string="Centimeters" name="cm" domain="[('unit', '=', 'cm')]"/>
                <filter string="Size Only" name="size" domain="[('unit', '=', 'size')]"/>
                <filter string="Text" name="text" domain="[('unit', '=', 'text')]"/>
                <group expand="0" string="Group By">
                    <filter string="Size Chart" name="group_chart" context="{'group_by': 'chart_id'}"/>
                    <filter string="Unit Type" name="group_unit" context="{'group_by': 'unit'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Size Chart Header Action -->
    <record id="action_size_chart_headers" model="ir.actions.act_window">
        <field name="name">Size Chart Headers</field>
        <field name="res_model">website.size.chart.header</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="size_chart_header_list_view"/>
        <field name="search_view_id" ref="size_chart_header_search_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first size chart header!
            </p>
            <div class="alert alert-info">
                <h4><i class="fa fa-columns"></i> Managing Size Chart Headers</h4>
                <p><strong>Headers define what measurements are shown in your size charts.</strong></p>
                <ol>
                    <li><strong>Header Name:</strong> What customers see (e.g., "Chest", "Waist", "Hip")</li>
                    <li><strong>Header Key:</strong> Internal mapping key (e.g., "chest", "waist", "hip")</li>
                    <li><strong>Unit Type:</strong> How the measurement is displayed (cm, size, text)</li>
                    <li><strong>Sequence:</strong> Controls the order of columns in the size chart</li>
                </ol>
                <p><em>💡 Pro Tip: Different product categories automatically get appropriate headers, but you can customize them here.</em></p>
            </div>
        </field>
    </record>

    <record id="action_product_brands" model="ir.actions.act_window">
        <field name="name">Product Brands</field>
        <field name="res_model">product.brand</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="product_brand_list_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first brand!
            </p>
            <p>
                Brands can have specific fitting notes and size variations.
                This helps customers understand brand-specific sizing.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_size_chart_main" name="Size Charts &amp; Pricing" parent="website.menu_website_configuration" sequence="50"/>

    <menuitem id="menu_size_charts" name="Size Charts" parent="menu_size_chart_main" action="action_size_charts" sequence="10"/>
    <menuitem id="menu_size_chart_headers" name="Size Chart Headers" parent="menu_size_chart_main" action="action_size_chart_headers" sequence="12"/>
    <menuitem id="menu_size_chart_lines" name="Size Chart Lines" parent="menu_size_chart_main" action="action_size_chart_lines" sequence="15"/>
    <menuitem id="menu_product_brands" name="Product Brands" parent="menu_size_chart_main" action="action_product_brands" sequence="20"/>
    <menuitem id="menu_product_categories" name="Product Categories" parent="menu_size_chart_main" action="product.product_category_action_form" sequence="25"/>

</odoo>
