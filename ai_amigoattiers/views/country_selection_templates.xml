<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--
    Country Selection Modal and Currency Management System

    This template provides a country selection modal that integrates with Odoo's
    built-in currency and pricing system. The system works as follows:

    1. COUNTRY SELECTION:
       - Users select their country from a modal dialog
       - Countries are loaded dynamically from the server
       - Selection is stored in session and cookies for persistence

    2. CURRENCY HANDLING:
       - Each country is associated with its appropriate currency
       - Currency rates are managed by the ai_currencyrateupdate module
       - Rates are updated automatically via cron job from OpenExchangeRates API

    3. PRICING SYSTEM:
       - All pricing is handled by Odoo's native currency conversion
       - Price lists are automatically created/matched for each currency
       - No manual JavaScript price conversion is performed
       - Page reload ensures all prices are recalculated by Odoo

    4. PERSISTENCE:
       - User selections are stored in browser localStorage and cookies
       - Session data maintains currency selection across page loads
       - Modal is shown only on first visit or when explicitly requested

    This approach ensures accurate, up-to-date pricing using Odoo's robust
    currency management system rather than hardcoded exchange rates.
    -->

    <!-- Country Selection Modal - DISABLED -->
    <!--
    <template id="country_selection_modal" name="Country Selection Modal">
    -->
        <!--
        <div class="modal fade" id="countrySelectionModal" tabindex="-1" role="dialog" aria-labelledby="countrySelectionModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="countrySelectionModalLabel">
                            <i class="fa fa-globe"></i> Select Your Country
                        </h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p class="text-center mb-4">Please select your country to see prices in your local currency</p>
                        
                        <!-- Search Box -->
                        <div class="mb-3">
                            <input type="text" class="form-control" id="countrySearch" placeholder="Search for your country..."/>
                        </div>
                        
                        <!-- Countries Selection -->
                        <div class="mb-4">
                            <h6 class="mb-3">Select Your Country</h6>
                        </div>
                        
                        <!-- All Countries Grid -->
                        <div class="country-grid" id="countryGrid">
                            <div class="row">
                                <!-- Popular Countries -->
                                <div class="col-md-3 col-sm-6 col-6 mb-3 country-item" data-country-name="india">
                                    <div class="country-option" data-country-code="IN" data-currency-id="INR">
                                        <div class="country-card">
                                            <div class="country-flag-placeholder mb-2">
                                                <i class="fa fa-star"></i>
                                            </div>
                                            <div class="country-name">India</div>
                                            <div class="country-currency">INR (₹)</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-6 col-6 mb-3 country-item" data-country-name="united states">
                                    <div class="country-option" data-country-code="US" data-currency-id="USD">
                                        <div class="country-card">
                                            <div class="country-flag-placeholder mb-2">
                                                <i class="fa fa-star"></i>
                                            </div>
                                            <div class="country-name">United States</div>
                                            <div class="country-currency">USD ($)</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-6 col-6 mb-3 country-item" data-country-name="united kingdom">
                                    <div class="country-option" data-country-code="GB" data-currency-id="GBP">
                                        <div class="country-card">
                                            <div class="country-flag-placeholder mb-2">
                                                <i class="fa fa-star"></i>
                                            </div>
                                            <div class="country-name">United Kingdom</div>
                                            <div class="country-currency">GBP (£)</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-6 col-6 mb-3 country-item" data-country-name="canada">
                                    <div class="country-option" data-country-code="CA" data-currency-id="CAD">
                                        <div class="country-card">
                                            <div class="country-flag-placeholder mb-2">
                                                <i class="fa fa-star"></i>
                                            </div>
                                            <div class="country-name">Canada</div>
                                            <div class="country-currency">CAD (C$)</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-6 col-6 mb-3 country-item" data-country-name="australia">
                                    <div class="country-option" data-country-code="AU" data-currency-id="AUD">
                                        <div class="country-card">
                                            <div class="country-flag-placeholder mb-2">
                                                <i class="fa fa-globe"></i>
                                            </div>
                                            <div class="country-name">Australia</div>
                                            <div class="country-currency">AUD (A$)</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-6 col-6 mb-3 country-item" data-country-name="germany">
                                    <div class="country-option" data-country-code="DE" data-currency-id="EUR">
                                        <div class="country-card">
                                            <div class="country-flag-placeholder mb-2">
                                                <i class="fa fa-globe"></i>
                                            </div>
                                            <div class="country-name">Germany</div>
                                            <div class="country-currency">EUR (€)</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-6 col-6 mb-3 country-item" data-country-name="france">
                                    <div class="country-option" data-country-code="FR" data-currency-id="EUR">
                                        <div class="country-card">
                                            <div class="country-flag-placeholder mb-2">
                                                <i class="fa fa-globe"></i>
                                            </div>
                                            <div class="country-name">France</div>
                                            <div class="country-currency">EUR (€)</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-6 col-6 mb-3 country-item" data-country-name="singapore">
                                    <div class="country-option" data-country-code="SG" data-currency-id="SGD">
                                        <div class="country-card">
                                            <div class="country-flag-placeholder mb-2">
                                                <i class="fa fa-globe"></i>
                                            </div>
                                            <div class="country-name">Singapore</div>
                                            <div class="country-currency">SGD (S$)</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-6 col-6 mb-3 country-item" data-country-name="united arab emirates">
                                    <div class="country-option" data-country-code="AE" data-currency-id="AED">
                                        <div class="country-card">
                                            <div class="country-flag-placeholder mb-2">
                                                <i class="fa fa-globe"></i>
                                            </div>
                                            <div class="country-name">United Arab Emirates</div>
                                            <div class="country-currency">AED (د.إ)</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-6 col-6 mb-3 country-item" data-country-name="japan">
                                    <div class="country-option" data-country-code="JP" data-currency-id="JPY">
                                        <div class="country-card">
                                            <div class="country-flag-placeholder mb-2">
                                                <i class="fa fa-globe"></i>
                                            </div>
                                            <div class="country-name">Japan</div>
                                            <div class="country-currency">JPY (¥)</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-6 col-6 mb-3 country-item" data-country-name="china">
                                    <div class="country-option" data-country-code="CN" data-currency-id="CNY">
                                        <div class="country-card">
                                            <div class="country-flag-placeholder mb-2">
                                                <i class="fa fa-globe"></i>
                                            </div>
                                            <div class="country-name">China</div>
                                            <div class="country-currency">CNY (¥)</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-6 col-6 mb-3 country-item" data-country-name="brazil">
                                    <div class="country-option" data-country-code="BR" data-currency-id="BRL">
                                        <div class="country-card">
                                            <div class="country-flag-placeholder mb-2">
                                                <i class="fa fa-globe"></i>
                                            </div>
                                            <div class="country-name">Brazil</div>
                                            <div class="country-currency">BRL (R$)</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Default/Continue without selection -->
                        <div class="text-center mt-4">
                            <button type="button" class="btn btn-link text-muted" id="continueWithoutSelection">
                                Continue with INR (Indian Rupees)
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        -->
    <!-- </template> -->

    <!-- Country Selection Script Include - DISABLED -->
    <!--
    <template id="country_selection_include" name="Country Selection Include" inherit_id="website.layout" priority="20">
        <xpath expr="//body" position="inside">
            <!-- Always include the modal, JavaScript will handle when to show it -->
            <t t-call="ai_amigoattiers.country_selection_modal"/>

            <!-- Inline JavaScript for Country Selection - DISABLED -->
            <!--
            <script type="text/javascript">
                // Immediate jQuery check - if not available, use vanilla JavaScript
                var useVanillaJS = (typeof $ === 'undefined');

                // Wait for both DOM and jQuery to be ready
                function initCountrySelection() {
                    // Prevent multiple initializations
                    if (window.countrySelectionInitialized) {
                        console.log('Country selection already initialized, skipping...');
                        return;
                    }

                    // Check if jQuery is available
                    if (typeof $ === 'undefined') {
                        console.log('jQuery not available, retrying in 200ms...');
                        // Try up to 10 times (2 seconds) then fallback
                        if (!window.jqueryRetryCount) window.jqueryRetryCount = 0;
                        if (window.jqueryRetryCount &lt; 10) {
                            window.jqueryRetryCount++;
                            setTimeout(initCountrySelection, 200);
                            return;
                        } else {
                            console.error('jQuery failed to load after 2 seconds, using vanilla JavaScript fallback');
                            initCountrySelectionVanilla();
                            return;
                        }
                    }

                    // Mark as initialized
                    window.countrySelectionInitialized = true;

                    $(document).ready(function() {
                    // Country Selection Functionality
                    console.log('Initializing country selection...');

                    // Apply currency from localStorage if available (only once)
                    var selectedCurrency = localStorage.getItem('selected_currency');
                    if (selectedCurrency &amp;&amp; selectedCurrency !== 'INR' &amp;&amp; !window.currencyAppliedOnLoad) {
                        console.log('Found stored currency:', selectedCurrency);
                        window.currencyAppliedOnLoad = true;
                        // Set a cookie so the server can read it
                        document.cookie = 'selected_currency=' + selectedCurrency + '; path=/; max-age=86400';
                        // Only apply currency, don't update prices immediately as page will reload
                        // applyCurrencyFromStorage(selectedCurrency); // DISABLED - Currency API not available
                    }

                    // Check if we should auto-show the modal
                    if (!localStorage.getItem('country_selection_shown')) {
                        setTimeout(function() {
                            $('#countrySelectionModal').modal('show');
                        }, 1000);
                    }

                    // Modal show event
                    $('#countrySelectionModal').on('show.bs.modal', function() {
                        console.log('Country modal opening...');
                        // Countries are already loaded in HTML, no need to fetch
                    });

                    // Country option clicks (remove existing handlers first)
                    $('#countrySelectionModal').off('click', '.country-option').on('click', '.country-option', function(e) {
                        e.preventDefault();
                        const countryCode = $(this).data('country-code');
                        const currencyCode = $(this).data('currency-id'); // This is actually currency code now
                        selectCountry(countryCode, currencyCode);
                    });

                    // Continue without selection (remove existing handlers first)
                    $('#countrySelectionModal').off('click', '#continueWithoutSelection').on('click', '#continueWithoutSelection', function(e) {
                        e.preventDefault();
                        localStorage.setItem('country_selection_shown', 'true');
                        $('#countrySelectionModal').modal('hide');
                    });

                    // Search functionality (remove existing handlers first)
                    $('#countrySelectionModal').off('input', '#countrySearch').on('input', '#countrySearch', function() {
                        const searchTerm = $(this).val().toLowerCase();
                        filterCountries(searchTerm);
                    });

                    // Countries are now static HTML, no need for dynamic loading

                    // Render functions removed - using static HTML instead

                    function filterCountries(searchTerm) {
                        const countryItems = document.querySelectorAll('.country-item');

                        countryItems.forEach(function(item) {
                            const countryName = item.getAttribute('data-country-name');
                            if (countryName.includes(searchTerm)) {
                                item.style.display = 'block';
                            } else {
                                item.style.display = 'none';
                            }
                        });
                    }

                    function selectCountry(countryCode, currencyCode) {
                        // Prevent multiple selections
                        if (window.countrySelectionInProgress) {
                            console.log('Country selection already in progress, ignoring...');
                            return;
                        }
                        window.countrySelectionInProgress = true;

                        console.log('Selecting country:', countryCode, 'Currency:', currencyCode);

                        // Store selection in localStorage
                        localStorage.setItem('country_selection_shown', 'true');
                        localStorage.setItem('selected_country', countryCode);
                        localStorage.setItem('selected_currency', currencyCode);

                        // Set cookie so server can read it
                        document.cookie = 'selected_currency=' + currencyCode + '; path=/; max-age=86400';
                        document.cookie = 'selected_country=' + countryCode + '; path=/; max-age=86400';

                        // Apply currency immediately
                        // applyCurrencyFromStorage(currencyCode); // DISABLED - Currency API not available

                        // Close modal and reload page (no need to update prices as page will reload)
                        $('#countrySelectionModal').modal('hide');
                        setTimeout(() => {
                            window.location.reload();
                        }, 500);
                    }

                    // DISABLED - Currency API functionality removed
                    /*
                    function applyCurrencyFromStorage(currencyCode) {
                        // Prevent multiple simultaneous currency applications
                        if (window.currencyApplicationInProgress) {
                            console.log('Currency application already in progress, skipping...');
                            return;
                        }
                        window.currencyApplicationInProgress = true;

                        console.log('Applying currency from storage:', currencyCode);

                        $.ajax({
                            url: '/shop/apply_currency',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                jsonrpc: '2.0',
                                method: 'call',
                                params: {
                                    currency_code: currencyCode
                                }
                            }),
                            success: function(data) {
                                window.currencyApplicationInProgress = false;
                                console.log('Currency applied:', data);

                                // Handle Odoo JSON-RPC response structure
                                var result = data.result || data;

                                if (result &amp;&amp; result.success) {
                                    console.log('Currency successfully set to:', result.currency);
                                } else {
                                    console.error('Failed to apply currency:', result?.error || 'Unknown error');
                                }
                            },
                            error: function(xhr, status, error) {
                                window.currencyApplicationInProgress = false;
                                console.error('Error applying currency:', error);
                            }
                        });
                    }
                    */

                    function updatePricesOnPage(currencyCode) {
                        console.log('Currency changed to:', currencyCode);

                        // Let Odoo handle all pricing through its currency and pricelist system
                        // No need for JavaScript price conversion - just reload the page
                        // to let Odoo recalculate all prices with the new currency

                        console.log('Reloading page to apply new currency pricing...');
                        // Small delay to ensure currency change is processed
                        setTimeout(function() {
                            window.location.reload();
                        }, 500);
                    }

                    // TEMPORARILY DISABLED - Functions below need proper documentation
                    /*
                    function showError(message) {
                        const countryGrid = document.getElementById('countryGrid');
                        if (countryGrid) {
                            countryGrid.innerHTML = '&lt;div class="alert alert-danger text-center"&gt;' +
                                                   '&lt;i class="fa fa-exclamation-triangle"&gt;&lt;/i&gt; ' + message +
                                                   '&lt;/div&gt;';
                        }
                    }

                    // Global functions for external access
                    window.CountrySelection = {
                        openModal: function() {
                            $('#countrySelectionModal').modal('show');
                        },
                        resetSelection: function() {
                            localStorage.removeItem('country_selection_shown');
                            localStorage.removeItem('selected_country');
                            localStorage.removeItem('selected_currency');
                        }
                    };
                    });
                }

                // Vanilla JavaScript fallback for when jQuery is not available
                function initCountrySelectionVanilla() {
                    // Prevent multiple initializations
                    if (window.countrySelectionVanillaInitialized) {
                        console.log('Country selection vanilla already initialized, skipping...');
                        return;
                    }
                    window.countrySelectionVanillaInitialized = true;

                    console.log('Initializing country selection with vanilla JavaScript...');

                    // Apply currency from localStorage if available
                    var selectedCurrency = localStorage.getItem('selected_currency');
                    if (selectedCurrency &amp;&amp; selectedCurrency !== 'INR' &amp;&amp; !window.currencyAppliedOnLoad) {
                        console.log('Found stored currency:', selectedCurrency);
                        window.currencyAppliedOnLoad = true;
                        document.cookie = 'selected_currency=' + selectedCurrency + '; path=/; max-age=86400';
                        // applyCurrencyVanilla(selectedCurrency); // DISABLED - Currency API not available
                    */
                    }

                    // Check if we should auto-show the modal (wait for cookie consent)
                    function checkAndShowCountryModal() {
                        // Check if cookie consent has been handled
                        var cookieConsent = localStorage.getItem('website_cookies_bar') ||
                                          document.cookie.indexOf('website_cookies_bar=') !== -1;

                        if (!localStorage.getItem('country_selection_shown')) {
                            var modal = document.getElementById('countrySelectionModal');
                            if (modal &amp;&amp; typeof bootstrap !== 'undefined') {
                                var bsModal = new bootstrap.Modal(modal);
                                bsModal.show();
                            }
                        }
                    }

                    // Wait for cookie consent or show after delay
                    if (typeof odoo !== 'undefined' &amp;&amp; odoo.define) {
                        // Wait for Odoo's cookie consent
                        setTimeout(function() {
                            checkAndShowCountryModal();
                        }, 2000);
                    } else {
                        // Fallback for non-Odoo environments
                        setTimeout(function() {
                            checkAndShowCountryModal();
                        }, 1500);
                    }

                    // Add event listeners for country options
                    var countryOptions = document.querySelectorAll('.country-option');
                    countryOptions.forEach(function(option) {
                        option.addEventListener('click', function(e) {
                            e.preventDefault();
                            var countryCode = this.getAttribute('data-country-code');
                            var currencyCode = this.getAttribute('data-currency-id');
                            selectCountryVanilla(countryCode, currencyCode);
                        });
                    });

                    // Continue without selection
                    var continueBtn = document.getElementById('continueWithoutSelection');
                    if (continueBtn) {
                        continueBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            localStorage.setItem('country_selection_shown', 'true');
                    */
                            var modal = document.getElementById('countrySelectionModal');
                            if (modal &amp;&amp; typeof bootstrap !== 'undefined') {
                                var bsModal = bootstrap.Modal.getInstance(modal);
                                if (bsModal) bsModal.hide();
                            }
                        });
                    }

                    // Search functionality
                    var searchInput = document.getElementById('countrySearch');
                    if (searchInput) {
                        searchInput.addEventListener('input', function() {
                            var searchTerm = this.value.toLowerCase();
                            filterCountries(searchTerm);
                        });
                    }

                    function selectCountryVanilla(countryCode, currencyCode) {
                        console.log('Selecting country (vanilla):', countryCode, 'Currency:', currencyCode);

                        localStorage.setItem('country_selection_shown', 'true');
                        localStorage.setItem('selected_country', countryCode);
                        localStorage.setItem('selected_currency', currencyCode);

                        document.cookie = 'selected_currency=' + currencyCode + '; path=/; max-age=86400';
                        document.cookie = 'selected_country=' + countryCode + '; path=/; max-age=86400';

                        // Apply currency immediately
                        // applyCurrencyVanilla(currencyCode); // DISABLED - Currency API not available

                        var modal = document.getElementById('countrySelectionModal');
                        if (modal &amp;&amp; typeof bootstrap !== 'undefined') {
                            var bsModal = bootstrap.Modal.getInstance(modal);
                            if (bsModal) bsModal.hide();
                        }

                        setTimeout(function() {
                            window.location.reload();
                        }, 500);
                    }

                    // DISABLED - Currency API functionality removed
                    /*
                    function applyCurrencyVanilla(currencyCode) {
                        console.log('Applying currency with vanilla JavaScript:', currencyCode);

                        fetch('/shop/apply_currency', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                jsonrpc: '2.0',
                                method: 'call',
                                params: {
                                    currency_code: currencyCode
                                }
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            console.log('Currency applied (vanilla):', data);
                            var result = data.result || data;
                            if (result &amp;&amp; result.success) {
                                console.log('Currency successfully set to:', result.currency);
                            } else {
                                console.error('Failed to apply currency:', result?.error || 'Unknown error');
                            }
                        })
                        .catch(error => {
                            console.error('Error applying currency (vanilla):', error);
                        });
                    }
                    */

                    // Global functions for external access
                    window.CountrySelection = {
                        openModal: function() {
                            var modal = document.getElementById('countrySelectionModal');
                            if (modal &amp;&amp; typeof bootstrap !== 'undefined') {
                                var bsModal = new bootstrap.Modal(modal);
                                bsModal.show();
                            }
                        },
                        resetSelection: function() {
                            localStorage.removeItem('country_selection_shown');
                            localStorage.removeItem('selected_country');
                            localStorage.removeItem('selected_currency');
                        }
                    };
                }
                */

                // DISABLED - Start initialization when DOM is ready
                /*
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', function() {
                        if (useVanillaJS || typeof $ === 'undefined') {
                            console.log('Using vanilla JavaScript for country selection');
                            initCountrySelectionVanilla();
                        } else {
                            initCountrySelection();
                        }
                    });
                } else {
                    if (useVanillaJS || typeof $ === 'undefined') {
                        console.log('Using vanilla JavaScript for country selection (immediate)');
                        initCountrySelectionVanilla();
                    } else {
                        initCountrySelection();
                    }
                }
                */
            </script>
            -->
        </xpath>
    <!-- </template> -->

    <!-- Country Selection CSS - DISABLED -->
    <!--
    <template id="country_selection_css" name="Country Selection CSS" inherit_id="website.layout" priority="15">
        <xpath expr="//head" position="inside">
            <style>
                /* Country Selection Modal Styles */
                .country-grid {
                    max-height: 400px;
                    overflow-y: auto;
                }
                
                .country-option {
                    cursor: pointer;
                    padding: 10px;
                    border: 1px solid #e9ecef;
                    border-radius: 8px;
                    margin-bottom: 10px;
                    transition: all 0.3s ease;
                    background: white;
                }
                
                .country-option:hover {
                    background-color: #f8f9fa;
                    border-color: #007bff;
                    transform: translateY(-2px);
                    box-shadow: 0 4px 8px rgba(0,123,255,0.15);
                }
                
                .country-card {
                    text-align: center;
                    padding: 15px 10px;
                }
                
                .country-flag {
                    width: 32px;
                    height: 24px;
                    object-fit: cover;
                    border-radius: 4px;
                    margin-bottom: 8px;
                }
                
                .country-flag-placeholder {
                    width: 32px;
                    height: 24px;
                    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
                    border-radius: 4px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 8px;
                    color: #6c757d;
                }
                
                .country-name {
                    font-weight: 600;
                    color: #495057;
                    font-size: 14px;
                    margin-bottom: 4px;
                }
                
                .country-currency {
                    font-size: 12px;
                    color: #6c757d;
                }
                
                .popular-country {
                    margin-bottom: 15px;
                }
                
                .popular-country .country-card {
                    padding: 20px 15px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border-radius: 12px;
                }
                
                .popular-country .country-name {
                    color: white;
                    font-size: 16px;
                }
                
                .popular-country .country-currency {
                    color: rgba(255,255,255,0.8);
                }
                
                .popular-country:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 8px 25px rgba(102,126,234,0.3);
                }
                
                #countrySearch {
                    border-radius: 25px;
                    padding: 12px 20px;
                    border: 2px solid #e9ecef;
                    font-size: 16px;
                }
                
                #countrySearch:focus {
                    border-color: #007bff;
                    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
                }
                
                .modal-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border-bottom: none;
                }
                
                .modal-header .close {
                    color: white;
                    opacity: 0.8;
                }
                
                .modal-header .close:hover {
                    opacity: 1;
                }
            </style>
        </xpath>
    </template>
</odoo>
