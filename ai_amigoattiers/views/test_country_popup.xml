<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- DISABLED - Test page for country popup -->
    <!--
    <template id="test_country_popup" name="Test Country Popup">
        <t t-call="website.layout">
            <div id="wrap">
                <div class="container mt-5">
                    <div class="row">
                        <div class="col-12">
                            <h1>Test Country Selection Popup</h1>
                            <p>This page is for testing the country selection popup functionality.</p>
                            
                            <div class="mt-4">
                                <button class="btn btn-primary" data-toggle="modal" data-target="#countrySelectionModal">
                                    <i class="fa fa-globe"></i> Open Country Selection
                                </button>
                            </div>
                            
                            <div class="mt-4">
                                <h3>Current Selection:</h3>
                                <div id="current-selection">
                                    <p>No country selected yet.</p>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <h3>Debug Info:</h3>
                                <div id="debug-info">
                                    <p>Check browser console for debug messages.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Include the country selection modal -->
            <t t-call="ai_amigoattiers.country_selection_modal"/>
            
            <script>
                <![CDATA[
                document.addEventListener('DOMContentLoaded', function() {
                    console.log('Test page loaded');

                    // Test AJAX endpoint directly
                    setTimeout(function() {
                        console.log('Testing /shop/get_countries endpoint...');
                        fetch('/shop/get_countries', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                jsonrpc: '2.0',
                                method: 'call',
                                params: {},
                                id: 1
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            console.log('Countries response:', data);
                            if (data.result && data.result.countries) {
                                document.getElementById('debug-info').innerHTML =
                                    '<p>Found ' + data.result.countries.length + ' countries</p>' +
                                    '<pre>' + JSON.stringify(data.result.countries.slice(0, 3), null, 2) + '</pre>';
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            document.getElementById('debug-info').innerHTML = '<p class="text-danger">Error: ' + error.message + '</p>';
                        });
                    }, 1000);
                });
                ]]>
            </script>
        </t>
    </template>
    -->
</odoo>
