<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Module Overview Page -->
        <template id="module_overview_page" name="AI Amigo Attires - Module Overview">
            <t t-call="website.layout">
                <div id="wrap" class="oe_structure oe_empty">
                    <div class="container mt-4">
                        <div class="row">
                            <div class="col-12">
                                <div class="jumbotron bg-primary text-white text-center">
                                    <h1 class="display-4">
                                        <i class="fa fa-tshirt"></i> AI Amigo Attires Module
                                    </h1>
                                    <p class="lead">Complete Size Chart E-commerce Solution</p>
                                    <hr class="my-4"/>
                                    <p>Comprehensive module for Indian ethnic wear with dynamic size charts and brand management.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Features Overview -->
                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="card h-100">
                                    <div class="card-header bg-success text-white">
                                        <h5><i class="fa fa-ruler"></i> Dynamic Size Charts</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul>
                                            <li>Customizable size chart headers</li>
                                            <li>Gender-specific charts (Male/Female/Unisex)</li>
                                            <li>Brand-specific sizing</li>
                                            <li>Automatic cm/inch conversion</li>
                                            <li>Indian sizing standards</li>
                                        </ul>
                                        <a href="/web#action=ai_amigoattiers.action_size_charts" class="btn btn-success btn-sm">Manage Size Charts</a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- DISABLED - Multi-Currency Support -->
                            <!--
                            <div class="col-md-4">
                                <div class="card h-100">
                                    <div class="card-header bg-info text-white">
                                        <h5><i class="fa fa-globe"></i> Multi-Currency Support</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul>
                                            <li>Real-time currency conversion</li>
                                            <li>Country-specific pricing</li>
                                            <li>Automatic rate updates</li>
                                            <li>INR base currency</li>
                                            <li>Cookie-based preferences</li>
                                        </ul>
                                        <a href="/test/currency-simple" class="btn btn-info btn-sm">Test Currency</a>
                                    </div>
                                </div>
                            </div>
                            -->
                            
                            <div class="col-md-4">
                                <div class="card h-100">
                                    <div class="card-header bg-warning text-white">
                                        <h5><i class="fa fa-tags"></i> Product Management</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul>
                                            <li>Brand management system</li>
                                            <li>Gender-based categorization</li>
                                            <li>Material specifications</li>
                                            <li>Size chart assignments</li>
                                            <li>Fitting notes</li>
                                        </ul>
                                        <a href="/web#action=ai_amigoattiers.action_product_brands" class="btn btn-warning btn-sm">Manage Brands</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Start Guide -->
                        <div class="row mt-5">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-dark text-white">
                                        <h4><i class="fa fa-rocket"></i> Quick Start Guide</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h5>1. Set Up Size Charts</h5>
                                                <ol>
                                                    <li>Go to <strong>Website → Configuration → Size Charts</strong></li>
                                                    <li>Click <strong>"New"</strong> to create a size chart</li>
                                                    <li>Choose gender (Male/Female/Unisex)</li>
                                                    <li>Add size lines with measurements in cm</li>
                                                    <li>Assign to products in product form</li>
                                                </ol>
                                                
                                                <h5>2. Configure Brands</h5>
                                                <ol>
                                                    <li>Go to <strong>Website → Configuration → Product Brands</strong></li>
                                                    <li>Create brands with fitting notes</li>
                                                    <li>Assign brands to products</li>
                                                </ol>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <!-- DISABLED - Currency Features -->
                                                <!--
                                                <h5>3. Test Currency Features</h5>
                                                <ol>
                                                    <li>Visit <strong>/test/currency-simple</strong> for currency testing</li>
                                                    <li>Check country selection popup on website</li>
                                                    <li>Verify automatic currency conversion</li>
                                                </ol>
                                                -->

                                                <h5>3. Verify Size Charts</h5>
                                                <ol>
                                                    <li>Visit <strong>/test/size-charts</strong> for debugging</li>
                                                    <li>Check size chart display on product pages</li>
                                                    <li>Test cm/inch unit conversion</li>
                                                </ol>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Technical Information -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white">
                                        <h5><i class="fa fa-cogs"></i> Technical Features</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul>
                                            <li><strong>Models:</strong> Size Charts, Size Chart Lines, Product Brands</li>
                                            <li><strong>Controllers:</strong> Size chart API</li>
                                            <li><strong>Templates:</strong> Size chart display</li>
                                            <li><strong>Integration:</strong> Odoo 18 CE compatible</li>
                                            <li><strong>Dependencies:</strong> website, website_sale</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-danger text-white">
                                        <h5><i class="fa fa-bug"></i> Troubleshooting</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul>
                                            <!-- DISABLED - Currency troubleshooting -->
                                            <!-- <li><strong>Currency Issues:</strong> Check /test/currency-simple</li> -->
                                            <li><strong>Size Chart Problems:</strong> Visit /test/size-charts</li>
                                            <!-- <li><strong>Country Selection:</strong> Clear browser cookies</li> -->
                                            <!-- <li><strong>Rate Updates:</strong> Use /test/update-currency-rates</li> -->
                                            <li><strong>Module Errors:</strong> Check Odoo logs</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Links -->
                        <div class="row mt-4 mb-5">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5><i class="fa fa-link"></i> Quick Links</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <a href="/web#action=ai_amigoattiers.action_size_charts" class="btn btn-primary m-2">Size Charts</a>
                                        <a href="/web#action=ai_amigoattiers.action_size_chart_lines" class="btn btn-primary m-2">Size Chart Lines</a>
                                        <a href="/web#action=ai_amigoattiers.action_product_brands" class="btn btn-primary m-2">Product Brands</a>
                                        <!-- DISABLED - Currency and country test links -->
                                        <!-- <a href="/test/currency-simple" class="btn btn-info m-2">Currency Test</a> -->
                                        <a href="/test/size-charts" class="btn btn-info m-2">Size Chart Debug</a>
                                        <!-- <a href="/test/popups" class="btn btn-warning m-2">Test Popups</a> -->
                                        <a href="/shop" class="btn btn-success m-2">Visit Shop</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Route for module overview -->
        <record id="module_overview_page_route" model="website.page">
            <field name="name">AI Amigo Attires Overview</field>
            <field name="type">qweb</field>
            <field name="url">/module-overview</field>
            <field name="website_published">True</field>
            <field name="key">ai_amigoattiers.module_overview_page</field>
        </record>
    </data>
</odoo>
