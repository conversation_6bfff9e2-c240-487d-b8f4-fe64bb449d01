# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request
import json
import logging

_logger = logging.getLogger(__name__)


class WebsiteSizeChartPricing(http.Controller):

    @http.route('/shop/size_chart', type='json', auth='public', website=True)
    def get_size_chart(self, product_id, **kwargs):
        """Get size chart data for a product"""
        try:
            product_id = int(product_id)
            product = request.env['product.template'].browse(product_id)

            if not product.exists():
                return {'error': 'Product not found'}

            # Get size chart for the product
            size_chart = product.get_size_chart()

            if not size_chart:
                return {'error': 'No size chart available for this product'}

            # Format size chart data for frontend
            size_chart_data = {
                'id': size_chart.id,
                'name': size_chart.name,
                'brand_name': size_chart.brand_id.name if size_chart.brand_id else '',
                'sizing_standard': size_chart.sizing_standard,
                'description': size_chart.description,
                'fitting_notes': size_chart.brand_id.fitting_notes if size_chart.brand_id else '',
                'sizes': []
            }

            # Add size lines
            for line in size_chart.size_line_ids:
                size_chart_data['sizes'].append({
                    'size_name': line.size_name,
                    'size_number': line.size_number,
                    'measurements': {
                        'chest': {'cm': line.chest_cm, 'inch': line.chest_inch},
                        'waist': {'cm': line.waist_cm, 'inch': line.waist_inch},
                        'hip': {'cm': line.hip_cm, 'inch': line.hip_inch},
                        'shoulder': {'cm': line.shoulder_cm, 'inch': line.shoulder_inch},
                        'sleeve': {'cm': line.sleeve_cm, 'inch': line.sleeve_inch},
                        'length': {'cm': line.length_cm, 'inch': line.length_inch}
                    }
                })

            return {'size_chart': size_chart_data}

        except Exception as e:
            return {'error': str(e)}

    # Currency functionality has been disabled - controller endpoint removed
    # @http.route('/shop/apply_currency', type='json', auth='public', website=True, csrf=False)
    # def apply_currency(self, currency_code, **kwargs):
    #     """Apply currency selection for the website"""
    #     try:
    #         _logger.info(f"Currency change request: {currency_code}")
    #
    #         # Find the currency
    #         currency = request.env['res.currency'].search([
    #             ('name', '=', currency_code),
    #             ('active', '=', True)
    #         ], limit=1)
    #
    #         if not currency:
    #             _logger.warning(f"Currency not found: {currency_code}")
    #             return {
    #                 'success': False,
    #                 'error': f'Currency {currency_code} not found or not active'
    #             }
    #
    #         # Set currency in session
    #         if hasattr(request, 'session'):
    #             request.session['website_sale_current_currency'] = currency.id
    #             _logger.info(f"Currency set in session: {currency_code} (ID: {currency.id})")
    #
    #         # Set currency in response cookie
    #         response_data = {
    #             'success': True,
    #             'currency': currency_code,
    #             'currency_id': currency.id,
    #             'currency_symbol': currency.symbol
    #         }
    #
    #         _logger.info(f"Currency successfully applied: {currency_code}")
    #         return response_data
    #
    #     except Exception as e:
    #         _logger.error(f"Error applying currency {currency_code}: {str(e)}")
    #         return {
    #             'success': False,
    #             'error': f'Error applying currency: {str(e)}'
    #         }
