# Website Size Chart & Country Pricing Module

A comprehensive Odoo 18 CE module that provides dynamic size charts and country-based pricing for e-commerce websites.

## Features

### Size Chart Module
- **Gender-based size charts** - Separate charts for male and female clothing
- **Brand-specific fitting variations** - Different brands can have unique size charts
- **Indian sizing standards** - Built with Indian measurements as the default
- **Unit switching** - Toggle between centimeters and inches with image-based UX
- **Popup modal interface** - Clean, responsive modal for size chart display
- **Responsive design** - Works on desktop, tablet, and mobile devices

### Country-Based Pricing
- **Country selection popup** - Appears on first visit to select country
- **Multi-currency support** - Display prices in local currencies
- **Flexible pricing methods** - Multiplier, fixed exchange rate, or pricelist-based
- **Session persistence** - Remembers user's country selection
- **Header country display** - Shows current country/currency in header

## Installation

1. Copy this module to your Odoo addons directory
2. Update the apps list in Odoo
3. Install the "Website Size Chart & Country Pricing" module
4. Configure your size charts and country pricing

## Configuration

### Setting up Size Charts

1. Go to **Website > Configuration > Size Charts & Pricing > Size Charts**
2. Create a new size chart:
   - Set the name (e.g., "Men's Shirt Size Chart")
   - Select gender (Male/Female/Unisex)
   - Choose brand (optional)
   - Set sizing standard (Indian/International/Brand Specific)
   - Add size lines with measurements in centimeters

3. Add size lines with measurements:
   - Size name (XS, S, M, L, XL, XXL)
   - Size number (Indian sizing: 28, 30, 32, 34, 36, 38, 40, 42)
   - Measurements: Chest, Waist, Hip, Shoulder, Sleeve, Length

### Setting up Country Pricing

1. Go to **Website > Configuration > Size Charts & Pricing > Country Pricing**
2. Create pricing configurations for each country:
   - Select country
   - Choose currency
   - Set pricing method (multiplier recommended)
   - Set conversion multiplier (e.g., 0.012 for INR to USD)
   - Enable "Show in Popup" to include in country selection

### Assigning Size Charts to Products

1. Edit a product in **Sales > Products > Products**
2. Set the **Gender** field
3. Optionally assign a specific **Size Chart**
4. If no specific chart is assigned, the system will auto-detect based on gender and brand

## Usage

### For Customers

1. **Country Selection**: On first visit, customers see a popup to select their country
2. **Size Chart Access**: Click the "Size Chart" button on product pages
3. **Unit Toggle**: Switch between centimeters and inches using the toggle buttons
4. **Country Change**: Use the country selector in the header to change country/currency

### For Administrators

1. **Size Chart Management**: Create and manage size charts from the backend
2. **Country Pricing**: Configure pricing for different countries
3. **Brand Management**: Set up brands with specific fitting notes
4. **Analytics**: Track size chart usage and country selections

## Technical Details

### Models

- `website.size.chart` - Main size chart model
- `website.size.chart.line` - Individual size measurements
- `website.country.pricing` - Country-based pricing configuration
- `product.brand` - Product brand with fitting notes

### Controllers

- `/shop/size_chart` - AJAX endpoint for size chart data
- `/shop/set_country` - AJAX endpoint for country selection
- `/shop/get_countries` - AJAX endpoint for available countries

### JavaScript Widgets

- `SizeChart` - Handles size chart modal functionality
- `CountryPricing` - Manages country selection popup
- `SizeChartButton` - Handles size chart button clicks

## Customization

### Adding New Measurements

To add new measurement fields:

1. Add fields to `website.size.chart.line` model
2. Update the size chart template to display new fields
3. Update the JavaScript to handle unit conversion

### Custom Pricing Logic

To implement custom pricing logic:

1. Extend the `calculate_price` method in `website.country.pricing`
2. Add new pricing methods to the selection field
3. Update the controller to handle new pricing logic

### Styling Customization

Modify `static/src/css/modals.css` to customize:
- Modal appearance
- Button styles
- Table layouts
- Responsive behavior

## Indian Sizing Standards

This module is built with Indian sizing standards in mind:

- **Men's Clothing**: Sizes 28-46 (chest measurements in cm)
- **Women's Clothing**: Sizes 32-42 (bust measurements in cm)
- **Default Unit**: Centimeters (with inch conversion)
- **Measurements**: Based on Indian body measurements

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Dependencies

- Odoo 18 CE
- website
- website_sale
- product
- sale

## License

This module is provided as-is for educational and commercial use.

## Support

For support and customization requests, please contact the module developer.
