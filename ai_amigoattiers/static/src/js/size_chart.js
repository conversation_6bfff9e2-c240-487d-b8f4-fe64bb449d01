odoo.define('ai_amigoattiers.size_chart', function (require) {
'use strict';

var publicWidget = require('web.public.widget');
var ajax = require('web.ajax');

publicWidget.registry.SizeChart = publicWidget.Widget.extend({
    selector: '#sizeChartModal',
    events: {
        'click .unit-toggle .btn': '_onUnitToggle',
        'show.bs.modal': '_onModalShow',
        'hidden.bs.modal': '_onModalHidden',
    },

    /**
     * @override
     */
    start: function () {
        this._super.apply(this, arguments);
        this.currentUnit = 'cm'; // Default to centimeters (Indian standard)
        return this._loadSizeChart();
    },

    /**
     * Load size chart data for the current product
     */
    _loadSizeChart: function () {
        var self = this;
        var productId = this.$el.find('[data-product-id]').data('product-id');
        
        if (!productId) {
            // Try to get product ID from the page
            productId = $('input[name="product_id"]').val() || 
                       $('[data-product-id]').first().data('product-id');
        }

        if (productId) {
            return ajax.jsonRpc('/shop/size_chart', 'call', {
                'product_id': productId,
            }).then(function (data) {
                if (data.size_chart) {
                    self._renderSizeChart(data.size_chart);
                }
            }).catch(function (error) {
                console.error('Error loading size chart:', error);
            });
        }
        return Promise.resolve();
    },

    /**
     * Render size chart data in the modal
     */
    _renderSizeChart: function (sizeChart) {
        // Update modal title
        var title = 'Size Chart';
        if (sizeChart.brand_name) {
            title += ' - ' + sizeChart.brand_name;
        }
        this.$('.modal-title').text(title);

        // Update sizing standard info
        var standardText = 'Indian Standard';
        if (sizeChart.sizing_standard === 'international') {
            standardText = 'International';
        } else if (sizeChart.sizing_standard === 'brand_specific') {
            standardText = 'Brand Specific';
        }
        this.$('.sizing-standard').text(standardText);

        // Update brand fitting notes if available
        if (sizeChart.fitting_notes) {
            this.$('.brand-fitting-notes').html(sizeChart.fitting_notes).show();
        } else {
            this.$('.brand-fitting-notes').hide();
        }

        // Update description if available
        if (sizeChart.description) {
            this.$('.size-guide-description').html(sizeChart.description).show();
        } else {
            this.$('.size-guide-description').parent().hide();
        }
    },

    /**
     * Handle unit toggle between cm and inches
     */
    _onUnitToggle: function (ev) {
        ev.preventDefault();
        var $btn = $(ev.currentTarget);
        var unit = $btn.data('unit');
        
        if (unit === this.currentUnit) {
            return; // Already selected
        }

        // Update button states
        this.$('.unit-toggle .btn').removeClass('active');
        $btn.addClass('active');
        
        // Update current unit
        this.currentUnit = unit;
        
        // Toggle measurement displays
        this._toggleMeasurementUnit(unit);
        
        // Add visual feedback
        this._animateUnitChange();
    },

    /**
     * Toggle between cm and inch measurements
     */
    _toggleMeasurementUnit: function (unit) {
        var $table = this.$('.size-chart-table');
        
        if (unit === 'cm') {
            $table.find('.cm-value').removeClass('d-none');
            $table.find('.inch-value').addClass('d-none');
        } else {
            $table.find('.cm-value').addClass('d-none');
            $table.find('.inch-value').removeClass('d-none');
        }
        
        // Update table headers to show current unit
        $table.find('.measurement-col').each(function() {
            var $th = $(this);
            var measurement = $th.data('measurement');
            var headerText = measurement.charAt(0).toUpperCase() + measurement.slice(1);
            
            if (unit === 'cm') {
                $th.text(headerText + ' (cm)');
            } else {
                $th.text(headerText + ' (in)');
            }
        });
    },

    /**
     * Add animation effect when changing units
     */
    _animateUnitChange: function () {
        var $table = this.$('.size-chart-table tbody');
        
        $table.addClass('changing-units');
        
        setTimeout(function () {
            $table.removeClass('changing-units');
        }, 300);
    },

    /**
     * Handle modal show event
     */
    _onModalShow: function () {
        // Track analytics if needed
        if (typeof gtag !== 'undefined') {
            gtag('event', 'size_chart_opened', {
                'event_category': 'engagement',
                'event_label': 'size_chart'
            });
        }
        
        // Ensure proper unit is displayed
        this._toggleMeasurementUnit(this.currentUnit);
    },

    /**
     * Handle modal hidden event
     */
    _onModalHidden: function () {
        // Reset to default unit
        this.currentUnit = 'cm';
        this.$('.unit-toggle .btn[data-unit="cm"]').addClass('active');
        this.$('.unit-toggle .btn[data-unit="inch"]').removeClass('active');
    },
});

// Size Chart Button Widget
publicWidget.registry.SizeChartButton = publicWidget.Widget.extend({
    selector: '[data-toggle="modal"][data-target="#sizeChartModal"]',
    events: {
        'click': '_onSizeChartClick',
    },

    /**
     * Handle size chart button click
     */
    _onSizeChartClick: function (ev) {
        var productId = $(ev.currentTarget).data('product-id') || 
                       $('input[name="product_id"]').val();
        
        if (productId) {
            // Load size chart data via AJAX
            this._loadSizeChartData(productId);
        }
    },

    /**
     * Load size chart data for product
     */
    _loadSizeChartData: function (productId) {
        var self = this;
        
        // Show loading state
        var $modal = $('#sizeChartModal');
        $modal.find('.modal-body').append('<div class="loading-overlay"><div class="loading-spinner"></div></div>');
        
        return ajax.jsonRpc('/shop/size_chart', 'call', {
            'product_id': productId,
        }).then(function (data) {
            // Remove loading state
            $modal.find('.loading-overlay').remove();
            
            if (data.error) {
                self._showError(data.error);
            } else if (data.size_chart) {
                // Size chart will be rendered by the SizeChart widget
                console.log('Size chart loaded successfully');
            } else {
                self._showError('No size chart available for this product.');
            }
        }).catch(function (error) {
            // Remove loading state
            $modal.find('.loading-overlay').remove();
            self._showError('Failed to load size chart. Please try again.');
            console.error('Size chart loading error:', error);
        });
    },

    /**
     * Show error message in modal
     */
    _showError: function (message) {
        var $modal = $('#sizeChartModal');
        var errorHtml = '<div class="alert alert-error text-center">' +
                       '<i class="fa fa-exclamation-triangle"></i> ' + message +
                       '</div>';
        
        $modal.find('.modal-body').html(errorHtml);
    },
});

// Initialize when DOM is ready
$(document).ready(function () {
    // Add CSS class for unit change animation
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .changing-units {
                opacity: 0.7;
                transition: opacity 0.3s ease;
            }
            .measurement-col {
                transition: all 0.3s ease;
            }
        `)
        .appendTo('head');
});

return {
    SizeChart: publicWidget.registry.SizeChart,
    SizeChartButton: publicWidget.registry.SizeChartButton,
};

});
