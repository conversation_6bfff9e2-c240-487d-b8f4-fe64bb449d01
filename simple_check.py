#!/usr/bin/env python3

import xmlrpc.client
import sys

URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

try:
    print("Connecting to Odoo...")
    common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
    models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
    uid = common.authenticate(DB, USERNAME, PASSWORD, {})
    
    if not uid:
        print("Authentication failed")
        sys.exit(1)
    
    print(f"Connected as user {uid}")
    
    # Check IMCA modules
    modules = ['imca_groups', 'imca_services', 'imca_client_documents', 'imca_crednetials_manager', 'imca_dsc_management']
    
    for module_name in modules:
        try:
            module_data = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.module.module', 'search_read',
                [('name', '=', module_name)],
                {'fields': ['name', 'state']}
            )
            
            if module_data:
                print(f"{module_name}: {module_data[0]['state']}")
            else:
                print(f"{module_name}: NOT FOUND")
                
        except Exception as e:
            print(f"{module_name}: ERROR - {e}")
    
    print("Check completed")
    
except Exception as e:
    print(f"Error: {e}")
    sys.exit(1)
