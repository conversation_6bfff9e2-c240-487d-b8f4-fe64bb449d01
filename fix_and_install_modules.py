#!/usr/bin/env python3

import xmlrpc.client
import sys
import time

URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

IMCA_MODULES = [
    'imca_groups',
    'imca_services', 
    'imca_client_documents',
    'imca_crednetials_manager',
    'imca_dsc_management'
]

def connect_odoo():
    try:
        common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
        models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
        uid = common.authenticate(DB, USERNAME, PASSWORD, {})
        
        if not uid:
            raise Exception("Authentication failed")
            
        return common, models, uid
    except Exception as e:
        print(f"Connection error: {e}")
        return None, None, None

def install_module(models, uid, module_name):
    """Install a module"""
    try:
        print(f"Installing {module_name}...")
        
        # Search for the module
        module_ids = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.module.module', 'search',
            [('name', '=', module_name)]
        )
        
        if not module_ids:
            print(f"  ❌ Module {module_name} not found in module list")
            return False
        
        module_id = module_ids[0]
        
        # Get current state
        module_data = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.module.module', 'read',
            [module_id], {'fields': ['state', 'name']}
        )[0]
        
        print(f"  Current state: {module_data['state']}")
        
        if module_data['state'] == 'uninstalled':
            # Install the module
            models.execute_kw(
                DB, uid, PASSWORD,
                'ir.module.module', 'button_immediate_install',
                [module_id]
            )
            print(f"  ✅ Installation initiated")
            return True
            
        elif module_data['state'] == 'installed':
            # Upgrade the module
            models.execute_kw(
                DB, uid, PASSWORD,
                'ir.module.module', 'button_immediate_upgrade',
                [module_id]
            )
            print(f"  ✅ Upgrade initiated")
            return True
            
        else:
            print(f"  ⚠️  Module in state: {module_data['state']}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_models(models, uid):
    """Test if models are accessible"""
    print("\n" + "="*50)
    print("TESTING MODEL ACCESS")
    print("="*50)
    
    # Test basic model access with search_count (safer than search_read)
    model_tests = {
        'x_groups': 'Groups',
        'x_services': 'Services',
        'x_client_documents': 'Client Documents',
        'x_credentials': 'Credentials',
        'x_dsc': 'DSC Management'
    }
    
    results = {}
    
    for model, name in model_tests.items():
        try:
            print(f"🧪 Testing {name} model ({model})...")
            
            # Use search_count instead of search_read to avoid field issues
            count = models.execute_kw(
                DB, uid, PASSWORD,
                model, 'search_count', [[]]
            )
            print(f"   ✅ {name} model accessible, {count} records found")
            results[model] = True
            
        except Exception as e:
            print(f"   ❌ Error accessing {name} model: {str(e)[:100]}...")
            results[model] = False
    
    return results

def test_partner_fields(models, uid):
    """Test partner fields"""
    try:
        print("🧪 Testing res.partner basic access...")
        
        # Test basic partner access
        count = models.execute_kw(
            DB, uid, PASSWORD,
            'res.partner', 'search_count', [[]]
        )
        print(f"   ✅ Partner model accessible, {count} records found")
        
        # Try to read a partner with basic fields only
        partner_ids = models.execute_kw(
            DB, uid, PASSWORD,
            'res.partner', 'search',
            [[]], {'limit': 1}
        )
        
        if partner_ids:
            partner_data = models.execute_kw(
                DB, uid, PASSWORD,
                'res.partner', 'read',
                [partner_ids[0]], {'fields': ['name']}
            )[0]
            print(f"   ✅ Can read partner: {partner_data['name']}")
            return True
        else:
            print("   ⚠️  No partners found")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing partner: {str(e)[:100]}...")
        return False

def main():
    print("="*60)
    print("IMCA MODULES INSTALLATION/UPGRADE")
    print("="*60)
    
    # Connect
    common, models, uid = connect_odoo()
    if not models:
        print("Failed to connect")
        sys.exit(1)
    
    print(f"✅ Connected as user {uid}")
    
    # Install/upgrade modules
    print("\n" + "="*50)
    print("INSTALLING/UPGRADING MODULES")
    print("="*50)
    
    results = {}
    for module in IMCA_MODULES:
        results[module] = install_module(models, uid, module)
        time.sleep(2)  # Wait between operations
    
    # Wait for operations to complete
    print("\n⏳ Waiting 20 seconds for operations to complete...")
    time.sleep(20)
    
    # Test models
    model_results = test_models(models, uid)
    
    # Test partner
    partner_result = test_partner_fields(models, uid)
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    print("Module Operations:")
    for module, success in results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"  {module}: {status}")
    
    print("\nModel Access Tests:")
    for model, success in model_results.items():
        status = "✅ ACCESSIBLE" if success else "❌ FAILED"
        print(f"  {model}: {status}")
    
    print(f"\nPartner Test: {'✅ PASSED' if partner_result else '❌ FAILED'}")
    
    successful_modules = sum(results.values())
    successful_models = sum(model_results.values())
    
    print(f"\nOverall: {successful_modules}/{len(results)} modules, {successful_models}/{len(model_results)} models accessible")
    
    if successful_modules == len(results) and successful_models == len(model_results):
        print("\n🎉 ALL OPERATIONS SUCCESSFUL!")
    else:
        print("\n⚠️  Some issues detected. Check the output above.")

if __name__ == "__main__":
    main()
