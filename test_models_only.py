#!/usr/bin/env python3
"""
Test script to upgrade modules without partner view extensions
This will help us verify if the partner model extensions work when view extensions are removed
"""

import xmlrpc.client
import time
import sys
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

IMCA_MODULES = [
    'imca_groups',
    'imca_services', 
    'imca_client_documents',
    'imca_crednetials_manager',
    'imca_dsc_management'
]

def connect_to_odoo():
    """Connect to Odoo"""
    try:
        logger.info(f"Connecting to Odoo at {URL}")
        common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
        models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
        
        uid = common.authenticate(DB, USERNAME, PASSWORD, {})
        if not uid:
            raise Exception("Authentication failed")
            
        logger.info(f"Successfully connected as user ID: {uid}")
        return common, models, uid
        
    except Exception as e:
        logger.error(f"Connection failed: {e}")
        return None, None, None

def temporarily_remove_partner_views():
    """Temporarily rename partner view files to disable them"""
    logger.info("Temporarily disabling partner view extensions...")
    
    view_files = [
        'imca_services/views/services_views.xml',
        'imca_client_documents/views/client_documents_views.xml',
        'imca_crednetials_manager/views/credentials_views.xml',
        'imca_dsc_management/views/dsc_management_views.xml'
    ]
    
    for view_file in view_files:
        if os.path.exists(view_file):
            temp_file = view_file + '.temp_disabled'
            os.rename(view_file, temp_file)
            logger.info(f"Disabled {view_file}")

def restore_partner_views():
    """Restore partner view files"""
    logger.info("Restoring partner view extensions...")
    
    view_files = [
        'imca_services/views/services_views.xml',
        'imca_client_documents/views/client_documents_views.xml',
        'imca_crednetials_manager/views/credentials_views.xml',
        'imca_dsc_management/views/dsc_management_views.xml'
    ]
    
    for view_file in view_files:
        temp_file = view_file + '.temp_disabled'
        if os.path.exists(temp_file):
            os.rename(temp_file, view_file)
            logger.info(f"Restored {view_file}")

def upgrade_module(models, uid, module_name):
    """Upgrade a specific module"""
    try:
        logger.info(f"Upgrading module: {module_name}")
        
        # Search for the module
        module_ids = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.module.module', 'search',
            [[('name', '=', module_name)]]
        )
        
        if not module_ids:
            logger.error(f"Module {module_name} not found")
            return False
        
        module_id = module_ids[0]
        
        # Get current state
        module_data = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.module.module', 'read',
            [module_id], {'fields': ['state']}
        )[0]
        
        current_state = module_data['state']
        logger.info(f"Module {module_name} current state: {current_state}")
        
        if current_state == 'installed':
            # Upgrade the module
            try:
                result = models.execute_kw(
                    DB, uid, PASSWORD,
                    'ir.module.module', 'button_immediate_upgrade',
                    [[module_id]]
                )
                logger.info(f"✅ Upgrade initiated for {module_name}")
                return True
            except Exception as e:
                logger.error(f"❌ Error during upgrade of {module_name}: {e}")
                return False
        else:
            logger.warning(f"Module {module_name} not in installed state: {current_state}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error upgrading {module_name}: {e}")
        return False

def test_partner_fields(models, uid):
    """Test if partner fields exist"""
    logger.info("Testing partner field access...")
    
    fields_to_test = {
        'x_group_id': 'imca_groups',
        'x_service_ids': 'imca_services',
        'x_client_document_ids': 'imca_client_documents', 
        'x_credential_ids': 'imca_crednetials_manager',
        'x_dsc_ids': 'imca_dsc_management'
    }
    
    results = {}
    
    try:
        # Get partner fields
        partner_fields = models.execute_kw(
            DB, uid, PASSWORD,
            'res.partner', 'fields_get',
            [], {'attributes': ['string', 'relation']}
        )
        
        for field_name, module in fields_to_test.items():
            if field_name in partner_fields:
                field_info = partner_fields[field_name]
                logger.info(f"✅ Field {field_name} exists: {field_info.get('string', 'No description')}")
                results[field_name] = True
            else:
                logger.warning(f"❌ Field {field_name} does not exist in res.partner")
                results[field_name] = False
                
    except Exception as e:
        logger.error(f"❌ Error testing partner fields: {e}")
        
    return results

def main():
    """Main execution function"""
    logger.info("=" * 60)
    logger.info("TEST MODELS ONLY - WITHOUT PARTNER VIEW EXTENSIONS")
    logger.info("=" * 60)
    
    # Connect to Odoo
    common, models, uid = connect_to_odoo()
    if not models:
        logger.error("Failed to connect to Odoo. Exiting.")
        sys.exit(1)
    
    try:
        # Step 1: Temporarily disable partner view extensions
        temporarily_remove_partner_views()
        
        # Step 2: Upgrade all modules
        logger.info("\n" + "=" * 40)
        logger.info("UPGRADING MODULES WITHOUT VIEW EXTENSIONS")
        logger.info("=" * 40)
        
        upgrade_results = {}
        for module in IMCA_MODULES:
            upgrade_results[module] = upgrade_module(models, uid, module)
            time.sleep(3)  # Wait between upgrades
        
        # Wait for upgrades to complete
        logger.info("\n⏳ Waiting 15 seconds for upgrades to complete...")
        time.sleep(15)
        
        # Step 3: Test partner fields
        logger.info("\n" + "=" * 40)
        logger.info("TESTING PARTNER FIELDS AFTER MODEL UPGRADE")
        logger.info("=" * 40)
        
        field_results = test_partner_fields(models, uid)
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("RESULTS SUMMARY")
        logger.info("=" * 60)
        
        logger.info("Module Upgrade Results:")
        successful_upgrades = 0
        for module, success in upgrade_results.items():
            status = "✅ SUCCESS" if success else "❌ FAILED"
            logger.info(f"  {module}: {status}")
            if success:
                successful_upgrades += 1
        
        logger.info("\nPartner Fields Test:")
        successful_fields = 0
        for field, exists in field_results.items():
            status = "✅ EXISTS" if exists else "❌ MISSING"
            logger.info(f"  {field}: {status}")
            if exists:
                successful_fields += 1
        
        logger.info(f"\nOverall Results:")
        logger.info(f"  Modules upgraded: {successful_upgrades}/{len(IMCA_MODULES)}")
        logger.info(f"  Partner fields: {successful_fields}/{len(field_results)}")
        
        if successful_upgrades == len(IMCA_MODULES) and successful_fields == len(field_results):
            logger.info("\n🎉 SUCCESS! All modules upgraded and all partner fields exist!")
            logger.info("This confirms that the partner model extensions work correctly.")
            logger.info("The issue is only with the partner view extensions.")
        elif successful_fields == len(field_results):
            logger.info("\n✅ All partner fields exist! The model extensions are working.")
            logger.info("Some modules may have failed for other reasons.")
        else:
            logger.warning(f"\n⚠️  Some partner fields are missing. Need to investigate further.")
        
    finally:
        # Step 4: Always restore partner view extensions
        logger.info("\n" + "=" * 40)
        logger.info("RESTORING PARTNER VIEW EXTENSIONS")
        logger.info("=" * 40)
        restore_partner_views()
    
    logger.info("\nTest completed!")

if __name__ == "__main__":
    main()
