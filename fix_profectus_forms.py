#!/usr/bin/env python3
"""
Fix contact forms in profectusaccounts.com database
"""

import xmlrpc.client
import re

# Working connection details
url = 'http://localhost:8069'
db = 'profectusaccounts.com'
username = 'demo'
password = 'demo'

def connect_to_odoo():
    """Connect to profectusaccounts.com database"""
    try:
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        if not uid:
            print("❌ Authentication failed")
            return None, None
        
        print(f"✅ Connected to profectusaccounts.com as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        return models, uid
        
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return None, None

def check_available_models(models, uid):
    """Check what models are available for form handling"""
    try:
        print("\n🔍 Checking Available Models for Form Handling...")
        print("-" * 50)
        
        # Models that could handle form submissions
        potential_models = [
            'project.task',
            'mail.message', 
            'res.partner',
            'website.visitor',
            'ir.attachment',
            'mail.thread',
            'mail.activity'
        ]
        
        available_models = []
        for model_name in potential_models:
            try:
                # Try to search and see if we have access
                count = models.execute_kw(db, uid, password, model_name, 'search_count', [[]])
                print(f"   ✅ {model_name}: {count} records - ACCESSIBLE")
                available_models.append(model_name)
                
                # Check if we can create records
                try:
                    # Test with minimal data
                    if model_name == 'project.task':
                        test_fields = models.execute_kw(db, uid, password, model_name, 'fields_get', [], 
                                                      {'attributes': ['required', 'type']})
                        required_fields = [f for f, info in test_fields.items() if info.get('required')]
                        print(f"      Required fields: {required_fields}")
                        
                except Exception as e:
                    print(f"      ⚠️ Limited access: {e}")
                    
            except Exception as e:
                print(f"   ❌ {model_name}: Not accessible - {e}")
        
        return available_models
        
    except Exception as e:
        print(f"❌ Error checking models: {e}")
        return []

def create_project_task_from_form(models, uid, form_data):
    """Create a project task from form submission"""
    try:
        print(f"\n📝 Creating project task from form data...")
        
        # Prepare task data
        task_data = {
            'name': f"Website Contact: {form_data.get('name', 'Unknown')} - {form_data.get('subject', 'Contact Form')}",
            'description': f"""
Contact Form Submission:

Name: {form_data.get('name', 'Not provided')}
Email: {form_data.get('email_from', 'Not provided')}
Phone: {form_data.get('phone', 'Not provided')}
Company: {form_data.get('company', 'Not provided')}
Subject: {form_data.get('subject', 'Not provided')}

Message:
{form_data.get('description', form_data.get('Message', 'No message provided'))}

Submitted from: Website Contact Form
Submission Time: {form_data.get('submission_time', 'Unknown')}
            """.strip(),
            'user_ids': [],  # Unassigned initially
            'tag_ids': [],
        }
        
        # Try to create the task
        task_id = models.execute_kw(db, uid, password, 'project.task', 'create', [task_data])
        print(f"   ✅ Created project task with ID: {task_id}")
        return task_id
        
    except Exception as e:
        print(f"   ❌ Error creating project task: {e}")
        return None

def update_contact_form_views(models, uid):
    """Update contact form views to use project.task instead of mail.mail"""
    try:
        print("\n🔧 Updating Contact Form Views...")
        print("-" * 50)
        
        # Get contact form views
        contact_views = models.execute_kw(db, uid, password, 'ir.ui.view', 'search_read',
                                        [[('key', '=', 'website.contactus')]], 
                                        {'fields': ['id', 'name', 'arch_db']})
        
        for view in contact_views:
            print(f"   📄 Updating view: {view['name']} (ID: {view['id']})")
            
            arch = view['arch_db']
            
            # Update the form to use project.task instead of mail.mail
            updated_arch = arch.replace(
                'data-model_name="mail.mail"',
                'data-model_name="project.task"'
            )
            
            # Update field names to match project.task model
            updated_arch = updated_arch.replace('name="email_from"', 'name="partner_email"')
            updated_arch = updated_arch.replace('name="description"', 'name="description"')
            updated_arch = updated_arch.replace('name="Message"', 'name="description"')
            
            # Add hidden fields for better task creation
            if 'name="website_form_signature"' in updated_arch:
                # Add task-specific hidden fields
                signature_field = '<input type="hidden" name="website_form_signature"/>'
                task_fields = '''
                <input type="hidden" name="website_form_signature"/>
                <input type="hidden" name="stage_id" value=""/>
                <input type="hidden" name="project_id" value=""/>
                '''
                updated_arch = updated_arch.replace(signature_field, task_fields)
            
            if updated_arch != arch:
                try:
                    # Update the view
                    models.execute_kw(db, uid, password, 'ir.ui.view', 'write', 
                                    [view['id']], {'arch_db': updated_arch})
                    print(f"      ✅ Updated view {view['id']}")
                except Exception as e:
                    print(f"      ❌ Failed to update view {view['id']}: {e}")
            else:
                print(f"      ℹ️ No changes needed for view {view['id']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating views: {e}")
        return False

def create_custom_form_handler(models, uid):
    """Create a custom form handler using available models"""
    try:
        print("\n🔧 Creating Custom Form Handler...")
        print("-" * 50)
        
        # Since we can't modify the core website form handling,
        # let's create a simple solution using project tasks
        
        # Check if we can create a simple automation
        try:
            # Look for existing automations
            automations = models.execute_kw(db, uid, password, 'base.automation', 'search_read',
                                          [[]], {'fields': ['name', 'model_id'], 'limit': 5})
            print(f"   📋 Found {len(automations)} existing automations")
            
        except Exception as e:
            print(f"   ℹ️ Automation model not accessible: {e}")
        
        # Alternative: Create a simple webhook or use project tasks directly
        print("   💡 Recommendation: Use project.task model for form submissions")
        print("   💡 Forms will create tasks that can be managed in the Project app")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating form handler: {e}")
        return False

def test_form_submission(models, uid):
    """Test form submission with project.task"""
    try:
        print("\n🧪 Testing Form Submission...")
        print("-" * 50)
        
        # Simulate a form submission
        test_form_data = {
            'name': 'Test Contact',
            'email_from': '<EMAIL>',
            'phone': '+1234567890',
            'company': 'Test Company',
            'subject': 'Test Inquiry',
            'description': 'This is a test message from the contact form.',
            'submission_time': '2024-01-01 12:00:00'
        }
        
        # Create a project task from this data
        task_id = create_project_task_from_form(models, uid, test_form_data)
        
        if task_id:
            # Verify the task was created
            task = models.execute_kw(db, uid, password, 'project.task', 'read',
                                   [task_id], {'fields': ['name', 'description', 'create_date']})
            
            if task:
                task = task[0]
                print(f"   ✅ Test task created successfully:")
                print(f"      Name: {task['name']}")
                print(f"      Created: {task['create_date']}")
                
                # Clean up test task
                try:
                    models.execute_kw(db, uid, password, 'project.task', 'unlink', [task_id])
                    print(f"   🧹 Test task cleaned up")
                except:
                    print(f"   ⚠️ Could not clean up test task {task_id}")
                
                return True
        
        return False
        
    except Exception as e:
        print(f"❌ Error testing form submission: {e}")
        return False

def check_email_capabilities(models, uid):
    """Check email sending capabilities"""
    try:
        print("\n📧 Checking Email Capabilities...")
        print("-" * 50)
        
        # Check mail server configuration
        try:
            mail_servers = models.execute_kw(db, uid, password, 'ir.mail_server', 'search_count', [[]])
            print(f"   📮 Mail servers configured: {mail_servers}")
        except:
            print("   ❌ Cannot access mail server configuration")
        
        # Check if we can send emails via project tasks
        try:
            # Check if project.task has mail capabilities
            task_fields = models.execute_kw(db, uid, password, 'project.task', 'fields_get', [], 
                                          {'attributes': ['type']})
            
            mail_related_fields = [f for f in task_fields.keys() if 'mail' in f or 'message' in f]
            if mail_related_fields:
                print(f"   ✅ Project tasks have mail capabilities: {mail_related_fields}")
            else:
                print("   ℹ️ Project tasks may have limited mail capabilities")
                
        except Exception as e:
            print(f"   ⚠️ Error checking task mail capabilities: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking email capabilities: {e}")
        return False

def main():
    """Main function to fix forms"""
    print("🚀 Fixing Contact Forms in profectusaccounts.com")
    print("=" * 60)
    
    # Connect to database
    models, uid = connect_to_odoo()
    if not models:
        return False
    
    # Check available models
    available_models = check_available_models(models, uid)
    
    if 'project.task' in available_models:
        print("\n✅ project.task model is available - we can use this for form handling")
        
        # Test form submission
        test_success = test_form_submission(models, uid)
        
        if test_success:
            print("\n🎯 SOLUTION APPROACH:")
            print("1. ✅ Use project.task model to store form submissions")
            print("2. ✅ Forms will create tasks in the Project app")
            print("3. ✅ Staff can manage inquiries through Project interface")
            print("4. ✅ Email notifications can be set up through Project workflows")
            
            # Update form views if possible
            # Note: We may not have write access to views with demo user
            print("\n⚠️ NOTE: View updates may require admin access")
            print("   Current user (demo) may have limited permissions")
            
        else:
            print("\n❌ Form submission test failed")
    
    else:
        print("\n❌ No suitable models available for form handling")
    
    # Check email capabilities
    check_email_capabilities(models, uid)
    
    # Summary and recommendations
    print("\n" + "=" * 60)
    print("📊 SOLUTION SUMMARY")
    print("=" * 60)
    print("✅ CURRENT STATUS:")
    print("   • Contact forms exist and are accessible")
    print("   • Success pages are configured")
    print("   • project.task model is available for form storage")
    
    print("\n🔧 REQUIRED ACTIONS:")
    print("   1. Update form views to use project.task instead of mail.mail")
    print("   2. Configure email notifications for new project tasks")
    print("   3. Set up project workflow for handling contact inquiries")
    print("   4. Test form submissions end-to-end")
    
    print("\n⚠️ LIMITATIONS:")
    print("   • Demo user has limited permissions")
    print("   • Admin access needed for view modifications")
    print("   • Email configuration may need admin setup")

if __name__ == "__main__":
    main()
