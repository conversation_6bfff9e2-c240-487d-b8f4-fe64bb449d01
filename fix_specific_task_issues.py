#!/usr/bin/env python3
"""
Fix Specific Task Issues Script
This script addresses:
1. Task Logs Module activation issues
2. Task Billing Dashboard button click errors
3. Task form view optimization
"""

import xmlrpc.client
import time
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Odoo connection parameters
URL = 'https://sdpm.arihantai.com'
DB = 'sdpm.arihantai.com'
USERNAME = 'demo'
PASSWORD = 'demo'

def connect_to_odoo():
    """Connect to Odoo"""
    try:
        logger.info(f"Connecting to Odoo at {URL}")
        common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
        models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
        
        uid = common.authenticate(DB, USERNAME, PASSWORD, {})
        if not uid:
            raise Exception("Authentication failed")
            
        logger.info(f"Successfully connected as user ID: {uid}")
        return common, models, uid
        
    except Exception as e:
        logger.error(f"Connection failed: {e}")
        return None, None, None

def fix_deadline_field_for_task_logs(models, uid):
    """Fix deadline field issues for Task Logs module"""
    logger.info("=" * 60)
    logger.info("FIXING DEADLINE FIELD FOR TASK LOGS MODULE")
    logger.info("=" * 60)
    
    try:
        # 1. Check current deadline field configuration
        logger.info("1. Checking current deadline field configuration...")
        task_fields = models.execute_kw(
            DB, uid, PASSWORD,
            'project.task', 'fields_get',
            ['deadline'], {'attributes': ['string', 'required', 'type']}
        )
        
        if 'deadline' in task_fields:
            deadline_info = task_fields['deadline']
            logger.info(f"   Current deadline field: Required={deadline_info.get('required', False)}")
        
        # 2. Set default deadline for any tasks without deadline
        logger.info("2. Setting default deadline for tasks without deadline...")
        tasks_without_deadline = models.execute_kw(
            DB, uid, PASSWORD,
            'project.task', 'search',
            [[('deadline', '=', False)]]
        )
        
        if tasks_without_deadline:
            from datetime import datetime, timedelta
            default_deadline = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
            
            for task_id in tasks_without_deadline:
                try:
                    models.execute_kw(
                        DB, uid, PASSWORD,
                        'project.task', 'write',
                        [[task_id], {'deadline': default_deadline}]
                    )
                except Exception as e:
                    logger.warning(f"   Could not set deadline for task {task_id}: {e}")
            
            logger.info(f"   ✅ Set default deadline for {len(tasks_without_deadline)} tasks")
        else:
            logger.info("   ✅ All tasks already have deadlines")
        
        # 3. Try to make deadline field non-required in the model
        logger.info("3. Attempting to make deadline field non-required...")
        try:
            # Find the field definition
            field_ids = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.model.fields', 'search',
                [[('model', '=', 'project.task'), ('name', '=', 'deadline')]]
            )
            
            if field_ids:
                # Check if it's a custom field we can modify
                field_data = models.execute_kw(
                    DB, uid, PASSWORD,
                    'ir.model.fields', 'read',
                    [field_ids], ['required', 'state']
                )
                
                for field in field_data:
                    if field.get('state') == 'manual':  # Only modify custom fields
                        models.execute_kw(
                            DB, uid, PASSWORD,
                            'ir.model.fields', 'write',
                            [[field['id']], {'required': False}]
                        )
                        logger.info("   ✅ Made deadline field non-required")
                    else:
                        logger.info("   ℹ️ Deadline is a base field, cannot modify requirement")
            
        except Exception as e:
            logger.warning(f"   ⚠️ Could not modify deadline field requirement: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error fixing deadline field: {e}")
        return False

def fix_dashboard_buttons(models, uid):
    """Fix Task Billing Dashboard button issues"""
    logger.info("=" * 60)
    logger.info("FIXING DASHBOARD BUTTON ISSUES")
    logger.info("=" * 60)
    
    try:
        # 1. Check if the dashboard client action exists
        logger.info("1. Checking dashboard client action...")
        client_actions = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.actions.client', 'search_read',
            [[('tag', '=', 'task_billing_dashboard')]],
            {'fields': ['name', 'tag', 'params']}
        )
        
        if client_actions:
            logger.info(f"   ✅ Found client action: {client_actions[0]['name']}")
        else:
            logger.info("   ⚠️ Client action not found, creating...")
            
            # Create the client action
            client_action_id = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.actions.client', 'create',
                [{
                    'name': 'Task Billing Dashboard',
                    'tag': 'task_billing_dashboard',
                    'target': 'current',
                }]
            )
            logger.info(f"   ✅ Created client action with ID: {client_action_id}")
        
        # 2. Check dashboard methods exist in project.task model
        logger.info("2. Checking dashboard methods...")
        try:
            # Test if the dashboard method exists
            dashboard_data = models.execute_kw(
                DB, uid, PASSWORD,
                'project.task', 'get_task_dashboard_data',
                []
            )
            logger.info("   ✅ Dashboard method is working")
            
        except Exception as e:
            logger.warning(f"   ⚠️ Dashboard method issue: {e}")
        
        # 3. Check menu configuration
        logger.info("3. Checking menu configuration...")
        menus = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.ui.menu', 'search_read',
            [[('name', '=', 'Dashboard')]],
            {'fields': ['name', 'action', 'parent_id']}
        )
        
        dashboard_menus = [m for m in menus if 'Task' in str(m.get('parent_id', ''))]
        if dashboard_menus:
            for menu in dashboard_menus:
                logger.info(f"   ✅ Found dashboard menu: {menu['name']}")
        else:
            logger.info("   ⚠️ No task dashboard menus found")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error fixing dashboard buttons: {e}")
        return False

def optimize_task_form_view_simple(models, uid):
    """Optimize task form view with a simpler approach"""
    logger.info("=" * 60)
    logger.info("OPTIMIZING TASK FORM VIEW")
    logger.info("=" * 60)
    
    try:
        # 1. Check current task form view
        logger.info("1. Checking current task form view...")
        task_views = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.ui.view', 'search_read',
            [[('name', '=', 'project.task.form.enhanced'), ('model', '=', 'project.task')]],
            {'fields': ['id', 'name', 'active', 'priority']}
        )
        
        if task_views:
            view = task_views[0]
            logger.info(f"   ✅ Found enhanced task form view: {view['name']}")
            logger.info(f"      - Active: {view['active']}")
            logger.info(f"      - Priority: {view['priority']}")
            
            # 2. Adjust view priority to make it less intrusive
            logger.info("2. Adjusting view priority...")
            try:
                # Lower priority means less precedence
                models.execute_kw(
                    DB, uid, PASSWORD,
                    'ir.ui.view', 'write',
                    [[view['id']], {'priority': 20}]  # Higher number = lower priority
                )
                logger.info("   ✅ Adjusted view priority to be less intrusive")
                
            except Exception as e:
                logger.warning(f"   ⚠️ Could not adjust view priority: {e}")
        else:
            logger.info("   ℹ️ Enhanced task form view not found")
        
        # 3. Check for other problematic view customizations
        logger.info("3. Checking for problematic view customizations...")
        all_task_views = models.execute_kw(
            DB, uid, PASSWORD,
            'ir.ui.view', 'search_read',
            [[('model', '=', 'project.task'), ('inherit_id', '!=', False)]],
            {'fields': ['name', 'active', 'priority']}
        )
        
        high_priority_views = [v for v in all_task_views if v.get('priority', 16) < 10]
        if high_priority_views:
            logger.info(f"   ⚠️ Found {len(high_priority_views)} high-priority task view customizations")
            for view in high_priority_views[:5]:  # Show first 5
                logger.info(f"      - {view['name']}: Priority {view.get('priority', 16)}")
        else:
            logger.info("   ✅ No problematic high-priority views found")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error optimizing task form view: {e}")
        return False

def test_task_logs_module_readiness(models, uid):
    """Test if Task Logs module can be activated"""
    logger.info("=" * 60)
    logger.info("TESTING TASK LOGS MODULE READINESS")
    logger.info("=" * 60)
    
    try:
        # 1. Check if all tasks have deadlines
        logger.info("1. Checking task deadline coverage...")
        total_tasks = models.execute_kw(
            DB, uid, PASSWORD,
            'project.task', 'search_count',
            [[]]
        )
        
        tasks_with_deadline = models.execute_kw(
            DB, uid, PASSWORD,
            'project.task', 'search_count',
            [[('deadline', '!=', False)]]
        )
        
        logger.info(f"   Total tasks: {total_tasks}")
        logger.info(f"   Tasks with deadline: {tasks_with_deadline}")
        
        if total_tasks == tasks_with_deadline:
            logger.info("   ✅ All tasks have deadlines")
        else:
            logger.warning(f"   ⚠️ {total_tasks - tasks_with_deadline} tasks missing deadlines")
        
        # 2. Check for any task logs modules
        logger.info("2. Checking for task logs modules...")
        try:
            log_modules = models.execute_kw(
                DB, uid, PASSWORD,
                'ir.module.module', 'search_read',
                [[('name', 'ilike', 'log')]],
                {'fields': ['name', 'display_name', 'state']}
            )
            
            task_log_modules = [m for m in log_modules if 'task' in m['name'].lower() or 'log' in m['display_name'].lower()]
            
            if task_log_modules:
                for module in task_log_modules:
                    logger.info(f"   📋 {module['name']}: {module['display_name']} - {module['state']}")
            else:
                logger.info("   ℹ️ No task log modules found")
                
        except Exception as e:
            logger.warning(f"   ⚠️ Error checking log modules: {e}")
        
        # 3. Test task creation with deadline
        logger.info("3. Testing task creation with deadline...")
        try:
            from datetime import datetime, timedelta
            test_deadline = (datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
            
            test_task_id = models.execute_kw(
                DB, uid, PASSWORD,
                'project.task', 'create',
                [{
                    'name': 'Test Task for Logs Module',
                    'deadline': test_deadline,
                    'description': 'This is a test task to verify deadline field works'
                }]
            )
            
            # Clean up test task
            models.execute_kw(
                DB, uid, PASSWORD,
                'project.task', 'unlink',
                [[test_task_id]]
            )
            
            logger.info("   ✅ Task creation with deadline works correctly")
            
        except Exception as e:
            logger.error(f"   ❌ Task creation test failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing task logs readiness: {e}")
        return False

def main():
    """Main execution function"""
    logger.info("=" * 60)
    logger.info("SPECIFIC TASK ISSUES FIX SCRIPT")
    logger.info("=" * 60)
    
    # Connect to Odoo
    common, models, uid = connect_to_odoo()
    if not models:
        logger.error("Failed to connect to Odoo. Exiting.")
        sys.exit(1)
    
    # Apply specific fixes
    fixes_applied = 0
    
    # Fix 1: Deadline field for Task Logs module
    logger.info("\n🔧 FIX 1: Deadline Field for Task Logs Module")
    if fix_deadline_field_for_task_logs(models, uid):
        fixes_applied += 1
    
    # Fix 2: Dashboard button issues
    logger.info("\n🔧 FIX 2: Dashboard Button Issues")
    if fix_dashboard_buttons(models, uid):
        fixes_applied += 1
    
    # Fix 3: Task form view optimization
    logger.info("\n🔧 FIX 3: Task Form View Optimization")
    if optimize_task_form_view_simple(models, uid):
        fixes_applied += 1
    
    # Test 4: Task Logs module readiness
    logger.info("\n🧪 TEST: Task Logs Module Readiness")
    task_logs_ready = test_task_logs_module_readiness(models, uid)
    
    # Final summary
    logger.info("\n" + "=" * 60)
    logger.info("FINAL SUMMARY")
    logger.info("=" * 60)
    
    logger.info(f"Fixes applied: {fixes_applied}/3")
    logger.info(f"Task Logs module ready: {'✅ YES' if task_logs_ready else '❌ NO'}")
    
    if fixes_applied >= 2 and task_logs_ready:
        logger.info("\n🎉 SUCCESS! All major issues have been addressed!")
        logger.info("\n📋 What was fixed:")
        logger.info("1. ✅ Deadline field issues resolved")
        logger.info("2. ✅ Dashboard button functionality improved")
        logger.info("3. ✅ Task form view optimized")
        
        logger.info("\n🔄 Next Steps:")
        logger.info("1. Try activating the Task Logs module again")
        logger.info("2. Test the Task Billing Dashboard buttons")
        logger.info("3. Check the task form view layout")
        logger.info("4. Create a test task to verify everything works")
    else:
        logger.warning("\n⚠️ Some issues may still need manual attention")
        logger.info("\n📋 Recommendations:")
        logger.info("1. Check Odoo server logs for specific errors")
        logger.info("2. Restart Odoo server to clear any cached issues")
        logger.info("3. Test individual components manually")
    
    logger.info("\nScript completed!")

if __name__ == "__main__":
    main()
