<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Partner Form View Extension for Credentials -->
    <record id="view_partner_form_credentials" model="ir.ui.view">
        <field name="name">res.partner.form.credentials</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page string="Credentials" name="credentials_info">
                    <field name="x_credential_ids" nolabel="1">
                        <list editable="bottom">
                            <field name="name"/>
                            <field name="x_type"/>
                            <field name="x_url" widget="url"/>
                            <field name="x_username"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <record id="view_x_credentials_form" model="ir.ui.view">
        <field name="name">x_credentials.form</field>
        <field name="model">x_credentials</field>
        <field name="arch" type="xml">
            <form string="Credentials">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Credential Name..."/>
                        </h1>
                    </div>
                    <group>
                        <group name="credentials" string="Service Information">
                            <field name="x_url" widget="url"/>
                            <field name="x_partner_id"/>
                            <field name="x_type"/>
                        </group>
                        <group name="usr_details" string="Login Details">
                            <field name="x_username"/>
                            <field name="x_password" password="True"/>
                        </group>
                    </group>
                </sheet>
            </form>


        </field>
    </record>

    <record id="view_x_credentials_list" model="ir.ui.view">
        <field name="name">x_credentials.list</field>
        <field name="model">x_credentials</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="x_url" widget="url"/>
                <field name="x_partner_id"/>
                <field name="x_username"/>
                <field name="x_type"/>
            </list>
        </field>
    </record>

    <record id="view_x_credentials_search" model="ir.ui.view">
        <field name="name">x_credentials.search</field>
        <field name="model">x_credentials</field>
        <field name="arch" type="xml">
            <search>
                <field name="name" string="Credential Name"/>
                <field name="x_partner_id" string="Client"/>
                <field name="x_username" string="Username"/>
                <field name="x_type" string="Type"/>
                <field name="x_url" string="URL"/>
                <group expand="0" string="Group By">
                    <filter string="Client" name="group_client" context="{'group_by': 'x_partner_id'}"/>
                    <filter string="Type" name="group_type" context="{'group_by': 'x_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <menuitem id="menu_x_credentials" name="Credentials" parent="imca_groups.menu_ca_management_root" action="action_x_credentials" sequence="50"/>
</odoo>
