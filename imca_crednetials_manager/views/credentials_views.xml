<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Partner Form View Extension for Credentials -->
    <record id="view_partner_form_credentials" model="ir.ui.view">
        <field name="name">res.partner.form.credentials</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page string="Credentials" name="credentials_info">
                    <field name="x_credential_ids" nolabel="1">
                        <list editable="bottom">
                            <field name="name"/>
                            <field name="x_type_id"/>
                            <field name="x_username"/>
                            <field name="x_password" password="True"/>
                            <field name="x_url" widget="url"/>
                            <field name="is_active"/>
                            <field name="last_updated" readonly="1"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <record id="view_x_credentials_form" model="ir.ui.view">
        <field name="name">x_credentials.form</field>
        <field name="model">x_credentials</field>
        <field name="arch" type="xml">
            <form string="Credentials">
                <header>
                    <field name="is_active" widget="boolean_toggle"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Credential Name..."/>
                        </h1>
                    </div>
                    <group>
                        <group name="credentials" string="Service Information">
                            <field name="x_partner_id"/>
                            <field name="x_type_id"/>
                            <field name="x_url" widget="url"/>
                        </group>
                        <group name="usr_details" string="Login Details">
                            <field name="x_username"/>
                            <field name="x_password" password="True"/>
                        </group>
                    </group>
                    <group>
                        <group name="additional_info" string="Additional Information">
                            <field name="notes"/>
                            <field name="last_updated" readonly="1"/>
                        </group>
                        <group name="additional_info_2" string="Additional Details">
                            <field name="notes"/>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_x_credentials_list" model="ir.ui.view">
        <field name="name">x_credentials.list</field>
        <field name="model">x_credentials</field>
        <field name="arch" type="xml">
            <list decoration-muted="not is_active">
                <field name="name"/>
                <field name="x_partner_id" string="Client"/>
                <field name="x_type_id"/>
                <field name="x_username"/>
                <field name="x_password" password="True"/>
                <field name="x_url" widget="url"/>
                <field name="is_active"/>
                <field name="last_updated"/>
            </list>
        </field>
    </record>

    <record id="view_x_credentials_search" model="ir.ui.view">
        <field name="name">x_credentials.search</field>
        <field name="model">x_credentials</field>
        <field name="arch" type="xml">
            <search>
                <field name="name" string="Credential Name"/>
                <field name="x_partner_id" string="Client"/>
                <field name="x_username" string="Username"/>
                <field name="x_type_id" string="Type"/>
                <field name="x_url" string="URL"/>
                <group expand="0" string="Group By">
                    <filter string="Client" name="group_client" context="{'group_by': 'x_partner_id'}"/>
                    <filter string="Type" name="group_type" context="{'group_by': 'x_type_id'}"/>
                    <filter string="Active" name="group_active" context="{'group_by': 'is_active'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Credential Types Views -->
    <record id="view_x_credential_types_form" model="ir.ui.view">
        <field name="name">x_credential_types.form</field>
        <field name="model">x_credential_types</field>
        <field name="arch" type="xml">
            <form string="Credential Type">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Type Name..."/>
                        </h1>
                    </div>
                    <group>
                        <group name="basic_info" string="Basic Information">
                            <field name="description"/>
                            <field name="active"/>
                        </group>
                        <group name="statistics" string="Statistics">
                            <field name="credential_count" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Related Credentials" name="credentials">
                            <field name="credential_ids" nolabel="1">
                                <list>
                                    <field name="name"/>
                                    <field name="x_partner_id"/>
                                    <field name="x_username"/>
                                    <field name="x_password" password="True"/>
                                    <field name="x_url" widget="url"/>
                                    <field name="is_active"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_x_credential_types_list" model="ir.ui.view">
        <field name="name">x_credential_types.list</field>
        <field name="model">x_credential_types</field>
        <field name="arch" type="xml">
            <list string="Credential Types">
                <field name="name"/>
                <field name="description"/>
                <field name="credential_count"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <record id="view_x_credential_types_search" model="ir.ui.view">
        <field name="name">x_credential_types.search</field>
        <field name="model">x_credential_types</field>
        <field name="arch" type="xml">
            <search>
                <field name="name" string="Type Name"/>
                <field name="description" string="Description"/>
                <filter string="Active" name="filter_active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="filter_inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Active Status" name="group_active" context="{'group_by': 'active'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Credential Types Action -->
    <record id="action_x_credential_types" model="ir.actions.act_window">
        <field name="name">Credential Types</field>
        <field name="res_model">x_credential_types</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new credential type
            </p>
            <p>
                Credential types help categorize and organize client credentials
                for different services like Income Tax Portal, GST Portal, etc.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_x_credentials" name="Credentials" parent="imca_groups.menu_ca_management_root" action="action_x_credentials" sequence="50"/>
    <menuitem id="menu_x_credential_types" name="Credential Types" parent="imca_groups.menu_ca_management_root" action="action_x_credential_types" sequence="45"/>
</odoo>
