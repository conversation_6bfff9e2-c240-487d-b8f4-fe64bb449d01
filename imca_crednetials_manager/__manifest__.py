{
    'name': 'Credentials Manager',
    'version': '********.0',
    'summary': 'Credentials Manager with Types',
    'description': '''
        Comprehensive Credentials Manager for CA Management:
        - Credential Types model for categorizing credentials
        - Many2one relationship between credentials and types
        - Separate menu for managing credential types
        - Enhanced credential management with additional fields
        - Odoo 18 compatible implementation
    ''',
    'category': 'Technical',
    'author': '<PERSON>hant ai',
    'depends': ['base', 'mail', 'imca_groups'],
    'data': [
        'security/ir.model.access.csv',
        'data/credential_types_data.xml',
        'views/action_views.xml',
        'views/credentials_views.xml',
    ],
    'installable': True,
    'application': True,
    'license': 'LGPL-3',
}
