from odoo import models, fields, api


class Res<PERSON><PERSON>ner(models.Model):
    _inherit = 'res.partner'

    x_credential_ids = fields.One2many('x_credentials', 'x_partner_id', string='Credentials')



class CredentialTypes(models.Model):
    _name = 'x_credential_types'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Credential Types'
    _order = 'name'

    name = fields.Char(string='Type Name', required=True, tracking=True)
    description = fields.Text(string='Description')
    active = fields.Boolean(string='Active', default=True)

    # Relations
    credential_ids = fields.One2many('x_credentials', 'x_type_id', string='Credentials')
    credential_count = fields.Integer(string='Credential Count', compute='_compute_credential_count')

    @api.depends('credential_ids')
    def _compute_credential_count(self):
        for record in self:
            record.credential_count = len(record.credential_ids)


class CrednetialsManager(models.Model):
    _name = 'x_credentials'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Crednetials Manager'

    name = fields.Char(string='Name', required=True, tracking=True)
    x_partner_id = fields.Many2one('res.partner', string='Client Ref', required=True, tracking=True)
    x_password = fields.Char(string='Password', tracking=True)
    x_type_id = fields.Many2one('x_credential_types', string='Credential Type', tracking=True)
    x_url = fields.Char(string='URL', tracking=True)
    x_username = fields.Char(string='Username', tracking=True)

    # Additional fields for better credential management
    notes = fields.Text(string='Notes')
    last_updated = fields.Datetime(string='Last Updated', default=fields.Datetime.now)
    is_active = fields.Boolean(string='Active', default=True)

    @api.model
    def create(self, vals):
        vals['last_updated'] = fields.Datetime.now()
        return super().create(vals)

    def write(self, vals):
        vals['last_updated'] = fields.Datetime.now()
        return super().write(vals)