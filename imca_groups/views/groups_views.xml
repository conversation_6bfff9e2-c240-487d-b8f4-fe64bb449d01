<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Partner Form View Extension for Groups -->
    <record id="view_partner_form_groups" model="ir.ui.view">
        <field name="name">res.partner.form.groups</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page string="Groups" name="groups_info">
                    <group>
                        <group string="Group Assignment">
                            <field name="x_group_id"/>
                        </group>
                    </group>
                </page>
            </xpath>
        </field>
    </record>

    <record id="view_x_groups_form" model="ir.ui.view">
        <field name="name">x_groups.form</field>
        <field name="model">x_groups</field>
        <field name="arch" type="xml">
           <form string="Groups">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Group Name..."/>
                        </h1>
                    </div>
                    <field name="x_partners" nolabel="1">
                        <list editable="bottom">
                            <field name="name"/>
                            <field name="email"/>
                            <field name="phone"/>
                            <field name="mobile"/>
                        </list>
                    </field>
                </sheet>
                </form>


        </field>
    </record>

    <record id="view_x_groups_list" model="ir.ui.view">
        <field name="name">x_groups.list</field>
        <field name="model">x_groups</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="x_partners" widget="many2many_tags"/>
            </list>
        </field>
    </record>

    <record id="view_x_groups_search" model="ir.ui.view">
        <field name="name">x_groups.search</field>
        <field name="model">x_groups</field>
        <field name="arch" type="xml">
            <search>
                <field name="name" string="Group Name"/>
                <field name="x_partners" string="Clients"/>
            </search>
        </field>
    </record>

    <!-- Enhanced CA Management Menu Structure -->
    <menuitem id="menu_ca_management_root" name="CA Management" sequence="10"/>

    <!-- Clients submenu with enhanced partner view -->
    <record id="action_ca_clients" model="ir.actions.act_window">
        <field name="name">CA Clients</field>
        <field name="res_model">res.partner</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="domain">[('is_company', '=', False), ('customer_rank', '>', 0)]</field>
        <field name="context">{'default_customer_rank': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new client for CA services
            </p>
        </field>
    </record>

    <menuitem id="menu_ca_clients" name="Clients" parent="menu_ca_management_root" action="action_ca_clients" sequence="10"/>
    <menuitem id="menu_x_groups" name="Groups" parent="menu_ca_management_root" action="action_x_groups" sequence="20"/>

</odoo>
