<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- List/Tree View -->
    <record id="view_currency_rate_update_tree" model="ir.ui.view">
        <field name="name">currency.rate.update.tree</field>
        <field name="model">currency.rate.update</field>
        <field name="arch" type="xml">
            <list string="Currency Rate Updates">
                <field name="name"/>
                <field name="create_date" string="Update Date"/>
                <field name="create_uid" string="Updated By"/>
                <field name="success" string="Status"/>
                <field name="message"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_currency_rate_update_form" model="ir.ui.view">
        <field name="name">currency.rate.update.form</field>
        <field name="model">currency.rate.update</field>
        <field name="arch" type="xml">
            <form string="Currency Rate Update">
                <header>
                    <button string="Update Rates" name="update_currency_rates" type="object" class="oe_highlight"/>
                    <button string="Fix Existing Rates" name="action_fix_existing_rates" type="object" class="btn-secondary"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="create_date" string="Update Date"/>
                            <field name="create_uid" string="Updated By"/>
                        </group>
                        <group>
                            <field name="success"/>
                            <field name="message"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_currency_rate_update_search" model="ir.ui.view">
        <field name="name">currency.rate.update.search</field>
        <field name="model">currency.rate.update</field>
        <field name="arch" type="xml">
            <search string="Search Currency Rate Updates">
                <field name="name"/>
                <field name="create_date"/>
                <field name="create_uid"/>
                <field name="success"/>
                <separator/>
                <filter string="Successful" name="successful" domain="[('success', '=', True)]"/>
                <filter string="Failed" name="failed" domain="[('success', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_by_success" context="{'group_by': 'success'}"/>
                    <filter string="Date" name="group_by_date" context="{'group_by': 'create_date:day'}"/>
                    <filter string="Updated By" name="group_by_user" context="{'group_by': 'create_uid'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_currency_rate_update" model="ir.actions.act_window">
        <field name="name">Currency Rate Updates</field>
        <field name="res_model">currency.rate.update</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_currency_rate_update_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No currency rate updates yet!
            </p>
            <p>
                Click 'Create' to perform a new currency rate update.
            </p>
        </field>
    </record>

</odoo>