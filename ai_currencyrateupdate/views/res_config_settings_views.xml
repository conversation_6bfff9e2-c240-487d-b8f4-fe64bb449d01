<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.currency.rate.update</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//app[1]" position="after">
                <app string="Currency Rate Update" name="ai_currencyrateupdate">
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <label for="openexchangerates_api_key" string="OpenExchangeRates API Key"/>
                                <div class="text-muted">
                                    Enter your OpenExchangeRates API key for automatic currency rate updates
                                </div>
                                <div class="mt8">
                                    <field name="openexchangerates_api_key" class="o_field_char"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </app>
            </xpath>
        </field>
    </record>

    <!-- Main Menu and Actions -->
    <menuitem id="menu_currency_rate_update_root"
        name="Currency Update"
        sequence="95"/>

    <!-- Settings Action -->
    <record id="action_currency_rate_update_configuration" model="ir.actions.act_window">
        <field name="name">Currency Rate Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
    </record>

    <!-- Settings Menu Item -->
    <menuitem id="menu_currency_rate_update_settings"
        name="Settings"
        parent="menu_currency_rate_update_root"
        action="action_currency_rate_update_configuration"
        sequence="10"/>

    <!-- Custom Currency Rate List View with Correct Values -->
    <record id="view_currency_rate_tree_custom" model="ir.ui.view">
        <field name="name">res.currency.rate.tree.custom</field>
        <field name="model">res.currency.rate</field>
        <field name="arch" type="xml">
            <list string="Currency Rates" default_order="name desc">
                <field name="name" string="Date"/>
                <field name="currency_id" string="Currency"/>
                <field name="correct_unit_per_inr" string="Unit per INR" widget="float" digits="[16,10]"/>
                <field name="correct_inr_per_unit" string="INR per Unit" widget="float" digits="[16,6]"/>
                <field name="rate" string="System Rate" widget="float" digits="[16,6]" optional="hide"/>
            </list>
        </field>
    </record>

    <!-- Custom Currency Rate Form View with Correct Values -->
    <record id="view_currency_rate_form_custom" model="ir.ui.view">
        <field name="name">res.currency.rate.form.custom</field>
        <field name="model">res.currency.rate</field>
        <field name="arch" type="xml">
            <form string="Currency Rate">
                <sheet>
                    <group>
                        <group>
                            <field name="name" string="Date"/>
                            <field name="currency_id" string="Currency"/>
                            <field name="company_id" string="Company" groups="base.group_multi_company"/>
                        </group>
                        <group>
                            <field name="correct_inr_per_unit" string="INR per Unit" digits="[16,6]"/>
                            <field name="correct_unit_per_inr" string="Unit per INR" digits="[16,10]"/>
                            <separator string="System Fields" colspan="2"/>
                            <field name="rate" string="System Rate" digits="[16,6]" readonly="1"/>
                        </group>
                    </group>
                    <group string="Help">
                        <div colspan="2">
                            <p><strong>How to use:</strong></p>
                            <ul>
                                <li><strong>INR per Unit:</strong> Enter how many INR equals 1 foreign currency unit (e.g., 85.94 for USD)</li>
                                <li><strong>Unit per INR:</strong> This will auto-calculate, or you can enter it directly</li>
                                <li><strong>System Rate:</strong> This is automatically updated and used by Odoo</li>
                            </ul>
                        </div>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Override Currency Rate Form View to Fix Display -->
    <record id="view_currency_rate_form_inherit" model="ir.ui.view">
        <field name="name">res.currency.rate.form.inherit</field>
        <field name="model">res.currency.rate</field>
        <field name="inherit_id" ref="base.view_currency_rate_form"/>
        <field name="arch" type="xml">
            <!-- Replace the rate field with proper label -->
            <xpath expr="//field[@name='rate']" position="attributes">
                <attribute name="string">Technical Rate (Raw)</attribute>
                <attribute name="digits">[16,6]</attribute>
            </xpath>

            <!-- Replace company_rate field with our corrected version -->
            <xpath expr="//field[@name='company_rate']" position="replace">
                <field name="rate" string="INR per Unit" digits="[16,6]"/>
            </xpath>

            <!-- Replace inverse_company_rate field with our corrected version -->
            <xpath expr="//field[@name='inverse_company_rate']" position="replace">
                <field name="inverse_rate" string="Unit per INR" digits="[16,10]"/>
            </xpath>
        </field>
    </record>

    <!-- Currency Rates Action -->
    <record id="action_currency_rates" model="ir.actions.act_window">
        <field name="name">Currency Rates</field>
        <field name="res_model">res.currency.rate</field>
        <field name="view_mode">list,form</field>
        <field name="view_ids" eval="[(5, 0, 0),
                                      (0, 0, {'view_mode': 'list', 'view_id': ref('view_currency_rate_tree_custom')}),
                                      (0, 0, {'view_mode': 'form', 'view_id': ref('view_currency_rate_form_custom')})]"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No currency rates found!
            </p>
            <p>
                Use the <strong>Correct INR per Unit</strong> and <strong>Correct Unit per INR</strong> fields to enter accurate rates.
                The system will automatically update the original Odoo rate field.
            </p>
        </field>
    </record>

    <!-- Currency Rates Menu Item -->
    <menuitem id="menu_currency_rates"
        name="Currency Rates"
        parent="menu_currency_rate_update_root"
        action="action_currency_rates"
        sequence="20"/>

    <!-- Currency Rate Updates Menu Item -->
    <menuitem id="menu_currency_rate_updates"
        name="Currency Rate Updates"
        parent="menu_currency_rate_update_root"
        action="action_currency_rate_update"
        sequence="30"/>

    <!-- Manual Rate Update Menu Item -->
    <menuitem id="menu_manual_rate_update"
        name="Manual Rate Update"
        parent="menu_currency_rate_update_root"
        action="action_manual_rate_update"
        sequence="40"/>
</odoo>