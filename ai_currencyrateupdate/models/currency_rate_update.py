import requests
import logging
from datetime import datetime
from odoo import models, api, fields

_logger = logging.getLogger(__name__)


class ResCurrencyRate(models.Model):
    _inherit = 'res.currency.rate'

    # Custom fields for correct rate calculation
    correct_inr_per_unit = fields.Float(
        string='Correct INR per Unit',
        digits=(16, 6),
        help='Correctly calculated rate: How many INR = 1 foreign currency unit'
    )

    correct_unit_per_inr = fields.Float(
        string='Correct Unit per INR',
        digits=(16, 10),
        help='Correctly calculated inverse rate: How many foreign currency units = 1 INR'
    )

    @api.onchange('correct_inr_per_unit')
    def _onchange_correct_inr_per_unit(self):
        """When correct rate is entered, update the original rate field and calculate inverse"""
        if self.correct_inr_per_unit and self.correct_inr_per_unit > 0:
            # Update the original Odoo rate field
            self.rate = self.correct_inr_per_unit
            # Calculate the inverse rate
            self.correct_unit_per_inr = 1.0 / self.correct_inr_per_unit

    @api.onchange('correct_unit_per_inr')
    def _onchange_correct_unit_per_inr(self):
        """When correct inverse rate is entered, update the original rate field"""
        if self.correct_unit_per_inr and self.correct_unit_per_inr > 0:
            # Calculate the direct rate
            self.correct_inr_per_unit = 1.0 / self.correct_unit_per_inr
            # Update the original Odoo rate field
            self.rate = self.correct_inr_per_unit

    # Keep the old inverse_rate field for compatibility
    inverse_rate = fields.Float(
        string='Unit per INR (Legacy)',
        compute='_compute_inverse_rate',
        digits=(16, 10),
        help='Legacy field - use Correct Unit per INR instead'
    )

    @api.depends('rate')
    def _compute_inverse_rate(self):
        for record in self:
            if record.rate and record.rate != 0:
                record.inverse_rate = 1.0 / record.rate
            else:
                record.inverse_rate = 0.0

    @api.onchange('correct_inr_per_unit')
    def _onchange_correct_inr_per_unit(self):
        """When correct_inr_per_unit is changed, update the main rate field and inverse"""
        if self.correct_inr_per_unit:
            self.rate = self.correct_inr_per_unit
            if self.correct_inr_per_unit != 0:
                self.correct_unit_per_inr = 1.0 / self.correct_inr_per_unit
            else:
                self.correct_unit_per_inr = 0.0

    @api.onchange('correct_unit_per_inr')
    def _onchange_correct_unit_per_inr(self):
        """When correct_unit_per_inr is changed, update the main rate field and inr_per_unit"""
        if self.correct_unit_per_inr and self.correct_unit_per_inr != 0:
            self.correct_inr_per_unit = 1.0 / self.correct_unit_per_inr
            self.rate = self.correct_inr_per_unit

    def write(self, vals):
        """Override write to ensure rate field is always synchronized with correct_inr_per_unit"""
        # If correct_inr_per_unit is being updated, also update the main rate field
        if 'correct_inr_per_unit' in vals and vals['correct_inr_per_unit']:
            vals['rate'] = vals['correct_inr_per_unit']
            if vals['correct_inr_per_unit'] != 0:
                vals['correct_unit_per_inr'] = 1.0 / vals['correct_inr_per_unit']

        # If correct_unit_per_inr is being updated, calculate and update rate
        elif 'correct_unit_per_inr' in vals and vals['correct_unit_per_inr'] and vals['correct_unit_per_inr'] != 0:
            vals['correct_inr_per_unit'] = 1.0 / vals['correct_unit_per_inr']
            vals['rate'] = vals['correct_inr_per_unit']

        return super().write(vals)





class CurrencyRateUpdate(models.Model):
    _name = 'currency.rate.update'
    _description = 'Currency Rate Update using OpenExchangeRates API'
    _order = 'create_date desc'

    name = fields.Char(string='Name', compute='_compute_name', store=True)
    success = fields.Boolean(string='Success', default=False)
    message = fields.Text(string='Message', readonly=True)
    
    @api.depends('create_date')
    def _compute_name(self):
        for record in self:
            if record.create_date:
                record.name = f"Update - {record.create_date.strftime('%Y-%m-%d %H:%M:%S')}"
            else:
                record.name = "New Update"

    def update_currency_rates(self):
        """
        Update currency rates from OpenExchangeRates API
        This method is called from button action or scheduled action
        """
        try:
            # Get API configuration from system parameters
            ICP = self.env['ir.config_parameter'].sudo()
            api_key = ICP.get_param('openexchangerates.api_key', False)
            
            if not api_key:
                message = "OpenExchangeRates API key is not configured"
                _logger.error(message)
                self.write({'success': False, 'message': message})
                return {'type': 'ir.actions.act_window_close'}
            
            # API endpoint
            url = f"https://openexchangerates.org/api/latest.json?app_id={api_key}"
            
            # Make API request
            response = requests.get(url)
            response.raise_for_status()
            
            # Parse response data
            data = response.json()
            base_currency_code = data.get('base')  # Usually USD from OpenExchangeRates
            rates = data.get('rates', {})
            timestamp = data.get('timestamp')
            date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
            
            if not rates:
                message = "No rates returned from OpenExchangeRates API"
                _logger.error(message)
                self.write({'success': False, 'message': message})
                return {'type': 'ir.actions.act_window_close'}
            
            # Get currency objects from Odoo
            currency_obj = self.env['res.currency']
            
            # Get base currency from Odoo (company currency)
            company = self.env.company
            company_currency = company.currency_id
            company_currency_code = company_currency.name
            
            # Get all active currencies in Odoo
            active_currencies = currency_obj.search([('active', '=', True)])

            # CRITICAL FIX: Odoo rate field for INR as company currency
            #
            # OpenExchangeRates API gives us rates with USD as base:
            # - rates['INR'] = 85.94 means 1 USD = 85.94 INR
            # - rates['EUR'] = 0.85 means 1 USD = 0.85 EUR
            #
            # Odoo's rate field stores: "how many company currency units = 1 foreign currency"
            # Since our company currency is INR:
            # - For USD: rate should be 85.94 (1 USD = 85.94 INR)
            # - For EUR: rate should be 85.94/0.85 = 101.11 (1 EUR = 101.11 INR)
            #
            # The display issue is that the view shows rates incorrectly.
            # We need to fix both calculation AND display.

            _logger.info(f"=== CURRENCY UPDATE DEBUG START ===")
            _logger.info(f"API Base Currency: {base_currency_code}")
            _logger.info(f"Company Currency: {company_currency_code}")
            _logger.info(f"API Response Keys: {list(rates.keys())}")

            # Get INR rate from API (how many INR per 1 USD)
            if company_currency_code != base_currency_code:
                if company_currency_code not in rates:
                    message = f"Company currency {company_currency_code} not found in OpenExchangeRates response"
                    _logger.error(message)
                    self.write({'success': False, 'message': message})
                    return {'type': 'ir.actions.act_window_close'}
                usd_to_inr_rate = rates[company_currency_code]  # e.g., 85.94 (1 USD = 85.94 INR)
                _logger.info(f"USD to INR rate from API: 1 USD = {usd_to_inr_rate} INR")
            else:
                usd_to_inr_rate = 1.0
                _logger.info(f"Company currency is same as API base currency: {company_currency_code}")

            updated_count = 0

            # Process all active currencies
            for currency in active_currencies:
                if currency.id == company_currency.id:
                    _logger.info(f"Skipping company currency: {currency.name}")
                    continue  # Skip company currency as it's the base (rate=1)

                currency_code = currency.name
                _logger.info(f"--- Processing currency: {currency_code} ---")

                # Calculate the correct Odoo rate for INR company currency
                # Odoo rate field = how many INR = 1 foreign currency unit
                if currency_code == base_currency_code:
                    # This is USD - skip if company is also USD
                    if company_currency_code == base_currency_code:
                        _logger.info(f"Skipping {currency_code} - same as company currency")
                        continue
                    else:
                        # Company is INR, we want: 1 USD = ? INR
                        # API gives us: 1 USD = 85.94 INR
                        # Store in Odoo: rate = 85.94
                        odoo_rate = usd_to_inr_rate
                        _logger.info(f"USD: 1 USD = {odoo_rate:.6f} INR")

                elif currency_code in rates:
                    # This is another currency (EUR, GBP, etc.)
                    # API gives us: 1 USD = 0.85 EUR, 1 USD = 85.94 INR
                    # We want: 1 EUR = ? INR
                    # Cross rate: 1 EUR = (1 USD / 0.85 EUR) * (85.94 INR / 1 USD) = 85.94/0.85 INR
                    api_rate = rates[currency_code]  # e.g., 0.85 for EUR

                    if company_currency_code == base_currency_code:
                        # Company is USD
                        # API: 1 USD = 0.85 EUR, so 1 EUR = 1/0.85 USD
                        odoo_rate = 1.0 / api_rate
                        _logger.info(f"{currency_code}: 1 {currency_code} = {odoo_rate:.6f} USD")
                    else:
                        # Company is INR
                        # 1 EUR = (INR per USD) / (EUR per USD) = 85.94 / 0.85 INR
                        odoo_rate = usd_to_inr_rate / api_rate
                        _logger.info(f"{currency_code}: 1 {currency_code} = {odoo_rate:.6f} INR")
                else:
                    _logger.warning(f"Currency {currency_code} not found in OpenExchangeRates response")
                    continue

                # Store the calculated rate
                existing_rate = self.env['res.currency.rate'].search([
                    ('currency_id', '=', currency.id),
                    ('name', '=', date)
                ], limit=1)

                _logger.info(f"Storing rate for {currency_code}: {odoo_rate:.6f}")
                _logger.info(f"Date: {date}")

                # Calculate the inverse rate for our custom fields
                inverse_rate = 1.0 / odoo_rate if odoo_rate > 0 else 0.0

                # CRITICAL FIX: Store the correct rate in the main 'rate' field
                # The 'rate' field in Odoo should store: how many company currency units = 1 foreign currency
                # For INR company: rate should be the INR amount per 1 foreign currency unit

                if existing_rate:
                    old_rate = existing_rate.rate
                    existing_rate.write({
                        'rate': odoo_rate,  # This is the correct INR per foreign currency unit
                        'correct_inr_per_unit': odoo_rate,
                        'correct_unit_per_inr': inverse_rate
                    })
                    updated_count += 1
                    _logger.info(f"UPDATED {currency_code}: OLD={old_rate:.6f} -> NEW={odoo_rate:.6f}")
                    _logger.info(f"Meaning: 1 {currency_code} = {odoo_rate:.6f} {company_currency_code}")
                    _logger.info(f"Inverse: 1 {company_currency_code} = {inverse_rate:.10f} {currency_code}")
                else:
                    new_rate = self.env['res.currency.rate'].create({
                        'currency_id': currency.id,
                        'rate': odoo_rate,  # This is the correct INR per foreign currency unit
                        'correct_inr_per_unit': odoo_rate,
                        'correct_unit_per_inr': inverse_rate,
                        'name': date
                    })
                    updated_count += 1
                    _logger.info(f"CREATED {currency_code}: rate={odoo_rate:.6f} (ID: {new_rate.id})")
                    _logger.info(f"Meaning: 1 {currency_code} = {odoo_rate:.6f} {company_currency_code}")
                    _logger.info(f"Inverse: 1 {company_currency_code} = {inverse_rate:.10f} {currency_code}")

                # Verify what was actually stored
                verify_rate = self.env['res.currency.rate'].search([
                    ('currency_id', '=', currency.id),
                    ('name', '=', date)
                ], limit=1)
                if verify_rate:
                    _logger.info(f"VERIFICATION: {currency_code} rate in DB = {verify_rate.rate:.6f}")
                    _logger.info(f"VERIFICATION: This means 1 {currency_code} = {verify_rate.rate:.6f} {company_currency_code}")
                else:
                    _logger.error(f"VERIFICATION FAILED: Could not find {currency_code} rate in DB")
            
            message = f"Successfully updated {updated_count} currency rates"
            _logger.info(f"=== CURRENCY UPDATE DEBUG END ===")
            _logger.info(message)
            self.write({'success': True, 'message': message})
            return {'type': 'ir.actions.act_window_close'}

        except requests.exceptions.RequestException as e:
            message = f"Error fetching currency rates: {str(e)}"
            _logger.error(message)
            self.write({'success': False, 'message': message})
            return {'type': 'ir.actions.act_window_close'}
        except Exception as e:
            message = f"Unexpected error: {str(e)}"
            _logger.error(message)
            self.write({'success': False, 'message': message})
            return {'type': 'ir.actions.act_window_close'}

    @api.model
    def cron_update_currency_rates(self):
        """
        Method to be called by scheduled action (cron job)
        Creates a new record and updates currency rates
        """
        record = self.create({})
        record.update_currency_rates()

    def action_fix_existing_rates(self):
        """Button action to fix existing rates"""
        _logger.info("=== FIXING EXISTING CURRENCY RATES ===")

        # DIAGNOSTIC: Check what currency rates exist
        all_rates = self.env['res.currency.rate'].search([])
        _logger.info(f"TOTAL CURRENCY RATES IN SYSTEM: {len(all_rates)}")

        rates_with_correct = self.env['res.currency.rate'].search([
            ('correct_inr_per_unit', '>', 0)
        ])
        _logger.info(f"RATES WITH correct_inr_per_unit: {len(rates_with_correct)}")

        # Show details of recent rates
        recent_rates = self.env['res.currency.rate'].search([
            ('name', '>=', '2025-07-01')
        ], order='name desc')

        for rate in recent_rates[:10]:  # Show first 10
            _logger.info(f"RATE: {rate.currency_id.name} | Date: {rate.name} | Rate: {rate.rate:.6f} | Correct: {rate.correct_inr_per_unit:.6f}")

        # Find all currency rates that have correct_inr_per_unit values
        currency_rates = self.env['res.currency.rate'].search([
            ('correct_inr_per_unit', '>', 0)
        ])

        fixed_count = 0
        for rate_record in currency_rates:
            if rate_record.correct_inr_per_unit > 0:
                old_rate = rate_record.rate
                # Only fix if the rate is different from correct_inr_per_unit
                if abs(old_rate - rate_record.correct_inr_per_unit) > 0.000001:  # Small tolerance for float comparison
                    rate_record.write({
                        'rate': rate_record.correct_inr_per_unit,
                        'correct_unit_per_inr': 1.0 / rate_record.correct_inr_per_unit if rate_record.correct_inr_per_unit != 0 else 0.0
                    })
                    fixed_count += 1
                    _logger.info(f"FIXED {rate_record.currency_id.name}: {old_rate:.6f} -> {rate_record.correct_inr_per_unit:.6f}")
                else:
                    _logger.info(f"SKIPPED {rate_record.currency_id.name}: already correct ({old_rate:.6f})")

        # MANUAL FIX FOR USD: If USD rate is wrong, fix it directly
        usd_currency = self.env['res.currency'].search([('name', '=', 'USD')], limit=1)
        if usd_currency:
            latest_usd_rate = self.env['res.currency.rate'].search([
                ('currency_id', '=', usd_currency.id)
            ], order='name desc', limit=1)

            if latest_usd_rate:
                current_usd_rate = latest_usd_rate.rate
                _logger.info(f"CURRENT USD RATE: {current_usd_rate:.6f}")

                # If USD rate is around 0.69 (inverted), fix it to ~85.76
                if 0.5 < current_usd_rate < 2.0:
                    correct_usd_rate = 85.76  # Approximate correct rate
                    _logger.info(f"FIXING USD RATE: {current_usd_rate:.6f} -> {correct_usd_rate:.6f}")

                    latest_usd_rate.write({
                        'rate': correct_usd_rate,
                        'correct_inr_per_unit': correct_usd_rate,
                        'correct_unit_per_inr': 1.0 / correct_usd_rate
                    })
                    fixed_count += 1

        # CRITICAL: Force refresh the main res.currency model cache
        # This ensures ecommerce applications pick up the new rates
        _logger.info("=== FORCING CURRENCY MODEL REFRESH ===")

        # Clear currency rate cache
        self.env['res.currency'].invalidate_model(['rate'])
        self.env['res.currency.rate'].invalidate_model(['rate'])

        # Force recalculation of currency rates by accessing them
        active_currencies = self.env['res.currency'].search([('active', '=', True)])
        company = self.env.company
        today = fields.Date.today()

        for currency in active_currencies:
            if currency.name != 'INR':  # Skip base currency
                try:
                    # Force Odoo to recalculate the conversion rate
                    rate = currency._get_conversion_rate(currency, company.currency_id, company, today)
                    _logger.info(f"REFRESHED {currency.name} conversion rate: {rate:.6f}")
                except Exception as e:
                    _logger.error(f"Error refreshing {currency.name}: {e}")

        _logger.info("=== CURRENCY MODEL REFRESH COMPLETE ===")
        _logger.info(f"=== FIXED {fixed_count} CURRENCY RATES ===")

        message = f"Fixed {fixed_count} currency rates and refreshed currency model cache. Ecommerce should now use updated rates."
        self.write({'success': True, 'message': message})
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Success',
                'message': message,
                'type': 'success',
                'sticky': False,
            }
        }

class ResCurrency(models.Model):
    _inherit = 'res.currency'

    @api.model
    def _get_conversion_rate(self, from_currency, to_currency, company, date):
        """Override to fix currency conversion issues with INR base currency"""

        # If same currency, return 1.0
        if from_currency == to_currency:
            return 1.0

        # Get rates for both currencies (INR per unit)
        def get_currency_rate(currency):
            if currency.name == 'INR':
                return 1.0  # Base currency

            rate_record = self.env['res.currency.rate'].search([
                ('currency_id', '=', currency.id),
                ('name', '<=', date),
                ('company_id', 'in', [company.id, False])
            ], order='name desc', limit=1)

            if rate_record and rate_record.rate > 0:
                return rate_record.rate  # INR per foreign currency unit
            return 1.0

        from_rate = get_currency_rate(from_currency)  # INR per from_currency unit
        to_rate = get_currency_rate(to_currency)      # INR per to_currency unit

        # Calculate cross rate: from_currency -> INR -> to_currency
        # 1 from_currency = from_rate INR
        # 1 to_currency = to_rate INR
        # Therefore: 1 from_currency = (from_rate / to_rate) to_currency

        if to_rate > 0:
            conversion_rate = from_rate / to_rate
            _logger.info(f"CUSTOM CONVERSION {from_currency.name} -> {to_currency.name}: {conversion_rate:.6f} (via INR: {from_rate:.6f} / {to_rate:.6f})")
            return conversion_rate

        # Fallback to standard method if something goes wrong
        _logger.warning(f"FALLBACK CONVERSION {from_currency.name} -> {to_currency.name}")
        return super()._get_conversion_rate(from_currency, to_currency, company, date)


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    openexchangerates_api_key = fields.Char(
        string='OpenExchangeRates API Key',
        config_parameter='openexchangerates.api_key',
        help='API Key for OpenExchangeRates service'
    )