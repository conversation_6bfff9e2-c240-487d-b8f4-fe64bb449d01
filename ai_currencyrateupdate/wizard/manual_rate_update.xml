<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_manual_rate_update_form" model="ir.ui.view">
        <field name="name">manual.rate.update.form</field>
        <field name="model">manual.rate.update</field>
        <field name="arch" type="xml">
            <form string="Manual Rate Update">
                <group>
                    <group>
                        <field name="currency_id"/>
                        <field name="date"/>
                    </group>
                    <group>
                        <field name="correct_inr_per_unit"/>
                        <field name="correct_unit_per_inr" readonly="1"/>
                    </group>
                </group>
                <group string="Help">
                    <div colspan="2">
                        <p><strong>How to use:</strong></p>
                        <ul>
                            <li>Select the currency you want to update</li>
                            <li>Choose the date for the rate</li>
                            <li>Enter how many INR equals 1 unit of the foreign currency</li>
                            <li>The inverse rate will be calculated automatically</li>
                        </ul>
                        <p><strong>Example:</strong> If 1 USD = 85.94 INR, enter 85.94 in "INR per Unit"</p>
                    </div>
                </group>
                <footer>
                    <button name="action_update_rate" string="Update Rate" type="object" class="oe_highlight"/>
                    <button string="Cancel" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_manual_rate_update" model="ir.actions.act_window">
        <field name="name">Manual Rate Update</field>
        <field name="res_model">manual.rate.update</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
