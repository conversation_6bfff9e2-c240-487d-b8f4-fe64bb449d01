from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class ManualRateUpdate(models.TransientModel):
    _name = 'manual.rate.update'
    _description = 'Manual Currency Rate Update'

    currency_id = fields.Many2one('res.currency', string='Currency', required=True)
    date = fields.Date(string='Date', required=True, default=fields.Date.today)
    correct_inr_per_unit = fields.Float(
        string='INR per Unit',
        digits=(16, 6),
        required=True,
        help='How many INR equals 1 unit of the foreign currency'
    )
    correct_unit_per_inr = fields.Float(
        string='Unit per INR',
        digits=(16, 10),
        compute='_compute_unit_per_inr',
        help='How many units of foreign currency equals 1 INR'
    )

    @api.depends('correct_inr_per_unit')
    def _compute_unit_per_inr(self):
        for record in self:
            if record.correct_inr_per_unit and record.correct_inr_per_unit > 0:
                record.correct_unit_per_inr = 1.0 / record.correct_inr_per_unit
            else:
                record.correct_unit_per_inr = 0.0

    def action_update_rate(self):
        """Update the currency rate with the correct values"""
        self.ensure_one()
        
        if not self.correct_inr_per_unit or self.correct_inr_per_unit <= 0:
            raise UserError(_("Please enter a valid rate greater than 0"))
        
        # Check if rate already exists for this date
        existing_rate = self.env['res.currency.rate'].search([
            ('currency_id', '=', self.currency_id.id),
            ('name', '=', self.date)
        ], limit=1)
        
        rate_data = {
            'currency_id': self.currency_id.id,
            'name': self.date,
            'rate': self.correct_inr_per_unit,
            'correct_inr_per_unit': self.correct_inr_per_unit,
            'correct_unit_per_inr': self.correct_unit_per_inr,
        }
        
        if existing_rate:
            existing_rate.write(rate_data)
            message = _("Updated rate for %s on %s: 1 %s = %.6f INR") % (
                self.currency_id.name, self.date, self.currency_id.name, self.correct_inr_per_unit
            )
        else:
            self.env['res.currency.rate'].create(rate_data)
            message = _("Created rate for %s on %s: 1 %s = %.6f INR") % (
                self.currency_id.name, self.date, self.currency_id.name, self.correct_inr_per_unit
            )
        
        _logger.info(message)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _("Rate Updated"),
                'message': message,
                'type': 'success',
            }
        }
