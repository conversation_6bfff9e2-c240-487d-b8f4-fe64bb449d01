#!/usr/bin/env python3
"""
Check what pages are actually available on the website
"""

import asyncio
import xmlrpc.client
from playwright.async_api import async_playwright

# Configuration
WEBSITE_URL = "http://localhost:8069"
DATABASE_CONFIG = {
    'url': 'http://localhost:8069',
    'db': 'profectusaccounts.com',
    'username': 'demo',
    'password': 'demo'
}

def connect_to_odoo():
    """Connect to Odoo database"""
    try:
        common = xmlrpc.client.ServerProxy(f'{DATABASE_CONFIG["url"]}/xmlrpc/2/common')
        uid = common.authenticate(
            DATABASE_CONFIG['db'], 
            DATABASE_CONFIG['username'], 
            DATABASE_CONFIG['password'], 
            {}
        )
        
        if not uid:
            return None, None
        
        models = xmlrpc.client.ServerProxy(f'{DATABASE_CONFIG["url"]}/xmlrpc/2/object')
        return models, uid
        
    except Exception as e:
        print(f"❌ Odoo connection error: {e}")
        return None, None

def check_website_pages():
    """Check what pages are available in the database"""
    try:
        print("🔍 Checking Available Website Pages...")
        print("-" * 50)
        
        models, uid = connect_to_odoo()
        if not models:
            return []
        
        # Get all website pages
        pages = models.execute_kw(
            DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
            'website.page', 'search_read',
            [[]], 
            {'fields': ['name', 'url', 'website_published', 'website_id']}
        )
        
        print(f"📋 Found {len(pages)} website pages:")
        
        published_pages = []
        contact_pages = []
        
        for page in pages:
            status = "✅ Published" if page['website_published'] else "❌ Unpublished"
            website = page['website_id'][1] if page['website_id'] else 'No website'
            print(f"   • {page['name']} - {page['url']} - {status} - {website}")
            
            if page['website_published']:
                published_pages.append(page)
            
            if 'contact' in page['url'].lower() or 'contact' in page['name'].lower():
                contact_pages.append(page)
        
        print(f"\n📊 Summary:")
        print(f"   • Total pages: {len(pages)}")
        print(f"   • Published pages: {len(published_pages)}")
        print(f"   • Contact-related pages: {len(contact_pages)}")
        
        if contact_pages:
            print(f"\n📞 Contact-related pages:")
            for page in contact_pages:
                status = "✅ Published" if page['website_published'] else "❌ Unpublished"
                print(f"   • {page['name']} - {page['url']} - {status}")
        
        return published_pages
        
    except Exception as e:
        print(f"❌ Error checking pages: {e}")
        return []

async def test_page_accessibility(pages):
    """Test accessibility of published pages"""
    try:
        print(f"\n🌐 Testing Page Accessibility...")
        print("-" * 50)
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()
            
            accessible_pages = []
            
            try:
                for web_page in pages[:10]:  # Test first 10 pages
                    url = f"{WEBSITE_URL}{web_page['url']}"
                    try:
                        print(f"   Testing: {url}")
                        response = await page.goto(url, timeout=5000)
                        print(f"      Status: {response.status}")
                        
                        if response.status == 200:
                            accessible_pages.append(web_page)
                            
                            # Check for forms on this page
                            forms = await page.query_selector_all('form')
                            if forms:
                                print(f"      ✅ Found {len(forms)} form(s)")
                                for i, form in enumerate(forms):
                                    action = await form.get_attribute('action')
                                    model = await form.get_attribute('data-model_name')
                                    print(f"         Form {i+1}: action={action}, model={model}")
                            else:
                                print(f"      ℹ️ No forms found")
                        else:
                            print(f"      ❌ Not accessible")
                            
                    except Exception as e:
                        print(f"      ❌ Error: {str(e)[:50]}...")
                
                print(f"\n📊 Accessibility Summary:")
                print(f"   • Tested: {min(len(pages), 10)} pages")
                print(f"   • Accessible: {len(accessible_pages)} pages")
                
                return accessible_pages
                
            finally:
                await browser.close()
        
    except Exception as e:
        print(f"❌ Error testing accessibility: {e}")
        return []

def check_website_configuration():
    """Check website configuration"""
    try:
        print(f"\n⚙️ Checking Website Configuration...")
        print("-" * 50)
        
        models, uid = connect_to_odoo()
        if not models:
            return
        
        # Check websites
        websites = models.execute_kw(
            DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
            'website', 'search_read',
            [[]], 
            {'fields': ['name', 'domain', 'default_lang_id']}
        )
        
        print(f"🌐 Found {len(websites)} website(s):")
        for site in websites:
            domain = site['domain'] or 'No domain'
            lang = site['default_lang_id'][1] if site['default_lang_id'] else 'No language'
            print(f"   • {site['name']} - Domain: {domain} - Language: {lang}")
        
        # Check menus
        menus = models.execute_kw(
            DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
            'website.menu', 'search_read',
            [[]], 
            {'fields': ['name', 'url', 'website_id'], 'limit': 20}
        )
        
        print(f"\n📋 Found {len(menus)} menu items (showing first 20):")
        contact_menus = []
        for menu in menus:
            website = menu['website_id'][1] if menu['website_id'] else 'No website'
            print(f"   • {menu['name']} - {menu['url']} - {website}")
            
            if 'contact' in menu['url'].lower() or 'contact' in menu['name'].lower():
                contact_menus.append(menu)
        
        if contact_menus:
            print(f"\n📞 Contact-related menus:")
            for menu in contact_menus:
                website = menu['website_id'][1] if menu['website_id'] else 'No website'
                print(f"   • {menu['name']} - {menu['url']} - {website}")
        
        return websites, menus
        
    except Exception as e:
        print(f"❌ Error checking configuration: {e}")
        return [], []

async def find_working_contact_form():
    """Try to find any working contact form"""
    try:
        print(f"\n🔍 Searching for Working Contact Forms...")
        print("-" * 50)
        
        # Common contact page URLs to try
        contact_urls = [
            '/contact',
            '/contactus',
            '/contact-us',
            '/contact_us',
            '/page/contact',
            '/page/contactus',
            '/web/contact',
            '/website/contact'
        ]
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()
            
            working_forms = []
            
            try:
                for contact_url in contact_urls:
                    url = f"{WEBSITE_URL}{contact_url}"
                    try:
                        print(f"   Testing: {url}")
                        response = await page.goto(url, timeout=5000)
                        
                        if response.status == 200:
                            print(f"      ✅ Accessible (Status: {response.status})")
                            
                            # Check for forms
                            forms = await page.query_selector_all('form')
                            if forms:
                                print(f"      🎯 Found {len(forms)} form(s)!")
                                
                                for i, form in enumerate(forms):
                                    action = await form.get_attribute('action')
                                    method = await form.get_attribute('method')
                                    model = await form.get_attribute('data-model_name')
                                    form_id = await form.get_attribute('id')
                                    
                                    print(f"         Form {i+1}:")
                                    print(f"            ID: {form_id}")
                                    print(f"            Action: {action}")
                                    print(f"            Method: {method}")
                                    print(f"            Model: {model}")
                                    
                                    # Get form fields
                                    inputs = await form.query_selector_all('input, textarea, select')
                                    field_names = []
                                    for input_elem in inputs:
                                        name = await input_elem.get_attribute('name')
                                        if name and name not in ['website_form_signature']:
                                            field_names.append(name)
                                    
                                    print(f"            Fields: {field_names}")
                                    
                                    working_forms.append({
                                        'url': contact_url,
                                        'action': action,
                                        'model': model,
                                        'fields': field_names
                                    })
                                
                                # Take screenshot
                                screenshot_name = f"working_form_{contact_url.replace('/', '_')}.png"
                                await page.screenshot(path=screenshot_name)
                                print(f"      📸 Screenshot saved: {screenshot_name}")
                            else:
                                print(f"      ℹ️ No forms found")
                        else:
                            print(f"      ❌ Not accessible (Status: {response.status})")
                            
                    except Exception as e:
                        print(f"      ❌ Error: {str(e)[:50]}...")
                
                return working_forms
                
            finally:
                await browser.close()
        
    except Exception as e:
        print(f"❌ Error searching for forms: {e}")
        return []

async def main():
    """Main function"""
    print("🔍 Checking Available Pages and Forms")
    print("=" * 60)
    
    # Step 1: Check database pages
    pages = check_website_pages()
    
    # Step 2: Test page accessibility
    if pages:
        accessible_pages = await test_page_accessibility(pages)
    else:
        accessible_pages = []
    
    # Step 3: Check website configuration
    websites, menus = check_website_configuration()
    
    # Step 4: Search for working contact forms
    working_forms = await find_working_contact_form()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FINAL SUMMARY")
    print("=" * 60)
    
    print(f"📋 Database Pages: {len(pages)} total")
    print(f"🌐 Accessible Pages: {len(accessible_pages)}")
    print(f"⚙️ Websites Configured: {len(websites)}")
    print(f"📞 Working Contact Forms: {len(working_forms)}")
    
    if working_forms:
        print(f"\n🎯 WORKING CONTACT FORMS FOUND:")
        for form in working_forms:
            print(f"   • URL: {form['url']}")
            print(f"     Action: {form['action']}")
            print(f"     Model: {form['model']}")
            print(f"     Fields: {form['fields']}")
    else:
        print(f"\n❌ NO WORKING CONTACT FORMS FOUND")
        print(f"   Possible issues:")
        print(f"   • Contact pages are not published")
        print(f"   • URLs have changed")
        print(f"   • Website configuration issues")
    
    print(f"\n🎯 RECOMMENDATIONS:")
    if working_forms:
        print(f"   1. Use the working contact form URLs found above")
        print(f"   2. Test form submission with the identified fields")
        print(f"   3. Update your tests to use the correct URLs")
    else:
        print(f"   1. Check if contact pages are published in Odoo")
        print(f"   2. Verify website configuration")
        print(f"   3. Check if contact module is installed")
        print(f"   4. Create a new contact page if needed")

if __name__ == "__main__":
    asyncio.run(main())
