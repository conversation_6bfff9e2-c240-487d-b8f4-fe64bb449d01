# Quick Implementation: Smart Buttons for Partner Forms

## Overview
Add smart buttons to partner forms to show counts and provide quick access to related CA records.

## Implementation Steps

### 1. Enhance imca_groups Model

Add to `imca_groups/models/models.py`:

```python
class ResPartner(models.Model):
    _inherit = 'res.partner'

    x_group_id = fields.Many2one('x_groups', string="Group Ref", store=True)
    
    # Count fields for smart buttons
    service_count = fields.Integer(compute='_compute_ca_counts', string='Services')
    document_count = fields.Integer(compute='_compute_ca_counts', string='Documents')
    credential_count = fields.Integer(compute='_compute_ca_counts', string='Credentials')
    dsc_count = fields.Integer(compute='_compute_ca_counts', string='DSCs')
    
    def _compute_ca_counts(self):
        for partner in self:
            partner.service_count = len(partner.x_service_ids) if hasattr(partner, 'x_service_ids') else 0
            partner.document_count = len(partner.x_client_document_ids) if hasattr(partner, 'x_client_document_ids') else 0
            partner.credential_count = len(partner.x_credential_ids) if hasattr(partner, 'x_credential_ids') else 0
            partner.dsc_count = len(partner.x_dsc_ids) if hasattr(partner, 'x_dsc_ids') else 0
    
    def action_view_services(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Services',
            'res_model': 'x_services',
            'view_mode': 'list,form',
            'domain': [('x_partner_id', '=', self.id)],
            'context': {'default_x_partner_id': self.id}
        }
    
    def action_view_documents(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Documents',
            'res_model': 'x_client_documents',
            'view_mode': 'list,form',
            'domain': [('x_client', '=', self.id)],
            'context': {'default_x_client': self.id}
        }
    
    def action_view_credentials(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Credentials',
            'res_model': 'x_credentials',
            'view_mode': 'list,form',
            'domain': [('x_partner_id', '=', self.id)],
            'context': {'default_x_partner_id': self.id}
        }
    
    def action_view_dsc(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'DSC Management',
            'res_model': 'x_dsc',
            'view_mode': 'list,form',
            'domain': [('x_manager', '=', self.id)],
            'context': {'default_x_manager': self.id}
        }
```

### 2. Add Smart Buttons to Partner Form

Add to `imca_groups/views/groups_views.xml`:

```xml
<!-- Enhanced Partner Form View with Smart Buttons -->
<record id="view_partner_form_ca_smart_buttons" model="ir.ui.view">
    <field name="name">res.partner.form.ca.smart.buttons</field>
    <field name="model">res.partner</field>
    <field name="inherit_id" ref="base.view_partner_form"/>
    <field name="arch" type="xml">
        <div name="button_box" position="inside">
            <button type="object" name="action_view_services" class="oe_stat_button" icon="fa-cogs" 
                    attrs="{'invisible': [('service_count', '=', 0)]}">
                <field name="service_count" widget="statinfo" string="Services"/>
            </button>
            <button type="object" name="action_view_documents" class="oe_stat_button" icon="fa-file-text-o"
                    attrs="{'invisible': [('document_count', '=', 0)]}">
                <field name="document_count" widget="statinfo" string="Documents"/>
            </button>
            <button type="object" name="action_view_credentials" class="oe_stat_button" icon="fa-key"
                    attrs="{'invisible': [('credential_count', '=', 0)]}">
                <field name="credential_count" widget="statinfo" string="Credentials"/>
            </button>
            <button type="object" name="action_view_dsc" class="oe_stat_button" icon="fa-certificate"
                    attrs="{'invisible': [('dsc_count', '=', 0)]}">
                <field name="dsc_count" widget="statinfo" string="DSCs"/>
            </button>
        </div>
    </field>
</record>
```

### 3. Enhanced List Views with Status Colors

Add to each module's list views:

```xml
<!-- Example for Services -->
<record id="view_x_services_list_enhanced" model="ir.ui.view">
    <field name="name">x_services.list.enhanced</field>
    <field name="model">x_services</field>
    <field name="inherit_id" ref="imca_services.view_x_services_list"/>
    <field name="arch" type="xml">
        <list position="attributes">
            <attribute name="decoration-success">x_nextcall and x_nextcall &gt; context_today()</attribute>
            <attribute name="decoration-warning">x_nextcall and x_nextcall &lt;= context_today() + datetime.timedelta(days=7)</attribute>
            <attribute name="decoration-danger">x_nextcall and x_nextcall &lt; context_today()</attribute>
        </list>
    </field>
</record>
```

### 4. Quick Filters for Better Navigation

Add to search views:

```xml
<!-- Enhanced search for Services -->
<record id="view_x_services_search_enhanced" model="ir.ui.view">
    <field name="name">x_services.search.enhanced</field>
    <field name="model">x_services</field>
    <field name="inherit_id" ref="imca_services.view_x_services_search"/>
    <field name="arch" type="xml">
        <search position="inside">
            <separator/>
            <filter string="Due Today" name="due_today" 
                    domain="[('x_nextcall', '&lt;=', context_today())]"/>
            <filter string="Due This Week" name="due_week" 
                    domain="[('x_nextcall', '&lt;=', context_today() + datetime.timedelta(days=7))]"/>
            <filter string="Active Services" name="active" 
                    domain="[('x_nextcall', '!=', False)]"/>
        </search>
    </field>
</record>
```

## Benefits of This Implementation

1. **Immediate Visual Impact**: Users can see related record counts at a glance
2. **Quick Navigation**: One-click access to related records
3. **Better UX**: Color-coded lists show status at a glance
4. **Improved Efficiency**: Faster access to client information

## Installation Notes

1. Update the modules in this order:
   - imca_groups (contains the smart button logic)
   - imca_services
   - imca_client_documents
   - imca_crednetials_manager
   - imca_dsc_management

2. After installation, the smart buttons will appear on partner forms
3. Buttons are hidden when count is zero to keep the interface clean

## Testing Checklist

- [ ] Smart buttons appear on partner forms
- [ ] Clicking buttons opens correct filtered views
- [ ] Counts update when records are added/removed
- [ ] Color coding works in list views
- [ ] Quick filters function properly
- [ ] No errors in server logs

This implementation provides immediate value with minimal development effort!
