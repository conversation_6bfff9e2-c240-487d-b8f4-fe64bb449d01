#!/usr/bin/env python3
"""
Test contact forms with proper domain configuration
"""

import asyncio
import xmlrpc.client
from playwright.async_api import async_playwright
from datetime import datetime

# Configuration
WEBSITE_URL = "http://localhost:8069"
DOMAIN = "profectusaccounts.com"
DATABASE_CONFIG = {
    'url': 'http://localhost:8069',
    'db': 'profectusaccounts.com',
    'username': 'demo',
    'password': 'demo'
}

def connect_to_odoo():
    """Connect to Odoo database"""
    try:
        common = xmlrpc.client.ServerProxy(f'{DATABASE_CONFIG["url"]}/xmlrpc/2/common')
        uid = common.authenticate(
            DATABASE_CONFIG['db'], 
            DATABASE_CONFIG['username'], 
            DATABASE_CONFIG['password'], 
            {}
        )
        
        if not uid:
            return None, None
        
        models = xmlrpc.client.ServerProxy(f'{DATABASE_CONFIG["url"]}/xmlrpc/2/object')
        return models, uid
        
    except Exception as e:
        print(f"❌ Odoo connection error: {e}")
        return None, None

async def test_with_host_header():
    """Test accessing pages with proper Host header"""
    try:
        print("🌐 Testing with Host Header...")
        print("-" * 50)
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(
                extra_http_headers={
                    'Host': DOMAIN
                }
            )
            page = await context.new_page()
            
            try:
                # Test contact page with Host header
                contact_url = f"{WEBSITE_URL}/contactus"
                print(f"   Testing: {contact_url} with Host: {DOMAIN}")
                
                response = await page.goto(contact_url, timeout=10000)
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    print("   ✅ Contact page accessible with Host header!")
                    
                    # Take screenshot
                    await page.screenshot(path='contact_with_host_header.png')
                    print("   📸 Screenshot saved: contact_with_host_header.png")
                    
                    # Check for forms
                    forms = await page.query_selector_all('form')
                    print(f"   📋 Found {len(forms)} form(s)")
                    
                    if forms:
                        for i, form in enumerate(forms):
                            action = await form.get_attribute('action')
                            method = await form.get_attribute('method')
                            model = await form.get_attribute('data-model_name')
                            form_id = await form.get_attribute('id')
                            
                            print(f"      Form {i+1}:")
                            print(f"         ID: {form_id}")
                            print(f"         Action: {action}")
                            print(f"         Method: {method}")
                            print(f"         Model: {model}")
                            
                            # Get form fields
                            inputs = await form.query_selector_all('input, textarea, select')
                            field_names = []
                            for input_elem in inputs:
                                name = await input_elem.get_attribute('name')
                                input_type = await input_elem.get_attribute('type')
                                if name and input_type != 'hidden':
                                    field_names.append(name)
                            
                            print(f"         Fields: {field_names}")
                        
                        return True
                    else:
                        print("   ℹ️ No forms found")
                        return False
                else:
                    print(f"   ❌ Still not accessible (Status: {response.status})")
                    return False
                
            finally:
                await browser.close()
        
    except Exception as e:
        print(f"❌ Error testing with host header: {e}")
        return False

async def test_form_submission_with_domain():
    """Test form submission with proper domain"""
    try:
        print("\n🧪 Testing Form Submission with Domain...")
        print("-" * 50)
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(
                extra_http_headers={
                    'Host': DOMAIN
                }
            )
            page = await context.new_page()
            
            try:
                # Navigate to contact page
                contact_url = f"{WEBSITE_URL}/contactus"
                response = await page.goto(contact_url, timeout=10000)
                
                if response.status != 200:
                    print(f"   ❌ Cannot access contact page (Status: {response.status})")
                    return False
                
                print("   ✅ Contact page loaded")
                
                # Look for contact form
                form = await page.query_selector('form[data-model_name], form#contactus_form, form[action*="/website/form/"]')
                
                if not form:
                    print("   ❌ No contact form found")
                    return False
                
                print("   ✅ Contact form found")
                
                # Generate test data
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                test_data = {
                    'name': f'Domain Test {timestamp}',
                    'email': f'domain_test_{timestamp}@example.com',
                    'phone': '******-777-8888',
                    'company': f'Domain Test Co {timestamp}',
                    'subject': f'Domain Test {timestamp}',
                    'message': f'This is a test message with proper domain header at {datetime.now()}'
                }
                
                print("   📝 Filling form with test data...")
                
                # Fill form fields
                field_selectors = {
                    'name': ['input[name="name"]', '#contact1'],
                    'email': ['input[name="email"]', 'input[name="email_from"]', '#contact3'],
                    'phone': ['input[name="phone"]', '#contact2'],
                    'company': ['input[name="parent_name"]', 'input[name="company"]', '#contact4'],
                    'subject': ['input[name="function"]', 'input[name="subject"]', '#contact5'],
                    'message': ['textarea[name="comment"]', 'textarea[name="description"]', 'textarea[name="Message"]', '#contact6']
                }
                
                filled_fields = 0
                for field_name, selectors in field_selectors.items():
                    for selector in selectors:
                        try:
                            element = await page.query_selector(selector)
                            if element:
                                await element.fill(test_data[field_name])
                                print(f"      ✅ {field_name}: filled")
                                filled_fields += 1
                                break
                        except:
                            continue
                
                print(f"   📊 Filled {filled_fields}/{len(field_selectors)} fields")
                
                if filled_fields < 3:
                    print("   ⚠️ Not enough fields filled to submit")
                    return False
                
                # Take screenshot before submission
                await page.screenshot(path='domain_form_before_submit.png')
                print("   📸 Screenshot saved: domain_form_before_submit.png")
                
                # Submit form
                submit_selectors = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    '.s_website_form_send',
                    'a[role="button"].btn'
                ]
                
                submitted = False
                for selector in submit_selectors:
                    try:
                        submit_btn = await page.query_selector(selector)
                        if submit_btn:
                            await submit_btn.click()
                            print(f"   🚀 Form submitted using: {selector}")
                            submitted = True
                            break
                    except:
                        continue
                
                if not submitted:
                    print("   ❌ Could not find submit button")
                    return False
                
                # Wait for response
                await page.wait_for_timeout(5000)
                
                # Take screenshot after submission
                await page.screenshot(path='domain_form_after_submit.png')
                print("   📸 Screenshot saved: domain_form_after_submit.png")
                
                # Check current URL
                current_url = page.url
                print(f"   🔗 Current URL after submit: {current_url}")
                
                # Check if redirected to success page
                if 'thank-you' in current_url or 'success' in current_url:
                    print("   ✅ Redirected to success page")
                    
                    # Check database for the contact
                    print("   🔍 Checking database for submitted contact...")
                    models, uid = connect_to_odoo()
                    if models:
                        # Search for the contact by email
                        contacts = models.execute_kw(
                            DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
                            'res.partner', 'search_read',
                            [[('email', '=', test_data['email'])]],
                            {'fields': ['name', 'email', 'phone', 'parent_name', 'function', 'comment', 'create_date']}
                        )
                        
                        if contacts:
                            contact = contacts[0]
                            print(f"   ✅ Contact found in database:")
                            print(f"      ID: {contact['id']}")
                            print(f"      Name: {contact['name']}")
                            print(f"      Email: {contact['email']}")
                            print(f"      Created: {contact['create_date']}")
                            
                            # Clean up test data
                            models.execute_kw(
                                DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
                                'res.partner', 'unlink', [contact['id']]
                            )
                            print(f"   🧹 Test contact cleaned up")
                            
                            return True
                        else:
                            print(f"   ⚠️ Contact not found in database")
                            return False
                    else:
                        print(f"   ⚠️ Could not check database")
                        return True  # Form submission worked, database check failed
                else:
                    print(f"   ⚠️ No clear success indication")
                    return False
                
            finally:
                await browser.close()
        
    except Exception as e:
        print(f"❌ Error testing form submission: {e}")
        return False

def update_website_domain():
    """Update website domain to localhost for testing"""
    try:
        print("\n⚙️ Updating Website Domain for Testing...")
        print("-" * 50)
        
        models, uid = connect_to_odoo()
        if not models:
            return False
        
        # Get the Profectus Accounts website
        websites = models.execute_kw(
            DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
            'website', 'search_read',
            [[('name', '=', 'Profectus Accounts')]],
            {'fields': ['name', 'domain']}
        )
        
        if websites:
            website = websites[0]
            print(f"   📋 Found website: {website['name']}")
            print(f"   🔗 Current domain: {website['domain']}")
            
            # Update domain to localhost
            try:
                models.execute_kw(
                    DATABASE_CONFIG['db'], uid, DATABASE_CONFIG['password'],
                    'website', 'write',
                    [website['id']], {'domain': 'localhost:8069'}
                )
                print(f"   ✅ Updated domain to: localhost:8069")
                return True
            except Exception as e:
                print(f"   ❌ Failed to update domain: {e}")
                return False
        else:
            print(f"   ❌ Profectus Accounts website not found")
            return False
        
    except Exception as e:
        print(f"❌ Error updating domain: {e}")
        return False

async def test_after_domain_update():
    """Test contact page after domain update"""
    try:
        print("\n🧪 Testing After Domain Update...")
        print("-" * 50)
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                # Test contact page
                contact_url = f"{WEBSITE_URL}/contactus"
                print(f"   Testing: {contact_url}")
                
                response = await page.goto(contact_url, timeout=10000)
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    print("   ✅ Contact page accessible after domain update!")
                    
                    # Take screenshot
                    await page.screenshot(path='contact_after_domain_update.png')
                    print("   📸 Screenshot saved: contact_after_domain_update.png")
                    
                    # Check for forms
                    forms = await page.query_selector_all('form')
                    print(f"   📋 Found {len(forms)} form(s)")
                    
                    return len(forms) > 0
                else:
                    print(f"   ❌ Still not accessible (Status: {response.status})")
                    return False
                
            finally:
                await browser.close()
        
    except Exception as e:
        print(f"❌ Error testing after domain update: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Testing Contact Forms with Domain Configuration")
    print("=" * 60)
    
    # Step 1: Test with Host header
    host_header_works = await test_with_host_header()
    
    if host_header_works:
        # Step 2: Test form submission with domain
        form_submission_works = await test_form_submission_with_domain()
    else:
        # Step 3: Try updating domain
        print("\n🔧 Host header didn't work, trying domain update...")
        domain_updated = update_website_domain()
        
        if domain_updated:
            # Step 4: Test after domain update
            form_submission_works = await test_after_domain_update()
        else:
            form_submission_works = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FINAL TEST RESULTS")
    print("=" * 60)
    
    if host_header_works:
        print("✅ SOLUTION FOUND: Host Header Method")
        print("   • Contact forms accessible with Host header")
        print("   • Use Host: profectusaccounts.com in requests")
        
        if form_submission_works:
            print("   • Form submission works correctly")
            print("   • Data is stored in database")
            print("   • Success page redirection works")
        else:
            print("   • Form submission needs attention")
    
    elif form_submission_works:
        print("✅ SOLUTION FOUND: Domain Update Method")
        print("   • Website domain updated to localhost:8069")
        print("   • Contact forms now accessible")
        print("   • Form submission should work")
    
    else:
        print("❌ NO WORKING SOLUTION FOUND")
        print("   • Host header method failed")
        print("   • Domain update method failed")
        print("   • Contact forms remain inaccessible")
    
    print("\n📁 Files created:")
    print("   • contact_with_host_header.png")
    print("   • domain_form_before_submit.png")
    print("   • domain_form_after_submit.png")
    print("   • contact_after_domain_update.png")
    
    print("\n🎯 Next steps:")
    if host_header_works or form_submission_works:
        print("   1. Forms are working - ready for production testing")
        print("   2. Update any automated tests to use proper headers/domain")
        print("   3. Verify email notifications are configured")
    else:
        print("   1. Check Odoo website configuration")
        print("   2. Verify contact module installation")
        print("   3. Check website permissions and publishing")

if __name__ == "__main__":
    asyncio.run(main())
